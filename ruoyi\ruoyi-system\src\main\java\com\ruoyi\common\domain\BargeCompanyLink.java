package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 驳船-公司关联表
 * <AUTHOR>
 * @Date 2020/8/29 11:33
 */
@Getter
@Setter
@ToString
@TableName(value = "PB6_BARGE_COMPANY")
@KeySequence("SEQ_PB6_BARGE_COMPANY")
public class BargeCompanyLink {
    @TableId(value = "id",type = IdType.INPUT)
    private Long id;
    @TableField(value = "BARGEID")
    private Long bargeId;
    @TableField(value = "COMPANYID")
    private Long companyId;
    @TableField(value = "BINDINGTYPE")
    private Integer bindingType;
    @TableField(value = "STATUS")
    private Integer status;
    /**
     * 修改情况下的挂靠类型
     */
    @TableField(value = "UPDATEBINDINGTYPE")
    private Integer updateBindingType;
    /**
     * 审核人
     */
    @TableField(value = "AUDITBYID")
    private Long auditById;
    /**
     * 审核人名称
     */
    @TableField(value = "AUDITBYNAME")
    private String auditByName;
    /**
     * 审核时间
     */
    @TableField(value = "AUDITTIME")
    private String auditTime;
    /**
     * 挂靠审核标识
     */
    @TableField(value = "ISAUDIT")
    private Integer isAudit;
    /**
     * 创建人
     */
    @TableField(value = "CREATEBYID")
    private Long createById;
    /**
     * 创建人名称
     */
    @TableField(value = "CREATEBYNAME")
    private String createByName;
    @TableField(value = "CREATETIME")
    private String createTime;
    @TableField(value = "UPDATEBYID")
    private Long updatebyId;
    @TableField(value = "UPDATEBYNAME")
    private String updatebyName;
    @TableField(value = "UPDATETIME")
    private String updateTime;
    @TableField(value = "AUDITSTATUS")
    private Integer auditStatus;
    /**
     * 是否已删除（解除绑定关系时使用） 0-删除 ， 1-生效（默认）
     */
    @TableField(value = "ISDELETE")
    private Integer isDelete;

    /**
     * 是否添加到审核表PB6_BARGE_CHECK_MESSAGE
     */
    @TableField(exist = false)
    private boolean checkFlag = false;

    @TableField(value = "COMPANYNAME")
    private String companyName;

    @TableField(exist = false)
    private String bargeName;
}

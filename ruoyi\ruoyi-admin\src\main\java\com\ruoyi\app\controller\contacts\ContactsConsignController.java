package com.ruoyi.app.controller.contacts;

import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.app.controller.databarge.Pb6BargeworkController;
import com.ruoyi.app.controller.support.print.PrintUtil;
import com.ruoyi.barge.domain.bo.BargeUserBO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.Customer;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.vo.WaterwayCargoVO;
import com.ruoyi.common.service.CustomerService;
import com.ruoyi.contacts.service.ContactsConsignService;
import com.ruoyi.databarge.domain.Pb6Bargework;
import com.ruoyi.databarge.domain.Pb6Cargoconsignment;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.databarge.service.Pb6BargeworkService;
import com.ruoyi.databarge.service.Pb6CargoconsignmentService;
import com.ruoyi.databarge.service.Pb6CargoconsignmentdetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;

import static com.ruoyi.common.core.domain.AjaxResult.getErrors;

/**
 * @Description 托运单联系人-托运单控制层
 * <AUTHOR>
 * @Date 2020/8/18  9:16
 */

@RestController
@Slf4j
@Api(value = "托运单联系人-托运单-api", tags = "托运单联系人-托运单-api")
@RequestMapping("/contacts/consign")
public class ContactsConsignController {

    @Autowired
    private ContactsConsignService contactsConsignService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private Pb6BargeworkService pb6BargeworkService;
    @Autowired
    private PrintUtil printUtil;

    @Autowired
    private Pb6BargeworkController pb6BargeworkController;

    @Autowired
    private Pb6CargoconsignmentdetailService cargoconsignmentdetailService;

    @Autowired
    private Pb6CargoconsignmentService cargoconsignmentService;

    /**
     *  支付（月结、现结）
     * @return
     */
    @ApiOperation("支付")
    @PostMapping("/defray")
    public AjaxResult defray(@RequestBody CargocmentdetailBO cargocmentdetailBO){

        if (null == cargocmentdetailBO.getId()) {
            log.info("托运单明细id不能为空");
            return AjaxResult.error("支付失败，托运单明细id不能为空");
        }

        if (null == cargocmentdetailBO.getChargeBalanceType()) {
            log.info("支付方式不能为空");
            return AjaxResult.error("支付失败，支付方式不能为空");
        }

        return contactsConsignService.defray(cargocmentdetailBO);
    }


    /**
     * 预约、取消预约
     * @return
     */
    @ApiOperation("预约、取消预约")
    @PostMapping("/reservation")
    public AjaxResult reservation(@RequestBody CargocmentdetailBO cargocmentdetailBO) {

        Long id = cargocmentdetailBO.getId();
        String wxAppOintmentTime = cargocmentdetailBO.getWxAppOintmentTime();
        Integer wxOperateState = cargocmentdetailBO.getWxOperateState();

        if (null == cargocmentdetailBO.getId()) {
            log.info("托运单明细id不能为空");
            return AjaxResult.error("预约失败，托运单明细id不能为空");
        }

        try {
            AjaxResult result = contactsConsignService.reservation(id, wxAppOintmentTime, wxOperateState);
            //判断是否绿色驳船
            Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = cargoconsignmentdetailService.getOne(new LambdaQueryWrapper<Pb6Cargoconsignmentdetail>().eq(Pb6Cargoconsignmentdetail::getId,id));
            Pb6Cargoconsignment pb6Cargoconsignment = cargoconsignmentService.getOne(new LambdaQueryWrapper<Pb6Cargoconsignment>().eq(Pb6Cargoconsignment::getConsignflag,pb6Cargoconsignmentdetail.getConsignflag()));
            if(pb6BargeworkController.isGreenBarge(pb6Cargoconsignment.getComid(),pb6Cargoconsignmentdetail.getBargename())){
                return result;
            }
            else {
                return AjaxResult.success("预约成功请申报绿色驳船");
            }
        }catch (Exception e){
            return AjaxResult.error(e.getMessage());
        }

    }



    /**
     *  待确认审核
     * @param cargocmentdetailBO
     * @return
     */
    @ApiOperation("待确认审核")
    @PostMapping("/check")
    public AjaxResult check(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        contactsConsignService.check(cargocmentdetailBO);
        return AjaxResult.success();
    }


    /**
     * 查询托运单列表
     *
     * @param status
     * @return
     */
    @ApiOperation("不同状态栏数据展示")
    @GetMapping("/status")
    public AjaxResult bookingNoteList(@RequestParam(value = "status", required = false) Long status,
                                      @RequestParam(value = "searchValue", required = false) String searchValue,
                                      @RequestParam(value = "beginTime", required = false) String beginTime,
                                      @RequestParam(value = "endTime", required = false) String endTime) {

        return AjaxResult.success(contactsConsignService.bookingNoteList(status, searchValue, beginTime, endTime));
    }

    /**
     * 水路运单详情
     *
     * @param id 托运单明细id
     * @param status 1 改单需要数据 2水路详情
     * @return
     */
    @ApiOperation("水路运单详情")
    @GetMapping("/waybillDetail")
    public AjaxResult waybillDetail(@RequestParam("id") Long id , @RequestParam("status") Integer status) throws Exception {

        WaterwayCargoVO waterwayCargoVO = contactsConsignService.waybillDetail(id, status);

        return AjaxResult.success(waterwayCargoVO);

    }


    /**
     * 差额退款申请
     *
     * @param cargocmentdetailBO
     * @return
     */
    @ApiOperation("差额退款申请")
    @PostMapping("/balanceRefund")
    public AjaxResult balanceRefund(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        if (cargocmentdetailBO.getId() == null) {
            log.error("托运单明细id不能为空");
            return AjaxResult.error("托运单明细id不能为空");
        }
        return contactsConsignService.balanceRefund(cargocmentdetailBO);
    }


    /**
     * 获取月结公司列表
     * @return
     */
    @PostMapping("/getMonthCompany")
    public AjaxResult getMonthCompany(@RequestBody Customer customer) {
        List<Customer> customerList = customerService.getMothCompanyList(customer);
        return AjaxResult.success(customerList);
    }



    /**
     * 个人信息保存
     * @param
     * @return
     */
    @ApiOperation("个人信息保存")
    @PostMapping("/personRecord")
    public AjaxResult personRecord(@RequestBody @Valid BargeUserBO bargeUserBO, BindingResult result) {

        if (bargeUserBO.getUserId() == null) {
            log.error("用户id不能为空");
            return AjaxResult.error("用户id不能为空");
        }

        // 判断BindingResult是否保存错误的验证信息，如果有，则直接return
        if (result.hasErrors()) {
            return getErrors(result);
        }

        return contactsConsignService.personRecord(bargeUserBO);
    }

    /**
     * 个人详情
     * @return
     */
    @ApiOperation("个人详情")
    @PostMapping("/recordDetail")
    public AjaxResult recordDetail() {
        return contactsConsignService.recordDetail();
    }

    /**
     * 获取 水路货物运单.pdf 货物交接清单.docx 广州港新沙港务有限公司驳船装货交接凭证.docx 文件
     * @param waterwayCargoId 水路运单编号
     * @return base64
     */
    @GetMapping("/getPdfOrWord")
    public AjaxResult getPdfOrWord(@RequestParam("waterwayCargoId") String waterwayCargoId) throws Exception {

        if (StringUtils.isBlank(waterwayCargoId)) {
            return AjaxResult.error("水路运单编号不能为空");
        }
        Pb6Bargework pb6Bargework = pb6BargeworkService.getBaseMapper().selectOne(new QueryWrapper<Pb6Bargework>().eq("WATERWAYCARGOID", waterwayCargoId));
        HashMap<String, String> base64Map = new HashMap<>();
        for (int type = 1; type <= 3 ; type++) {
            ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
            String fileName="";
            switch (type){
                case 1:
                    if (pb6Bargework != null) {
                        printUtil.printBCJJPZ(pb6Bargework.getId(),byteArrayOutputStream);
                        fileName="广州港新沙港务有限公司驳船装货交接凭证.docx";
                    }
                    break;
                case 2:
                    printUtil.printHWJJQD(waterwayCargoId,byteArrayOutputStream);
                    fileName="货物交接清单.docx";
                    break;
                case 3:
                    printUtil.printSLHWYD(waterwayCargoId,byteArrayOutputStream);
                    fileName="水路货物运单.pdf";
                    break;
                default:
                    break;
            }
            base64Map.put(fileName, Base64.encode(byteArrayOutputStream.toByteArray()));
        }
        return AjaxResult.success(base64Map);
    }


}

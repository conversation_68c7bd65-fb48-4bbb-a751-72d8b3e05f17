<!--pages/consignment/consigndetail/bargeOutboundInquiry.wxml-->
<view class="outbount-page" hover-class="none" hover-stop-propagation="false">
  <view class="search-warp" hover-class="none" hover-stop-propagation="false">
    <view class="search-area" hover-class="none" hover-stop-propagation="false">
      <input type="text" class="search-ipt" placeholder="驳船名称"  value="{{form.search}}" bindinput="searchInput"/>
    </view>
    <view class="search-area" hover-class="none" hover-stop-propagation="false">
      <picker class="search-data-box" mode="date" value="{{form.startTime}}" bindchange="handleBeginDateChange">
        <view class="{{form.startTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{form.startTime ? form.startTime :'开始时间'}}</view>
      </picker>
      <text class="padding-text">-</text>
      <picker class="search-data-box" mode="date" value="{{form.endTime}}" bindchange="handleEndDateChange">
        <view class="{{form.endTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{form.endTime ? form.endTime :'截止时间'}}</view>
      </picker>
    </view>
    <view class="search-area" hover-class="none" hover-stop-propagation="false">
      <input type="text" class="search-ipt" placeholder="出库单号或到验号"  value="{{form.outboundNum}}" bindinput="outboundNumInput"/>
      <button class="publish-btn search-btn" bindtap="handleSearch">搜索</button>
      <button class="publish-btn search-btn" bindtap="handleExport">导出</button>
    </view>
  </view>
  <view class="data-list" hover-class="none" hover-stop-propagation="false">
    <view class="data-item" wx:key="index" wx:for="{{listData}}" hover-class="none" hover-stop-propagation="false">
      <view class="data-item-title" hover-class="none" hover-stop-propagation="false" data-id="{{item.id}}" data-item="{{item}}" bindtap="handleSupplyDetail">
        <view class="data-item-text" hover-class="none" hover-stop-propagation="false">驳船{{index+1}}</view>
        <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{20}}"></mp-icon>
      </view>
    </view>
  </view>  
</view>

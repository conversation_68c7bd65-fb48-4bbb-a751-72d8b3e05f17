package com.ruoyi.base.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 盖章日志
 * @TableName STAMP_LOG
 */
@TableName(value ="STAMP_LOG")
@Data
@KeySequence(value = "SEQ_STAMP_LOG")
public class StampLog extends BaseEntity implements Serializable {
    /**
     * id
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 驳船名
     */
    private String bargeName;

    // 驳船航次
    private String bargeNumber;

    /**
     * 大船名
     */
    private String shipName;

    /**
     * 大船航次
     */
    private String shipNumber;

    /**
     * 托运单下驳船详情id
     */
    private String detailId;

    private String status;

    private String result;

    private String stampBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
package com.ruoyi.databarge.domain.dto;

import com.ruoyi.databarge.domain.*;
import lombok.Getter;
import lombok.Setter;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/10/29.
 * @Date: 2020/10/29 16:53
 */
@Getter
@Setter
public class Pb6CargoconsignmentMainAndDetailAndBakDTO {

    private Pb6Cargoconsignment pb6Cargoconsignment;

    private Pb6CargoconsignmentBak pb6CargoconsignmentBak;

    private Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail;

    private Pb6CargoconsignmentdetailBak pb6CargoconsignmentdetailBak;

    private Integer checkStatus; // 审核状态： 1审核通过，2审核不通过

    private String failureReasons;

    private Long pb6BargeCheckMessageId;

    private Integer sign; //退改单标识: 0退单， 1改单
}

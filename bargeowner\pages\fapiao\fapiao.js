// pages/consigncontact/fapiao/fapiao.js
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    fapiaoTypeList: [{
      type: 0,
      name: '普票',
      checked: true
    }, {
      type: 1,
      name: '专票',
    }],
    fapiaoHeadTypeList: [{
      type: 1,
      name: '个人', 
    }, {
      type: 0,
      name: '企业', 
    }],
    // 选中的发票类型
    fapiaoType: 0, // 默认为普票
    // 选中的抬头类型 
    fapiaoHeadType: 1, // 默认为个人
    // 发票抬头 个人
    fapiaoHeadPerson: [],
    // 发票抬头 企业
    fapiaoHeadCompany: [],
    // 发票抬头
    fapiaoHead: [],
    fapiaoIndex: '',

    saveParamData: {
      waterwayCargoId: '', // 水路运单编号

      invoicetype: 0, // 发票类型  0 普票 1 专票

      type: 1, // 发票类型 0是企业 1是个人

      buyername: '', // type = 0 为个人名称 type = 1 为企业名称

      taxnum: '', // 纳税识别号  企业必填

      phone: '',//购方手机(开票成功会短信提醒购方),必填
      
      address: '',//购方地址,企业要填，个人可为空,非必填

      account: '',//购方银行账号,企业要填，个人可为空,非必填

      tsfs: 1,//推送方式:-1不推送,0邮箱,1手机(默认),2邮箱和手机,非必填

      email: '',//推送邮箱,tsfs为0或2时必填
    },
    // 托运单详情信息
    consignMsg: {

    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let userInfo = JSON.parse(wx.getStorageSync('userInfo'));
    let currentConsignObj = JSON.parse(wx.getStorageSync('payCurrentConsignObj'));// 当前托运对象
    this.setData({
      ['saveParamData.shipUserId']: userInfo.userId,
      ['saveParamData.shipUserName']: userInfo.nickName,
    });
    let param = {
      id: currentConsignObj.consignDetailId,
    };
    app.$post(app.$url.consign.getConsignDetail, param).then(res => {
      if(res.code == 200) {
        if(!res.data.waterWayCargoId) {
          app.$message('水路运单号缺失，请确认单号是否正常')
          return
        }
        // 获取发票抬头数据
        let headerDataPerson = [];
        let fapiaoHeadCompany = [];
        headerDataPerson.push(res.data.bargeName);
        fapiaoHeadCompany.push(res.data.bargeName);

        //2021.10.25添加，抬头类型为个人时，只能选驳船
        res.data.consignee ? fapiaoHeadCompany.push(res.data.consignee) : '';
        res.data.consigner ? fapiaoHeadCompany.push(res.data.consigner) : '';
        res.data.bargeCallCompanyName ? fapiaoHeadCompany.push(res.data.bargeCallCompanyName) : '';
        this.setData({
          'fapiaoHeadPerson': headerDataPerson,
          'fapiaoHeadCompany': fapiaoHeadCompany,
          'saveParamData.waterwayCargoId': res.data.waterWayCargoId,
          'consignMsg': res.data
        });

        if(this.data.fapiaoHeadType == 0){
          this.setData({
            'fapiaoHead': fapiaoHeadCompany
          });
        } else {
          this.setData({
            'fapiaoHead': headerDataPerson
          });
        }
      }else {
        app.$message(`获取运单详情失败，原因${res.msg}`);
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },
  // 发票类型改变
  handleTypeChange(e) {
    this.setData({
      'fapiaoType': e.detail.value,
      ['saveParamData.invoicetype']: e.detail.value,
    });
  },
  // 发票抬头类型改变
  handleHeadTypeChange(e) {
    this.setData({
      'fapiaoHeadType': e.detail.value,
      ['saveParamData.type']: e.detail.value,
      'fapiaoIndex': ''
    })
    if(e.detail.value == 0){
      this.setData({
        'fapiaoHead': this.data.fapiaoHeadCompany,
      })
    } else {
      this.setData({
        'fapiaoHead': this.data.fapiaoHeadPerson,
      })
    }
  }, 
  // 发票抬头改变
  handlePickChange(e) {
    this.setData({
      'fapiaoIndex': e.detail.value,
      ['saveParamData.buyername']: this.data.fapiaoHead[e.detail.value]
    });
    app.$post(app.$url.getHistoryInvoice, {buyername: this.data.fapiaoHead[e.detail.value], type: this.data.saveParamData.type}).then((res) => {
      if(res.code == 200) {
        // 如果历史开票记录存在 那么回填信息
        if(res.data && res.data.length >= 1) {
          let indexRow = res.data[0];
          let fapiaoHeadTypeList = this.data.fapiaoHeadTypeList;
          for(let i = 0; i < fapiaoHeadTypeList.length; i++) {
            if(fapiaoHeadTypeList[i].type == indexRow.type) {
              fapiaoHeadTypeList[i].checked = true;
            }else {
              fapiaoHeadTypeList[i].checked = false;
            }
          }
          this.setData({
            'fapiaoHeadTypeList': fapiaoHeadTypeList,
            'fapiaoHeadType': indexRow.type || '',
            ['saveParamData.type']: indexRow.type || '',
            ['saveParamData.address']: indexRow.address || '',
            ['saveParamData.account']: indexRow.account || '',
            ['saveParamData.email']: indexRow.email || '',
            ['saveParamData.taxnum']: indexRow.taxnum || '',
            ['saveParamData.phone']: indexRow.phone || '',
          })
        }
      }else {
        app.$message('获取发票历史信息失败，请手动填写发票信息')
      }
    })
  },
  // 统一输入
  handleValueInput(e) {
    let key = e.currentTarget.dataset.key;
    this.setData({
      [key]: e.detail.value
    });
  },
  // 点击提交 普票
  handleSubmit() {
    let param = {};
    if(!this.data.saveParamData.buyername) {
      app.$message('请选择发票抬头！');
      return;
    }
    if(!/^[1][3,4,5,7,8][0-9]{9}$/.test(this.data.saveParamData.phone)) {
      app.$message('请输入正确的手机号！');
      return;
    }
    // 如果邮箱存在 那么判断邮箱是否正确
    if(this.data.saveParamData.email) {
      if(!/^\w+@[a-zA-Z0-9]{2,10}(?:\.[a-z]{2,4}){1,3}$/.test(this.data.saveParamData.email)) {
        app.$message('请输入正确的邮箱！');
        return;
      }
    }
    if(this.data.saveParamData.type == 1) {
      param = {
        waterwayCargoId: this.data.saveParamData.waterwayCargoId,
        type: this.data.saveParamData.type,
        invoicetype: this.data.saveParamData.invoicetype,
        buyername: this.data.saveParamData.buyername,
        phone: this.data.saveParamData.phone,
        tsfs: this.data.saveParamData.tsfs,
        email: this.data.saveParamData.email
      };
    }
    if(this.data.saveParamData.type == 0) {
      this.data.saveParamData.taxnum = this.data.saveParamData.taxnum.replace(/(^\s*)|(\s*$)/g,'');
      this.data.saveParamData.address = this.data.saveParamData.address.replace(/(^\s*)|(\s*$)/g,'');
      let reg = /^[a-zA-Z0-9]{15,}$/
      if(!this.data.saveParamData.taxnum) {
        app.$message('请输入纳税识别号！');
        return;
      }
      if(!reg.test(this.data.saveParamData.taxnum)) {
        app.$message('纳税人识别号，只能输入英文和字母，且不能少于15位！');
        return;
      }
/*       if(!this.data.saveParamData.address) {
        app.$message('请输入地址！');
        return;
      }
      if(!/^([1-9]{1})(\d{11}|\d{15}|\d{16}|\d{17}|\d{18})$/.test(this.data.saveParamData.account)) {
        app.$message('银行卡号不正确');
        return;
      } */
      param = {
        waterwayCargoId: this.data.saveParamData.waterwayCargoId,
        type: this.data.saveParamData.type,
        invoicetype: this.data.saveParamData.invoicetype,
        buyername: this.data.saveParamData.buyername,
        taxnum: this.data.saveParamData.taxnum,
        phone: this.data.saveParamData.phone,
/*         address: this.data.saveParamData.address,
        account: this.data.saveParamData.account, */
        tsfs: this.data.saveParamData.tsfs,
        email: this.data.saveParamData.email
      };

    }
    param.detail = [];
    /* if(Number(this.data.consignMsg.cargoPortCharge)> 0) {
      param.detail.push({
        "goodsname": '货物港物费',
        "price": this.data.consignMsg.cargoPortCharge,
        "num":"1",
        "hsbz":"0",
        "taxrate":"0.13",
        "fphxz":"0",
        "yhzcbs":"0",
        "spbm":"***********"
      })
    }
    if(Number(this.data.consignMsg.serviceAgentCharge) > 0) {
      param.detail.push({
        "goodsname": '围油栏费',
        "price": this.data.consignMsg.serviceAgentCharge,
        "num":"1",
        "hsbz":"0",
        "taxrate":"0.13",
        "fphxz":"0",
        "yhzcbs":"0",
        "spbm":"***********"
      })
    } */
    if(Number(this.data.consignMsg.businessAgentCharge) > 0) {
      param.detail.push({
        "goodsname": '代理费',
        "price": this.data.consignMsg.businessAgentCharge,
        "num":"1",
        "hsbz":"0",
        "taxrate":"0.13",
        "fphxz":"0",
        "yhzcbs":"0",
        "spbm":"***********"
      })
    }
/*     if(Number(this.data.consignMsg.berthCharge) > 0) {
      param.detail.push({
        "goodsname": '停泊费',
        "price": this.data.consignMsg.berthCharge,
        "num":"1",
        "hsbz":"0",
        "taxrate":"0.13",
        "fphxz":"0",
        "yhzcbs":"0",
        "spbm":"***********"
      })
    } */
    app.$post(app.$url.saveInvoice, param).then((res) => {
      if(res.code == 200) {
        app.$post(app.$url.pay.applyBargePay, this.data.consignMsg.waterWayCargoId).then(res => {
          if(res.code == 200 && res.data!=null){
            if(res.data.resmsg.reshead.procd == "0000"){
              let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):"";//获取用户信息
              if(userInfos != ""){
                wx.navigateTo({
                  url: '/pages/pay/pay?gatewayurl=' + res.data.resmsg.gatewayurl + "&orderid=" + res.data.resmsg.orderid + "&openid=" + userInfos.openId,
                })
              } else {
                wx.showToast({
                  title: "没有登录用户信息！",
                  icon: 'none',
                  duration: 2000
                })
              }
            } else {
              wx.showToast({
                title: res.data.resmsg.reshead.proinfo,
                icon: 'none',
                duration: 2000
              })
            }
          }
          else{
            const mes = res.msg;
            console.log(mes);
            app.$message(mes);
          }
        })
      }else {
        app.$message(`申请开发票失败`);
      }
    })
  },
  // 点击提交 专票
  handleProSubmit() {
    // if(!this.data.consignMsg.bargeCallCompanyName) {
    //   app.$message('驳船没有租用公司，不能开专票');
    //   return;
    // }
    if(!this.data.saveParamData.buyername) {
        app.$message('请选择发票抬头！');
        return;
      }
      if(!/^[1][3,4,5,7,8][0-9]{9}$/.test(this.data.saveParamData.phone)) {
        app.$message('请输入正确的手机号！');
        return;
      }
      // 如果邮箱存在 那么判断邮箱是否正确
      if(this.data.saveParamData.email) {
        if(!/^\w+@[a-zA-Z0-9]{2,10}(?:\.[a-z]{2,4}){1,3}$/.test(this.data.saveParamData.email)) {
          app.$message('请输入正确的邮箱！');
          return;
        }
      }
      if(this.data.saveParamData.type == 0) {
        this.data.saveParamData.taxnum = this.data.saveParamData.taxnum.replace(/(^\s*)|(\s*$)/g,'');
        let reg = /^[a-zA-Z0-9]{15,}$/
        if(!this.data.saveParamData.taxnum) {
          app.$message('请输入纳税识别号！');
          return;
        }
        if(!reg.test(this.data.saveParamData.taxnum)) {
          app.$message('纳税人识别号，只能输入英文和字母，且不能少于15位！');
          return;
        }
    }
    let param = {
        waterwayCargoId: this.data.saveParamData.waterwayCargoId,
        type: this.data.saveParamData.type,
        invoicetype: this.data.saveParamData.invoicetype,
        buyername: this.data.saveParamData.buyername,
        taxnum: this.data.saveParamData.taxnum,
        phone: this.data.saveParamData.phone,
        tsfs: this.data.saveParamData.tsfs,
        email: this.data.saveParamData.email
    }
    app.$post(app.$url.saveInvoice, param).then((res) => {
      if(res.code == 200) {
        app.$post(app.$url.pay.applyBargePay, this.data.consignMsg.waterWayCargoId).then(res => {
          if(res.code == 200){
            if(res.data.resmsg.reshead.procd == "0000"){
              let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):"";//获取用户信息
              if(userInfos != ""){
                wx.navigateTo({
                  url: '/pages/pay/pay?gatewayurl=' + res.data.resmsg.gatewayurl + "&orderid=" + res.data.resmsg.orderid + "&openid=" + userInfos.openId,
                })
              } else {
                wx.showToast({
                  title: "没有登录用户信息！",
                  icon: 'none',
                  duration: 2000
                })
              }
            } else {
              wx.showToast({
                title: res.data.resmsg.reshead.proinfo,
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      }else {
        app.$message(`申请开发票失败`);
      }
    })
    // app.$post(app.$url.pay.applyBargePay, this.data.consignMsg.waterWayCargoId).then(res => {
    //   if(res.code == 200){
    //     if(res.data.resmsg.reshead.procd == "0000"){
    //       let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):"";//获取用户信息
    //       if(userInfos != ""){
    //         wx.navigateTo({
    //           url: '/pages/pay/pay?gatewayurl=' + res.data.resmsg.gatewayurl + "&orderid=" + res.data.resmsg.orderid + "&openid=" + userInfos.openId,
    //         })
    //       } else {
    //         wx.showToast({
    //           title: "没有登录用户信息！",
    //           icon: 'none',
    //           duration: 2000
    //         })
    //       }
    //     } else {
    //       wx.showToast({
    //         title: res.data.resmsg.reshead.proinfo,
    //         icon: 'none',
    //         duration: 2000
    //       })
    //     }
    //   }
    // })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
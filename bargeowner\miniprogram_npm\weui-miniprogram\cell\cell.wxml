<block wx:if="{{link}}">
    <view bindtap="navigateTo" class="weui-cell weui-cell_access {{extClass}} {{outerClass}}{{inForm ? ' weui-cell-inform' : ''}}{{inline ? '' : ' .weui-cell_label-block'}}" hover-class="{{hover ? 'weui-cell_active weui-active' : extHoverClass}}">
        <view wx:if="{{hasHeader}}" class="weui-cell__hd {{iconClass}}">
            <block wx:if="{{icon}}">
                <image src="{{icon}}" class="weui-cell__icon" mode="aspectFit"></image>
            </block>
            <block wx:else>
                <slot name="icon"></slot>
            </block>
            <block wx:if="{{inForm}}">
                <block wx:if="{{title}}"><view class="weui-label">{{title}}</view></block>
                <block wx:else>
                    <slot name="title"></slot>
                </block>
            </block>
            <block wx:else>
                <block wx:if="{{title}}">{{title}}</block>
                <block wx:else>
                    <slot name="title"></slot>
                </block>
            </block>
        </view>
        <view wx:if="{{hasBody}}" class="weui-cell__bd">
            <block wx:if="{{value}}">{{value}}</block>
            <block wx:else>
                <slot></slot>
            </block>
        </view>
        <view wx:if="{{hasFooter}}" class="weui-cell__ft weui-cell__ft_in-access {{footerClass}}">
            <block wx:if="{{footer}}">{{footer}}</block>
            <block wx:else>
                <slot name="footer"></slot>
            </block>
        </view>
    </view>
</block>
<block wx:else>
    <view bindtap="navigateTo" class="weui-cell {{showError && error ? 'weui-cell_warn' : ''}} {{inForm ? 'weui-cell-inform' : ''}} {{extClass}} {{outerClass}}" hover-class="{{hover ? 'weui-cell_active weui-active' : extHoverClass}}">
        <view wx:if="{{hasHeader}}" class="weui-cell__hd {{iconClass}}">
            <block wx:if="{{icon}}">
                <image src="{{icon}}" class="weui-cell__icon" mode="aspectFit"></image>
            </block>
            <block wx:else>
                <slot name="icon"></slot>
            </block>
            <block wx:if="{{inForm}}">
                <block wx:if="{{title}}"><view class="weui-label">{{title}}</view></block>
                <block wx:else>
                    <slot name="title"></slot>
                </block>
            </block>
            <block wx:else>
                <block wx:if="{{title}}">{{title}}</block>
                <block wx:else>
                    <slot name="title"></slot>
                </block>
            </block>
        </view>
        <view wx:if="{{hasBody}}" class="weui-cell__bd {{bodyClass}}">
            <block wx:if="{{value}}">{{value}}</block>
            <block wx:else>
                <slot></slot>
            </block>
        </view>
        <view wx:if="{{hasFooter}}" class="weui-cell__ft {{footerClass}}">
            <block wx:if="{{footer}}">{{footer}}</block>
            <block wx:else>
                <slot name="footer"></slot>
            </block>
            <icon wx:if="{{showError && error}}" type="warn" size="23" color="#E64340"></icon>
        </view>
    </view>
</block>
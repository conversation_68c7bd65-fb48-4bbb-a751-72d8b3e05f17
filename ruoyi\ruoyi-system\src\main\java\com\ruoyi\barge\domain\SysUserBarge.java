package com.ruoyi.barge.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/30 10:23
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
@TableName("SYS_USER_BARGE_RECOVERY")
public class SysUserBarge {

    /**
     * 用户id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 驳船id
     */
    @TableField("barge_id")
    private Long bargeId;

    /**
     * 驳船备份id
     */
    @TableField("barge_audit_id")
    private Long bargeAuditId;
}

package com.ruoyi.framework.config.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.utils.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/2/21 16:50
 */
@Component
public class BaseMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        if (metaObject.hasSetter("createBy")) {
            try {

                metaObject.setValue("createBy", null);
                this.strictInsertFill(metaObject, "createBy", String.class, SecurityUtils.getUsername());

            } catch (Exception e) {
                this.strictInsertFill(metaObject, "createBy", String.class, "");
            }
        }

        if (metaObject.hasSetter("createName")) {
            try {

                metaObject.setValue("createName", null);
                this.strictInsertFill(metaObject, "createName", String.class, SecurityUtils.getUsername());

            } catch (Exception e) {
                this.strictInsertFill(metaObject, "createName", String.class, "");
            }
        }

        if (metaObject.hasSetter("createTime")) {
            metaObject.setValue("createTime", null);
            this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (metaObject.hasSetter("updateBy")) {
            try {
                metaObject.setValue("updateBy", null);
                this.strictUpdateFill(metaObject, "updateBy", String.class, SecurityUtils.getUsername());
            } catch (Exception e) {
                this.strictUpdateFill(metaObject, "updateBy", String.class, "");
            }
        }

        if (metaObject.hasSetter("updateName")) {
            try {
                metaObject.setValue("updateName", null);
                this.strictUpdateFill(metaObject, "updateName", String.class, SecurityUtils.getUsername());
            } catch (Exception e) {
                this.strictUpdateFill(metaObject, "updateName", String.class, "");
            }
        }

        if (metaObject.hasSetter("updateTime")) {
            //            System.out.println(DateUtils.getTime());
            metaObject.setValue("updateTime", null);
            this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
        }
    }
}

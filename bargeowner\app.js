// app.js
// const root = "https://bulkbarge.gzport.com/gzgapp";//生产服务器
// const root = "http://localhost:8080/gzgapp"; // 调试使用
// const root = "http://*************:1220/gsapp"; // 港盛测试环境开单调试使用
const root = "http://localhost:1220/gsapp"; // 港盛测试环境开单调试使用
// const root = "https://scbs.gzport.com/gsapp"; // 港盛正式环境开单调试使用
//const root = "http://************:8080/gzgapp"; // 调试使用
const appLogin = "/appLogin";
const getInfo = "/getInfo";
const consignment = "/barge/consignment";//托运
const capacity = "/barge/capacity";//主页
const center = "/barge/center";//我的
const authorization = "/authorization";//权限
const notice = "/notice";//弹窗消息
const common = "/common";//公共
const uploadFile = "/barge/uploadaddress"

// jinn 2021-01-28 支付
const bargePay = "/barge/pay"; //支付
// jinn 2021-01-28 支付

// jinn 2021-07-13 代理发货
const agentDelivery = "/barge/agentDelivery"; //代理发货
const upload = "/barge/uploadaddress"; //上传
const pb3coutform="/barge/pb3coutform"
// jinn 2021-07-13 代理发货

// jinn 2021-10-08 驳卡详表(南粮)
const rootCar = "https://vcbooking.gzport.com/test-api";
const carExtract = "/carExtract";
// jinn 2021-10-08 驳卡详表(南粮)
App({
  onLaunch: function () {
    // 小程序版本更新
    const updateManager = wx.getUpdateManager()
    updateManager.onCheckForUpdate(function (res) {
      console.log(res.hasUpdate)
    })
    updateManager.onUpdateReady(function () {
      wx.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success(res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate()
          }
        }
      })
    })
    updateManager.onUpdateFailed(function () {
      
    })
    // 获取手机系统信息
    wx.getSystemInfo({
      success: e => {
        this.globalData.StatusBar = e.statusBarHeight;
        let capsule = wx.getMenuButtonBoundingClientRect();
        if (capsule) {
          this.globalData.Custom = capsule;
          this.globalData.CustomBar = capsule.bottom + capsule.top - e.statusBarHeight + 5;
        } else {
          this.globalData.CustomBar = e.statusBarHeight + 50;
        }
      }
    });
  },
  $post(url,data) {
    let that = this;
    return new Promise(function(resolve,reject) {
      if(!url) {
        wx.showToast({
          title: '请求url为空，请求终止！',
          icon: 'none',
          duration: 5000
        })
        reject();
      }
      wx.showLoading({
        title: '加载中',
        mask: true,
      })
      let header = null;
      if(url.indexOf('/appLogin') >= 0){
        header = {'content-type':'application/json'};
      }else {
        console.log(wx.getStorageSync('token'));
        header = {
          'content-type': 'application/json',
          'Authorization': wx.getStorageSync('token'),
        };
      }
      wx.request({
        url: url,
        data: data,
        header: header,
        timeout:60000,
        method: 'POST',
        dataType: 'json',
        responseType: 'text',
        success: (result)=>{ 
          wx.hideLoading()
          if(result.statusCode == 200){
            if(result.data.code == 401){
              that.$message("当前账号异常，请重新登录！")
              setTimeout(() => {
                wx.redirectTo({
                  url:"/pages/login/login"
                })
              }, 1000);
              return
            }
            resolve(result.data)
          }else {
            wx.showToast({
              title: '请求发生了错误，请重试',
              icon: 'none',
              duration: 2000
            });
            reject(result.data);
          }
        },
        fail: ()=>{
          wx.hideLoading()
          wx.showToast({
            title: '请求发生了错误，请重试',
            icon: 'none',
            duration: 2000
          })
          reject()
        },
        complete: ()=>{
        }
      });
    })
  },
  $get(url,data) {
    let that = this;
    return new Promise(function(resolve,reject) {
      if(!url) {
        wx.showToast({
          title: '请求url为空，请求终止！',
          icon: 'none',
          duration: 2000
        })
        reject();
      }
      wx.showLoading({
        title: '加载中',
        mask: true,
      })
      let header = null;
      if(url.indexOf('/appLogin') >= 0){
        header = {'content-type':'application/json'};
      }else {
        console.log(wx.getStorageSync('token'));
        header = {
          'content-type': 'application/json',
          'Authorization': wx.getStorageSync('token'),
        };
      }
      wx.request({
        url: url,
        data: data,
        header: header,
        timeout:60000,
        method: 'GET',
        dataType: 'json',
        responseType: 'text',
        success: (result)=>{
          wx.hideLoading()
          if(result.statusCode == 200){
            if(result.data.code == 401){
              that.$message("当前账号异常，请重新登录！")
              setTimeout(() => {
                wx.redirectTo({
                  url:"/pages/login/login"
                })
              }, 1000);
              return
            }
            resolve(result.data)
          }else {
            wx.showToast({
              title: '请求发生了错误，请重试',
              icon: 'none',
              duration: 2000
            });
            reject(result.data);
          }
        },
        fail: ()=>{
          wx.hideLoading()
          wx.showToast({
            title: '请求发生了错误，请重试',
            icon: 'none',
            duration: 2000
          })
          reject()
        },
        complete: ()=>{
        }
      });
    })
  },
  $downLoad(url,data,responseType) {
    let that = this;
    return new Promise(function(resolve,reject) {
      if(!url) {
        wx.showToast({
          title: '请求url为空，请求终止！',
          icon: 'none',
          duration: 2000
        })
        reject();
      }
      wx.showLoading({
        title: '加载中',
        mask: true,
      })
      let header = null;
      if(url.indexOf('/appLogin') >= 0){
        header = {'content-type':'application/json'};
      }else {
        console.log(wx.getStorageSync('token'));
        header = {
          'content-type': 'application/json',
          'Authorization': wx.getStorageSync('token'),
        };
      }
      wx.request({
        url: url,
        data: data,
        header: header,
        timeout:60000,
        method: 'GET',
        dataType: 'json',
        responseType: responseType ? responseType : 'arraybuffer',
        success: (result)=>{
          wx.hideLoading()
          if(result.statusCode == 200){
            if(result.data.code == 401){
              that.$message("当前账号异常，请重新登录！")
              setTimeout(() => {
                wx.redirectTo({
                  url:"/pages/login/login"
                })
              }, 1000);
              return
            }
            resolve(result.data)
          }else {
            wx.showToast({
              title: '请求发生了错误，请重试',
              icon: 'none',
              duration: 2000
            });
            reject(result.data);
          }
        },
        fail: ()=>{
          wx.hideLoading()
          wx.showToast({
            title: '请求发生了错误，请重试',
            icon: 'none',
            duration: 2000
          })
          reject()
        },
        complete: ()=>{
        }
      });
    })
  },
  globalData: {
    userInfo: null,
    CustomBar: 20, // 导航栏高度 默认为20 可以通过计算
    StatusBar: 20, // 状态栏高度 例如时间 电量等等
  },
  $message(title) {
    title? title : title='请勿频繁重复点击，请退出后重试';
    wx.showToast({
      title: title,
      icon: 'none',
      duration: 2000
    })
  },
  // 一个数组对象根据属性去重
  $arrObjReduce(arr,key) {
    let hash = {};
    let newArr = arr.reduce((item, next) => {
      hash[next[key]] ? "" : hash[next[key]] = true && item.push(next);
      return item
    }, []);
    return newArr
  },
  $url: {
    getWxOpenId: root + appLogin + '/getWxOpenId', // 获取openid
    getWxUserInfo: root + appLogin + '/getWxUserInfo', // 获取手机号
    login: root + appLogin + '/login', // 登录接口
    getInfo: root + getInfo, // 获取用户信息
    saveInvoice: root + '/wxInvoice/saveInvoice', // 申请开具发票
    getInvoice: root + '/wxInvoice/getInvoice', // 获取发票信息 
    getInvoiceAnnex: root + '/wxInvoice/getInvoiceAnnex', // 获取发票附件
    getHistoryInvoice: root + '/wxInvoice/getHistoryInvoice', // 获取发票历史附件
    sendInvoiceAnnexToEmail: root + '/wxInvoice/sendInvoiceAnnexToEmail', // 发送发票到邮箱
    consign:{
      getConsignmentList: root + consignment + '/getConsignmentList',//托运列表
      confirmConsign: root + consignment + '/confirmConsign',//确认
      cancelConfirmConsign: root + consignment + '/cancelConfirmConsign',//取消确认
      defray: root + consignment + '/defray',//支付
      reservation: root + consignment + '/reservation',//预约
      chargeback: root + consignment + '/chargeback',//退单
      getConsignDetail: root + consignment + '/getConsignDetail',//托运单详情
      getCarrierList: root + consignment + '/getCarrierList',//获取船公司列表
      sendEmail: root + common + '/sendEmail',//发送邮件
      bargeRefund: root + consignment +  '/bargeRefund', // 差额退款
      getMonthCompany: root + consignment + '/getMonthCompany', // 获取月结单位公司
      getPdfOrWord: root  + consignment +'/getPdfOrWord',//获取水路运单等资料
      searchPb6WatercargoFileApp: root  + uploadFile +'/searchPb6WatercargoFileApp',//获取水路运单文件
      
    },
    //我的
    mine:{
      updateUserType: root + center + '/updateUserType',//修改用户类型
      personRecord: root + center + '/personRecord',//个人备案
      checkRecordIsSuccess: root + center + '/checkRecordIsSuccess',//检查是否备案成功
      switchIdentity: root + center + '/switchIdentity',// 切换身份
      getBargeList: root + center + '/getBargeList',//获取驳船列表
      addBarge: root + center + '/addBarge',//新增驳船-目前这个接口只校验唯一性，并不是新增
      bargeRecord: root + center + '/bargeRecord',//驳船备案
      bargeRecordAdd: root + center + '/bargeRecordAdd',//驳船备案
      getCrewList: root + center + '/getCrewList',// 获取船员列表
      addCrew: root + center + '/addCrew',//增船员
      delCrew: root + center + '/delCrew',//删除船员
      recordDetail: root + center + '/recordDetail',//获取备案信息详情
      adminMenu: root + authorization + '/adminMenu',//获取授权列表
      authorize: root + authorization + '/authorize',//授权
      getNoticeList: root + notice + '/getNoticeList',//获取消息列表
      updateIsRead: root + notice + '/updateIsRead',//消息已读
      subscription: root + notice + '/subscription',//订阅
      cancelSubscription: root + notice + '/cancelSubscription',//取消订阅
      weChatUploadFile: root + common + '/weChatUploadFile',//上传
      getSeal: root + '/seal/search/byShipFddUserRel',//获取电子签章详情
      getLoading: root + '/barge/center/getLoading', // 获取水路运单接口
      //confirmLoadingOver: root + '/barge/center/confirmLoadingOver', // 确认实装数接口
      confirmLoadingOver: root + '/barge/consignment/confirmLoadingOver', // 确认实装数接口
      confirmLoadingOverForApp: root + '/barge/consignment/confirmLoadingOverForApp', // 确认实装数接口 驳船组
      reGenerateWaterWayCargoApp: root + '/barge/consignment/reGenerateWaterWayCargoApp', // 重新生成水路运单
      getGoodsCategorylist: root + '/basic/goodsCategory/GoodsCategorylist' // 获取货类列表

    },
    //主页
    main:{
      getCargoSourceList: root + capacity + '/getCargoSourceList',//获取货源信息列表
      getCapacityList: root + capacity + '/getCapacityList',//获取运力信息列表
      addCapacity: root + capacity + '/addCapacity',//增加运力
      updateCapacity: root + capacity + '/updateCapacity',// 修改运力
      deleteCapacity: root + capacity + '/deleteCapacity',//删除运力

    },

    // jinn 2021-01-28 支付
    pay: {
      applyBargePay: root + bargePay + '/apply', // 创建订单
      payDetails: root + bargePay + '/search/payDetails', // 获取支付记录信息
    },
    // jinn 2021-07-13 代理发货
    agentDelivery: {
      list: root + agentDelivery + '/list', //查询列表数据
      add: root + agentDelivery + '/add', //添加
      delete: root + agentDelivery + '/deleteById', //删除
      update: root + agentDelivery + '/update', //修改
      selectUserBargeName: root + agentDelivery + '/selectUserBargeName', //查询用户驳船
      updateAgentDeliveryImage: root + upload + '/updateAgentDeliveryImage', //上传代理发货图片
      selectAgentDeliveryImages: root + upload + '/selectAgentDeliveryImages', //获取代理发货图片
      listCOutFormId: root + pb3coutform + '/listCOutFormId'
    },
    // jinn 2021-10-08 驳卡详表(南粮)
    bargeDetail: {
      generatePdf: rootCar + carExtract + "/export/generatePdf", //导出pdf
      list: rootCar + carExtract + "/list", //获取List
      download: rootCar + "/common/download", //下载pdf
      sendEmail: rootCar + carExtract + "/export/generatePdf/email" //下载邮箱
    },
    notice: {
      saveCaptainStatement: root + '/notice/saveCaptainStatement', // 保存船长声明
      getCaptainStatement: root + '/notice/getCaptainStatement', // 获取船长声明
      saveAnchorageCheck: root + '/notice/anchorageCheck/save', // 保存安全检查表
      getAnchorageCheck: root + '/notice/anchorageCheck/get', // 获取安全检查表
      getAnchorageCheckItems: root + '/notice/anchorageCheck/getItems', // 获取安全检查表项目
      saveSubQuestions: root + '/notice/anchorageCheck/saveSubQuestions', // 保存子问题
      getSubQuestions: root + '/notice/anchorageCheck/getSubQuestions',
      updateSubQuestions: root + '/notice/anchorageCheck/updateSubQuestions',
      saveLoadingConfirm: root + '/notice/saveLoadingConfirm', // 保存安全装货确认书
      getLoadingConfirm: root + '/notice/getLoadingConfirm' // 获取安全装货确认书
    }

    
  },
})
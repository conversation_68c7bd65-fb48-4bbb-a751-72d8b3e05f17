package com.ruoyi.carrier.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.*;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/12/24 11:17
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class CompanyMonthlyCodeBO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 月结码船公司id
     */
    private Long companyId;

    /**
     * 月结码（随机6位数字）
     */
    private String monthlyCode;

    /**
     * 使用月结码的用户id
     */
    private Long useUserId;

    /**
     * 使用月结码的驳船id
     */
    private Long useBargeId;

    /**
     * 使用月结码的公司id
     */
    private Long useCompanyId;

    /**
     * 是否使用（0-未使用，1-已使用）
     */
    private String isUsed;

    /**
     * 使用月结码时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date usedTime;

    /**
     * 创建月结码时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 重写请求参数 */
    private Map<String, Object> params;

    /**
     * 生成月结码数量
     */
    private Integer codeNumbers;
}

package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.databarge.domain.PubCompany;
import com.ruoyi.databarge.mapper.PubCompanyMapper;
import com.ruoyi.databarge.service.PubCompanyService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 公司表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-18
 */
@Service
public class PubCompanyServiceImpl extends ServiceImpl<PubCompanyMapper, PubCompany> implements PubCompanyService {
    @Override
    public String getComsnameById(Long id) {
        return super.baseMapper.getComsnameById(id);
    }
}

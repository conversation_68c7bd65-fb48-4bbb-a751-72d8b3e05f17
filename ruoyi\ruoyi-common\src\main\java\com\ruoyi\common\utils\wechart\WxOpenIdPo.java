package com.ruoyi.common.utils.wechart;

import lombok.ToString;

/**
 * 请求微信openid接口返回的json实体类
 * 
 * <AUTHOR>
 *
 */
@ToString
public class WxOpenIdPo {
	private String openid;// 用户唯一标识
	private String session_key;// 会话密钥
	private String unionid;// 用户在开放平台的唯一标识符，在满足 UnionID 下发条件的情况下会返回，详见 UnionID机制说明。
	private Integer errcode;// 错误码
	private String errmsg;// 错误信息
	private String expires_in;

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	public String getSession_key() {
		return session_key;
	}

	public void setSession_key(String session_key) {
		this.session_key = session_key;
	}

	public String getUnionid() {
		return unionid;
	}

	public void setUnionid(String unionid) {
		this.unionid = unionid;
	}

	public Integer getErrcode() {
		return errcode;
	}

	public void setErrcode(Integer errcode) {
		this.errcode = errcode;
	}

	public String getErrmsg() {
		return errmsg;
	}

	public void setErrmsg(String errmsg) {
		this.errmsg = errmsg;
	}

	public String getExpires_in() {
		return expires_in;
	}

	public void setExpires_in(String expires_in) {
		this.expires_in = expires_in;
	}

}

<!--pages/mainpage/main/transportcom/transport.wxml-->
<view class="supply-com" hover-class="none" hover-stop-propagation="false">
  <view class="search-warp" hover-class="none" hover-stop-propagation="false">
    <view class="search-area" hover-class="none" hover-stop-propagation="false">
      <view class="search-ipt-wrap" hover-class="none" hover-stop-propagation="false">
        <input type="text" class="search-ipt" placeholder="驳船名称/船舶识别号" value="{{form.searchValue}}" bindinput="searchValueInput"/>
        <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-icon" wx:if="{{form.searchValue}}" bindtap="deleteSearchValue"></mp-icon>
      </view>
      <view class="weight-wrap" hover-class="none" hover-stop-propagation="false">
        <view class="search-ipt-wrap1" hover-class="none" hover-stop-propagation="false">
          <input type="digit" class="search-zaizhong" placeholder="最小载重量" value="{{form.minWeight}}" bindinput="minWeightInput"/>
          <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-icon" wx:if="{{form.minWeight}}" bindtap="deleteMinWeight"></mp-icon>
        </view>
        <text class="padding-text">-</text>
        <view class="search-ipt-wrap1" hover-class="none" hover-stop-propagation="false">
          <input type="digit" class="search-zaizhong" placeholder="最大载重量" value="{{form.maxWeight}}" bindinput="maxWeightInput"/>
          <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-icon" wx:if="{{form.maxWeight}}" bindtap="deleteMaxWeight"></mp-icon>
        </view>
      </view>
    </view>
    <view class="search-area" hover-class="none" hover-stop-propagation="false">
      <picker class="search-data-box" mode="date" value="{{form.beginTime}}" bindchange="handleBeginDateChange">
        <view class="{{form.beginTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{form.beginTime ? form.beginTime :'开始时间'}}</view>
        <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-iconp" wx:if="{{form.beginTime}}" catchtap="deleteBeginTime"></mp-icon>
      </picker>
      <text class="padding-text">-</text>
      <picker class="search-data-box" mode="date" value="{{form.endTime}}" bindchange="handleEndDateChange">
        <view class="{{form.endTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{form.endTime ? form.endTime :'截止时间'}}</view>
        <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-iconp" wx:if="{{form.endTime}}" catchtap="deleteEndTime"></mp-icon>
      </picker>
      <button class="publish-btn search-btn" bindtap="handleSearch">搜索</button>
      <button class="publish-btn" bindtap="handleTransportPublish" wx:if="{{userInfos.userType == 14}}">运力发布</button>
    </view>
  </view>
  <view class="data-list" hover-class="none" hover-stop-propagation="false">
    <view class="data-item" wx:key="index" wx:for="{{listData}}" hover-class="none" hover-stop-propagation="false">
      <view class="data-item-title" hover-class="none" hover-stop-propagation="false" data-id="{{item.id}}" data-item="{{item}}" bindtap="handleTransportDetail">
        <view class="data-item-text" hover-class="none" hover-stop-propagation="false">{{item.bargeName}}</view>
        <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{20}}"></mp-icon>
      </view>
      <view class="data-item-message" hover-class="none" hover-stop-propagation="false">
        <view class="message-wrap" hover-class="none" hover-stop-propagation="false">
          <view class="message-wrap-title" hover-class="none" hover-stop-propagation="false">承载吨数：</view>
          <view class="message-wrap-value" hover-class="none" hover-stop-propagation="false">{{item.capacity}}吨</view>
        </view>
        <view class="message-wrap" hover-class="none" hover-stop-propagation="false">
          <view class="message-wrap-title" hover-class="none" hover-stop-propagation="false">联系方式：</view>
          <view class="message-wrap-value" hover-class="none" hover-stop-propagation="false">{{item.phone}}</view>
        </view>
        <view class="message-wrap" hover-class="none" hover-stop-propagation="false">
          <view class="message-wrap-title" hover-class="none" hover-stop-propagation="false">开始时间：</view>
          <view class="message-wrap-value" hover-class="none" hover-stop-propagation="false">{{item.startTime}}</view>
        </view>
        <view class="message-wrap" hover-class="none" hover-stop-propagation="false">
          <view class="message-wrap-title" hover-class="none" hover-stop-propagation="false">结束时间：</view>
          <view class="message-wrap-value" hover-class="none" hover-stop-propagation="false">{{item.endTime}}</view>
        </view>
        <view class="message-operate" hover-class="none" hover-stop-propagation="false">
          <button class='operate-btn' bindtap="modifyHandle" data-item="{{item}}" wx:if="{{userInfos.userType == 14}}">修改</button>
          <button class='operate-btn' bindtap="deleteHandle" data-item="{{item}}" data-index="{{index}}" wx:if="{{userInfos.userType == 14}}">删除</button>
        </view>
      </view>
    </view>
  </view>
  <!-- 删除确认弹窗 -->
  <mp-dialog title="" show="{{deleteDialog}}" bindbuttontap="tapDeleteButton" buttons="{{deleteButtons}}">
    <view>是否确认删除该条数据</view>
  </mp-dialog>
</view>

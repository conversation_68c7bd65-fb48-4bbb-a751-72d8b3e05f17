package com.ruoyi.basic.controller;


import com.ruoyi.basic.domain.BasicGoodsName;
import com.ruoyi.basic.service.impl.BasicGoodsNameServiceImpl;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023/11/16 12:25
 */
@RequestMapping("/basic/goodsName")
@RestController
public class BasicGoodsNameController extends BaseController {

    @Autowired
    BasicGoodsNameServiceImpl basicGoodsNameService;

    /**
     * @param
     * @return
     * @description lsit
     * <AUTHOR>
     * @date 2023/11/16 14:43
     */
    @RequestMapping("/list")
    public TableDataInfo list(BasicGoodsName basicGoodsName){
        startPage();
        return getDataTable(basicGoodsNameService.selectBasicGoodsNameList(basicGoodsName));
    }

    /**
     * @param
     * @return
     * @description add
     * @date 2023/8/21 15:45
     */
    @PostMapping("add")
    public AjaxResult add(@RequestBody BasicGoodsName basicGoodsName) {
        return basicGoodsNameService.addBasicGoodsName(basicGoodsName);
    }

    /**
     * @param
     * @return
     * @description edit
     * @date 2023/8/21 15:45
     */
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody BasicGoodsName basicGoodsName) {
        return basicGoodsNameService.updateBasicGoodsName(basicGoodsName);
    }

    /**
     * @param
     * @return
     * @description delete
     * @date 2023/8/21 15:45
     */
    @DeleteMapping("delete/{goodsNameId}")
    public AjaxResult delete(@PathVariable Long goodsNameId){
        return AjaxResult.success(basicGoodsNameService.removeById(goodsNameId));
    }

    /**
     * @param
     * @return
     * @description get
     * <AUTHOR>
     * @date 2023/11/16 16:31
     */
    @GetMapping("get/{goodsNameId}")
    public AjaxResult get(@PathVariable Long goodsNameId){
        return AjaxResult.success(basicGoodsNameService.getById(goodsNameId));
    }


}

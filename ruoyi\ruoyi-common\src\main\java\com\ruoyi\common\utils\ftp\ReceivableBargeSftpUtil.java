package com.ruoyi.common.utils.ftp;

import com.jcraft.jsch.*;
import com.ruoyi.common.exception.CustomException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.SocketException;
import java.util.*;

/**
 * FTP工具类
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/28 21:35
 */
@Component
public class ReceivableBargeSftpUtil {

    @Value("${receivable.ftp.host}")
    private String host;

    @Value("${receivable.ftp.port}")
    private int port;

    @Value("${receivable.ftp.username}")
    private String username;

    @Value("${receivable.ftp.password}")
    private String password;


    private ChannelSftp channel=null;

    private Session session = null;

    /**
     * sftp服务器路径
     */
    @Value("${receivable.ftp.path}")
    private volatile String ftpBasePath;

    /**
     * 字符集
     */
    private static final String DEFAULT_CHARSET = "UTF-8";

    /**
     * 超时时间
     */
    private static final int DEFAULT_TIMEOUT = 60 * 1000;

    /**
     * 连接sftp服务器
     *
     * @throws SocketException SocketException
     * @throws IOException     IOException
     * @throws JSchException   JSchException
     */
    private void connectServer() throws JSchException {
        JSch jsch = new JSch();
        // 根据用户名，主机ip，端口获取一个Session对象
        session = jsch.getSession(username, host, port);
        // 设置密码
        session.setPassword(password);
        // 为Session对象设置properties
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);
        // 通过Session建立链接
        session.connect();
        // 打开SFTP通道
        channel = (ChannelSftp) session.openChannel("sftp");
        // 建立SFTP通道的连接
        channel.connect();

    }
    public ChannelSftp getConnect() throws JSchException {
        JSch jsch = new JSch();
        // 根据用户名，主机ip，端口获取一个Session对象
        session = jsch.getSession(username, host, port);
        // 设置密码
        session.setPassword(password);
        // 为Session对象设置properties
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);
        // 通过Session建立链接
        session.connect();
        // 打开SFTP通道
        channel = (ChannelSftp) session.openChannel("sftp");
        // 建立SFTP通道的连接
        channel.connect();
        return channel;

    }
    /**
     * 自动关闭资源
     */
    private void close() {
        if (channel != null) {
            channel.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
    }

    public void channelClose(){
        if (channel != null) {
            channel.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
    }
    public List<ChannelSftp.LsEntry> getDirList(String path) throws SftpException {
        List<ChannelSftp.LsEntry> list = new ArrayList<>();
        if (channel != null) {
            Vector vv = channel.ls(path);
            if (vv == null && vv.size() == 0) {
                return list;
            } else {
                Object[] aa = vv.toArray();
                for (int i = 0; i < aa.length; i++) {
                    ChannelSftp.LsEntry temp = (ChannelSftp.LsEntry) aa[i];
                    list.add(temp);

                }
            }
        }
        return list;
    }

    /**
     * 下载文件
     *
     * @throws SftpException SftpException
     * @throws IOException   IOException
     */
    public void downloadFile(String path, OutputStream os) throws Exception {
        connectServer();
        if (channel == null) {
            throw new CustomException("sftp客户端未登录", 500);
        }
        channel.get(path, os);
        close();
    }

    /**
     * 下载文件
     *
     * @throws SftpException SftpException
     * @throws IOException   IOException
     */
    public synchronized String downloadBase64(String path){
        String re=null;
        try {
            connectServer();
            if (channel == null) {
                throw new CustomException("sftp客户端未登录", 500);
            }
            InputStream inputStream = channel.get(path);
            if (inputStream != null) {
                byte[] data=null;
                ByteArrayOutputStream outStream =new ByteArrayOutputStream();
                data=new byte[2048];
                int len=0;
                while((len=inputStream.read(data))!=-1){
                    outStream.write(data,0,len);
                }
                data=outStream.toByteArray();
                Base64.Encoder encoder= Base64.getEncoder();
                re=encoder.encodeToString(data);
            }
        } catch (IOException | JSchException e) {
            e.printStackTrace();
            throw new CustomException("加载超时");
        } catch (SftpException e){
            e.printStackTrace();
            throw new CustomException("服务器上没有找到相关驳船备案资料!");
        } finally {
            close();
        }
        return re;
    }


    /**
     * 上传文件
     *
     * @throws SftpException
     * @throws IOException
     */
    public String uploadFile(MultipartFile file, Long id) throws SftpException, IOException, JSchException {
        connectServer();
        if (channel == null) {
            throw new CustomException("sftp客户端未登录", 500);
        }

        String newPath = ftpBasePath + id;

        try {
            channel.cd(newPath);
        } catch (SftpException e) {
            channel.mkdir(newPath);
            channel.cd(newPath);
        }
        try (InputStream in = file.getInputStream()) {
            channel.put(in, file.getOriginalFilename());
        }
        close();
        return newPath + "/" + file.getOriginalFilename();
    }

    /**
     * 上传文件
     *
     * @throws SftpException
     * @throws IOException
     */
    public String uploadFile(InputStream in,String filename) throws SftpException, IOException, JSchException {
        connectServer();
        if (channel == null) {
            throw new CustomException("sftp客户端未登录", 500);
        }

        String newPath = ftpBasePath + "/" + filename;

        try {
            channel.cd(newPath);
        } catch (SftpException e) {
            channel.mkdir(newPath);
            channel.cd(newPath);
        }

        channel.put(in, filename);
        close();
        return newPath + "/" + filename;
    }

    public String uploadFiles(MultipartFile file,String userId, ChannelSftp channelSftp) throws JSchException, SftpException {

        if (channelSftp == null) {
            throw new CustomException("sftp客户端未登录", 500);
        }
        String newPath = ftpBasePath + userId;
        try {
            channelSftp.cd(newPath);
        } catch (SftpException e) {
            channelSftp.mkdir(newPath);
            channelSftp.cd(newPath);
        }
        try (InputStream in = file.getInputStream()) {
            channelSftp.put(in, file.getOriginalFilename());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return newPath;
    }

    /** * 删除文件 *
     * @param filePath FTP服务器保存目录
     * @param filename 要删除的文件名称
     * @return 是否成功删除
     **/
    public boolean deleteFile(String filePath, String filename) throws JSchException {
        connectServer();
        boolean flag = false;
        if (channel == null) {
            throw new CustomException("sftp客户端未登录", 500);
        }
        try {
            channel.cd(filePath);
            channel.rm(filename);
            flag = true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close();
        }
        return flag;
    }
}

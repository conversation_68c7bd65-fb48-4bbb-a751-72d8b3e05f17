package com.ruoyi.app.controller.support.seal;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.druid.sql.visitor.functions.If;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fdd.api.client.release.base.ClientFactory;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.app.controller.support.fdd.FddCompanyService;
import com.ruoyi.app.controller.support.fdd.FddShipOwnerService;
import com.ruoyi.app.controller.support.fdd.Sha256Util;
import com.ruoyi.app.controller.support.ftp.FtpTemplate;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.enums.PayWayEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.service.FtpService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.databarge.domain.Pb6Bargeinfo;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.service.Pb6BargeCompanyService;
import com.ruoyi.databarge.service.Pb6BargeinfoAuditService;
import com.ruoyi.databarge.service.Pb6BargeinfoService;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import com.ruoyi.web.utils.BargeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/seal")
@AllArgsConstructor
@Slf4j
public class SealController {

    private ClientFactory clientFactory;
    private ShipFddUserRelService shipFddUserRelService;
    private Pb6BargeinfoAuditService pb6BargeinfoAuditService;
    private Pb6BargeinfoService pb6BargeinfoService;
    private FtpTemplate ftpTemplate;
    private FddCommonService fddCommonService;
    private Pb6BargeCompanyService pb6BargeCompanyService;
    @Autowired
    private FddShipOwnerService fddShipOwnerService;
    @Autowired
    private FddCompanyService fddCompanyService;
    @Autowired
    private CargocmentdetailService cargocmentdetailService;
    @Autowired
    private  SysUserBargeMapper sysUserBargeMapper;
    private BargeUtils bargeUtils;
    @Autowired
    private  BargeCenterService bargeCenterService;
    @Autowired
    private  FtpService ftpService;
    @PostMapping("/search/byShipFddUserRel")
    public AjaxResult searchByShipFddUserRel(@RequestBody ShipFddUserRel shipFddUserRel){
        if(shipFddUserRel.getId()!=null){// 如果传入id，则直接通过id获取
            shipFddUserRel=shipFddUserRelService.getById(shipFddUserRel.getId());
            try {
                ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
                ftpTemplate.download(shipFddUserRel.getSealUrl(),byteArrayOutputStream);
                String fileBase64=Base64.encode(byteArrayOutputStream.toByteArray());
                shipFddUserRel.setFileBase64(fileBase64);
                return AjaxResult.success(shipFddUserRel);
            }catch (IOException e){
                return AjaxResult.error(e.getMessage());
            }
        }else{// 未传入id，则根据shipUserId和shipId去获取
            List<ShipFddUserRel> shipFddUserRelList=shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
//                    .eq(ShipFddUserRel::getShipUserId,shipFddUserRel.getShipUserId())
                    .eq(ShipFddUserRel::getType,"1")
                    .eq(ShipFddUserRel::getReviewStatus,"1")
                    .eq(shipFddUserRel.getShipId()!=null,ShipFddUserRel::getShipId,shipFddUserRel.getShipId()));// 因为船公司没有shipId，所以需要判断一下
            if(shipFddUserRelList.size()==0){
                //物流公司修改印章
                //查询该驳船是否有物流公司上传过印章
                List<ShipFddUserRel> shipFddUserRelList1=shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                        .eq(ShipFddUserRel::getType,"4")
                        .eq(ShipFddUserRel::getReviewStatus,"1")
                        .eq(ShipFddUserRel::getShipId,shipFddUserRel.getShipId()));

                if (shipFddUserRelList1.size()==0){
                    ShipFddUserRel newShipFddUserRel=new ShipFddUserRel();
                    long ShipUserId = 0;//物流公司插入数据规定ShipUserId为0
                    newShipFddUserRel.setType("4");
                    newShipFddUserRel.setShipUserId(ShipUserId);
                    newShipFddUserRel.setReviewStatus("1");
                    newShipFddUserRel.setShipId(shipFddUserRel.getShipId());
                    shipFddUserRelService.save(newShipFddUserRel);
                    shipFddUserRel=newShipFddUserRel;
                    return AjaxResult.success(shipFddUserRel);
                }else {
                    if (shipFddUserRelList1.get(0).getSealUrl()==null){
                        ShipFddUserRel shipFddUserRelWX=shipFddUserRelList1.get(0);
                        shipFddUserRelService.removeById(shipFddUserRelWX.getId());
                        ShipFddUserRel newShipFddUserRel=new ShipFddUserRel();
                        long ShipUserId = 0;//物流公司插入数据规定ShipUserId为0
                        newShipFddUserRel.setType("4");
                        newShipFddUserRel.setShipUserId(ShipUserId);
                        newShipFddUserRel.setReviewStatus("1");
                        newShipFddUserRel.setShipId(shipFddUserRel.getShipId());
                        shipFddUserRelService.save(newShipFddUserRel);
                        shipFddUserRel=newShipFddUserRel;
                        return AjaxResult.success(shipFddUserRel);
                    }else{
                        shipFddUserRel=shipFddUserRelList1.get(0);
                        try {
                            ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
                            ftpTemplate.download(shipFddUserRel.getSealUrl(),byteArrayOutputStream);
                            String fileBase64=Base64.encode(byteArrayOutputStream.toByteArray());
                            shipFddUserRel.setFileBase64(fileBase64);
                            return AjaxResult.success(shipFddUserRel);
                        }catch (IOException e){
                            return AjaxResult.error(e.getMessage());
                        }
                    }

                }
            }else if(shipFddUserRelList.size()==1){
                shipFddUserRel=shipFddUserRelList.get(0);
                try {
                    ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
                    ftpTemplate.download(shipFddUserRel.getSealUrl(),byteArrayOutputStream);
                    String fileBase64=Base64.encode(byteArrayOutputStream.toByteArray());
                    shipFddUserRel.setFileBase64(fileBase64);
                    return AjaxResult.success(shipFddUserRel);
                }catch (IOException e){
                    return AjaxResult.error(e.getMessage());
                }
            }else if(shipFddUserRelList.size()==2){// shipFddUserRelList有2条记录(修改时)，一条是有fdd_account_id，一条没有
                ShipFddUserRel shipFddUserRelWithFddAcountId=null;// 有fdd_account_id
                ShipFddUserRel shipFddUserRelWithOutFddAcountId=null;// 没有fdd_account_id
                for (ShipFddUserRel temp:shipFddUserRelList){
                    if (StringUtils.isNotEmpty(temp.getFddAccountId())){
                        shipFddUserRelWithFddAcountId=temp;
                    }else{
                        shipFddUserRelWithOutFddAcountId=temp;
                    }
                }
                if(shipFddUserRelWithFddAcountId==null||shipFddUserRelWithOutFddAcountId==null){
                    throw new CustomException("数据错误,驳船信息有2条,必须1条有fdd_account,1条没有");
                }
                shipFddUserRel=shipFddUserRelWithFddAcountId;
                try {
                    ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
                    ftpTemplate.download(shipFddUserRel.getSealUrl(),byteArrayOutputStream);
                    String fileBase64=Base64.encode(byteArrayOutputStream.toByteArray());
                    shipFddUserRel.setFileBase64(fileBase64);
                    return AjaxResult.success(shipFddUserRel);
                }catch (IOException e){
                    return AjaxResult.error(e.getMessage());
                }
            }else{
                throw new CustomException("数据错误,用户和驳船对应记录大于2条");
            }
        }
    }

    @PutMapping("/update/seal")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateSeal(@RequestBody ShipFddUserRel shipFddUserRel){
        switch (shipFddUserRel.getType()){
            case "1":// 1驳船主
                try {
                    File file = File.createTempFile("ship-", ".png");
                    file.deleteOnExit();
                    FileOutputStream fileOutputStream=new FileOutputStream(file);
                    InputStream inputStream=new ByteArrayInputStream(Base64.decode(shipFddUserRel.getFileBase64()));
                    Thumbnails
                            .of(inputStream)
                            .size(175,175)
                            .outputQuality(1f)
                            .outputFormat("png")
                            .toOutputStream(fileOutputStream);
                    // 修改fdd
                    fddCommonService.uploadAndUpdateSeal(shipFddUserRel.getFddAccountId(),shipFddUserRel.getShipName(),file);
                    inputStream.close();
                    fileOutputStream.close();

                    // 修改本地
                    inputStream=new FileInputStream(file);
                    ftpTemplate.upload(shipFddUserRel.getSealUrl(),inputStream);
                    ftpTemplate.download(shipFddUserRel.getSealUrl(),new FileOutputStream(file));
                    inputStream.close();
                    shipFddUserRelService.updateById(shipFddUserRel);
                    Pb6Bargeinfo pb6Bargeinfo=pb6BargeinfoService.getById(shipFddUserRel.getShipId());

                    if (StringUtils.isNotEmpty(shipFddUserRel.getShipCompanyName())){
                        pb6Bargeinfo.setAuditstate("Y");
                        pb6BargeinfoService.updateById(pb6Bargeinfo);
                    }else{
                        pb6Bargeinfo.setAuditstate("");
                        pb6BargeinfoService.updateById(pb6Bargeinfo);
                    }
                    return AjaxResult.success("修改印章成功!");
                }catch (IOException e){
                    throw new CustomException("服务器写文件失败!",e);
                }
            case "2":// 2船公司
                throw new CustomException("船公司无法修改印章!");
            case "3":// 3船公司帮驳船主备案
                // 船公司帮驳船主备案时没有shipId和fdd账户，只需修改印章图片即可
                try {
                    File file = File.createTempFile("ship-", ".png");
                    file.deleteOnExit();
                    FileOutputStream fileOutputStream=new FileOutputStream(file);
                    InputStream inputStream=new ByteArrayInputStream(Base64.decode(shipFddUserRel.getFileBase64()));
                    Thumbnails
                            .of(inputStream)
                            .size(175,175)
                            .outputQuality(1f)
                            .outputFormat("png")
                            .toOutputStream(fileOutputStream);
                    //fddCommonService.uploadAndUpdateSeal(shipFddUserRel.getFddAccountId(),shipFddUserRel.getShipName(),file);
                    inputStream.close();
                    fileOutputStream.close();

                    // 修改本地
                    inputStream=new FileInputStream(file);
                    ftpTemplate.upload(shipFddUserRel.getSealUrl(),inputStream);
                    inputStream.close();
                    shipFddUserRelService.updateById(shipFddUserRel);
                    return AjaxResult.success("修改印章成功!");
                }catch (IOException e){
                    throw new CustomException("服务器写文件失败!",e);
                }
            case "4"://物流公司
                if (StringUtils.isNotBlank(shipFddUserRel.getFddAccountId())){
                    Pb6Bargeinfo pb6Bargeinfo=pb6BargeinfoService.getById(shipFddUserRel.getShipId());
                    String sealName=pb6Bargeinfo.getBargename();// 公章名称直接为船名
                    try {
                        File file = File.createTempFile("ship-", ".png");
                        file.deleteOnExit();
                        FileOutputStream fileOutputStream=new FileOutputStream(file);
                        InputStream inputStream=new ByteArrayInputStream(Base64.decode(shipFddUserRel.getFileBase64()));
                        Thumbnails
                                .of(inputStream)
                                .size(175,175)
                                .outputQuality(1f)
                                .outputFormat("png")
                                .toOutputStream(fileOutputStream);
                        // 修改fdd
                        fddCommonService.uploadAndUpdateSeal(shipFddUserRel.getFddAccountId(),sealName,file);
                        inputStream.close();
                        fileOutputStream.close();

                        // 修改本地
                        inputStream=new FileInputStream(file);
                        ftpTemplate.upload(shipFddUserRel.getSealUrl(),inputStream);
                        inputStream.close();

                        shipFddUserRelService.updateById(shipFddUserRel);

                        return AjaxResult.success("修改印章成功!");
                    }catch (IOException e){
                        throw new CustomException("服务器写文件失败!",e);
                    }

                }else {
                    try {
                        File file = File.createTempFile("ship-", ".png");
                        file.deleteOnExit();
                        FileOutputStream fileOutputStream=new FileOutputStream(file);
                        InputStream inputStream=new ByteArrayInputStream(Base64.decode(shipFddUserRel.getFileBase64()));
                        Thumbnails
                                .of(inputStream)
                                .size(175,175)
                                .outputQuality(1f)
                                .outputFormat("png")
                                .toOutputStream(fileOutputStream);


                        Pb6Bargeinfo pb6Bargeinfo=pb6BargeinfoService.getById(shipFddUserRel.getShipId());
                        String sealName=pb6Bargeinfo.getBargename();// 公章名称直接为船名
                        String transactionNo= IdUtils.fastSimpleUUID();

                        String accountNo=fddCompanyService.companyRegister(IdUtils.fastSimpleUUID(),"驳船主");
                        String evidenceNo=fddShipOwnerService.shipOwnerSaveHashEvidence(
                                accountNo,
                                shipFddUserRel.getShipId()+"的存证",
                                shipFddUserRel.getShipId()+"的存证描述",
                                file.getName(),
                                ""+file.lastModified()/1000,
                                ""+file.length(),
                                Sha256Util.getSha256(file.getName()),
                                transactionNo);
                        String evidenceNumberCert=fddShipOwnerService.applyEvidenceNumberCert(accountNo,evidenceNo);

                        // 添加印章
                        fddCommonService.uploadAndSaveSeal(accountNo,sealName,file);

                        // 修改本地
                        inputStream=new FileInputStream(file);
                        String path = "/upload/seal/" + IdUtil.simpleUUID() + ".png";
                        shipFddUserRel.setSealUrl(path);
                        ftpTemplate.upload(shipFddUserRel.getSealUrl(),inputStream);
                        inputStream.close();
                        fileOutputStream.close();
                        shipFddUserRel.setFddAccountId(accountNo);
                        shipFddUserRelService.updateById(shipFddUserRel);
                        return AjaxResult.success("添加印章成功!");
                    }catch (IOException e) {
                        throw new CustomException("服务器写文件失败!", e);
                    }
                }

            default:
                throw new CustomException("未知用户类型!");
        }

    }

    @GetMapping("/search/ShipCompanyName")
    public AjaxResult searchShipCompanyName( @RequestParam(value="bargeId",required=false) String bargeId){
        ShipFddUserRel shipFddUserRel1=shipFddUserRelService.getOne(Wrappers.<ShipFddUserRel>lambdaQuery().
                eq(ShipFddUserRel::getShipId,bargeId).eq(ShipFddUserRel::getType,1).eq(ShipFddUserRel::getReviewStatus,1));
        if (shipFddUserRel1!=null){
            return AjaxResult.success(shipFddUserRel1);
        }else{
            ShipFddUserRel shipFddUserRel=shipFddUserRelService.getOne(Wrappers.<ShipFddUserRel>lambdaQuery().
                    eq(ShipFddUserRel::getShipId,bargeId).eq(ShipFddUserRel::getType,4).eq(ShipFddUserRel::getReviewStatus,1));
            return AjaxResult.success(shipFddUserRel);
        }
    }

    @PostMapping("/search/byShipId")
    public AjaxResult searchByShipId(@RequestBody ShipFddUserRel shipFddUserRel){
        //首先查询该驳船是否有驳船主上传印章
            List<ShipFddUserRel> shipFddUserRelList=shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                    .eq(ShipFddUserRel::getType,"1")
                    .eq(ShipFddUserRel::getReviewStatus,"1")
                    .eq(ShipFddUserRel::getShipId,shipFddUserRel.getShipId()));

            if(shipFddUserRelList.size()==0){
                //若驳船主没有印章，查询物流公司是否上传
                List<ShipFddUserRel> shipFddUserRelList1=shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                        .eq(ShipFddUserRel::getType,"4")
                        .eq(ShipFddUserRel::getReviewStatus,"1")
                        .eq(ShipFddUserRel::getShipId,shipFddUserRel.getShipId()));
                if (shipFddUserRelList1.size()==0){
                    ShipFddUserRel newShipFddUserRel=new ShipFddUserRel();
                    return AjaxResult.success(newShipFddUserRel);
                }else {
                    if (shipFddUserRelList1.get(0).getSealUrl()==null){
                        ShipFddUserRel newShipFddUserRel=new ShipFddUserRel();
                        return AjaxResult.success(newShipFddUserRel);
                    }else{
                        shipFddUserRel=shipFddUserRelList1.get(0);
                        try {
                            ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
                            ftpTemplate.download(shipFddUserRel.getSealUrl(),byteArrayOutputStream);
                            String fileBase64=Base64.encode(byteArrayOutputStream.toByteArray());
                            shipFddUserRel.setFileBase64(fileBase64);
                            return AjaxResult.success(shipFddUserRel);
                        }catch (IOException e){
                            return AjaxResult.error(e.getMessage());
                        }
                    }

                }
            }else if(shipFddUserRelList.size()==1){
                shipFddUserRel=shipFddUserRelList.get(0);
                try {
                    ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
                    ftpTemplate.download(shipFddUserRel.getSealUrl(),byteArrayOutputStream);
                    String fileBase64=Base64.encode(byteArrayOutputStream.toByteArray());
                    shipFddUserRel.setFileBase64(fileBase64);
                    return AjaxResult.success(shipFddUserRel);
                }catch (IOException e){
                    return AjaxResult.error(e.getMessage());
                }
            }else if(shipFddUserRelList.size()==2){// shipFddUserRelList有2条记录(修改时)，一条是有fdd_account_id，一条没有
                ShipFddUserRel shipFddUserRelWithFddAcountId=null;// 有fdd_account_id
                ShipFddUserRel shipFddUserRelWithOutFddAcountId=null;// 没有fdd_account_id
                for (ShipFddUserRel temp:shipFddUserRelList){
                    if (StringUtils.isNotEmpty(temp.getFddAccountId())){
                        shipFddUserRelWithFddAcountId=temp;
                    }else{
                        shipFddUserRelWithOutFddAcountId=temp;
                    }
                }
                if(shipFddUserRelWithFddAcountId==null||shipFddUserRelWithOutFddAcountId==null){
                    throw new CustomException("数据错误,驳船信息有2条,必须1条有fdd_account,1条没有");
                }
                shipFddUserRel=shipFddUserRelWithFddAcountId;
                try {
                    ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
                    ftpTemplate.download(shipFddUserRel.getSealUrl(),byteArrayOutputStream);
                    String fileBase64=Base64.encode(byteArrayOutputStream.toByteArray());
                    shipFddUserRel.setFileBase64(fileBase64);
                    return AjaxResult.success(shipFddUserRel);
                }catch (IOException e){
                    return AjaxResult.error(e.getMessage());
                }
            }else{
                throw new CustomException("数据错误,用户和驳船对应记录大于2条");
            }
        }

}

package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.databarge.domain.Pb30OlEpayorder;
import com.ruoyi.databarge.domain.dto.Pb30OlEpayorderDetailSearchDTO;
import com.ruoyi.databarge.domain.vo.Pb30OlEpayorderDetailVO;
import com.ruoyi.databarge.domain.vo.WaterCargoTagClickVO;
import com.ruoyi.databarge.pay.PayDetailsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface Pb30OlEpayorderMapper extends BaseMapper<Pb30OlEpayorder> {
    IPage<Pb30OlEpayorderDetailVO> searchPageDetailVO(Page<Pb30OlEpayorderDetailVO> page, @Param("pb30OlEpayorderDetailSearchDTO") Pb30OlEpayorderDetailSearchDTO pb30OlEpayorderDetailSearchDTO);

    List<Pb30OlEpayorderDetailVO> searchPageDetailVO(@Param("pb30OlEpayorderDetailSearchDTO") Pb30OlEpayorderDetailSearchDTO pb30OlEpayorderDetailSearchDTO);

    Pb30OlEpayorder searchOriginOrder(@Param("waterwaycargono") String waterwaycargono);

    List<PayDetailsVO> searchPayDetails(@Param("waterwaycargono") String waterwaycargono);

    //查询现结的水路运单的支付状态及已付金额
    WaterCargoTagClickVO searchWaterCargoCashTagClick(@Param("waterwaycargono") String waterwaycargono);

    //查询月结的水路运单状态、月结公司及月结码
    WaterCargoTagClickVO searchWaterCargoMonthlyTagClick(@Param("waterwaycargono") String waterwaycargono);
}

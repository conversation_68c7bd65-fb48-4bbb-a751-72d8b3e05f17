package com.ruoyi.common.enums;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/12 3:41
 */
public enum ConfirmEnum {
    /**
     * 1-提交审核，2-确认无误
     */
    PUT_CHECK(1, "提交审核"),
    CONFIRM(2, "确认无误");

    private final Integer code;

    private final String codeName;

    ConfirmEnum(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }

}

package com.ruoyi.common.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.bo.UserMessageBO;

/**
 * <AUTHOR> 订阅
 * @email <EMAIL>
 * @date 2020/8/5 9:49
 */
public interface AppNoticeService {

    /**
     * 订阅
     * @param userMessageBO 运单id,明细id
     * @return
     */
    AjaxResult subscription(UserMessageBO userMessageBO);

    /**
     * 取消订阅
     * @param userMessageBO 运单id,明细id
     * @return
     */
    AjaxResult cancelSubscription(UserMessageBO userMessageBO);

    /**
     * 发送消息
     * @param userMessageBO
     * @return
     */
    AjaxResult sendMessage(UserMessageBO userMessageBO);

    /**
     * 获取消息通知列表
     * @param userMessageBO
     * @return
     */
    AjaxResult getNoticeList(UserMessageBO userMessageBO);

    /**
     * 更新消息读取状态
     * @param userMessageBO
     * @return
     */
    AjaxResult updateIsRead(UserMessageBO userMessageBO);
}

// pages/minepage/messagedetail/messagedetail.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 获取路由对象
    getRouteData:{},
    // 按钮文字
    buttonText: "确定",
    // 水路运单表主键id
    waterwayCargoId: "",

    //jinn 2021-10-09 驳卡详表
    mainItem: null,
    listData: [],
    waterwayCargoNo: "",

    pdfInfo: 1,
    total: {
      weight: '',
      num: ''
    },

    //发送邮箱弹框
    sendEmailDialog: false,
    //用户邮箱 
    userEmail:"",
    //批量下载按钮
    batchButtons:[{text: '取消'}, {text: '确定'}],

    // 水路运单ID
    waterCargoId:""
  },

  /**
   * 
   * 方法
   */

  //获取水路运单
  getWaterwayId(id) {
    if(!id){
      // app.$message("id参数为空")
      return
    }
    app.$post(app.$url.mine.getLoading,{waterwayCargoId: id}).then(res=>{
      if(res.code == 200){
        // confirmLoadingOver 确认是否实装： Y 确认实装（显示确定按钮）， N或为空是没有确认实装（显示确认实装数按钮）
        if(res.data) {

          app.$post(app.$url.bargeDetail.list, {noticeNo: res.data.waterwayCargoId}).then(resCar =>{
            console.log(resCar)
            if(resCar.data.length != 0){
              let mainItem = resCar.data[0];
              mainItem.waterwayCargoId = res.data.waterwayCargoId;

              let weight = 0;
              for(let i in resCar.data){
                weight = weight + Number(resCar.data[i].netWeight)
              }
              this.setData({
                mainItem: mainItem,
                listData: resCar.data,
                'total.weight': weight.toFixed(4),
                'total.num': resCar.data.length,
                waterwayCargoNo: res.data.waterwayCargoId,
              })
            }
          })
          let buttonText = this.data.buttonText
          if(res.data.confirmloadingover == "N" || !res.data.confirmloadingover) {
            // buttonText = "确认实装数"
            // 不在这里确认实装数
            buttonText = "确定"
          }else{
            buttonText = "已确定"
          }
          this.setData({
            buttonText,
            waterwayCargoId: res.data.waterwayCargoId
          })
        }
      }else{
        app.$message(res.msg)
      }
    })
  },

  //订单完成类型-出现确认实装数按钮-点击按钮需要调确认实装数接口；非订单完成类型-出现确定按钮-点击按钮返回上一页
  submitEvent(){
    if(this.data.buttonText == "确认实装数") {
      let waterCargoId = this.data.waterCargoId 
      if(!waterCargoId){
        // app.$message("id参数为空")
        return
      }
      app.$post(app.$url.mine.confirmLoadingOverForApp,{id: waterCargoId,confirmloadingover:"Y"}).then(res=>{
        if(res.code == 200){
          wx.navigateBack({
            delta: 1
          });
        }else{
          app.$message(res.msg)
        }
      })
    }else{
      wx.navigateBack({
        delta: 1
      });
    }
   
  },

  //修改该消息状态成已读、修改tabbar的badge数量
  modifyReadStatus(id){
    if(!id){return}
    app.$post(app.$url.mine.updateIsRead,{userMessageId:id}).then(res=>{
      if(res.code == 200){

      }else{
        // app.$message(res.msg)
      }
    })
  },



  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    //获取数据
    if(JSON.stringify(options.currentObj) !=""){
      let currentObj = JSON.parse(options.currentObj);
      if(currentObj.content) {
        var reg = new RegExp('\\\\n', 'g')
        currentObj.content =  currentObj.content.replace(reg,'\n')
        /**
         * 
         * （1）先判断接口/notice/getNoticeList返回的code
        Xmeb_wuN3yJli8xvbKRe1nKIMXP4tPhvzCvw29Mvqfk 的就是订单完成通知；
        （2）订单完成通知类型的需要调/barge/center/confirmLoadingOver 确认实装数接口，返回的confirmLoadingOver为空或者N 显示 确认实装数按钮 
         */
        // code是yVwAtns-gVVFFXYvCmlRmYGcddTtq-8ocU1bhWrgQQ0 的就是订单完成通知
        if(currentObj.code === "yVwAtns-gVVFFXYvCmlRmYGcddTtq-8ocU1bhWrgQQ0") {
          // 截取content订单编号
          let sliceStart = currentObj.content.indexOf("订单号：")
          let sliceEnd = currentObj.content.indexOf("商品名称：")
          let sliceText = currentObj.content.substring(sliceStart,sliceEnd)
          let sliceTextArr = sliceText.split("：")
          if(sliceTextArr && sliceTextArr.length > 0) {
            this.getWaterwayId(sliceTextArr[1].trim())
          }

          this.setData({
            // buttonText:"确认实装数"
            buttonText:"确定"
          })
        }else {
          this.setData({
            buttonText:"确定"
          })
        }
      }
      console.log(currentObj)
      this.setData({
        getRouteData: currentObj,
        waterCargoId:currentObj.waterCargoId
      })
      this.modifyReadStatus(currentObj.id)
    }
  },

  //查看pdf
  getPdf(){
    if(this.data.waterwayCargoNo != null && this.data.waterwayCargoNo != ''){
      app.$post(app.$url.bargeDetail.generatePdf, {noticeNo:this.data.waterwayCargoNo}).then(res=>{
        if(res.code == 200){
          console.log(res)
          app.$downLoad(app.$url.bargeDetail.download, {fileName:res.msg}).then(res1=>{
            if(res.byteLength <= 100) {
              let uint8_msg = new Uint8Array(res1);
              let decodedString = JSON.parse(String.fromCharCode.apply(null, uint8_msg));
              app.$message(`预览文件失败，原因${decodedString.msg}`);
              return
            }
            const fs = wx.getFileSystemManager(); // 获取全局唯一的文件管理器
            // 写文件
            fs.writeFile({
              filePath: wx.env.USER_DATA_PATH + `/${res.msg}`, //写入的文件路径 (本地路径)
              data: res1, // 写入的文本或二进制数据
              encoding: "binary", // 文件的字符编码
              success(res2) {
                wx.openDocument({
                  filePath: wx.env.USER_DATA_PATH + `/${res.msg}`,  //拿上面存入的文件路径
                  success: function (res3) {
                    console.log(res3);
                  }
                })
              },
            })
          })
        }else{
          // app.$message(res.msg)
        }
      })
    }
  },

  //发送邮箱
  openSendEmail(){
    this.setData({
      sendEmailDialog:true
    })
  },
  //发送
  tapSendEmailDialogButton(e){
    // 点击取消按钮
    if(e.detail.index == 0) {
  
    }else {
      // 点击确定按钮-发送邮件
      //邮箱校验格式
      let emailReg = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
      let email = this.data.userEmail //邮箱
      if(!email){
        app.$message("请输入邮箱")
        return
      }
      if(email && !emailReg.test(email)) {
        app.$message("你输入的邮箱格式不正确!")
        return;
      }
      if(this.data.waterwayCargoNo != null && this.data.waterwayCargoNo != ''){
        app.$post(app.$url.bargeDetail.generatePdf, {noticeNo:this.data.waterwayCargoNo}).then(res=>{
          if(res.code == 200){
            let param = {
              sendTo: this.data.userEmail,
              fileName: res.msg,
              subject: "驳卡详表" + this.data.waterwayCargoNo
            }
            app.$post(app.$url.bargeDetail.sendEmail,param).then(res=>{
              if(res.code == 200){
                app.$message(res.msg)
              }else {
                app.$message(res.msg)
              }
            })
          }else{
            // app.$message(res.msg)
          }
        })
      }
    }
    this.setData({
      sendEmailDialog:false
    })
  },

  //邮箱input
  emailInput(e){
    this.setData({
      userEmail:e.detail.value
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):"";//获取用户信息
    this.setData({
      userEmail:userInfos.email,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
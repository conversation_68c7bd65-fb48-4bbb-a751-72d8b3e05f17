/* pages/minepage/mine/mine.wxss */
.mine-page{
  padding-top: 400rpx;
}
.navigation-warp{
  position: fixed;
  top: 0rpx;
  left: 0rpx;
  right: 0rpx;
  height: 400rpx;
  background: linear-gradient(270deg, rgba(96, 147, 168, 1) 0%, rgba(0, 65, 106, 1) 100%);
}
.navigation-title{
  position: absolute;
  left: 0rpx;
  right: 0rpx;
  font-size: 28rpx;
  text-align: center;
  color: #ffffff;
}
.user-image{
  position: absolute;
  left: 0rpx;
  right: 0rpx;
  height: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.user-head-image{
  height: 150rpx;
  width: 150rpx;
  border-radius: 75rpx;
}
.user-nickname{
  color: #ffffff;
  font-size: 28rpx;
  height: 50rpx;
  line-height: 50rpx;
}
.mine-operate{
  margin-top: 20rpx;
  padding: 0 20rpx;
}
.mine-operate-item{
  height: 90rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px solid #ddd;
  justify-content: space-between;
}
.operate-left{
  position: relative;
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.mine-icon{
  font-size: 40rpx;
  color: #00426B;
  padding-right: 20rpx;
}
.operate-item-text{
  font-size: 30rpx;
  color: #00426B;
}
.detail-icon{
  flex-shrink: 0;
  padding: 0 5rpx;
}
.user-btn{
  position: fixed;
  bottom: 200rpx;
  left:0;
  right:0;
}
.record-status{
  flex:1;
  font-size: 30rpx;
  color: #333;
  padding:30rpx;
  text-align: right;
}
.read-statu{
  position: absolute;
  left: 187rpx;
  top: 5rpx;
  color: #fff;
  font-size: 20rpx;
  width:auto;
  height: auto;
  background: #ff0000;
  border-radius: 50%;
  padding: 0 10rpx;
}
package com.ruoyi.basic.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.basic.domain.BasicRate;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【BASIC_RATE】的数据库操作Mapper
* @createDate 2023-11-20 09:31:59
* @Entity com.ruoyi.project.basic.domain.BasicRate
*/
public interface BasicRateMapper extends BaseMapper<BasicRate> {

    // 费率List
    List<BasicRate> selectBasicRateList(BasicRate basicRate);

    // 根据费率对象查询费率信息
    BasicRate selectBasicRateByRate(BasicRate basicRate);

}





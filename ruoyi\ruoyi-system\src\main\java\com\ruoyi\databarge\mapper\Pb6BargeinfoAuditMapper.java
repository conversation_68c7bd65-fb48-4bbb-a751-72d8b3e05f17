package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.databarge.domain.Pb6BargeinfoAudit;
import com.ruoyi.databarge.domain.dto.Pb6BargeinfoAuditSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeCompanyResultVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/8/27.
 * @Date: 2020/8/27 11:17
 */
public interface Pb6BargeinfoAuditMapper extends BaseMapper<Pb6BargeinfoAudit> {

    BargeCompanyResultVO searchPb6BargeinfoAuditById(@Param("pb6BargeInfoAuditId") Long pb6BargeInfoAuditId);

    BargeCompanyResultVO searchPb6BargeinfoAuditByBakId(@Param("pb6BargeInfoAuditId") Long pb6BargeInfoAuditId);

    List<Pb6BargeinfoAudit> selectPb6BargeinfoAuditListByBargeName(@Param("bargename") String bargename);
}

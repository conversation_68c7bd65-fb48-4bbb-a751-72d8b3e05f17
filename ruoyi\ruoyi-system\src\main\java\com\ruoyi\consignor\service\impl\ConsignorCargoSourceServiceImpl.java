package com.ruoyi.consignor.service.impl;

import com.ruoyi.barge.domain.bo.BargeCapacityPublishBO;
import com.ruoyi.barge.domain.vo.BargeCapacityPublishVO;
import com.ruoyi.barge.service.BargeCapacityService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.consignor.domain.CargoSourceInfo;
import com.ruoyi.consignor.domain.bo.CargoSourceBO;
import com.ruoyi.consignor.domain.vo.CargoSourceVO;
import com.ruoyi.consignor.mapper.ConsignorCargoSourceMapper;
import com.ruoyi.consignor.service.ConsignorCargoSourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;

import java.util.Date;
import java.util.List;

/**
 * @Description 货主的货源业务层实现类
 * <AUTHOR>
 * @Date 2020/7/24 15:35
 */
@Slf4j
@Service
public class ConsignorCargoSourceServiceImpl implements ConsignorCargoSourceService {

    @Autowired
    private ConsignorCargoSourceMapper consignorCargoSourceMapper;

    @Autowired
    private BargeCapacityService bargeCapacityService;

    /**
     *  保存货源信息
     * @param cargoSourceBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void saveCargoSource(CargoSourceBO cargoSourceBO, BindingResult result) {

        if (StringUtils.isNull(cargoSourceBO)) {
            throw new CustomException("货源数据不能为空");
        }
        if (result.hasErrors()) {
            throw new CustomException("保存货源信息验证信息有误！");
        }

        SysUser user = SecurityUtils.getLoginUser().getUser();

        CargoSourceInfo cargoSourceInfo = new CargoSourceInfo();
        try {
            BeanUtils.copyBeanProp(cargoSourceInfo, cargoSourceBO);
            if (StringUtils.isNotEmpty(cargoSourceBO.getStartTime())) {
                cargoSourceInfo.setStartTime(DateUtils.parseDate(cargoSourceBO.getStartTime()));
            }
            if (StringUtils.isNotEmpty(cargoSourceBO.getEndTime())) {
                cargoSourceInfo.setEndTime(DateUtils.parseDate(cargoSourceBO.getEndTime()));
            }
            cargoSourceInfo.setUserId(user.getUserId());
            cargoSourceInfo.setCreateTime(new Date());
            cargoSourceInfo.setCreateUser(user.getUserId().toString());
        } catch (Exception e) {
            throw new CustomException("保存货源信息数据转移出错！");
        }
        int argoSourceResult = consignorCargoSourceMapper.insert(cargoSourceInfo);

        if (argoSourceResult <= 0) {
            log.error("新增货源操作失败");
            throw new CustomException("新增货源操作失败");
        }
        log.info("保存货源成功！");

        // TODO 除了保存在本系统的表内, 还需要发送给码头的业务生产系统?

    }


    /**
     * 修改货源信息
     * @param cargoSourceBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void updateCargoSource(CargoSourceBO cargoSourceBO, BindingResult result) {

        if (StringUtils.isNull(cargoSourceBO)) {
            throw new CustomException("货源数据不能为空");
        }
        if (result.hasErrors()) {
            throw new CustomException("修改货源信息验证信息有误！");
        }

        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (!Long.valueOf(cargoSourceBO.getCreateUser()).equals(user.getUserId())) {
            log.error("需要货源创建人才能修改！");
            throw new CustomException("需要货源创建人才能修改");
       }

        CargoSourceInfo cargoSourceInfo = new CargoSourceInfo();
        try {
            BeanUtils.copyBeanProp(cargoSourceInfo, cargoSourceBO);
            cargoSourceInfo.setUpdateUser(user.getUserId().toString());
           if (StringUtils.isNotEmpty(cargoSourceBO.getStartTime())) {
                cargoSourceInfo.setStartTime(DateUtils.parseDate(cargoSourceBO.getStartTime()));
            }
            if (StringUtils.isNotEmpty(cargoSourceBO.getEndTime())) {
                cargoSourceInfo.setEndTime(DateUtils.parseDate(cargoSourceBO.getEndTime()));
            }
            cargoSourceInfo.setUpdateTime(new Date());
        } catch (Exception e) {
            throw new CustomException("修改货源信息数据转移出错！");
        }
        int cargoSourceResult = consignorCargoSourceMapper.updateById(cargoSourceInfo);

        if (cargoSourceResult <= 0) {
            log.error("修改货源操作失败！");
            throw new CustomException("修改货源操作失败");
        }
        log.info("修改货源成功！");
    }

    /**
     *  查询货源信息
     * @param cargoSourceBO
     * @return
     */
    @Override
    public List<CargoSourceVO> selectCargoSource(CargoSourceBO cargoSourceBO) {

        return consignorCargoSourceMapper.selectCargoSource(cargoSourceBO);
    }

    /**
     * 根据id删除货源
     * @param id
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void deleteCargoSourceById(Long id) {

        if (id == null) {
            throw new CustomException("货源id不能为空");
        }

        int result = consignorCargoSourceMapper.deleteById(id);

        if (result <= 0) {
            log.error("删除货源操作失败");
            throw new CustomException("删除操作失败");
        }
        log.info("删除货源成功");
    }


    /**
     *  动力查询
     * @param bargeCapacityPublishBO
     * @return
     */
    @Override
    public List<BargeCapacityPublishVO> selectImpetusData(BargeCapacityPublishBO bargeCapacityPublishBO) {

        List<BargeCapacityPublishVO> capacityList = bargeCapacityService.getCapacityList(bargeCapacityPublishBO);

        return capacityList;

    }

}

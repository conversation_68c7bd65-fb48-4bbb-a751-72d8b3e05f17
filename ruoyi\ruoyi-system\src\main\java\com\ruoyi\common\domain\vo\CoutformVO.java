package com.ruoyi.common.domain.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/11 16:05
 */
@Getter
@Setter
@ToString
public class CoutformVO {

    private Long id;
    /**
     * 出库单号
     */
    private String coutformId;
    /**
     * 出库单收货人id
     */
    private Long consigneeId;
    /**
     * 出库单收货人
     */
    private String consignee;

    /**
     * 提单号
     */
    private String unblId;

    /**
     * 可配置量
     */
    private String weightValue;

    /**
     * 可配载件数
     */
    private String pieceValue;

    /**
     * 货物名称
     */
    private String cargoName;

    /**
     * 包装方式
     */
    private String packageType;


    /**
     *  地磅单号
     */
    private String formloadometerId;

    /**
     *  地磅单号集合
     */
    private List<String> formloadometerIdList;

    /**
     *  公司代理主键
     */
    private Integer corporationId;


    public CoutformVO() {
    }

    public CoutformVO(Long id, String coutformId, Long consigneeId, String consignee, String unblId, String weightValue, String cargoName, String packageType, String formloadometerId, Integer corporationId) {
        this.id = id;
        this.coutformId = coutformId;
        this.consigneeId = consigneeId;
        this.consignee = consignee;
        this.unblId = unblId;
        this.weightValue = weightValue;
        this.cargoName = cargoName;
        this.packageType = packageType;
        this.formloadometerId = formloadometerId;
        this.corporationId = corporationId;
    }

    public CoutformVO(String coutformId, Long consigneeId, String consignee, String unblId, String weightValue, String cargoName, String packageType, String formloadometerId, Integer corporationId) {
        this.coutformId = coutformId;
        this.consigneeId = consigneeId;
        this.consignee = consignee;
        this.unblId = unblId;
        this.weightValue = weightValue;
        this.cargoName = cargoName;
        this.packageType = packageType;
        this.formloadometerId = formloadometerId;
        this.corporationId = corporationId;
    }
}

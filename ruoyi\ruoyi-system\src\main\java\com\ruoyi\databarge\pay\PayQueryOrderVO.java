package com.ruoyi.databarge.pay;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Description: 订单查询返回结果
 *
 * @Author: ChenJin on 2021/1/28.
 * @Date: 2021/1/28 11:24
 */
@Getter
@Setter
@ToString
public class PayQueryOrderVO {

    private Resmsg resmsg;

    @Getter
    @Setter
    public static class Resmsg{
        private Reshead reshead;
        private String orderid;
        private String sts;
        private String date;
        private String amt;
        private String cdtrid;
        private String cdtrnm;
        private String payorgnm;
        private String billid;
        private String billdesc;
        private String payid;
        private String paytime;
        private String remark;
        private String busprocd;
        private String busprodesc;
        private String resulturl;
        private String payway;
        private String signature;
    }
}

package com.ruoyi.databarge.pay;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * Description: 订单查询请求DTO
 *
 * @Author: ChenJin on 2021/1/28.
 * @Date: 2021/1/28 11:22
 */
@Getter
@Setter
@Builder
public class PayQueryOrderDTO {

    private Reqmsg reqmsg;

    @Getter
    @Setter
    @Builder
    public static class Reqmsg{
        private Reqhead reqhead;
        private String orgorderid;
        private String orgchanflow;
        private String signature;
    }
}

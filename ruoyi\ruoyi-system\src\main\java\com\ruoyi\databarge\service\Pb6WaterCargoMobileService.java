package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.Pb6WaterCargoMobile;
import com.ruoyi.databarge.domain.dto.Pb6WaterCargoForMobileSearchDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2021/6/11.
 * @Date: 2021/6/11 15:54
 */
public interface Pb6WaterCargoMobileService extends IService<Pb6WaterCargoMobile> {

    void updatePb6WaterCargoMobile();

    List<Pb6WaterCargoMobile> searchPb6WaterCargoForMobile(Pb6WaterCargoForMobileSearchDTO pb6WaterCargoForMobileSearchDTO);
}

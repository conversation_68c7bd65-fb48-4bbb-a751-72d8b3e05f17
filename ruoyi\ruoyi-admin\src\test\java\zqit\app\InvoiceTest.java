package zqit.app;

import com.ruoyi.RuoYiApplication;
import com.ruoyi.app.controller.support.invoice.DESDZFP;
import com.ruoyi.databarge.domain.ShipInvoiceHistory;
import com.ruoyi.databarge.service.Pb6BargeinfoService;
import com.ruoyi.databarge.service.Pb6WaterwaycargoService;
import com.ruoyi.databarge.service.ShipInvoiceHistoryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = RuoYiApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class InvoiceTest {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;
    @Autowired
    private Pb6BargeinfoService pb6BargeinfoService;
    @Autowired
    private ShipInvoiceHistoryService shipInvoiceHistoryService;

    private static final String identity="2329CC5F90EDAA8208F1F3C72A0CE72A713A9D425CD50CDE";
    private static final String saletaxnum="***************";

    @Test
    public void testShipInvoiceHistory(){
        /*ShipInvoiceHistory shipInvoiceHistory=new ShipInvoiceHistory();
        //shipInvoiceHistory.setId(1L);
        shipInvoiceHistory.setType("1");
        shipInvoiceHistory.setShipUserId(1L);
        shipInvoiceHistory.setShipUserName("1");
        shipInvoiceHistory.setBuyername("1");
        shipInvoiceHistory.setTaxnum("1");
        shipInvoiceHistory.setPhone("1");
        shipInvoiceHistory.setAddress("1");
        shipInvoiceHistory.setAccount("1");
        shipInvoiceHistory.setTelephone("1");
        shipInvoiceHistory.setOrderno("1");
        shipInvoiceHistory.setInvoicedate("2020-01-01 00:00:00");
        shipInvoiceHistory.setClerk("1");
        shipInvoiceHistory.setSaleaccount("1");
        shipInvoiceHistory.setSalephone("1");
        shipInvoiceHistory.setSaleaddres("1");
        shipInvoiceHistory.setSaletaxnum("1");
        shipInvoiceHistory.setKptype("1");
        shipInvoiceHistory.setMessage("1");
        shipInvoiceHistory.setPayee("1");
        shipInvoiceHistory.setChecker("1");
        shipInvoiceHistory.setFpdm("1");
        shipInvoiceHistory.setFphm("1");
        shipInvoiceHistory.setTsfs("1");
        shipInvoiceHistory.setEmail("1");
        shipInvoiceHistory.setQdbz("1");
        shipInvoiceHistory.setQdxmmc("1");
        shipInvoiceHistory.setDkbz("1");
        shipInvoiceHistory.setDeptid("1");
        shipInvoiceHistory.setClerkid("1");
        shipInvoiceHistory.setInvoiceline("1");
        shipInvoiceHistory.setCpybz("1");
        shipInvoiceHistory.setBillinfono("1");

        ShipInvoiceHistory.Detail detail1=new ShipInvoiceHistory.Detail();
        detail1.setGoodsname("1");
        detail1.setNum("1");
        detail1.setPrice("1");
        detail1.setHsbz("1");
        detail1.setTaxrate("1");
        detail1.setSpec("1");
        detail1.setUnit("1");
        detail1.setSpbm("1");
        detail1.setZsbm("1");
        detail1.setFphxz("1");
        detail1.setYhzcbs("1");
        detail1.setZzstsgl("1");
        detail1.setLslbs("1");
        detail1.setKce("1");
        detail1.setTaxfreeamt("1");
        detail1.setTax("1");
        detail1.setTaxamt("1");

        shipInvoiceHistory.setDetail(Arrays.asList(detail1));
        shipInvoiceHistory.setPb6WaterwaycargoId(1L);
        shipInvoiceHistory.setFpqqlsh("1");

        shipInvoiceHistoryService.save(shipInvoiceHistory);*/

        /*ShipInvoiceHistory shipInvoiceHistory=shipInvoiceHistoryService.getById(225L);
        System.out.println(shipInvoiceHistory.getType());

        shipInvoiceHistory.setType("2");
        shipInvoiceHistoryService.updateById(shipInvoiceHistory);

        shipInvoiceHistory=shipInvoiceHistoryService.getById(225L);
        System.out.println(shipInvoiceHistory.getType());*/

        /*shipInvoiceHistoryService.removeById(225L);*/
    }

    @Test
    public void kp(){
        String url="http://nnfpbox.nuonuocs.cn/shop/buyer/allow/cxfKp/cxfServerKpOrderSync.action?order=%s";
        String order=
                "{" +
                        "\"identity\":\""+identity+"\"," +
                        "\"order\":{" +
                        "\"buyername\":\"浙江爱信诺\"," +
                        "\"taxnum\":\"124511234993295177\"," +
                        "\"phone\":\"***********\"," +
                        "\"address\":\"\"," +
                        "\"account\":\"\"," +
                        "\"telephone\":\"\"," +
                        "\"orderno\":\"nuonuo01234567\"," +
                        "\"invoicedate\":\"2018-10-31 19:16:51\"," +
                        "\"clerk\":\"黄芝\"," +
                        "\"saleaccount\":\"\"," +
                        "\"salephone\":\"\"," +
                        "\"saleaddress\":\"\"," +
                        "\"saletaxnum\":\""+saletaxnum+"\"," +
                        "\"kptype\":\"1\"," +
                        "\"message\":\"\"," +
                        "\"payee\":\"\"," +
                        "\"checker\":\"\"," +
                        "\"tsfs\":\"2\"," +
                        "\"email\":\"<EMAIL>\"," +
                        "\"qdbz\":\"\"," +
                        "\"qdxmmc\":\"\"," +
                        "\"dkbz\":\"\"," +
                        "\"deptid\":\"\"," +
                        "\"clerkid\":\"\"," +
                        "\"invoiceLine\":\"\"," +
                        "\"cpybz\":\"\"," +
                        "\"detail\":[" +
                        "{" +
                        "\"goodsname\":\"苹果\"," +
                        "\"num\":\"1\"," +
                        "\"price\":\"1\"," +
                        "\"hsbz\":\"1\"," +
                        "\"taxrate\":\"0.13\"," +
                        "\"spec\":\"\"," +
                        "\"unit\":\"吨\"," +
                        "\"spbm\":\"10101150101\"," +
                        "\"zsbm\":\"\"," +
                        "\"fphxz\":\"0\"," +
                        "\"yhzcbs\":\"0\"," +
                        "\"zzstsgl\":\"\"," +
                        "\"lslbs\":\"\"," +
                        "\"kce\":\"\"" +
                        "}" +
                        "]" +
                        "}" +
                        "}";

        order= DESDZFP.encrypt(order);

        /**
         * {
         * status = 0000,
         * message = 同步成功,
         * fpqqlsh = 20101014594801296268
         * }
         */
        Object o=restTemplate.postForObject(String.format(url,order),null,Object.class);
        System.out.println(o);
    }

    @Test
    public void kpcxLsh(){
        String url="http://nnfpbox.nuonuocs.cn/shop/buyer/allow/ecOd/queryElectricKp.action?order=%s";

        String order=
                "{\n" +
                        "\"identity\":\""+identity+"\",\n" +
                        "\"fpqqlsh\":\"20101011520101292573\",\n" +
                        "\"isOfferInvoiceDetail\":true\n" +
                        "}";

        order=DESDZFP.encrypt(order);

        /**
         * {
         * 	result = success,
         * 	list = [
         * 	    {
         * 	        terminalNumber = null,
         * 			c_hjje = 1.0,
         * 			c_address = ,
         * 			c_telephone = ,
         * 			c_mail = ,
         * 			c_yfpdm = ,
         * 			c_buyername = 浙江爱信诺,
         * 			c_payee = payee,
         * 			c_taxnum = 124511234993295177,
         * 			c_phone = ,
         * 			c_yfphm = ,
         * 			c_salerTel = 0571 - 87022169,
         * 			c_remark = 交易时间: 2018 - 10 - 31 19: 16: 51,
         * 			cipherText = * 4 / & gt;
         * 			/&gt;+2&lt;+*-38&gt;6-+*+235*424/ & lt;5 & gt;2536 * 72 - /1/
         * 			861 - 61968 + 9 + -61279 & gt;09 + 617 + & lt; & gt;78669 & lt;83 - 178436 * 17 & gt; & lt;17 & gt; & lt;7 & lt; & gt; & gt;4 & gt;82 /*0867,
         * 			qrCode=01,10,************,********,0.88,********,47826967712675110927,ECE3,,
         * 			c_invoice_line=增值税电子普通发票,
         * 			c_resultmsg=,
         * 			c_ofd_url=,
         * 			c_qdbz=0,
         * 			c_salerAccount=saleAccount,
         * 			c_status=2,
         * 			c_kprq=*************,
         * 			c_checker=checker,
         * 			c_orderno=nuonuo0123456,
         * 			c_saleName=杭州爱信诺航天信息有限公司(新模式开票),
         * 			c_salerTaxNum=***************,
         * 			c_imgUrls=https://invtest.nntest.cn/fp/z2u2sGXrs_hDmv_kf5u0PlKlTC0D1xS_qzIe3sp1UemmhAtY_JQu9MYO2pP1bJM1m26oBDO4Y_8oCSv0W38qVA.jpg,
         * 			machineCode=************,
         * 			c_jpg_url=nnfpbox.nuonuocs.cn/vd7HoyE3-eeOwzj,
         * 			c_invoiceid=20101011520101292573,
         * 			c_hjse=0.12,
         * 			c_clerkId=,
         * 			c_qdxmmc=,
         * 			c_salerAddress=saleAddress,
         * 			checkCode=47826967712675110927,
         * 			c_deptId=,
         * 			c_fphm=********,
         * 			c_clerk=黄芝,
         * 			c_url=https://invtest.nntest.cn/fp/z2u2sGXrs_hDmv_kf5u0PlfN8_SCXnsjgLojID4r44kr17HQ0NqO5q2X751TtQAoisP21bFfS87VExEWsBNEzg.pdf,
         * 			invoiceItems=[
         * 		   	    {
         * 		   	        itemSpec=,
         * 		   	        invoiceLineProperty=0,
         * 		   	        favouredPolicyName=,
         * 		   	        itemUnit=吨,
         * 		   	        itemCode=1010115010100000000,
         * 		   	        favouredPolicyFlag=0,
         * 		   	        itemSumAmount=1.00,
         * 		   	        itemNum=1.000000000000000000,
         * 		   	        itemName=苹果,
         * 		   	        isIncludeTax=true,
         * 		   	        deduction=0.00,
         * 		   	        itemSelfCode=,
         * 		   	        zeroRateFlag=,
         * 		   	        itemPrice=1.000000000000000000,
         * 		   	        itemTaxAmount=0.12,
         * 		   	        itemTaxRate=0.13
         * 		   	    }
         * 		   	],
         * 		   	c_fpqqlsh=20101011520101292573,
         * 		   	c_fpdm=************,
         * 		   	productOilFlag=0,
         * 		   	extensionNumber=null,
         * 		   	c_bhsje=0.88,
         * 		   	c_msg=开票完成（最终状态）,
         * 		   	c_bankAccount=,
         * 		   	c_jym=47826967712675110927
         * 		}
         *    ]}
         */
        Object o=restTemplate.postForObject(String.format(url,order),null,Object.class);
        System.out.println(o);
    }

    @Test
    public void kpcxDdh(){
        String url="http://nnfpbox.nuonuocs.cn/shop/buyer/allow/ecOd/queryElectricKp.action?order=%s";

        String order=
                "{\n" +
                        "\"identity\":\""+identity+"\",\n" +
                        "\"orderno\":\"nuonuo0123456\",\n" +
                        "\"isOfferInvoiceDetail\":true\n" +
                        "}";

        order=DESDZFP.encrypt(order);

        /**
         * {
         * 	result = success,
         * 	list = [
         * 	    {
         * 	        terminalNumber = null,
         * 			c_hjje = 1.0,
         * 			c_address = ,
         * 			c_telephone = ,
         * 			c_mail = ,
         * 			c_yfpdm = ,
         * 			c_buyername = 浙江爱信诺,
         * 			c_payee = payee,
         * 			c_taxnum = 124511234993295177,
         * 			c_phone = ,
         * 			c_yfphm = ,
         * 			c_salerTel = 0571 - 87022169,
         * 			c_remark = 交易时间: 2018 - 10 - 31 19: 16: 51,
         * 			cipherText = * 4 / & gt;
         * 			/&gt;+2&lt;+*-38&gt;6-+*+235*424/ & lt;5 & gt;2536 * 72 - /1/
         * 			861 - 61968 + 9 + -61279 & gt;09 + 617 + & lt; & gt;78669 & lt;83 - 178436 * 17 & gt; & lt;17 & gt; & lt;7 & lt; & gt; & gt;4 & gt;82 /*0867,
         * 			qrCode=01,10,************,********,0.88,********,47826967712675110927,ECE3,,
         * 			c_invoice_line=增值税电子普通发票,
         * 			c_resultmsg=,
         * 			c_ofd_url=,
         * 			c_qdbz=0,
         * 			c_salerAccount=saleAccount,
         * 			c_status=2,
         * 			c_kprq=*************,
         * 			c_checker=checker,
         * 			c_orderno=nuonuo0123456,
         * 			c_saleName=杭州爱信诺航天信息有限公司(新模式开票),
         * 			c_salerTaxNum=***************,
         * 			c_imgUrls=https://invtest.nntest.cn/fp/z2u2sGXrs_hDmv_kf5u0PlKlTC0D1xS_qzIe3sp1UemmhAtY_JQu9MYO2pP1bJM1m26oBDO4Y_8oCSv0W38qVA.jpg,
         * 			machineCode=************,
         * 			c_jpg_url=nnfpbox.nuonuocs.cn/vd7HoyE3-eeOwzj,
         * 			c_invoiceid=20101011520101292573,
         * 			c_hjse=0.12,
         * 			c_clerkId=,
         * 			c_qdxmmc=,
         * 			c_salerAddress=saleAddress,
         * 			checkCode=47826967712675110927,
         * 			c_deptId=,
         * 			c_fphm=********,
         * 			c_clerk=黄芝,
         * 			c_url=https://invtest.nntest.cn/fp/z2u2sGXrs_hDmv_kf5u0PlfN8_SCXnsjgLojID4r44kr17HQ0NqO5q2X751TtQAoisP21bFfS87VExEWsBNEzg.pdf,
         * 			invoiceItems=[
         * 		   	    {
         * 		   	        itemSpec=,
         * 		   	        invoiceLineProperty=0,
         * 		   	        favouredPolicyName=,
         * 		   	        itemUnit=吨,
         * 		   	        itemCode=1010115010100000000,
         * 		   	        favouredPolicyFlag=0,
         * 		   	        itemSumAmount=1.00,
         * 		   	        itemNum=1.000000000000000000,
         * 		   	        itemName=苹果,
         * 		   	        isIncludeTax=true,
         * 		   	        deduction=0.00,
         * 		   	        itemSelfCode=,
         * 		   	        zeroRateFlag=,
         * 		   	        itemPrice=1.000000000000000000,
         * 		   	        itemTaxAmount=0.12,
         * 		   	        itemTaxRate=0.13
         * 		   	    }
         * 		   	],
         * 		   	c_fpqqlsh=20101011520101292573,
         * 		   	c_fpdm=************,
         * 		   	productOilFlag=0,
         * 		   	extensionNumber=null,
         * 		   	c_bhsje=0.88,
         * 		   	c_msg=开票完成（最终状态）,
         * 		   	c_bankAccount=,
         * 		   	c_jym=47826967712675110927
         * 		}
         *    ]}
         */
        Object o=restTemplate.postForObject(String.format(url,order),null,Object.class);
        System.out.println(o);
    }

}


package com.ruoyi.app.controller.databarge;


import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.Pb6Bargeinfo;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.databarge.domain.dto.BargeCheckInSearchDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentSearchDTO;
import com.ruoyi.databarge.domain.vo.Pb6CargoconsignmentVO;
import com.ruoyi.databarge.service.Pb6BargeinfoService;
import com.ruoyi.databarge.service.Pb6CargoconsignmentdetailBakService;
import com.ruoyi.databarge.service.Pb6CargoconsignmentdetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 驳船托运单明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@RestController
@RequestMapping("/barge/cargoconsignmentdetail-bak")
public class Pb6CargoconsignmentdetailBakController {

}

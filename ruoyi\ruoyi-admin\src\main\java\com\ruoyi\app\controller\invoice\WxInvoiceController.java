package com.ruoyi.app.controller.invoice;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.Invoice.service.WxInvoiceService;
import com.ruoyi.app.controller.support.invoice.InvoiceKpVO;
import com.ruoyi.app.controller.support.invoice.InvoiceUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.Invoice.domain.bo.InvoiceBO;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.domain.ShipInvoiceHistory;
import com.ruoyi.databarge.service.Pb6WaterwaycargoService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票相关接口
 * <AUTHOR> Chen
 * @email <EMAIL>
 * @date 2020/12/16 9:20
 */

@Api("发票相关接口")
@Slf4j
@RestController
@RequestMapping("/wxInvoice")
public class WxInvoiceController {

    @Autowired
    private WxInvoiceService wxInvoiceService;

    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;

    @Autowired
    private InvoiceUtil invoiceUtil;

    /**
     * 开票
     * @param invoiceBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @PostMapping("/kpInvoice")
    public AjaxResult kpInvoice(@RequestBody InvoiceBO invoiceBO) {
        if (StringUtils.isNull(invoiceBO.getWaterwayCargoId())) {
            log.info("水路运单编号不能为空");
            return AjaxResult.error("水路运单编号不能为空");
        }
        Pb6Waterwaycargo waterwayCargo = pb6WaterwaycargoService.getOne(new QueryWrapper<Pb6Waterwaycargo>().eq("WATERWAYCARGOID", invoiceBO.getWaterwayCargoId()));
        ShipInvoiceHistory shipInvoiceHistory = null;
        if (StringUtils.isNotNull(waterwayCargo)) {
            shipInvoiceHistory = wxInvoiceService.getOne(new QueryWrapper<ShipInvoiceHistory>().eq("PB6_WATERWAYCARGO_ID", waterwayCargo.getId()));
        }

        if (StringUtils.isNotNull(shipInvoiceHistory)) {
            InvoiceBO invoiceBO1 = new InvoiceBO();
            BeanUtils.copyBeanProp(invoiceBO1, shipInvoiceHistory);
            invoiceBO1.setKptype("1");
            AjaxResult result = invoiceUtil.invoicekp(invoiceBO1);
            Object obj = result.get("data");
            if (StringUtils.isNotNull(obj)) {
                InvoiceKpVO invoiceKpVO = (InvoiceKpVO) obj;
                if ("0000".equals(invoiceKpVO.getStatus())) {
                    // 删除发票信息
                    wxInvoiceService.removeById(invoiceBO1.getId());
                    return AjaxResult.success(invoiceKpVO.getMessage(), invoiceKpVO.getFpqqlsh());
                }
                return AjaxResult.error(invoiceKpVO.getStatus()+","+invoiceKpVO.getMessage());
            }
            return AjaxResult.error((String) result.get("msg"));
        }

        return AjaxResult.error("开票失败");
    }

    /**
     * 保存发票信息
     * @param invoiceBO
     * @return
     */
    @PostMapping("/saveInvoice")
    public AjaxResult saveInvoice(@RequestBody InvoiceBO invoiceBO) {
        String waterwayCargoId = invoiceBO.getWaterwayCargoId();
        if (StringUtils.isNull(invoiceBO.getWaterwayCargoId())) {
            log.info("水路运单编号不能为空");
            return AjaxResult.error("水路运单编号不能为空");
        }
        if (StringUtils.isBlank(invoiceBO.getInvoicetype())) {
            log.info("发票类型不能为空");
            return AjaxResult.error("发票类型不能为空");
        }
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        invoiceBO.setShipUserId(sysUser.getUserId());
        invoiceBO.setShipUserName(sysUser.getUserName());
        // 固定正票
        invoiceBO.setKptype("1");
        Pb6Waterwaycargo waterwayCargo = pb6WaterwaycargoService.getOne(new QueryWrapper<Pb6Waterwaycargo>().eq("WATERWAYCARGOID", invoiceBO.getWaterwayCargoId()));
        /*AjaxResult result = invoiceUtil.invoicekp(invoiceBO);
        Object obj = result.get("data");
        if (StringUtils.isNotNull(obj)) {
            InvoiceKpVO invoiceKpVO = (InvoiceKpVO) obj;
            if ("0000".equals(invoiceKpVO.getStatus())) {
                return AjaxResult.success(invoiceKpVO.getMessage(), invoiceKpVO.getFpqqlsh());
            }
            return AjaxResult.error(invoiceKpVO.getMessage());
        }
        return AjaxResult.error((String) result.get("msg"));*/
        ShipInvoiceHistory shipInvoiceHistory = null;
        if (StringUtils.isNotNull(waterwayCargo)) {
            shipInvoiceHistory = wxInvoiceService.getOne(new QueryWrapper<ShipInvoiceHistory>().eq("PB6_WATERWAYCARGO_ID", waterwayCargo.getId()));
        }
        if (StringUtils.isNull(shipInvoiceHistory)) {
            // 新增
            return wxInvoiceService.saveInvoice(invoiceBO);
        } else {
            // 修改
            invoiceBO.setId(shipInvoiceHistory.getId());
            return wxInvoiceService.updateInvoice(invoiceBO);
        }
    }

    /**
     * 获取发票信息
     * @param invoiceBO
     * @return
     */
    @PostMapping("/getInvoice")
    public AjaxResult getInvoice(@RequestBody InvoiceBO invoiceBO) {
        if (StringUtils.isNull(invoiceBO.getWaterwayCargoId())) {
            log.info("水路运单编号不能为空");
            return AjaxResult.error("水路运单编号不能为空");
        }
        return wxInvoiceService.getInvoice(invoiceBO);
    }

    /**
     * 检测开票条件
     * @param invoiceBO
     * @return
     */
    @PostMapping("/checkInvoice")
    public AjaxResult checkInvoice(@RequestBody InvoiceBO invoiceBO) {
        if (StringUtils.isNull(invoiceBO.getWaterwayCargoId())) {
            log.info("水路运单编号不能为空");
            return AjaxResult.error("水路运单编号不能为空");
        }
        if (StringUtils.isBlank(invoiceBO.getInvoicetype())) {
            log.info("发票类型不能为空");
            return AjaxResult.error("请选择普票或者专票");
        }
        return wxInvoiceService.checkInvoice(invoiceBO);
    }

    /**
     * 修改发票
     * @param invoiceBO
     * @return
     */
    @PostMapping("/updateInvoice")
    public AjaxResult updateInvoice(@RequestBody InvoiceBO invoiceBO) {
        if (StringUtils.isNull(invoiceBO.getId())) {
            log.info("id不能为空");
            return AjaxResult.error("id不能为空");
        }
        return wxInvoiceService.updateInvoice(invoiceBO);
    }

    /**
     * 获取发票附件
     * @param invoiceBO
     * @return
     */
    @PostMapping("/getInvoiceAnnex")
    public AjaxResult getInvoiceAnnex(@RequestBody InvoiceBO invoiceBO) {
        if (StringUtils.isNotBlank(invoiceBO.getFpqqlsh())) {
            return AjaxResult.success(invoiceUtil.kpcxByFpqqlsh(invoiceBO.getFpqqlsh()));
        } else if (StringUtils.isNotBlank(invoiceBO.getOrderno())) {
            return AjaxResult.success(invoiceUtil.kpcxByOrderno(invoiceBO.getOrderno()));
        } else {
            log.info("发票流水号或订单号不能为空");
            return AjaxResult.error("发票流水号或订单号不能为空");
        }
    }

    /**
     * 发送发票附件到邮箱
     * @param invoiceBO
     * @return
     */
    @PostMapping("/sendInvoiceAnnexToEmail")
    public AjaxResult sendInvoiceAnnexToEmail(@RequestBody InvoiceBO invoiceBO) {
        if (StringUtils.isBlank(invoiceBO.getToEmail())) {
            log.info("邮箱不能为空");
            return AjaxResult.error("邮箱不能为空");
        }
        if (StringUtils.isNull(invoiceBO.getWaterwayCargoId())) {
            log.info("水路运单编号不能为空");
            return AjaxResult.error("水路运单编号不能为空");
        }
        return wxInvoiceService.sendInvoiceAnnexToEmail(invoiceBO);
    }

    /**
     * 获取发票历史记录
     * @param invoiceBO
     * @return
     */
    @PostMapping("/getHistoryInvoice")
    public AjaxResult getHistoryInvoice(@RequestBody InvoiceBO invoiceBO) {
        return wxInvoiceService.getHistoryInvoice(invoiceBO);
    }
}

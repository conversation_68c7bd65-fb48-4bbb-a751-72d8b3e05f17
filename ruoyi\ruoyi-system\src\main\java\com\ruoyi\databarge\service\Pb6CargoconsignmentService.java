package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.databarge.domain.Pb6Cargoconsignment;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
/**
 * <p>
 * 托运单主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-23
 */
public interface Pb6CargoconsignmentService extends IService<Pb6Cargoconsignment> {

    /**
     * 手动新增托运单
     * @param pb6Cargoconsignment
     * @return
     */
    AjaxResult addConsign(Pb6Cargoconsignment pb6Cargoconsignment);

    /**
     * 手动修改托运单
     * @param pb6Cargoconsignment
     * @return
     */
    AjaxResult editConsign(Pb6Cargoconsignment pb6Cargoconsignment);

    // 删除托运单
    AjaxResult deleteCargoconsignment(List<Long> consignIds);

    List<Pb6Cargoconsignment> searchByConsignFlagForDealRepeat(String consignFlag);

    // 生成运单
    AjaxResult generateWaterWayCargo(List<Long> consignmentIds);

    // 新增驳船信息
    AjaxResult addCargoconsignmentDeatil(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail);

    // 更新驳船信息
    AjaxResult updateCargoconsignmentDeatil(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail);

    // 删除驳船信息
    AjaxResult deleteCargoconsignmentDeatil(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail);

    // 作废水路运单
    AjaxResult cancelCargoconsignment(List<Long> detailIds);

    // 修改水路运单吨数，导入excel
    AjaxResult importUpdateWeightExcel(MultipartFile file, Long consignId) throws IOException;
}

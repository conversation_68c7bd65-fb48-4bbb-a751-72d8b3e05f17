package com.ruoyi.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.api.controller.ReportApi;
import com.ruoyi.api.domain.BargeParam;
import com.ruoyi.databarge.domain.Pb6Cargoconsignment;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.ship.domain.ShipInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/6/13 17:24
 */

public interface ReportApiService extends IService<ReportApi> {

    // 统计大船量
    public List<ShipInfo> getShipCount(BargeParam bargeParam);

    // 统计驳船量
    public List<Pb6Cargoconsignmentdetail> getBargeCount(BargeParam bargeParam);

    // 根据大船id查询托运单信息
    public List<Pb6Cargoconsignment> selectByShipInfoId(Long shipInfoId);


}

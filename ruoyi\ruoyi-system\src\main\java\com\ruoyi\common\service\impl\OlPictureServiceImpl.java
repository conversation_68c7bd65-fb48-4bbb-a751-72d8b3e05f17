package com.ruoyi.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.domain.OLPicture;
import com.ruoyi.common.mapper.OlPictureMapper;
import com.ruoyi.common.service.OlPictureService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/22 17:17
 */
@Service
public class OlPictureServiceImpl extends ServiceImpl<OlPictureMapper, OLPicture> implements OlPictureService {
    @Override
    @Transactional
    public void savepictrues(List<OLPicture> olPictures,String userId) {


        for(OLPicture olPicture:olPictures){
            baseMapper.delete(new QueryWrapper<OLPicture>()
                    .eq("userid",userId)
                    .eq("image_info",olPicture.getImageInfo())
            );
        }
        this.saveBatch(olPictures);
    }
}

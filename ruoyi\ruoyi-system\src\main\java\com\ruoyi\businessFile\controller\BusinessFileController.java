package com.ruoyi.businessFile.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.businessFile.domain.BusinessFile;
import com.ruoyi.businessFile.domain.BusinessFileType;
import com.ruoyi.businessFile.domain.VFileSummary;
import com.ruoyi.businessFile.domain.vo.BusinessFileVo;
import com.ruoyi.businessFile.domain.vo.FileVo;
import com.ruoyi.businessFile.mapper.BusinessFileMapper;
import com.ruoyi.businessFile.service.BusinessFileService;
import com.ruoyi.businessFile.service.BusinessFileTypeService;
import com.ruoyi.businessFile.service.SysOssService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 15:10
 */
@Repository
@RestController
@RequestMapping("/businessFile")
public class BusinessFileController extends BaseController {


    @Autowired
    BusinessFileService businessFileService;

    @Autowired
    BusinessFileMapper businessFileMapper;

    @Autowired
    private SysOssService sysOssService;

    @Autowired
    BusinessFileTypeService businessFileTypeService;


    @GetMapping("/getFileOne")
    public AjaxResult getFileOneByfileBusinessID(FileVo fileVo){
        if(fileVo.getFileBusinessType()!=null&&fileVo.getFileBusinessID()!=null) {

            BusinessFileType businessFileType=businessFileTypeService.getOne(new QueryWrapper<BusinessFileType>().lambda()
                    .eq(BusinessFileType::getFileTypeId,fileVo.getFileTypeId()));
            BusinessFile businessFile =businessFileService.getOne(new QueryWrapper<BusinessFile>().lambda()
                    .eq(BusinessFile::getFileTypeName,businessFileType.getFileTypeName()).eq(BusinessFile::getFileBusinessType,fileVo.getFileBusinessType())
                    .eq(BusinessFile::getFileBusinessID,fileVo.getFileBusinessID()));
            return AjaxResult.success(businessFile);
        }else{
            return AjaxResult.error("缺失参数");
        }
    }

    @GetMapping("/list")
    public TableDataInfo getFileListByfileBusinessID(FileVo fileVo){
        if(fileVo.getFileBusinessType()!=null&&fileVo.getFileBusinessID()!=null) {

            BusinessFile businessFile = new BusinessFile();

            businessFile.setFileBusinessType(fileVo.getFileBusinessType());
            businessFile.setFileBusinessID(fileVo.getFileBusinessID());
            if(StringUtils.isNotEmpty(fileVo.getFileTypeName())){
                businessFile.setFileTypeName(fileVo.getFileTypeName());
            }
            List<FileVo> fileVos = businessFileMapper.listBusinessFile(businessFile);
            //排序
            fileVos.sort(Comparator.comparing(FileVo::getFileSort));
            return getDataTable(fileVos);
        }else{
            TableDataInfo rspData = new TableDataInfo();
            rspData.setMsg("缺失参数");
            return rspData;
        }
    }

    /**
     * 文件配置开启必须上传，有传FileTypeName，上传文件后，才会返回 true
     * @param fileVo
     * @return
     */
    @GetMapping("/getUploadState")
    public Boolean getUploadState(FileVo fileVo){
        if(fileVo.getFileBusinessType()!=null&&fileVo.getFileBusinessID()!=null) {
            if(StringUtils.isNotEmpty(fileVo.getFileTypeName())){
                BusinessFileType businessFileType= businessFileTypeService.getOne(new QueryWrapper<BusinessFileType>().lambda()
                        .eq(BusinessFileType::getFileBusinessType,fileVo.getFileBusinessType()).eq(BusinessFileType::getFileTypeName,fileVo.getFileTypeName()));
                if(businessFileType.getFileIsMust().toString().equals("1")){
                    BusinessFile businessFile = new BusinessFile();
                    businessFile.setFileBusinessType(fileVo.getFileBusinessType());
                    businessFile.setFileBusinessID(fileVo.getFileBusinessID());
                    businessFile.setFileTypeName(fileVo.getFileTypeName());
                    List<BusinessFile> businessFileList=businessFileService.list(new QueryWrapper<BusinessFile>().lambda()
                            .eq(BusinessFile::getFileBusinessID,fileVo.getFileBusinessID()).eq(BusinessFile::getFileTypeName,fileVo.getFileTypeName()));
                    if(businessFileList.size()<1){
                        return false;
                    }else{
                        return true;
                    }
                }

            }
            return false;
        }else{
            TableDataInfo rspData = new TableDataInfo();
            rspData.setMsg("缺失参数");
            return false;
        }
    }

    @GetMapping("/{fileId}")
    public AjaxResult getById(@PathVariable("fileId") String fileId){
        BusinessFileVo businessFile=businessFileService.selectBusinessFileByFileId(fileId);
        return AjaxResult.success(businessFile);
    }

    @DeleteMapping("delete/{fileId}")
    public AjaxResult delete(@PathVariable("fileId") String fileId){
        BusinessFile businessFile=businessFileService.getById(fileId);
        sysOssService.removeById(businessFile.getFileSysid());

        return businessFileService.removeById(fileId)?AjaxResult.success():AjaxResult.error();
    }

    @GetMapping("getFileSummary")
    public AjaxResult getFileSummary(FileVo fileVo){
        VFileSummary vFileSummary=new VFileSummary();
        if(fileVo.getFileBusinessID()!=null){
            vFileSummary= businessFileService.listVFileSummary(fileVo.getFileBusinessType(), fileVo.getFileBusinessID()+"");
        }
        return AjaxResult.success(vFileSummary);
    }

    /**
     * 上传文件
     */
    @PostMapping(value = "/uploadNew")
    @Transactional
    public AjaxResult uploadConFileNew(MultipartFile file, String fileBusinessID, String fileTypeId, String fileBusinessType) throws Exception {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择上传文件!");
        }
        BusinessFileType businessFileType = businessFileTypeService.getById(fileTypeId);
        String fileName = file.getOriginalFilename();
        String title = new String();
        List<BusinessFile> businessFiles = businessFileService.list(new QueryWrapper<BusinessFile>().lambda().eq(BusinessFile::getFileBusinessID, fileBusinessID).eq(BusinessFile::getFileTypeName, businessFileType.getFileTypeName()));
        if(businessFiles != null && businessFiles.size() > 0 &&businessFileType.getFileIsOnly().equals("1")){
            BusinessFile businessFile = businessFiles.get(0);
            sysOssService.removeById(businessFile.getFileSysid());
            AjaxResult result =  sysOssService.uploadFile(file);
            String aa[]=result.get("url").toString().split("/");
//            FileSortTime.uploadFile(file,aa[3]);
            if (result.isSuccess()) {
                businessFile.setFileBusinessType(fileBusinessType);
                businessFile.setFileBusinessID(fileBusinessID);
                businessFile.setFileTypeName(businessFileType.getFileTypeName());
                businessFile.setFileSort(businessFileType.getFileSort());
                businessFile.setFileName(fileName);
                businessFile.setFileSysid(Long.parseLong(result.get("id").toString()));
                businessFileService.updateById(businessFile);
            }
        } else {
            AjaxResult result =  sysOssService.uploadFile(file);
            String aa[]=result.get("url").toString().split("/");
//            FileSortTime.uploadFile(file,aa[3]);
            if (result.isSuccess()) {
                BusinessFile businessFileadd = new BusinessFile();
                businessFileadd.setFileBusinessType(fileBusinessType);
                businessFileadd.setFileBusinessID(fileBusinessID);
                businessFileadd.setFileTypeName(businessFileType.getFileTypeName());
                businessFileadd.setFileSort(businessFileType.getFileSort());
                businessFileadd.setFileName(fileName);
                businessFileadd.setFileSysid(Long.parseLong(result.get("id").toString()));
                businessFileService.save(businessFileadd);
            }
        }
        return AjaxResult.success();
    }

}

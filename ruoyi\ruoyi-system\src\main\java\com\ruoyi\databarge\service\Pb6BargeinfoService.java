package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.Pb6Bargeinfo;
import com.ruoyi.databarge.domain.dto.BargeInfoDTO;
import com.ruoyi.databarge.domain.dto.Pb6BargeinfoAuditSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeCompanyResultVO;

import java.awt.geom.Point2D;
import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/8/27.
 * @Date: 2020/8/27 16:20
 */
public interface Pb6BargeinfoService extends IService<Pb6Bargeinfo> {

    public String getBargeNameById(Long id);

    IPage<BargeCompanyResultVO> searchPagePb6Bargeinfo(Pb6BargeinfoAuditSearchDTO pb6BargeinfoAuditSearchDTO);

    BargeCompanyResultVO searchPb6BargeinfoById(Long pb6BargeInfoId);

    boolean updateBargeInfoAndCompanyBind(BargeInfoDTO bargeInfoDTO);

    List<Pb6Bargeinfo> searchForUpdateCheck(String bargeid);

    void insertById(Pb6Bargeinfo pb6Bargeinfo);

    List<Pb6Bargeinfo> searchByBargeName(String bargeName);

    IPage<Pb6Bargeinfo> searchListByBargeName(String bargeName);

    //通过驳船挂靠表中存的驳船基本信息表ID 查询驳船基本信息
    Pb6Bargeinfo searchPb6BargeinfoByPb6BargeCompanyId(Long pb6BargeInfoAuditId);

    /**
     * 判断点是否在多边形内
     *
     * @param point   检测点
     * @return 点在多边形内返回true，否则返回false
     */
    boolean IsPtInPoly(Point2D.Double point, List<Point2D.Double> polygon);

    String  searchMmsiByBargeName(String bargeName);
}

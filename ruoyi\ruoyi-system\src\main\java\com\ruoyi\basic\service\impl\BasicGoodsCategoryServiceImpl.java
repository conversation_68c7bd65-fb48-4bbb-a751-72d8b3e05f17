package com.ruoyi.basic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicGoodsCategory;
import com.ruoyi.basic.mapper.BasicGoodsCategoryMapper;
import com.ruoyi.basic.service.BasicGoodsCategoryService;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【BASIC_GOODS_CATEGORY】的数据库操作Service实现
* @createDate 2023-11-16 11:35:21
*/
@Service
public class BasicGoodsCategoryServiceImpl extends ServiceImpl<BasicGoodsCategoryMapper, BasicGoodsCategory>
    implements BasicGoodsCategoryService {

    @Autowired
    BasicGoodsNameServiceImpl basicGoodsNameService;

    @Override
    public List<BasicGoodsCategory> selectBasicGoodsCategoryList(BasicGoodsCategory basicGoodsCategory) {
        List<BasicGoodsCategory> basicGoodsCategories = baseMapper.selectBasicGoodsCategoryList(basicGoodsCategory);
        return basicGoodsCategories;
    }

    @Override
    public AjaxResult addBasicGoodsCategory(BasicGoodsCategory basicGoodsCategory) {
        if(baseMapper.insert(basicGoodsCategory) > 0){
            return AjaxResult.success("新增货类成功");
        }
        return AjaxResult.error("新增货类失败");
    }

    @Override
    public AjaxResult updateBasicGoodsCategory(BasicGoodsCategory basicGoodsCategory) {
        if(baseMapper.updateById(basicGoodsCategory) > 0){
            return AjaxResult.success("修改货类成功");
        }
        return AjaxResult.error("修改货类失败");
    }

    @Override
    public AjaxResult deleteBasicGoodsCategory(String goodsId) {
        if(baseMapper.deleteById(goodsId) > 0){
            // 删除货类成功，删除货类下的货物
            basicGoodsNameService.deleteBasicGoodsNameByCategoryId(goodsId);
            return AjaxResult.success("删除货类成功");
        }
        return AjaxResult.error("删除货类失败");
    }

    @Override
    public BasicGoodsCategory selectBasicGoodsCategoryByGoodsName(String goodsName) {
        return baseMapper.selectBasicGoodsCategoryByGoodsName(goodsName);
    }
}





<view class="login-page" style="padding-top:{{CustomBar}}px;">
  <view class="login-logo" hover-class="none" hover-stop-propagation="false">
    <image class="logo-image" src="/assets/image/logo.png" mode="aspectFit" lazy-load="false" binderror="" bindload=""></image>
    <view class="login-title" hover-class="none" hover-stop-propagation="false">
      广州港船务水上过驳服务平台  
    </view>
    <view class="login-warp" hover-class="none" hover-stop-propagation="false">
        <button wx:if = "{{showLoginBtn && !flag}}" class="login-btn" bindtap="loginGetPhoneNumber">手机号快捷登录</button>
        <button wx:if = "{{showLoginBtn && flag}}" class="login-btn" bindgetphonenumber="loginGetPhoneNumberFlag" open-type="getPhoneNumber">手机号快捷登录</button>
    </view>
  </view>
  <view class="login-protocol" hover-class="none" hover-stop-propagation="false">
    <radio value="r1" bindtap="radioChange" checked="{{checked}}"/>
    <text class="protocol-server" selectable="false" space="false" decode="false" bindtap="handleProtocolPage">《广州港船务水上过驳服务平台服务协议》</text>
  </view>
  <!-- <view>
    <text class="protocol-download" selectable="false" space="false" decode="false" bindtap="handleDownload">《船舶备案委托书模板》</text>
  </view> -->
  <view class="version">
    <text class="versionId">v2.5.0</text>
  </view>
  <!-- 身份弹窗 -->
  <mp-dialog title="请选择您当前身份" show="{{showIdentityDialog}}" mask="true" mask-closable="false" bindbuttontap="tapDialogButton" buttons="{{idenButtons}}">
      <radio-group bindchange="identityChange" class="iden-radio">
        <label class="iden-radio-item">
          <radio value="15" color="#00426B" checked="{{checkedUserType == 15}}"></radio>
          <view class="iden-radio-text">船员</view>
        </label>
        <label class="iden-radio-item">
          <radio value="14" color="#00426B" checked="{{checkedUserType == 14}}"></radio>
          <view class="iden-radio-text">船舶负责人</view>
        </label>
      </radio-group>
  </mp-dialog>
  <!-- 未绑船用户 -->
  <mp-dialog title="" show="{{showNormalDialog}}" bindbuttontap="reselectIdentity" buttons="{{normalIdenButtons}}" class="normal-dialog-wrap"  maskClosable="{{false}}" >
    <view class="normal-dialog" hover-class="none" hover-stop-propagation="false">
      <view class="normal-dialog-info" hover-class="none" hover-stop-propagation="false">
        当前身份为船员，请先联系船舶负责人添加船员
      </view>
      <view class="normal-dialog-btnwrap" hover-class="none" hover-stop-propagation="false">
        <view class="{{index==1 ?'normal-btn1 active-normal-btn1' : 'normal-btn1'}}" hover-class="none" catchtap="cancelNormalDialog" wx:for="{{normalIdenButtons1}}" wx:key="index" data-index="{{index}}">{{item.text}}</view>
      </view>
    </view>
  </mp-dialog>

  <!-- 管理员备案未通过、没有进行驳船备案弹窗 -->
  <mp-dialog title="" show="{{showUnpassedDialog}}" bindbuttontap="reselectIdentity" buttons="{{normalIdenButtons}}" class="normal-dialog-wrap" maskClosable="{{false}}" >
    <!-- 审核未通过弹窗加“重新选身份”按钮，首次备案成功就不显示这个“重新选身份”按钮 -->
    <view class="normal-dialog" hover-class="none" hover-stop-propagation="false">
      <view class="normal-dialog-info" hover-class="none" hover-stop-propagation="false">
        {{unpassTitle}}
        <!-- 当前备案审核未通过，请确定是否重新提交审核 -->
      </view>
      <view class="normal-dialog-btnwrap" hover-class="none" hover-stop-propagation="false">
        <view class="{{index==1 ?'normal-btn1 active-normal-btn1' : 'normal-btn1'}}" hover-class="none" catchtap="tapUnpassedDialog" wx:for="{{unpassedButtons}}" wx:key="index" data-index="{{index}}">{{item.text}}</view>
      </view>
    </view>
  </mp-dialog>

  <!-- 管理员备案审核中弹窗 -->
  <mp-dialog title="" show="{{showReviewDialog}}" bindbuttontap="tapReviewDialog" buttons="{{ReviewButtons}}" class="normal-dialog-wrap1" maskClosable="{{false}}" >
    <view class="normal-dialog" hover-class="none" hover-stop-propagation="false">
      <view class="review-dialog-info" hover-class="none" hover-stop-propagation="false">
        备案正在审核中,请耐心等待
      </view>
    </view>
  </mp-dialog>
</view>

package com.ruoyi.common.core.domain.entity;

import java.util.Date;
import java.util.List;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import com.ruoyi.common.annotation.Excel.Type;
import com.ruoyi.common.annotation.Excels;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户对象 sys_user
 * 
 * <AUTHOR>
 */
@ToString
@AllArgsConstructor
@Getter
@Setter
@TableName(value = "SYS_USER")
@KeySequence(value = "SEQ_SYS_USER")
public class SysUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户序号", cellType = ColumnType.NUMERIC, prompt = "用户编号")
    @TableId(value = "USER_ID", type = IdType.INPUT)
    private Long userId;


    @TableField(value = "USER_TYPE")
    private String userType;


    /** 部门ID */
    @Excel(name = "部门编号", type = Type.IMPORT)
    @TableField(value = "DEPT_ID")
    private Long deptId;

    /** 用户账号 */
    @Excel(name = "登录名称")
    @TableField(value = "USER_NAME")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户名称")
    @TableField(value = "NICK_NAME")
    private String nickName;

    /** 用户邮箱 */
    @Excel(name = "用户邮箱")
    @TableField(value = "EMAIL")
    private String email;

    /** 手机号码 */
    @Excel(name = "手机号码")
    @TableField(value = "PHONENUMBER")
    private String phonenumber;

    /** 用户性别 */
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    @TableField(value = "SEX")
    private String sex;

    /** 用户头像 */
    @TableField(value = "AVATAR")
    private String avatar;

    /** 密码 */
    @TableField(value = "PASSWORD")
    private String password;

    /** 盐加密 */
    @TableField(exist = false)
    private String salt;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    @TableField(value = "STATUS")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField(value = "DEL_FLAG")
    private String delFlag;

    /** 最后登陆IP */
    @Excel(name = "最后登陆IP", type = Type.EXPORT)
    @TableField(value = "LOGIN_IP")
    private String loginIp;

    /** 最后登陆时间 */
    @Excel(name = "最后登陆时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
    @TableField(value = "LOGIN_DATE")
    private Date loginDate;

    /** 创建人 */
    @TableField(value = "create_by")
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time")
    private Date createTime;

    /** 更新人 */
    @TableField(value = "update_by")
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time")
    private Date updateTime;

    /** opedId */
    @TableField(value = "OPEN_ID")
    private String openId;

    @TableField(value = "UNION_ID")
    private String unionId;//同个微信开放平台的unionid唯一

    /** 第三方公司id */
    @TableField(value = "COMPANY_ID")
    private Long companyId;

    /** 第三方公司名称 */
    @TableField(value = "COMPANY_NAME")
    private String companyName;

    /** 第三方用户id */
    @TableField(value = "GM_USER_ID")
    private Long gmUserId;

    /** 锁定次数 */
    @TableField(value = "LOCK_NUMBER",updateStrategy = FieldStrategy.IGNORED)
    private Long lockNumber;

    /** 锁定时间 */
    @TableField(value = "LOCK_TIME",updateStrategy = FieldStrategy.IGNORED)
    private Date lockTime;

    //第三方码头公司id
    @TableField(exist = false)
    private String comId;

    /**
     * 驳船id
     */
    @TableField(exist = false)
    private Long bargeId;

    /**
     * 第三方用户审核标识
     */
    @TableField(exist = false)
    private String passport;

    /** 菜单对象 */
    @TableField(exist = false)
    private List<SysMenu> menus;

    /** 部门对象 */
    @Excels({
            @Excel(name = "部门名称", targetAttr = "deptName", type = Type.EXPORT),
            @Excel(name = "部门负责人", targetAttr = "leader", type = Type.EXPORT)
    })
    @TableField(exist = false)
    private SysDept dept;

    /** 角色对象 */
    @TableField(exist = false)
    private List<SysRole> roles;

    /** 角色组 */
    @TableField(exist = false)
    private Long[] roleIds;

    /** 岗位组 */
    @TableField(exist = false)
    private Long[] postIds;

    @TableField(exist = false)
    private GMUser gmUser;

    /**
     * 身份证号码
     */
    @TableField(value = "IDENTITY_ID")
    private String identityId;

    /**
     * 身份证正面 bse64图片
     */
    @TableField(exist = false)
    private String identityPositiveUrl;

    /**
     * 身份证反面 bse64图片
     */
    @TableField(exist = false)
    private String identityNegativeUrl;

    //适航证书有效期
    @TableField(exist = false)
    private String validsaildate;

    @TableField(exist = false)
    private String bargename;

    public SysUser()
    {

    }

    public Long getLockNumber() {
        return lockNumber;
    }

    public void setLockNumber(Long lockNumber) {
        this.lockNumber = lockNumber;
    }

    public Date getLockTime() {
        return lockTime;
    }

    public void setLockTime(Date lockTime) {
        this.lockTime = lockTime;
    }

    public String getIdentityId() {
        return identityId;
    }

    public void setIdentityId(String identityId) {
        this.identityId = identityId;
    }

    public String getIdentityPositiveUrl() {
        return identityPositiveUrl;
    }

    public void setIdentityPositiveUrl(String identityPositiveUrl) {
        this.identityPositiveUrl = identityPositiveUrl;
    }

    public String getIdentityNegativeUrl() {
        return identityNegativeUrl;
    }

    public void setIdentityNegativeUrl(String identityNegativeUrl) {
        this.identityNegativeUrl = identityNegativeUrl;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public SysUser(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public boolean isAdmin()
    {
        return isAdmin(this.userId);
    }

    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    public String getNickName()
    {
        return nickName;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    public String getEmail()
    {
        return email;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    public String getPhonenumber()
    {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber)
    {
        this.phonenumber = phonenumber;
    }

    public String getSex()
    {
        return sex;
    }

    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public String getAvatar()
    {
        return avatar;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }

	@JsonIgnore
    @JsonProperty
    public String getPassword()
    {
        return password;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }

    public String getSalt()
    {
        return salt;
    }

    public void setSalt(String salt)
    {
        this.salt = salt;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getLoginIp()
    {
        return loginIp;
    }

    public void setLoginIp(String loginIp)
    {
        this.loginIp = loginIp;
    }

    public Date getLoginDate()
    {
        return loginDate;
    }

    public void setLoginDate(Date loginDate)
    {
        this.loginDate = loginDate;
    }

    public SysDept getDept()
    {
        return dept;
    }

    public void setDept(SysDept dept)
    {
        this.dept = dept;
    }

    public List<SysRole> getRoles()
    {
        return roles;
    }

    public void setRoles(List<SysRole> roles)
    {
        this.roles = roles;
    }

    public Long[] getRoleIds()
    {
        return roleIds;
    }

    public void setRoleIds(Long[] roleIds)
    {
        this.roleIds = roleIds;
    }

    public Long[] getPostIds()
    {
        return postIds;
    }

    public void setPostIds(Long[] postIds)
    {
        this.postIds = postIds;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("deptId", getDeptId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("email", getEmail())
            .append("phonenumber", getPhonenumber())
            .append("sex", getSex())
            .append("avatar", getAvatar())
            .append("password", getPassword())
            .append("salt", getSalt())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("loginIp", getLoginIp())
            .append("loginDate", getLoginDate())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("dept", getDept())
            .toString();
    }
}

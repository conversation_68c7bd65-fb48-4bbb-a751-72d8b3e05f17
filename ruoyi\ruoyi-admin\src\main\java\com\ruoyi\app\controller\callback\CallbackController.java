package com.ruoyi.app.controller.callback;

/**
 * @Description 回调统一接口
 * <AUTHOR>
 * @Date 2020/10/24 18:05
 */

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.app.controller.support.fdd.FddCompanyService;
import com.ruoyi.app.controller.support.ftp.FtpTemplate;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.mapper.ShipFddUserRelMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/callback")
public class CallbackController {

    @Autowired
    private FddCommonService fddCommonService;

    @Autowired
    private FddCompanyService fddCompanyService;

    @Autowired
    private ShipFddUserRelMapper shipFddUserRelMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private FtpTemplate ftpTemplate;

    /**
     * 回调更新船公司印章审核状态
     * @return
     */
    @RequestMapping("/updateSealState")
    @Transactional
    public AjaxResult updateSealState(@RequestBody Map<String, Object> map) throws IOException {
        //1.回调响应？？？

        /*Map<String,String[]> map=request.getParameterMap();
        log.info(String.valueOf(request.getParameterMap()));*/
        log.info("fdd回调响应");
        log.info(String.valueOf(map));

        String bizContent=(String)map.get("bizContent");
        byte[] bytes = Base64.decode(bizContent);
        //BASE64Decoder base64Decoder=new BASE64Decoder();
        //byte[] bytes=base64Decoder.decodeBuffer(bizContent);
        String content=new String(bytes);
        /*urlDecode={"statusDesc":"审核通过","appId":"100000","customerId":"af253037dec04bf691c0d714e236d1b1","auditFailReason":"","serialNo":"7bcd5ad804314f54af3682dd74c0a4ac","status":4}*/
        String urlDecode= URLDecoder.decode(content, StandardCharsets.UTF_8.name());
        log.info(urlDecode);

        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(urlDecode);
        Integer status = (Integer) jsonObject.get("status");

        if (status != null && status == 4) {
            ShipFddUserRel shipFdd = new ShipFddUserRel();
            String customerId = (String) jsonObject.get("customerId");
            shipFdd.setFddAccountId(customerId);
            ShipFddUserRel fdd = shipFddUserRelMapper.selectOne(new QueryWrapper<>(shipFdd));
            SysUser sysUser = sysUserMapper.selectUserById(fdd.getShipUserId());
            File file = null;
            try {
                log.info("获取印章文件");
                file = File.createTempFile(UUID.randomUUID().toString(), ".png");
                file.deleteOnExit();
                FileOutputStream outputStream = new FileOutputStream(file);
                ftpTemplate.download(fdd.getSealUrl(),outputStream);
                outputStream.close();
            }catch (Exception e){
                log.error("印章文件不存在!");
                log.error(String.valueOf(e));
                //throw new CustomException("印章文件不存在!",e);
            }

            // 上传印章
            if (file != null) {
                fddCommonService.uploadAndSaveSeal(customerId, sysUser.getCompanyName(), file);
            }

            // 通过成功
            fdd.setReviewStatus("1");
            shipFddUserRelMapper.updateById(fdd);

            // 申请企业实名证书
            fddCompanyService.companyCertifiedApply(fdd.getFddAccountId(), fdd.getVerifiedSerialNo());
        } else {
            log.error("status："+status+"，statusDesc："+jsonObject.get("statusDesc"));
        }

        return AjaxResult.success();
    }

    /**
     * fdd自动签署回调，不做任何操作
     * @return
     */
    @RequestMapping("/fddNotifyUrl")
    @Transactional
    public Map<String, Object>  fddNotifyUrl(@RequestBody Map<String, Object> map) throws IOException {
        //1.回调响应
        Map<String,Object> resultMap =new HashMap<>();
        resultMap.put("code",0);
        resultMap.put("msg","成功");
        return resultMap;
    }

    /**
     * fdd
     * @return
     */
    @PostMapping("/fddState")
    @Transactional
    public AjaxResult fddState(@RequestBody SysUser sysUser) {
        Long userId = sysUser.getUserId();
        if (userId == null) {
            return AjaxResult.error("用户id为空");
        }
        // 查询印章
        ShipFddUserRel shipFddUserRel = new ShipFddUserRel();
        shipFddUserRel.setType("2");
        shipFddUserRel.setShipUserId(userId);

        QueryWrapper<ShipFddUserRel> wrapper = new QueryWrapper<>(shipFddUserRel);
        ShipFddUserRel rel = shipFddUserRelMapper.selectOne(wrapper);

        return AjaxResult.success(rel);
    }

    @PostMapping("/selectFdd")
    @Transactional
    public AjaxResult selectFdd() {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        // 查询印章
        ShipFddUserRel shipFddUserRel = new ShipFddUserRel();
        shipFddUserRel.setType("2");
        shipFddUserRel.setShipUserId(userId);

        QueryWrapper<ShipFddUserRel> wrapper = new QueryWrapper<>(shipFddUserRel);
        ShipFddUserRel rel = shipFddUserRelMapper.selectOne(wrapper);

        // 审核不通过去fdd查询认证状态
        if (rel!=null && !"1".equals(rel.getReviewStatus())) {
            JSONObject jsonObject = fddCompanyService.companyCertificationStatus(rel.getFddAccountId());
            //String verifiedStatusDesc = (String) jsonObject.get("verifiedStatusDesc");
            Integer status = (Integer) jsonObject.get("verifiedStatus");
            log.info("法大大->"+jsonObject);
            if (4 == status) {
                ShipFddUserRel shipFdd = new ShipFddUserRel();
                shipFdd.setId(rel.getId());
                File file;
                try {
                    file = File.createTempFile("company-", ".png");
                    file.deleteOnExit();
                    ftpTemplate.download(rel.getSealUrl(),new FileOutputStream(file));
                }catch (Exception e){
                    throw new CustomException("印章文件不存在!",e);
                }

                SysUser sysUser = sysUserMapper.selectUserById(userId);
                // 上传印章
                fddCommonService.uploadAndSaveSeal(rel.getFddAccountId(), sysUser.getCompanyName(), file);

                // 审核成功
                shipFdd.setReviewStatus("1");
                shipFddUserRelMapper.updateById(shipFdd);

                // 申请企业实名证书
                fddCompanyService.companyCertifiedApply(rel.getFddAccountId(), rel.getVerifiedSerialNo());
            } else if ("4".equals(rel.getReviewStatus())) {
                ShipFddUserRel shipFdd = new ShipFddUserRel();
                shipFdd.setId(rel.getId());
                shipFdd.setReviewStatus("2");
                shipFddUserRelMapper.updateById(shipFdd);
            }
        }
        ShipFddUserRel fdd = null;
        if (StringUtils.isNotNull(rel)) {
            fdd = shipFddUserRelMapper.selectById(rel.getId());
        }
        return AjaxResult.success(fdd);
    }
}

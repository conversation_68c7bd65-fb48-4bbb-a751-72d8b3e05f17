package com.ruoyi.consignor.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.domain.UploadAddress;
import lombok.Data;

import java.util.List;

/**
 * @Description 货主托运VO
 * <AUTHOR>
 * @Date 2020/8/17  15:25
 */
@Data
public class ConsignorConsignVO {

    /**
     * 托运单明细id
     */
    private Long consignDetailId;

    /**
     * 驳船名称
     */
    private String bargeName;

    /**
     * 托号
     */
    private String consignFlag;

    /**
     * 运单主表id
     */
    private Long consignId;

    /**
     * 货物名称
     */
    private String cargeName;

    /**
     * 起运港
     */
    private String beginPort;

    /**
     * 目的港
     */
    private String endPort;

    /**
     * 收货人
     */
    private String consignee;

    /**
     * 驳船状态，0：未审核 1：已审核 2：审核不通过 3：已配载 4：已报到 5：已离港 6:已退单 7:已改单
     */
    private String flagBargeState;

    /**
     * 小程序操作状态(0:待驳船主确认 1:待支付  2:待船公司审批(月结)，3船公司审批不通过(月结) 4:待驳船主预约
     * 5:驳船主已预约 6:取消预约 )
     */
    private Integer wxOperateState;

    /**
     * 派船状态
     */
    private Integer wxSelectShipState;

    /**
     * 营业厅申请改单/退单
     */
    private String applyModify;


    /**
     * 托运单重量
     */
    private String rationWeight;


    /**
     * 出库单类型1.本司2.它司
     */
    private Integer wxOutorInformType;

    /**
     * 办单类型1-货主；   2-船公司
     */
    private Integer wxApplyUserType;


    /**
     * 托运单创建人id
     */
    @TableField("WXCREATEUSERBYID")
    private Integer wxCreateUserById;


    /**
     * 派船类型(货主办理托运单时)(0:货主派船 1：船公司派船)
     */
    private Integer wxSelectShipType;


    /**
     *  费用结算方式
     */
    private String chargeBalanceType;

    private List<UploadAddress> dataList;


    /**
     * 新托运单标志(0：正常办理，1：新生成的托运单)
     */
    private String newWaybill;


    /**
     * 派船公司id
     */
    @TableField("WXSELECTSHIPBYID")
    private Long wxSelectShipById;
    /**
     * 派船公司名称
     */
    @TableField("WXSELECTSHIPBYNAME")
    private String wxSelectShipByName;


    /**
     * 创建人所属公司id
     */
    @TableField("WXCREATECOMPANYID")
    private Long wxCreateCompanyId;


    /**
     * 是否订阅
     */
    private String isSubscription;

    /**
     * 创建时间-办单时间
     */
    private String applyDate;

    /**
     * 申请日期
     */
    private String recordDate;

    /**
     * 出库单号、
     */
    private String outOrInformId;

}

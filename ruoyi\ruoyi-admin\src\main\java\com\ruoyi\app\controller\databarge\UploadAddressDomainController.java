package com.ruoyi.app.controller.databarge;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import com.ruoyi.barge.domain.SysUserBargeBak;
import com.ruoyi.barge.service.SysUserBargeBakService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ftp.FtpUtils;
import com.ruoyi.databarge.domain.UploadAddressDomain;
import com.ruoyi.databarge.domain.dto.BargePictureSearchDTO;
import com.ruoyi.databarge.mapper.UploadAddressDomainMapper;
import com.ruoyi.databarge.service.UploadAddressDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/10/10.
 * @Date: 2020/10/10 16:26
 */
@RestController
@RequestMapping("/barge/uploadaddress")
public class UploadAddressDomainController {

    @Autowired
    private UploadAddressDomainService uploadAddressDomainService;

    @Autowired
    private FtpUtils ftpUtils;

    @Autowired
    private UploadAddressDomainMapper uploadAddressDomainMapper;

    @Autowired
    private SysUserBargeBakService sysUserBargeBakService;
    //数据公司 -->托运单界面查询托运单对应图片
    @PostMapping("/searchCargoconsignmentPictureByMainId")
    public AjaxResult searchCargoconsignmentPictureByMainId(@RequestBody Long id){
        List<UploadAddressDomain> list = uploadAddressDomainService.searchCargoconsignmentPictureByMainId(id);
        list.forEach(i -> {
            String base64 = ftpUtils.downloadBase64(i.getUrl());
            i.setFileBase64("data:image/jpeg;base64," + base64);
        });
        return AjaxResult.success(list);
    }

    //数据公司 -->消息界面查询驳船备案对应图片
    @PostMapping("/searchBargePicturesByIdAndStatus")
    public AjaxResult searchBargePicturesByIdAndStatus(@RequestBody BargePictureSearchDTO bargePictureSearchDTO){
        if(bargePictureSearchDTO.getStatus() != null && (bargePictureSearchDTO.getStatus()  == 0 || bargePictureSearchDTO.getStatus()  == 1)){
            List<UploadAddressDomain> list = new ArrayList<UploadAddressDomain>();
            if(bargePictureSearchDTO.getApplyId() == null){
                list = uploadAddressDomainService.searchBargePicturesByIdAndStatus(bargePictureSearchDTO);
                List<UploadAddressDomain> list1 = uploadAddressDomainService.searchBargeOwnerIdentityPic(bargePictureSearchDTO);
                if(list1 == null || list1.size() == 0){
                    list1 = uploadAddressDomainService.searchBargeOwnerIdentityPicByUploadUserId(bargePictureSearchDTO);
                }
                SysUserBargeBak sysUserBargeBak = sysUserBargeBakService.getOne(new LambdaQueryWrapper<SysUserBargeBak>().eq(SysUserBargeBak::getBargeId,bargePictureSearchDTO.getId()));
                if(!StringUtils.isNull(sysUserBargeBak)){
                    list1 = uploadAddressDomainService.list(new LambdaQueryWrapper<UploadAddressDomain>().in(UploadAddressDomain::getLinkId,sysUserBargeBak.getUserId()).eq(UploadAddressDomain::getLinkType,50).eq(UploadAddressDomain::getStatus,1));
                }
                List<UploadAddressDomain> finalList = list1;
                /*list.forEach(item ->{
                    if(item.getDataType().equals(53) && finalList.size() != 0){
                        for(int i = 0; i < finalList.size() ; i++){
                            if (finalList.get(i).getDataType().equals(53)){
                                finalList.remove(i);
                            }
                        }
                    }
                });*/
                List<UploadAddressDomain> attachmentList = uploadAddressDomainService.searchBargeAttachmentPic(bargePictureSearchDTO);
                list.addAll(attachmentList);
                list.addAll(finalList);
            }
            else {
                list = uploadAddressDomainService.list(new LambdaQueryWrapper<UploadAddressDomain>().eq(UploadAddressDomain::getLinkId,bargePictureSearchDTO.getId()).in(UploadAddressDomain::getLinkType,10).in(UploadAddressDomain::getDataType,11,12,13,14).eq(UploadAddressDomain::getUploadUserId,bargePictureSearchDTO.getApplyId()).eq(UploadAddressDomain::getStatus,0));
                List<UploadAddressDomain> list1 = uploadAddressDomainService.list(new LambdaQueryWrapper<UploadAddressDomain>().eq(UploadAddressDomain::getLinkId,bargePictureSearchDTO.getApplyId()).eq(UploadAddressDomain::getUploadUserId,bargePictureSearchDTO.getApplyId()).eq(UploadAddressDomain::getLinkType,50).in(UploadAddressDomain::getStatus,1));
                list.forEach(item ->{
                    if(item.getDataType().equals(53) && list1.size() != 0){
                        for(int i = 0 ; i < list1.size() ; i++){
                            list1.remove(i);
                        }
                    }
                });
                list.addAll(list1);
            }
//            list.forEach(i -> {
//                String base64 = ftpUtils.downloadBase64(i.getUrl());
//                i.setFileBase64("data:image/jpeg;base64," + base64);
//            });
            Collections.sort(list,Comparator.comparing(UploadAddressDomain::getUploadTime,(t1,t2) ->t2.compareTo(t1)));
            Set<Integer> set = new HashSet<Integer>();
            List<UploadAddressDomain> list1 = new ArrayList<UploadAddressDomain>();
            for(int i = 0 ; i < list.size() ; i++){
                UploadAddressDomain domain = list.get(i);
                if(!set.contains(list.get(i).getDataType())){
                    set.add(domain.getDataType());
                    list1.add(domain);
                }
            }
            return AjaxResult.success(list1);
        } else {
            return AjaxResult.error("状态码错误！");
        }
    }

    //数据公司 -->驳船基本信息界面更改驳船图片
    @PostMapping("/updatePictureForShipInfo")
    public AjaxResult updatePictureForShipInfo(@RequestParam("file") MultipartFile file, @RequestParam("bargeId") Long bargeId, @RequestParam("picType") Integer picType) throws SftpException, JSchException, IOException {
        if(!file.isEmpty() && bargeId != null && picType != null){
            return AjaxResult.success(uploadAddressDomainService.updatePictureForShipInfo(file, bargeId, picType));
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }

    //数据公司 -->驳船主小程序上传代理发货图片
    @PostMapping("/updateAgentDeliveryImage")
    public AjaxResult updateAgentDeliveryImage(@RequestParam("file") MultipartFile file, @RequestParam("agentDeliveryId") Long agentDeliveryId,
                                               @RequestParam("isAdd") Integer isAdd) throws SftpException, JSchException, IOException {
        if(!file.isEmpty() && agentDeliveryId != null){
            return AjaxResult.success(uploadAddressDomainService.uploadAgentDeliveryImage(file, agentDeliveryId, isAdd));
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }

    @PostMapping("/selectAgentDeliveryImages")
    public AjaxResult selectAgentDeliveryImages(@RequestBody Long agentDeliveryId){
        LambdaQueryWrapper<UploadAddressDomain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UploadAddressDomain::getLinkId, agentDeliveryId)
                .eq(UploadAddressDomain::getLinkType, 60);
        return AjaxResult.success(uploadAddressDomainService.list(queryWrapper));
    }

    @PostMapping("/searchPb6WatercargoFile")
    public AjaxResult searchPb6WatercargoFile(@RequestBody String waterwaycargoid){
        return AjaxResult.success(uploadAddressDomainService.searchPb6WatercargoFile(waterwaycargoid));
    }

    @PostMapping("/searchPb6WatercargoFileApp")
    public AjaxResult searchPb6WatercargoFileApp(@RequestBody String id){
        return AjaxResult.success(uploadAddressDomainService.searchPb6WatercargoFileApp(id));
    }

    @GetMapping("/countWaterwaycargoNumber")
    public AjaxResult countWaterwaycargoNumber(){
        return AjaxResult.success(uploadAddressDomainMapper.countWaterwaycargoNumber());
    }

    @PostMapping("/alreadyToNewPic")
    public AjaxResult alreadyToNewPic(@RequestBody BargePictureSearchDTO bargePictureSearchDTO){
        List<UploadAddressDomain> list = uploadAddressDomainService.list(new LambdaQueryWrapper<UploadAddressDomain>().eq(UploadAddressDomain::getLinkId,bargePictureSearchDTO.getId()).in(UploadAddressDomain::getLinkType,10,50).in(UploadAddressDomain::getDataType,11,12,13,14,53).eq(UploadAddressDomain::getStatus,1));
        List<UploadAddressDomain> list1 = uploadAddressDomainService.list(new LambdaQueryWrapper<UploadAddressDomain>().eq(UploadAddressDomain::getLinkId,bargePictureSearchDTO.getApplyId()).eq(UploadAddressDomain::getLinkType,50).eq(UploadAddressDomain::getStatus,1));
        list.addAll(list1);
        return AjaxResult.success(list);
    }
}

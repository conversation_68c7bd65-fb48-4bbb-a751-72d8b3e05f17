package com.ruoyi.businessFile.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.businessFile.domain.BusinessFile;
import com.ruoyi.businessFile.domain.vo.BusinessFileVo;
import com.ruoyi.businessFile.domain.vo.FileVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 14:59
 */
public interface BusinessFileMapper extends BaseMapper<BusinessFile> {

    public List<FileVo> listBusinessFile(BusinessFile businessFile);

    BusinessFileVo selectBusinessFileByFileId(String fileId);

    // 根据大船ID查询文件列表
    List<BusinessFile> selectFileListByShipId(Long fileBusinessFile);

}

package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 驳船审核消息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("PB6_BARGE_CHECK_MESSAGE")
@KeySequence("SEQ_PB6_BARGE_CHECK_MESSAGE")
public class Pb6BargeCheckMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 消息类型：0、驳船备案审核；1、驳船备案加挂靠； 2、驳船信息修改审核； 3、驳船添加挂靠； 4、驳船挂靠修改审核； 5、驳船取消挂靠；
     * 6、退单物流公司审核；7、退单码头审核； 8、改单物流公司审核； 9、改单码头审核； 10、差额退款审核； 11、可配载托运单； 12、船公司审核;13、驳船自动报到
     */
    @TableField("MTYPE")
    private Integer mtype;

    /**
     * 申请人ID
     */
    @TableField("APPLYMANID")
    private Long applymanid;

    /**
     * 申请人
     */
    @TableField("APPLYMAN")
    private String applyman;

    /**
     * 申请时间
     */
    @TableField("APPLYTIME")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Timestamp applytime;

    /**
     * 审核人ID
     */
    @TableField("AUDITMANID")
    private Long auditmanid;

    /**
     * 审核人
     */
    @TableField("AUDITMAN")
    private String auditman;

    @TableField(exist = false)
    private String auditManCh;

    /**
     * 审核时间
     */
    @TableField("AUDITTIME")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Timestamp audittime;

    /**
     * 审核标识： 0未审核(未读)，1审核通过(已读)，2审核不通过
     */
    @TableField("AUDITFLAG")
    private Integer auditflag;

    /**
     * 驳船名
     */
    @TableField("BARGENAME")
    private String bargename;

    /**
     * 水路运单号（不是ID，直接存运单号）
     */
    @TableField("WATERWAYCARGOID")
    private String waterwaycargoid;

    /**
     * 驳船信息备份表ID
     */
    @TableField("PB6BARGEINFOAUDITID")
    private Long pb6bargeinfoauditid;

    /**
     * 驳船挂靠信息表ID
     */
    @TableField("PB6BARGECOMPANYID")
    private Long pb6bargecompanyid;

    /**
     * 货物托运单明细表ID
     */
    @TableField(value = "PB6CARGOCONSIGNMENTDETAILID")
    private Long pb6cargoconsignmentdetailid;

    /**
     * 托运单号（不是ID，直接存托运单号）
     */
    @TableField(value = "CONSIGNFLAG")
    private String consignflag;

    /**
     * 备注（消息详情）
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 审核不通过原因
     */
    @TableField("FAILUREREASONS")
    private String failurereasons;

    /**
     * 申请人电话
     */
    @TableField(exist = false)
    private String phonenumber;

    /**
     * FDD表id
     */
    @TableField("SHIPFDDUSERRELID")
    private String shipfdduserrelid;

    /**
     * 代理发货表的ID
     */
    @TableField("AGENTDELIVERYID")
    private Long agentdeliveryid;

    /**
     * 删除标识 0 未删除， 1 已删除
     */
    @TableField("DELFLAG")
    private Integer delflag;
}

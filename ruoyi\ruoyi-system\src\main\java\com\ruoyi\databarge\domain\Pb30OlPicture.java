package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/8/22 16:00
 */
@Getter
@Setter
@ToString
@TableName(value = "PB30_OL_PICTURE")
@KeySequence("SEQ_PB30_OL_PICTURE")
public class Pb30OlPicture {
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;
    @TableField(value = "USERID")
    private String userId;
    @TableField(value = "IMAGE_TYPE")
    private String imageType;
    @TableField(value = "IMAGE_NAME")
    private String imageName;
    @TableField(value = "IMAGE_URL")
    private String imageUrl;
    @TableField(value = "IMAGE_INFO")
    private String imageInfo;
    @TableField(exist = false)
    private String base64Img;
}

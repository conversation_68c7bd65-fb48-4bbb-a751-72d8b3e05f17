// pages/consignment/reservation/reservation.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    modalHidden:true,
    showMessage:false,
    comId:"",
    //预约时间
    appointmentTime:"",
    //预约日期
    appointmentDate:"",
    //当前托运对象
    currentConsignObj:{},
    //表单
    form:{},
    //是否显示批量下载弹窗
    batchDialog:false,
    //用户邮箱 
    userEmail:"",
    //批量下载按钮
    batchButtons:[{text: '取消'}, {text: '确定'}],
    // payRecordList 支付记录
    payRecordList: [],
    payType: '', // 支付方式
    payStatus: '', // 支付状态
    payStatusObj: {
      'status0': '未支付',
      'status1': '支付成功',
      'status2': '支付失败',
      'status3': '处理中',
      'status4': '待退款',
      'status5': '退款成功',
      'status6': '退款失败',
      'status7': '退款中',
      'status8': '退款待审核',
      'status9': '订单关闭'
    },
  },

  /**
   * 
   * 方法
   */
  /**
   * 判断是否存在，不存在返回空字符串或其他
   * @param {传进来的变量} empty 
   * @param {为空时返回的值} replace 
   */
  emptyReturn(empty,replace){
    if(!replace || replace==null){
      replace = ""
    }
    if(empty == null || !empty){
      return replace
    }else{
      return empty
    }
  },

   /**
   *  获取水路运单资料
   * 参数：
   * waterwayCargoId 水路运单编号
     type 文件类型 1-广州港新沙港务有限公司驳船装货交接凭证.docx 2-货物交接清单.docx 3-水路货物运单.pdf
   */
  getSeaWayBill(e) {
    let item = e.currentTarget.dataset.item 
    let param = {
      type: item.type,
      waterwayCargoId: this.data.form.waterWayCargoId,
    }
    if(!param.waterwayCargoId) {
      app.$message('水路运单未生成，暂无可预览文件');
    }
    app.$downLoad(app.$url.consign.getPdfOrWord, param).then((res) => {
      if(res.byteLength <= 100) {
        let uint8_msg = new Uint8Array(res);
        let decodedString = JSON.parse(String.fromCharCode.apply(null, uint8_msg));
        app.$message(`预览文件失败，原因${decodedString.msg}`);
        return
      }
      const fs = wx.getFileSystemManager(); // 获取全局唯一的文件管理器
      // 写文件
      fs.writeFile({
        filePath: wx.env.USER_DATA_PATH + `/${item.dataName}`, //写入的文件路径 (本地路径)
        data: res, // 写入的文本或二进制数据
        encoding: "binary", // 文件的字符编码
        success(res) {
          wx.openDocument({
            filePath: wx.env.USER_DATA_PATH + `/${item.dataName}`,  //拿上面存入的文件路径
            success: function (res) {
              console.log(res);
            }
          })
        },
      })
    })

  
  
  },

  //出库记录查询
  outBoundJump(){
    wx.navigateTo({
      url: "/pages/consignment/bargeOutboundInquiry/bargeOutboundInquiry",
    })
  },

  //邮箱input
  emailInput(e){
    this.setData({
      userEmail:e.detail.value
    })
  },

  //点击批量下载弹窗按钮
  tapBatchDialogButton(e){
    // 点击取消按钮
    if(e.detail.index == 0) {
    }else {
      // 点击确定按钮-发送邮件 //参数
      let param = {
        toEmail:this.data.userEmail,
        waterwayCargoId: this.data.form.waterWayCargoId,
      }
      //邮箱校验格式
      let emailReg = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
      let email = param.toEmail //邮箱
      if(!email){
        app.$message("请输入邮箱")
        return
      }
      if(email && !emailReg.test(email)) {
        app.$message("你输入的邮箱格式不正确!")
        return;
      } 
      app.$post(app.$url.consign.sendEmail,param).then(res=>{
        if(res.code == 200){
          app.$message(res.msg)
        }else {
          app.$message(res.msg)
        }
      })
    }
    this.setData({
      batchDialog:false
    })
  },

  //批量下载
  batchDown(){
    this.setData({
      batchDialog:true
    })
  },

  //获取详情
  getDetailData(){
    let that = this 
    if(!this.data.currentConsignObj.consignDetailId){
      app.$message("详情参数id为空")
      return
    }
    //运单详情id
    app.$post(app.$url.consign.getConsignDetail,{id:this.data.currentConsignObj.consignDetailId}).then(res=>{
      if(res.code == 200){
        if(res.data){
          this.comId = res.data.comId;
          let appointmentDate = "" // 预约日期
          let appointmentTime = "" // 预约时间
          //校验null值
          for(let key in res.data){
            if(key !=  "wxOperateState"){
              res.data[key] = that.emptyReturn(res.data[key])
            } 
            if(key == "wxAppointmentTime" && res.data[key].length>0){
              appointmentDate = res.data[key].slice(0,10)
              appointmentTime = res.data[key].slice(11)
            }
          }
          this.setData({
            appointmentDate,
            appointmentTime
          })
      

          /**
           *  1、已支付才给展示水路运单文件，“待确认、待支付”状态下不展示文件。
              2、“待预约、待报道、已报到”这三个状态就展示一个文件：“水路运单”
              3、“已完成”状态下，展示3个文件：“水路运单、驳船交接凭证、驳船交接清单”
           */
         
          // 固定写死文件列表
          let dataList = [{
            type: 1,
            dataName: "广州港新沙港务有限公司驳船装货交接凭证.pdf",
          },{
            type: 2,
            dataName: "货物交接清单.pdf",
          },{
            type: 3,
            dataName: "水路货物运单.pdf",
          },]
          let resultDataList = [] //过滤后的文件列表
 
          let wxOperateStateText = this.data.currentConsignObj.wxOperateStateText //订单状态
          dataList.map(item=>{
            if(item.type == 3 && (wxOperateStateText == "待预约" || wxOperateStateText == "待报到" || wxOperateStateText == "已报到")) {
              resultDataList.push(item)
            }else if(wxOperateStateText == "已完成") {
              resultDataList.push(item)
            }
          })
          res.data.dataList = resultDataList
          
          this.setData({
            form:res.data,
          })
          // 如果水路运单号存在，那么加载支付记录
          if(this.data.form.waterWayCargoId) {
            this.getPayRecords(this.data.form.waterWayCargoId);
          }
        }
      }else {
      app.$message(res.msg)
      }
    })
  },

  // 点击日期改变
  bindDateChange(e) {
    this.setData({
      appointmentDate: e.detail.value
    })
    
  },

  // 点击时间改变
  bindTimeChange(e) {
    this.setData({
      appointmentTime: e.detail.value + ":00"
    })
    
  },

  //提交预约
  handleReview(e){
    this.setData({showMessage:false});
    let currentConsignObj = this.data.currentConsignObj
    let wxAppOintmentTime = this.data.appointmentDate + " " + this.data.appointmentTime//预约时间
    let param = {
      wxAppOintmentTime,/// 预约时间，取消预约不用传
      id:currentConsignObj.consignDetailId, //运单详情id
      wxOperateState:"5" // 5-预约，6-取消预约
    }
    if(!this.data.appointmentDate){
      app.$message("请选择预约日期")
      return
    }
    if(!this.data.appointmentTime){
      app.$message("请选择预约时间")
      return
    }
    app.$post(app.$url.consign.reservation,param).then(res=>{
        if(res.msg == '预约成功请申报绿色驳船'){
            this.setData({
              modalHidden:false
            })
        }
        else{
            if(res.code == 200){
                app.$message("预约成功")
                wx.setStorageSync("activeTab","3") //跳转报道tab
                setTimeout(()=>{
                  wx.switchTab({
                    url: '/pages/consignment/consignmanager/consignmanager',
                  })
                },1000)
              }else{
                app.$message(res.msg)
              }
        }
    })
   /*  if(parseInt(this.comId) == 1){
      this.setData({showMessage:true});
    }
    else{
      this.handleOk(e);
    } */
  },
  //点击确定弹窗后返回
  modalConfirm(){
    wx.setStorageSync("activeTab","3") //跳转报道tab
    setTimeout(()=>{
      wx.switchTab({
        url: '/pages/consignment/consignmanager/consignmanager',
      })
    },1000)
  },
  handleOk(e){
    /* this.setData({showMessage:false});
    let currentConsignObj = this.data.currentConsignObj
    let wxAppOintmentTime = this.data.appointmentDate + " " + this.data.appointmentTime//预约时间
    let param = {
      wxAppOintmentTime,/// 预约时间，取消预约不用传
      id:currentConsignObj.consignDetailId, //运单详情id
      wxOperateState:"5" // 5-预约，6-取消预约
    }
    if(!this.data.appointmentDate){
      app.$message("请选择预约日期")
      return
    }
    if(!this.data.appointmentTime){
      app.$message("请选择预约时间")
      return
    }
    app.$post(app.$url.consign.reservation,param).then(res=>{
      if(res.code == 200){
        app.$message("预约成功")
        wx.setStorageSync("activeTab","3") //跳转报道tab
        setTimeout(()=>{
          wx.switchTab({
            url: '/pages/consignment/consignmanager/consignmanager',
          })
        },1000)
      }else{
        app.$message(res.msg)
      }
    }) */
  },

  // 结算方式切换
  radioChange(e){
    this.setData({
      "form.chargeBalanceType":e.detail.value
    })
  },
  // 获取支付记录
  getPayRecords(waterWayCargoId) {
    app.$post(app.$url.pay.payDetails, waterWayCargoId).then((res) => {
      if(res.code == 200) {
        this.setData({
          'payRecordList': res.data.reverse()
        });
        if(res.data.length >= 1) {
          let lastItem = res.data[0];
          this.setData({
            'payType': lastItem.paytype,
            'payStatus': lastItem.status
          })
        }
      }else {
        app.$message('获取支付记录失败，请稍后重试。');
      }
    }) 
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.currentConsignObj && options.currentConsignObj != "{}"){
      let currentConsignObj = JSON.parse(options.currentConsignObj)
      this.setData({
        currentConsignObj,
      })
    }else{
      let currentConsignObj = JSON.parse(wx.getStorageSync('payCurrentConsignObj'))
      this.setData({
        currentConsignObj,
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getDetailData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
 
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
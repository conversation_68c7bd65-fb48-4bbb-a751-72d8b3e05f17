// pages/minepage/choosebarge/choosebarge.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //用户类型userType：00系统用户， 01-注册用户，10-货主，11-托运单联系人，12-船公司管理员，13-船公司业务员，14-驳船管理员，15-驳船业务员
    userInfos:"",//用户信息
    //搜素关键字
    searchValue:"",
    //驳船列表
    bargeList: [],
    //选中驳船数组
    selectedBargeArr: [] ,
    //选中的驳船id
    bargeId:"",
  },

  /**
   * 
   * 方法
   */

  //清空关键字
  deleteSearchValue(){
    this.setData({
      "searchValue":""
    })
  },

  //回显选中驳船
  getCheckedStatus(){
    let that = this
    let selectedArr = that.data.selectedBargeArr //选中驳船数组
    let getOptionalArr = that.data.bargeList //驳船数组列表
    selectedArr.map(item1=>{
      getOptionalArr.forEach(item2=>{
        if(item1.id === item2.id){
          item2.checked = true
        }
      })
    })
    that.setData({
      bargeList:getOptionalArr
    })
    // console.log("回显",that.data)
  },
  //选中驳船
  radioChange(e) {
    const checked = e.detail.value
    const changed = {}
    for (let i = 0; i < this.data.bargeList.length; i++) {
      if (checked && checked == this.data.bargeList[i].id) {
        changed['bargeList[' + i + '].checked'] = true
      } else {
        changed['bargeList[' + i + '].checked'] = false
      }
    }
    this.setData(changed)
  },
  //获取驳船列表
  getBargeListDatas(){
    let that = this 
    let searchValue = that.data.searchValue 
    let param = {
      searchValue,
    }
    app.$post(app.$url.mine.getBargeList,param).then(res=>{
      if(res.code == 200){
        res.data.map(item=>{
          item.checked = false //默认不选中
          // bargeLoadA和bargeLoadB 提交值和返回值都是kg,但是页面需要显示吨，需将kg转为吨
        if(item.bargeLoadA,item.bargeLoadB) {
              item.bargeLoadA = Number(item.bargeLoadA)*100/100000
              item.bargeLoadB = Number(item.bargeLoadB)*100/100000
        }
          if(that.data.bargeId && item.id == that.data.bargeId){
            item.checked = true
          }
          
        })
        that.setData({
          bargeList:res.data
        })

        that.getCheckedStatus()
      }else {
      app.$message(res.msg)
      }
    })
  },

  //搜索input
  searchHandle(e){
    let that = this
    let searchValue = e.detail.value 
    that.setData({
      searchValue,
    })
    setTimeout(()=>{
      that.getBargeListDatas()
    },1500)
    
  },

  // 点击确定
  handleConfirm() {
    let pages = getCurrentPages();
    let currPage = pages[pages.length - 1];   //当前页面
    let prevPage = pages[pages.length - 2];  //上一个页面
    
    let selectedBargeArr = this.data.bargeList.filter(item=>{
      return item.checked
    })
    if(selectedBargeArr && selectedBargeArr.length==0){
      app.$message("请选择驳船")
      return
    }
    // console.log("选中的数组",selectedBargeArr)
    //获取选中船舶的值
    let {id,bargeName,bargeId,mmsi,bargeLoadA,bargeLoadB,validSailDate,belongArea,bargeOwner,contactPhone,bargeOperator,dataList,loadingWeightMax,loadingWeightMin,cargoNotAllowed} = selectedBargeArr[0]
    // bargeLoadA和bargeLoadB 目前已经是处理过显示吨

    let coverBargeImgArr = []//船舶检验证书簿封面-临时存储图片数组
    let bargeMainItemImgArr = []//船舶主要项目内容-临时存储图片数组
    let seaworthinessCertificateImgArr = []//船舶适航证书-临时存储图片数组
    let automaticAisImgArr = []//船舶自动识别系统AIS证书-临时存储图片数组
    let powerOfAttorneyImgArr = [] //委托书-临时存储图片数组

    // 文件数组
    if(dataList && dataList.length>0){
      dataList.map(item=>{
        switch(item.dataType){
          case 11: coverBargeImgArr = [item]
          break;
          case 12: bargeMainItemImgArr = [item]
          break;
          case 13: seaworthinessCertificateImgArr = [item]
          break;
          case 14: automaticAisImgArr = [item]
          break;
          case 15: powerOfAttorneyImgArr = [item]
          break;
          default:break;
        }
      })
    }

    //选中驳船数组，给上个页面数据赋值
    prevPage.setData({
      selectedBargeArr,
      "bargeParam.id": id,// 驳船id主键
      "bargeParam.bargeName": bargeName, // 驳船名称
      "bargeParam.bargeId": bargeId, // 驳船识别号
      "bargeParam.mmsi": mmsi, // MMSI标识
      "bargeParam.bargeLoadA": bargeLoadA, // 船舶载货量（A级）
      "bargeParam.bargeLoadB": bargeLoadB, // 船舶载货量（B级）
      "bargeParam.validSailDate": validSailDate, //证书有效期
      "bargeParam.belongArea": belongArea, //船籍港
      "bargeParam.bargeOwner": bargeOwner,//驳船所有人
      "bargeParam.contactPhone": contactPhone,//联系电话
      "bargeParam.bargeOperator": bargeOperator,//驳船经营人
      "bargeParam.auditStatus": '', //租用状态
      "bargeParam.bargeType": '', //船支类型
      "bargeParam.bingingType": '', //租用方式
      "bargeParam.companyId": '', //租用公司id
      "bargeParam.companyName": '', //租用公司名称
      "bargeParam.loadingWeightMax": loadingWeightMax, //最大装载重量
      "bargeParam.loadingWeightMin": loadingWeightMin, //最小装载重量
      "bargeParam.cargoNotAllowed": cargoNotAllowed, //禁装货物
      
      isApplyBind: '', // 是否申请租用
      bargeTypeIndex: '', // 船支类型索引
      bingingTypeIndex: '', // 租用方式索引

      sealImg1: "",
      coverBargeImgArr,
      bargeMainItemImgArr,
      seaworthinessCertificateImgArr,
      automaticAisImgArr,
      powerOfAttorneyImgArr,
    })

   
    wx.navigateBack({
      delta: 1
    });
    app.$message("选择成功")
    
  },
  // 点击新增驳船
  handleAddBarge() {
    wx.navigateTo({
      url: '/pages/minepage/addbarge/addbarge',
      success: (result)=>{
        
      },
      fail: ()=>{},
      complete: ()=>{}
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    //获取选中的项目
    if(JSON.stringify(options.selectedBargeArr)!=""){
      this.setData({
        selectedBargeArr : JSON.parse(options.selectedBargeArr)
      })
    }
    this.setData({
      bargeId:options.bargeId
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息
    this.setData({
      userInfos,
    }) 
    this.getBargeListDatas()

    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
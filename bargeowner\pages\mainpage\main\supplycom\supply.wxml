<!--pages/mainpage/main/supplycom/supply.wxml-->
<view class="supply-com" hover-class="none" hover-stop-propagation="false">
  <view class="search-warp" hover-class="none" hover-stop-propagation="false">
    <view class="search-area" hover-class="none" hover-stop-propagation="false">
      <view class="search-ipt-wrap" hover-class="none" hover-stop-propagation="false">
        <input type="text" class="search-ipt" placeholder="货源号/货源名称"  value="{{form.search}}" bindinput="searchInput"/>
        <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-icon" wx:if="{{form.search}}" bindtap="deleteSearch"></mp-icon>
      </view>
    </view>
    <view class="search-area" hover-class="none" hover-stop-propagation="false">
      <picker class="search-data-box" mode="date" value="{{form.startTime}}" bindchange="handleBeginDateChange">
        <view class="{{form.startTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{form.startTime ? form.startTime :'开始时间'}}</view>
        <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-iconp" wx:if="{{form.startTime}}" catchtap="deleteStartTime"></mp-icon>
      </picker>
      <text class="padding-text">-</text>
      <picker class="search-data-box" mode="date" value="{{form.endTime}}" bindchange="handleEndDateChange">
        <view class="{{form.endTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{form.endTime ? form.endTime :'截止时间'}}</view>
        <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-iconp" wx:if="{{form.endTime}}" catchtap="deleteEndTime"></mp-icon>
      </picker>
      <button class="publish-btn search-btn" bindtap="handleSearch">搜索</button>
    </view>
  </view>
  <view class="data-list" hover-class="none" hover-stop-propagation="false">
    <view class="data-item" wx:key="index" wx:for="{{listData}}" hover-class="none" hover-stop-propagation="false">
      <view class="data-item-title" hover-class="none" hover-stop-propagation="false" data-id="{{item.id}}" data-item="{{item}}" bindtap="handleSupplyDetail">
        <view class="data-item-text" hover-class="none" hover-stop-propagation="false">{{item.cargoName}}</view>
        <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{20}}"></mp-icon>
      </view>
      <view class="message-wrap" hover-class="none" hover-stop-propagation="false">
        <view class="data-item-message" hover-class="none" hover-stop-propagation="false">
          <view class="message-wrap-title" hover-class="none" hover-stop-propagation="false">出发地：</view>
          <view class="message-wrap-value" hover-class="none" hover-stop-propagation="false">{{item.placeOfDeparture}}</view>
        </view>
        <view class="data-item-message" hover-class="none" hover-stop-propagation="false">
          <view class="message-wrap-title" hover-class="none" hover-stop-propagation="false">目的地：</view>
          <view class="message-wrap-value" hover-class="none" hover-stop-propagation="false">{{item.destination}}</view>
        </view>
      </view>
      <view class="message-wrap" hover-class="none" hover-stop-propagation="false">
        <view class="message-wrap-title" hover-class="none" hover-stop-propagation="false">开始时间：</view>
        <view class="message-wrap-value" hover-class="none" hover-stop-propagation="false">{{item.startTime}}</view>
      </view>
      <view class="message-wrap" hover-class="none" hover-stop-propagation="false">
        <view class="message-wrap-title" hover-class="none" hover-stop-propagation="false">结束时间：</view>
        <view class="message-wrap-value" hover-class="none" hover-stop-propagation="false">{{item.endTime}}</view>
      </view>
    </view>
  </view>
</view>

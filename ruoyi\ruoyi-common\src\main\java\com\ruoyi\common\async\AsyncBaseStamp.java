package com.ruoyi.common.async;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

/**
 * 盖章异步处理线程池
 *
 * <AUTHOR>
 * @date 2021年06月22日 11:52
 */
@Slf4j
public class AsyncBaseStamp {

    private static final int THREAD_SIZE = Runtime.getRuntime().availableProcessors();

    private static final int QUEUE_SIZE = 10000;

    private static ExecutorService stampAsync =
            new ThreadPoolExecutor(THREAD_SIZE,
                    THREAD_SIZE,
                    60L,
                    TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(QUEUE_SIZE),
                    r -> {
                        Thread t = new Thread(r);
                        t.setName("async_stamp");
                        return t;
                    },
                    (r, executor) -> log.error("异步发生错误：async stamp is error rejected, runnable: {}, executor: {}", r, executor));

    public static void submit(Runnable runnable) {
        stampAsync.submit(runnable);
    }
}

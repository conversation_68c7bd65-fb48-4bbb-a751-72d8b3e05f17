package com.ruoyi.common.enums;

import com.ruoyi.common.utils.StringUtils;

import java.util.Objects;

/**
 * 发送消息枚举
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/15 11:03
 */
public enum SendMessageEnum {

    BARGE_RECORD("_05C95a46vQb36hLLpxAplAEUiCMCWAiEf-gorMdLUE", "驳船备案、挂靠-审核结果通知通知"),

    LOGISTICS_STOWAGE("zo14mXjFoYPv7W_fmiCsvhIyCSLKJW3iRYMt-W1tbEE", "物流公司配载后发送驳船主订阅用户-订单生成通知"),

    PIER_CONFIRM("yVwAtns-gVVFFXYvCmlRmYGcddTtq-8ocU1bhWrgQQ0", "码头实装发送驳船主和预约人-订单完成通知"),

    BARGE_CONSIGN("4", "驳船托运单审核"),

    CHARGE_BACK("Chargeback", "退单/改单审核结构通知"),

    BARGE_MONTHLY_CHECK("vdXlggYZc2RhVLn891DF2qRXgUfmmNTilv9JqkcoHow", "驳船主月结审批-订单审批通知"),

    BARGE_CONSIGN_CONFIRM("confirmConsign", "驳船主确认托运单");

    private final String code;

    private final String codeName;

    SendMessageEnum(String code,String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public String getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }

    /**
     * 枚举值获取描述值
     * @param code
     * @return
     */
    public static String codeToCodeName(String code){
        if(Objects.nonNull(code)){
            for (SendMessageEnum status : SendMessageEnum.values()) {
                if (status.code.equals(code)){
                    return status.codeName;
                }
            }
        }
        return null;
    }

    /**
     * 描述值获取枚举值
     * @param codeName
     * @return
     */
    public static String codeNameToCode(String codeName){
        if(Objects.nonNull(codeName)){
            for (SendMessageEnum status : SendMessageEnum.values()) {
                if (status.codeName.equals(codeName)){
                    return status.code;
                }
            }
        }
        return null;
    }

    /**
     * 通过value取枚举
     * @param code
     * @return
     */
    public static SendMessageEnum getTypeByValue(String code){
        if (StringUtils.isNull(code)){
            return null;
        }
        String valueKey = code;
        for (SendMessageEnum enums : SendMessageEnum.values()) {
            if (enums.getCode() == valueKey) {
                return enums;
            }
        }
        return null;
    }

}

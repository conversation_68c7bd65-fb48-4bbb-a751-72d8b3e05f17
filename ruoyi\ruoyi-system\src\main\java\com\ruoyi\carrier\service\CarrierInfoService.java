package com.ruoyi.carrier.service;

import com.ruoyi.carrier.domain.CarrierUser;
import com.ruoyi.carrier.domain.bo.CarrierBO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;

import java.util.List;

public interface CarrierInfoService {

    /**
     * 注册
     * @param carrierBO
     * @return
     */
    AjaxResult webRegister(CarrierBO carrierBO);

    /**
     * 获取船公司普通用户列表
     * @return
     */
    List<CarrierUser> getCarrierUserList();

    /**
     * 获取船公司普通用户列表
     * @return
     */
    List<CarrierUser> getCarrierUsers(SysUser sysUser);
}

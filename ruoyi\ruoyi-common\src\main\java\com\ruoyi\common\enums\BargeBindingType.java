package com.ruoyi.common.enums;

/**
 * 绑定类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/6 15:09
 */
public enum BargeBindingType {

    /**
     * 1-自有、2-临时挂靠、3-长期挂靠，4-其他
     */
    OWN(1, "自有"),
    SHORT_TERM(2, "临时挂靠"),
    LONG_TERM(3, "长期挂靠"),
    OTHER(4, "其他");

    private final Integer code;

    private final String codeName;

    BargeBindingType(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

package com.ruoyi.common.domain;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
@TableName(value = "PB6_BARGE_CHECK_NODE")
@KeySequence("SEQ_PB6_BARGE_CHECK_NODE")
public class BargeCheckNode {

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 当前流程节点： 0提交审核，1物流公司直接审核，2码头审核， 3物流公司间接审核
     */
    @TableField("WXNODE")
    private Integer wxNode;

    /**
     * 提交人
     */
    @TableField("WXAPPLYMAN")
    private String wxApplyMan;

    /**
     * 提交时间
     */
    @TableField("WXAPPLYTIME")
    private String wxApplyTime;

    /**
     * 当前状态审核人
     */
    @TableField("WXAPPLYAUDITMAN")
    private String wxApplyAuditMan;

    /**
     * 当前状态审核时间
     */
    @TableField("WXAPPLYAUDITTIME")
    private String wxApplyAuditTime;

    /**
     * 审核状态： 0未审核，1审核通过，2审核不通过
     */
    @TableField("AUDITSTATES")
    private Integer auditStates;

    /**
     * 审核不通过原因
     */
    @TableField("FAILUREREASONS")
    private String failureReasons;

    /**
     * 关联表id： 审核节点类型为 0、1 时关联的是PB6_CARGOCONSIGNMENTDETAIL表ID
     */
    @TableField("LINKID")
    private Long linkId;

    /**
     * 审核节点类型： 0、退单， 1、改单
     */
    @TableField("WXNODEFLAG")
    private Integer wxNodeFlag;

}

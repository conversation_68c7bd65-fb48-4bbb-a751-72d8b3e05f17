package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.Pb3Coutform;

import java.util.List;

/**
 * <p>
 * 出库单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-13
 */
public interface Pb3CoutformService extends IService<Pb3Coutform> {

    List<Pb3Coutform> findByCoutformId(String coutformId);

    List<String> listCoutFormId(String coutformid);

    Pb3Coutform findOneByCoutformId(String coutformId);
}
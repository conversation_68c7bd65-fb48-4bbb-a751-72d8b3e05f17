package com.ruoyi.app.controller.databarge;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.databarge.domain.PubPort;
import com.ruoyi.databarge.domain.dto.PubPortSearchDTO;
import com.ruoyi.databarge.service.PubPortService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * ??? 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
@RestController
@RequestMapping("/barge/pubport")
public class PubPortController {

    @Autowired
    private PubPortService pubPortService;

    @PostMapping("/searchPage")
    public AjaxResult searchPageByNameAndCountry(@RequestBody PubPortSearchDTO pubPortSearchDTO){
        return AjaxResult.success(pubPortService.searchPageByNameAndCountry(pubPortSearchDTO));
    }

    @Log(title = "港口管理", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult updateWLInfo(@RequestBody PubPort pubPort){
        return AjaxResult.success(pubPortService.updateWLInfo(pubPort));
    }

    @PostMapping("/searchCountryByCName")
    public AjaxResult searchCountryByCName(@RequestBody String countrycname){
        return AjaxResult.success(pubPortService.searchCountryByCName(countrycname));
    }
    @PostMapping("/searchPortByName")
    public AjaxResult searchPortByName(@RequestBody String searchName){
        return AjaxResult.success(pubPortService.searchPortByName(searchName));
    }
}

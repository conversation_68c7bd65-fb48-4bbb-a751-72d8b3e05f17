<view class="mine-page" hover-class="none" hover-stop-propagation="false">
  <view class="navigation-warp" hover-class="none" hover-stop-propagation="false">
    <view class="navigation-title" hover-class="none" style="height:{{CustomBar-StatusBar}}px;top:{{StatusBar}}px;line-height:{{CustomBar-StatusBar}}px;" hover-stop-propagation="false">我的</view>
    <view class="user-image"  style="top:{{CustomBar+10}}px;" hover-class="none" hover-stop-propagation="false">
      <image class="user-head-image" src="{{imgUrl}}" mode="aspectFill" lazy-load="false"></image>
      <text class="user-nickname" selectable="false" space="false" decode="false">{{nickName}}</text>
    </view>
  </view>
  <view class="mine-operate" hover-class="none" hover-stop-propagation="false">
    <view class="mine-operate-item" hover-class="none" hover-stop-propagation="false" bindtap="handleMessagePage">
      <view class="operate-left" hover-class="none" hover-stop-propagation="false">
        <view class="iconfont iconxiaoxi_tongzhi mine-icon" hover-class="none" hover-stop-propagation="false"></view>
        <text class="operate-item-text" selectable="false" space="false" decode="false">信息通知</text>
        <view class="read-statu" hover-class="none" hover-stop-propagation="false"  wx:if="{{noticeNum != 0}}">
          <span class="raad-status-text">{{noticeNum}}</span>
        </view>
      </view>
      <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{20}}"></mp-icon>
    </view>

    <view class="mine-operate-item" hover-class="none" hover-stop-propagation="false" bindtap="handleRecordPage">
      <view class="operate-left" hover-class="none" hover-stop-propagation="false">
        <view class="iconfont iconbeian-tian mine-icon" hover-class="none" hover-stop-propagation="false"></view>
        <text class="operate-item-text" selectable="false" space="false" decode="false">备案</text>
        <text class="record-status" selectable="false" space="false" decode="false" wx:if="{{userInfos.userType == 14}}">{{recordResult}}</text>
      </view>
      <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{20}}"></mp-icon>
    </view>
    
    <view class="mine-operate-item" hover-class="none" hover-stop-propagation="false" bindtap="handleCrewpage" wx:if="{{userInfos.userType == 14}}">
      <view class="operate-left" hover-class="none" hover-stop-propagation="false">
        <view class="iconfont iconchuanyuan mine-icon" hover-class="none" hover-stop-propagation="false"></view>
        <text class="operate-item-text" selectable="false" space="false" decode="false">业务员列表</text>
      </view>
      <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{20}}"></mp-icon>
    </view>

    <!-- <view class="mine-operate-item" hover-class="none" hover-stop-propagation="false" bindtap="handleAgentdeliveryPage">
      <view class="operate-left" hover-class="none" hover-stop-propagation="false">
        <view class="iconfont iconchuanyuan mine-icon" hover-class="none" hover-stop-propagation="false"></view>
        <text class="operate-item-text" selectable="false" space="false" decode="false">代理发货</text>
      </view>
      <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{20}}"></mp-icon>
    </view> -->
  </view>

  <button  open-type="getUserInfo" bindgetuserinfo="bindGetUserInfo" type="primary" class="user-btn" wx:if="{{userAuthBtnShow}}">授权用户信息</button>
</view>
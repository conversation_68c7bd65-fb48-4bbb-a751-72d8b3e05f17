package com.ruoyi.barge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.barge.domain.BargeCapacityPublish;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.barge.domain.bo.BargeCapacityPublishBO;
import com.ruoyi.barge.domain.vo.BargeCapacityPublishVO;
import com.ruoyi.barge.domain.vo.CargoPublishVO;
import com.ruoyi.barge.mapper.BargeCapacityPublishMapper;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.BargeCapacityService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.UserType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.consignor.mapper.ConsignorCargoSourceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/11 10:58
 */
@Slf4j
@Service
public class BargeCapacityServiceImpl implements BargeCapacityService {

    @Autowired
    private BargeCapacityPublishMapper bargeCapacityPublishMapper;

    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    @Autowired
    private ConsignorCargoSourceMapper cargoSourceInfoMapper;

    /**
     * 获取货源信息列表
     * @param bargeCapacityPublishBO
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public List<CargoPublishVO> getCargoSourceList(BargeCapacityPublishBO bargeCapacityPublishBO) {
        return cargoSourceInfoMapper.getCargoSourceListOfBarge(bargeCapacityPublishBO);

    }

    /**
     * 获取运力信息列表
     * @param bargeCapacityPublishBO
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public List<BargeCapacityPublishVO> getCapacityList(BargeCapacityPublishBO bargeCapacityPublishBO) {
        return bargeCapacityPublishMapper.getCapacityList(bargeCapacityPublishBO);
    }

    /**
     * 添加运力
     * @param bargeCapacityPublishBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult addCapacity(BargeCapacityPublishBO bargeCapacityPublishBO) {

        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userId = loginUser.getUserId();

        if (!UserType.BARGEADMIN.getCode().equals(loginUser.getUserType()) && !UserType.CARRIERADMIN.getCode().equals(loginUser.getUserType())) {
            log.error("非驳船管理员或非船公司管理员不能发布运力信息");
            return AjaxResult.error("非驳船管理员或非船公司管理员不能发布运力信息");
        }

        // 查询驳船
        QueryWrapper<SysUserBarge> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        SysUserBarge barge = sysUserBargeMapper.selectOne(wrapper);

        BargeCapacityPublish capacity = new BargeCapacityPublish();
        capacity.setBargeId(StringUtils.isNull(barge)?null:barge.getBargeId());
        capacity.setCapacity(bargeCapacityPublishBO.getCapacity());
        capacity.setPhone(bargeCapacityPublishBO.getPhone());
        capacity.setStartTime(DateUtils.parseDate(bargeCapacityPublishBO.getBeginTime()));
        capacity.setEndTime(DateUtils.parseDate(bargeCapacityPublishBO.getEndTime()));
        capacity.setCreateUser(loginUser.getUserName());
        capacity.setCreateTime(new Date());
        capacity.setRemark(bargeCapacityPublishBO.getRemark());
        capacity.setBargeName(bargeCapacityPublishBO.getBargeName());
        capacity.setBargeIdentifier(bargeCapacityPublishBO.getBargeIdentifier());

        int result = bargeCapacityPublishMapper.insert(capacity);

        if (result <= 0) {
            log.info("添加运力失败");
            throw new CustomException("添加运力失败");
        }

        log.info("添加运力成功");

        return AjaxResult.success("添加运力成功");
    }

    /**
     * 修改运力
     * @param bargeCapacityPublishBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult updateCapacity(BargeCapacityPublishBO bargeCapacityPublishBO) {

        SysUser loginUser = SecurityUtils.getLoginUser().getUser();

        BargeCapacityPublish capacity = new BargeCapacityPublish();
        capacity.setId(bargeCapacityPublishBO.getId());
        capacity.setCapacity(bargeCapacityPublishBO.getCapacity());
        capacity.setPhone(bargeCapacityPublishBO.getPhone());
        capacity.setStartTime(DateUtils.parseDate(bargeCapacityPublishBO.getBeginTime()));
        capacity.setEndTime(DateUtils.parseDate(bargeCapacityPublishBO.getEndTime()));
        capacity.setUpdateTime(new Date());
        capacity.setUpdateUser(loginUser.getUserName());
        capacity.setRemark(bargeCapacityPublishBO.getRemark());
        capacity.setBargeName(bargeCapacityPublishBO.getBargeName());
        capacity.setBargeIdentifier(bargeCapacityPublishBO.getBargeIdentifier());

        int result = bargeCapacityPublishMapper.updateById(capacity);

        if (result <= 0) {
            log.info("修改运力失败");
            throw new CustomException("修改运力失败");
        }

        log.info("修改运力成功");

        return AjaxResult.success("修改运力成功");
    }

    /**
     * 删除运力
     * @param id
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult deleteCapacity(Long id) {

        int result = bargeCapacityPublishMapper.deleteById(id);

        if (result <= 0) {
            log.info("删除运力失败");
            throw new CustomException("删除运力失败");
        }

        log.info("删除运力成功");

        return AjaxResult.success("删除运力成功");
    }
}

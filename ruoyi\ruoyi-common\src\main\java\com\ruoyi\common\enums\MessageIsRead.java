package com.ruoyi.common.enums;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/6 11:06
 */
public enum MessageIsRead {

    /**
     * 0-未读，1-已读
     */
    UNREAD("0", "未读"),
    READ("1", "已读");

    private final String code;

    private final String codeName;

    MessageIsRead(String code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public String getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

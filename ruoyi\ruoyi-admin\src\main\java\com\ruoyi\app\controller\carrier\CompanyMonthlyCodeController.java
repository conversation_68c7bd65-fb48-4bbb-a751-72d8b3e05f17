package com.ruoyi.app.controller.carrier;

import com.ruoyi.carrier.domain.bo.CompanyMonthlyCodeBO;
import com.ruoyi.carrier.service.CompanyMonthlyCodeService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 月结码相关接口
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/12/24 11:33
 */
@RestController
@RequestMapping("/monthlyCode")
public class CompanyMonthlyCodeController extends BaseController {

    @Autowired
    private CompanyMonthlyCodeService companyMonthlyCodeService;

    /**
     * 获取月结码列表
     * @param companyMonthlyCodeBO
     * @return
     */
    @PostMapping("/getMonthlyCodeList")
    public AjaxResult getMonthlyCodeList(@RequestBody CompanyMonthlyCodeBO companyMonthlyCodeBO) {
        return companyMonthlyCodeService.getMonthlyCodeList(companyMonthlyCodeBO);
    }

    /**
     * 批量生成月结码
     * @param companyMonthlyCodeBO
     * @return
     */
    @PostMapping("/createMonthlyCode")
    public AjaxResult createMonthlyCode(@RequestBody CompanyMonthlyCodeBO companyMonthlyCodeBO) {
        if (StringUtils.isNull(companyMonthlyCodeBO.getCodeNumbers())) {
            logger.error("月结码个数不能为空");
            return AjaxResult.error("月结码个数不能为空");
        }
        return companyMonthlyCodeService.createMonthlyCode(companyMonthlyCodeBO);
    }

    /**
     * 改变月结码状态
     * @param companyMonthlyCodeBO
     * @return
     */
    @PostMapping("/updateMonthlyCode")
    public AjaxResult updateMonthlyCode(@RequestBody CompanyMonthlyCodeBO companyMonthlyCodeBO) {
        if (StringUtils.isNull(companyMonthlyCodeBO.getCompanyId())) {
            logger.error("月结公司id不能为空");
            return AjaxResult.error("月结公司id不能为空");
        }
        if (StringUtils.isNull(companyMonthlyCodeBO.getMonthlyCode())) {
            logger.error("月结码不能为空");
            return AjaxResult.error("月结码不能为空");
        }
        return companyMonthlyCodeService.updateMonthlyCode(companyMonthlyCodeBO);
    }
}

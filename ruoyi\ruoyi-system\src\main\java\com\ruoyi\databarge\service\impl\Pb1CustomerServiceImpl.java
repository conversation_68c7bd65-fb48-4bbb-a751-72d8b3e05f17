package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.Pb1Customer;
import com.ruoyi.databarge.domain.dto.Pb1CustomerSearchDTO;
import com.ruoyi.databarge.mapper.Pb1CustomerMapper;
import com.ruoyi.databarge.service.Pb1CustomerService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 客户基本资料 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-10
 */
@Service
public class Pb1CustomerServiceImpl extends ServiceImpl<Pb1CustomerMapper, Pb1Customer> implements Pb1CustomerService {

    @Override
    public IPage<Pb1Customer> searchForPage(Pb1CustomerSearchDTO pb1CustomerSearchDTO) {
        Long pageNum= Optional.ofNullable(pb1CustomerSearchDTO.getPageNum()).orElse(0L);
        Long pageSize= Optional.ofNullable(pb1CustomerSearchDTO.getPageSize()).orElse(-1L);
        return super.baseMapper.searchForPage(new Page<>(pageNum, pageSize), pb1CustomerSearchDTO);
    }

    @Override
    public List<Pb1Customer> searchPb1CustomerByName(String cfullname) {
        return super.baseMapper.searchPb1CustomerByName(cfullname);
    }

    @Override
    public boolean add(Pb1Customer pb1Customer) {
        List<Pb1Customer> pb1CustomerList = this.searchByCFullName(pb1Customer.getCfullname());
        if(pb1CustomerList.size() > 0){
            throw new CustomException("添加失败，该客户已经存在!");
        }
        if(pb1Customer.getSpecialuse().equals("1")){
            pb1Customer.setSpecialuse("");
        }
        Calendar date = Calendar.getInstance();
        String year = String.valueOf(date.get(Calendar.YEAR));
        String y = year.substring(year.length() - 2);
        String maxCustomerId = super.baseMapper.searchMaxCustomerId("BKH" + y);

        String currentCustomerId;
        if(StringUtils.isNotBlank(maxCustomerId)){
            int sn = Integer.parseInt(maxCustomerId.substring(maxCustomerId.length() - 5));
            sn += 1;
            currentCustomerId = "BKH" + y  + StringUtils.leftPad(String.valueOf(sn), 5, "0");
        } else {
            currentCustomerId = "BKH" + y + StringUtils.leftPad("1", 5, "0");
        }
        pb1Customer.setCustomerid(currentCustomerId);

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        pb1Customer.setCreatepeople(StringUtils.isNotBlank(sysUser.getUserName()) ? sysUser.getUserName() : "物流公司");
        pb1Customer.setCreatedata(String.valueOf(LocalDate.now()));
        pb1Customer.setCorporationid(0L);
        pb1Customer.setIsimport("N");
        pb1Customer.setFrequency(0L);
        pb1Customer.setIsregister("N");
        return super.save(pb1Customer);
    }

    @Override
    public boolean delete(List<Long> idList) {
        return super.removeByIds(idList);
    }

    @Override
    public boolean updateOne(Pb1Customer pb1Customer) {
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        pb1Customer.setModifypeople(StringUtils.isNotBlank(sysUser.getUserName()) ? sysUser.getUserName() : "物流公司");
        pb1Customer.setCreatedata(String.valueOf(LocalDate.now()));
        return super.updateById(pb1Customer);
    }

    @Override
    public boolean updateJudge(Pb1Customer pb1Customer) {
        List<Pb1Customer> pb1CustomerList = this.searchByCFullName(pb1Customer.getCfullname());

        int i = 0;
        for (Pb1Customer customer: pb1CustomerList){
            // 2020.11.11 洪工说只有一个 Y ，不会有多个 Y
            if(customer.getIsregister() != null && customer.getIsregister().equals("Y")){
                return customer.getId().equals(pb1Customer.getId());
            } else {
                i++;
            }
        }
        return i == pb1CustomerList.size();
    }

    @Override
    public List<Pb1Customer> searchByCFullName(String cFullName) {
        QueryWrapper<Pb1Customer> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb1Customer::getCfullname, cFullName);
        return super.list(queryWrapper);
    }

    @Override
    public Pb1Customer searchByCFullNameAndCType(String cFullName) {
        QueryWrapper<Pb1Customer> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb1Customer::getCfullname, cFullName)
                .eq(Pb1Customer::getCtype, "船公司");
        return super.getOne(queryWrapper);
    }
}

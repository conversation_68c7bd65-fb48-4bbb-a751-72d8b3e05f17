package com.ruoyi.barge.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.databarge.domain.Pb6Cargoconsignment;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/10 10:44
 */
public interface BargeConsignService {

    /**
     * 驳船主获取托运单列表
     * @param cargocmentdetailBO
     * @return
     */
    AjaxResult getConsignList(CargocmentdetailBO cargocmentdetailBO);

    /**
     * 驳船主托运单详情
     * @param cargocmentdetailBO
     * @return
     */
    AjaxResult getConsignDetail(CargocmentdetailBO cargocmentdetailBO);

    /**
     * 确认托运单
     * @param cargocmentdetailBO
     * @return
     */
    AjaxResult confirmConsign(CargocmentdetailBO cargocmentdetailBO);

    /**
     * 取消确认托运单
     * @param cargocmentdetailBO
     * @return
     */
    AjaxResult cancelConfirmConsign(CargocmentdetailBO cargocmentdetailBO);

    /**
     * 支付
     * @param cargocmentdetailBO
     * @return
     */
    AjaxResult defray(CargocmentdetailBO cargocmentdetailBO);

    /**
     * 预约
     * @param id 托运单明细主键id
     * @param wxAppOintmentTime 预约时间
     * @param wxOperateState 状态-预约、取消预约
     * @return
     */
    AjaxResult reservation(Long id, String wxAppOintmentTime, Integer wxOperateState);

    /**
     * 申请差额退款
     * @return
     */
    AjaxResult refund(CargocmentdetailBO cargocmentdetailBO);
}

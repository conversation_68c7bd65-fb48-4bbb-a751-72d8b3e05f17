package com.ruoyi.app.controller.support.print;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.itextpdf.forms.PdfAcroForm;
import com.itextpdf.forms.fields.PdfButtonFormField;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.tagging.StandardRoles;
import com.itextpdf.kernel.pdf.tagutils.AccessibilityProperties;
import com.itextpdf.kernel.pdf.tagutils.DefaultAccessibilityProperties;
import com.itextpdf.kernel.pdf.xobject.PdfFormXObject;
import com.itextpdf.kernel.pdf.xobject.PdfImageXObject;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.element.AbstractElement;
import com.itextpdf.layout.element.ILeafElement;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.layout.LayoutArea;
import com.itextpdf.layout.layout.LayoutContext;
import com.itextpdf.layout.layout.LayoutResult;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.VerticalAlignment;
import com.itextpdf.layout.renderer.AbstractRenderer;
import com.itextpdf.layout.renderer.DrawContext;
import com.itextpdf.layout.renderer.IRenderer;
import com.itextpdf.layout.tagging.IAccessibleElement;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.databarge.domain.InvoicePersonnel;
import com.ruoyi.databarge.domain.Pb3Coutform;
import com.ruoyi.databarge.domain.Pb6Bargework;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.domain.dto.XGPdfDTO;
import com.ruoyi.databarge.mapper.Pb6WaterwaycargoMapper;
import com.ruoyi.databarge.service.*;
import com.ruoyi.notice.bo.MiniappStatementBO;
import com.ruoyi.notice.bo.ShipAnchorageCheckBO;
import com.ruoyi.notice.bo.ShipAnchorageCheckItemBO;
import com.ruoyi.notice.bo.ShipAnchorageCheckSubQuestions;
import com.ruoyi.notice.mapper.ShipAnchorageCheckMapper;
import com.ruoyi.notice.service.impl.MiniappStatementServiceImpl;
import com.ruoyi.notice.service.impl.ShipAnchorageCheckServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/25 16:33
 * @Description:
 */
@Component
@Slf4j
public class PrintUtil {
    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;
    @Autowired
    private Pb3CoutformService pb3CoutformService;
    @Autowired
    private PubCompanyService pubCompanyService;
    @Autowired
    private PubPortService pubPortService;
    @Autowired
    private Pb6BargeinfoService pb6BargeinfoService;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Autowired
    private Pb6BargeworkService pb6BargeworkService;
    @Autowired
    private  InvoicePersonnelService invoicePersonnelService;
    @Autowired
    private Pb6WaterwaycargoMapper pb6WaterwaycargoMapper;
    @Autowired
    private MiniappStatementServiceImpl miniappStatementService;
    @Autowired
    private ShipAnchorageCheckServiceImpl shipAnchorageCheckService;
    @Autowired
    private ShipAnchorageCheckMapper shipAnchorageCheckMapper;

    public void printBCJJPZ(Long pb6BargeworkId, OutputStream outputStream) throws Exception{
        Pb6Bargework pb6Bargework=pb6BargeworkService.getById(pb6BargeworkId);
        if (pb6Bargework.getComid()==3 || pb6Bargework.getComid()==16){
            InputStream inputStream=new InputStream() {
                @Override
                public int read() throws IOException {
                    return 0;
                }
            };
            inputStream=new ClassPathResource("新港驳船装货交接凭证.pdf").getInputStream();// 类路径下的输入流
            PdfDocument pdfDocument=new PdfDocument(new PdfReader(inputStream),new PdfWriter(outputStream));
            PdfAcroForm pdfAcroForm = PdfAcroForm.getAcroForm(pdfDocument, true);
            pdfAcroForm.setGenerateAppearance(true);
            PdfFont font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H",true);

            try (Connection connection=sqlSessionFactory.openSession().getConnection();PreparedStatement preparedStatement= connection.prepareStatement(bCJJPZSql)){
                preparedStatement.setLong(1,pb6BargeworkId/*1041067L*/);
                ResultSet resultSet = preparedStatement.executeQuery();
                int count=0;// 因为这里是getOne()只获取一条数据，所以需要判断如果有多条数据的话要提示报错
                ByteArrayOutputStream baos=new ByteArrayOutputStream();
                LocalDateTime localDateTime=LocalDateTime.now();
                while (resultSet.next()){
                    if(count!=0){
                        throw new CustomException("后台数据错误!");
                    }
                    if (pb6Bargework.getComid()==1){
                        pdfAcroForm.getField("title").setValue(ObjectUtil.objectToString("广州港新沙港务有限公司驳船装货交接凭证"), font, 25f);
                    }else if(pb6Bargework.getComid()==16){
                        pdfAcroForm.getField("title").setValue(ObjectUtil.objectToString("广州港股份有限公司南沙粮食通用码头分公司驳船装货交接凭证"), font, 25f);
                    }else if (pb6Bargework.getComid()==4|| pb6Bargework.getComid()==3){
                        pdfAcroForm.getField("title").setValue(ObjectUtil.objectToString("广州港股份有限公司新港港务分公司驳船装货交接凭证"), font, 25f);
                    }
//                    pdfAcroForm.getField("title").setValue(ObjectUtil.objectToString("广州港股份有限公司新港港务分公司驳船装货交接凭证"), font, 25f);
                    pdfAcroForm.getField("waterwaycargoid").setValue(ObjectUtil.objectToString(resultSet.getString("waterwaycargoid")), font, 11f);
                    pdfAcroForm.getField("today").setValue(ObjectUtil.objectToString(localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))), font, 11f);
                    pdfAcroForm.getField("outorinformid").setValue(ObjectUtil.objectToString(resultSet.getString("outorinformid")), font, 11f);
                    pdfAcroForm.getField("uniqueCode").setValue(ObjectUtil.objectToString(resultSet.getString("uniqueCode")), font, 11f);
                    pdfAcroForm.getField("shipName").setValue(ObjectUtil.objectToString(resultSet.getString("shipName")), font, 11f);
                    pdfAcroForm.getField("shipmentstack").setValue(ObjectUtil.objectToString(resultSet.getString("shipmentstack")), font, 11f);
                    pdfAcroForm.getField("customer").setValue(ObjectUtil.objectToString(resultSet.getString("customer")), font, 11f);
                    pdfAcroForm.getField("cargename").setValue(ObjectUtil.objectToString(resultSet.getString("cargename")), font, 11f);
                    // consignee在原始sql里面没有，需要在select再选择pb6_waterwaycargo.consignee
                    pdfAcroForm.getField("consignee").setValue(ObjectUtil.objectToString(resultSet.getString("consignee")), font, 11f);
                    pdfAcroForm.getField("bargename").setValue(ObjectUtil.objectToString(resultSet.getString("bargename")), font, 11f);
                    pdfAcroForm.getField("rationweight").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("rationweight"),BigDecimal.valueOf(1000),3), font, 11f);
//                    pdfAcroForm.getField("workWeight").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("weightValue"),BigDecimal.valueOf(1000),3), font, 11f);
//                    pdfAcroForm.getField("workPiece").setValue(resultSet.getString("workPiece"), font, 11f);
//                    pdfAcroForm.getField("workWeight").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("weightValue"),BigDecimal.valueOf(1000),3), font, 11f);
                    pdfAcroForm.getField("endport").setValue(ObjectUtil.objectToString(resultSet.getString("endport")), font, 11f);
                    pdfAcroForm.getField("registertime").setValue(ObjectUtil.objectToString(resultSet.getString("registertime")), font, 11f);
                    pdfAcroForm.getField("endworktime").setValue(ObjectUtil.objectToString(resultSet.getString("endworktime")), font, 11f);
                    pdfAcroForm.getField("registerprincipal").setValue(ObjectUtil.objectToString(resultSet.getString("registerprincipal")), font, 11f);
                    XGPdfDTO xgPdfDTO=searchXGPdfDTO(pb6Bargework.getWaterwaycargoid());
                    pdfAcroForm.getField("lhszjs").setValue(ObjectUtil.objectToString( xgPdfDTO.getLhszjs()), font, 11f);
                    pdfAcroForm.getField("ckszjs").setValue(ObjectUtil.objectToString( xgPdfDTO.getCkszjs()), font, 11f);
                    pdfAcroForm.getField("lhszzl").setValue(ObjectUtil.objectToString( xgPdfDTO.getLhszzl()), font, 11f);
                    pdfAcroForm.getField("ckszzl").setValue(ObjectUtil.objectToString( xgPdfDTO.getCkszzl()), font, 11f);
                    pdfAcroForm.getField("totalweight").setValue(ObjectUtil.objectToString(xgPdfDTO.getTotalWeight()), font, 11f);
                    pdfAcroForm.getField("totalpiece").setValue(ObjectUtil.objectToString(xgPdfDTO.getTotalAmt()), font, 11f);
                    ++count;
                }

                pdfDocument.close();
            }

            pdfDocument.close();
        }else {
            InputStream inputStream=new InputStream() {
                @Override
                public int read() throws IOException {
                    return 0;
                }
            };
            inputStream=new ClassPathResource("驳船装货交接凭证.pdf").getInputStream();// 类路径下的输入流
            PdfDocument pdfDocument=new PdfDocument(new PdfReader(inputStream),new PdfWriter(outputStream));
            PdfAcroForm pdfAcroForm = PdfAcroForm.getAcroForm(pdfDocument, true);
            pdfAcroForm.setGenerateAppearance(true);
            PdfFont font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H",true);

            try (Connection connection=sqlSessionFactory.openSession().getConnection();PreparedStatement preparedStatement= connection.prepareStatement(bCJJPZSql)){
                preparedStatement.setLong(1,pb6BargeworkId/*1041067L*/);
                ResultSet resultSet = preparedStatement.executeQuery();
                int count=0;// 因为这里是getOne()只获取一条数据，所以需要判断如果有多条数据的话要提示报错
                ByteArrayOutputStream baos=new ByteArrayOutputStream();
                LocalDateTime localDateTime=LocalDateTime.now();
                while (resultSet.next()){
                    if(count!=0){
                        throw new CustomException("后台数据错误!");
                    }
                    if (pb6Bargework.getComid()==1){
                        pdfAcroForm.getField("title").setValue(ObjectUtil.objectToString("广州港新沙港务有限公司驳船装货交接凭证"), font, 25f);
                    }else if(pb6Bargework.getComid()==16){
                        pdfAcroForm.getField("title").setValue(ObjectUtil.objectToString("广州港股份有限公司南沙粮食通用码头分公司驳船装货交接凭证"), font, 25f);
                    }else if (pb6Bargework.getComid()==4 || pb6Bargework.getComid()==3){
                        pdfAcroForm.getField("title").setValue(ObjectUtil.objectToString("广州港股份有限公司新港港务分公司驳船装货交接凭证"), font, 25f);
                    }
                    pdfAcroForm.getField("waterwaycargoid").setValue(ObjectUtil.objectToString(resultSet.getString("waterwaycargoid")), font, 11f);
                    pdfAcroForm.getField("today").setValue(ObjectUtil.objectToString(localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))), font, 11f);
                    pdfAcroForm.getField("outorinformid").setValue(ObjectUtil.objectToString(resultSet.getString("outorinformid")), font, 11f);
                    pdfAcroForm.getField("uniqueCode").setValue(ObjectUtil.objectToString(resultSet.getString("uniqueCode")), font, 11f);
                    pdfAcroForm.getField("shipName").setValue(ObjectUtil.objectToString(resultSet.getString("shipName")), font, 11f);
                    pdfAcroForm.getField("shipmentstack").setValue(ObjectUtil.objectToString(resultSet.getString("shipmentstack")), font, 11f);
                    pdfAcroForm.getField("customer").setValue(ObjectUtil.objectToString(resultSet.getString("customer")), font, 11f);
                    pdfAcroForm.getField("cargename").setValue(ObjectUtil.objectToString(resultSet.getString("cargename")), font, 11f);
                    // consignee在原始sql里面没有，需要在select再选择pb6_waterwaycargo.consignee
                    pdfAcroForm.getField("consignee").setValue(ObjectUtil.objectToString(resultSet.getString("consignee")), font, 11f);
                    pdfAcroForm.getField("bargename").setValue(ObjectUtil.objectToString(resultSet.getString("bargename")), font, 11f);
                    pdfAcroForm.getField("rationweight").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("rationweight"),BigDecimal.valueOf(1000),3), font, 11f);
                    pdfAcroForm.getField("workWeight").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("weightValue"),BigDecimal.valueOf(1000),3), font, 11f);
                    pdfAcroForm.getField("workPiece").setValue(resultSet.getString("workPiece"), font, 11f);
                    pdfAcroForm.getField("workWeight").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("weightValue"),BigDecimal.valueOf(1000),3), font, 11f);
                    pdfAcroForm.getField("endport").setValue(ObjectUtil.objectToString(resultSet.getString("endport")), font, 11f);
                    pdfAcroForm.getField("registertime").setValue(ObjectUtil.objectToString(resultSet.getString("registertime")), font, 11f);
                    pdfAcroForm.getField("endworktime").setValue(ObjectUtil.objectToString(resultSet.getString("endworktime")), font, 11f);
                    pdfAcroForm.getField("registerprincipal").setValue(ObjectUtil.objectToString(resultSet.getString("registerprincipal")), font, 11f);

                    ++count;
                }

                pdfDocument.close();
            }

            pdfDocument.close();
        }

    }

    public void printHWJJQD(String waterwaycargono, OutputStream outputStream) throws Exception{
        Pb6Waterwaycargo pb6Waterwaycargo=pb6WaterwaycargoService.searchWaterwaycargoByWaterwaycargoid(waterwaycargono);
        if (pb6Waterwaycargo.getComid()==3 || pb6Waterwaycargo.getComid()==16){
            InputStream inputStream=new ClassPathResource("新港货物交接清单.pdf").getInputStream();// 类路径下的输入流
            PdfDocument pdfDocument=new PdfDocument(new PdfReader(inputStream),new PdfWriter(outputStream));
            PdfAcroForm pdfAcroForm = PdfAcroForm.getAcroForm(pdfDocument, true);
            pdfAcroForm.setGenerateAppearance(true);
            PdfFont font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H",true);


            try(Connection connection=sqlSessionFactory.openSession().getConnection(); PreparedStatement preparedStatement= connection.prepareStatement(hWJJQDSql)) {
                preparedStatement.setString(1,waterwaycargono/*"BBZ042010141004"*/);
                ResultSet resultSet = preparedStatement.executeQuery();
                int count=0;// 因为这里是getOne()只获取一条数据，所以需要判断如果有多条数据的话要提示报错
                ByteArrayOutputStream baos=new ByteArrayOutputStream();
                LocalDateTime localDateTime=LocalDateTime.now();
                while (resultSet.next()){
                    assert count==0:"后台数据错误!";

                    pdfAcroForm.getField("today").setValue(localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), font, 11f);
                    pdfAcroForm.getField("bargename").setValue(ObjectUtil.objectToString(resultSet.getString("bargename")), font, 11f);
                    pdfAcroForm.getField("shipnumber").setValue(ObjectUtil.objectToString(resultSet.getString("shipnumber")), font, 11f);
                    pdfAcroForm.getField("beginport").setValue(ObjectUtil.objectToString(resultSet.getString("beginport")), font, 11f);
                    pdfAcroForm.getField("endport").setValue(ObjectUtil.objectToString(resultSet.getString("endport")), font, 11f);
                    pdfAcroForm.getField("waterwaycargoid").setValue(ObjectUtil.objectToString(resultSet.getString("waterwaycargoid")), font, 11f);
                    pdfAcroForm.getField("consigner").setValue(ObjectUtil.objectToString(resultSet.getString("consigner")), font, 11f);
                    pdfAcroForm.getField("consignee").setValue(ObjectUtil.objectToString(resultSet.getString("consignee")), font, 11f);
                    pdfAcroForm.getField("outorinformid").setValue(ObjectUtil.objectToString(resultSet.getString("outorinformid")), font, 11f);
                    pdfAcroForm.getField("loadometerid").setValue(ObjectUtil.objectToString(resultSet.getString("loadometerid")), font, 11f);
                    pdfAcroForm.getField("remark").setValue(ObjectUtil.objectToString(resultSet.getString("consignmentflag")), font, 11f);
                    pdfAcroForm.getField("uniqecode").setValue(ObjectUtil.objectToString(resultSet.getString("uniqecode")), font, 11f);
                    pdfAcroForm.getField("rationweight").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("rationweight"),BigDecimal.valueOf(1000),3), font, 11f);
                    pdfAcroForm.getField("cargename").setValue(ObjectUtil.objectToString(resultSet.getString("cargename")), font, 11f);
                    pdfAcroForm.getField("packagetype").setValue(ObjectUtil.objectToString(resultSet.getString("packagetype")), font, 11f);
                    pdfAcroForm.getField("rationpiece").setValue(ObjectUtil.stringToBigDecimalString(resultSet.getString("rationpiece"),0), font, 11f);
//                    pdfAcroForm.getField("workWeight").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("weightValue"),BigDecimal.valueOf(1000),3), font, 11f);
//                    pdfAcroForm.getField("workPiece").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("workPiece"),BigDecimal.valueOf(1000),3), font, 11f);
                    pdfAcroForm.getField("shipname").setValue(ObjectUtil.objectToString(resultSet.getString("shipname")), font, 11f);
//                    pdfAcroForm.getField("workPiece").setValue(resultSet.getString("workPiece"), font, 11f);

                    XGPdfDTO xgPdfDTO=searchXGPdfDTO(waterwaycargono);
                    pdfAcroForm.getField("lhszjs").setValue(ObjectUtil.objectToString( xgPdfDTO.getLhszjs()), font, 11f);
                    pdfAcroForm.getField("ckszjs").setValue(ObjectUtil.objectToString( xgPdfDTO.getCkszjs()), font, 11f);
                    pdfAcroForm.getField("lhszzl").setValue(ObjectUtil.objectToString( xgPdfDTO.getLhszzl()), font, 11f);
                    pdfAcroForm.getField("ckszzl").setValue(ObjectUtil.objectToString( xgPdfDTO.getCkszzl()), font, 11f);
                    pdfAcroForm.getField("totalweight").setValue(ObjectUtil.objectToString(xgPdfDTO.getTotalWeight()), font, 11f);
                    pdfAcroForm.getField("totalpiece").setValue(ObjectUtil.objectToString(xgPdfDTO.getTotalAmt()), font, 11f);
                    ++count;
                }
            }

            pdfDocument.close();

        }else{
            InputStream inputStream=new ClassPathResource("货物交接清单.pdf").getInputStream();// 类路径下的输入流
            PdfDocument pdfDocument=new PdfDocument(new PdfReader(inputStream),new PdfWriter(outputStream));
            PdfAcroForm pdfAcroForm = PdfAcroForm.getAcroForm(pdfDocument, true);
            pdfAcroForm.setGenerateAppearance(true);
            PdfFont font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H",true);

            try(Connection connection=sqlSessionFactory.openSession().getConnection(); PreparedStatement preparedStatement= connection.prepareStatement(hWJJQDSql)) {
                preparedStatement.setString(1,waterwaycargono/*"BBZ042010141004"*/);
                ResultSet resultSet = preparedStatement.executeQuery();
                int count=0;// 因为这里是getOne()只获取一条数据，所以需要判断如果有多条数据的话要提示报错
                ByteArrayOutputStream baos=new ByteArrayOutputStream();
                LocalDateTime localDateTime=LocalDateTime.now();
                while (resultSet.next()){
                    assert count==0:"后台数据错误!";

                    pdfAcroForm.getField("today").setValue(localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), font, 11f);
                    pdfAcroForm.getField("bargename").setValue(ObjectUtil.objectToString(resultSet.getString("bargename")), font, 11f);
                    pdfAcroForm.getField("shipnumber").setValue(ObjectUtil.objectToString(resultSet.getString("shipnumber")), font, 11f);
                    pdfAcroForm.getField("beginport").setValue(ObjectUtil.objectToString(resultSet.getString("beginport")), font, 11f);
                    pdfAcroForm.getField("endport").setValue(ObjectUtil.objectToString(resultSet.getString("endport")), font, 11f);
                    pdfAcroForm.getField("waterwaycargoid").setValue(ObjectUtil.objectToString(resultSet.getString("waterwaycargoid")), font, 11f);
                    pdfAcroForm.getField("consigner").setValue(ObjectUtil.objectToString(resultSet.getString("consigner")), font, 11f);
                    pdfAcroForm.getField("consignee").setValue(ObjectUtil.objectToString(resultSet.getString("consignee")), font, 11f);
                    pdfAcroForm.getField("outorinformid").setValue(ObjectUtil.objectToString(resultSet.getString("outorinformid")), font, 11f);
                    pdfAcroForm.getField("loadometerid").setValue(ObjectUtil.objectToString(resultSet.getString("loadometerid")), font, 11f);
                    pdfAcroForm.getField("uniqecode").setValue(ObjectUtil.objectToString(resultSet.getString("uniqecode")), font, 11f);
                    pdfAcroForm.getField("rationweight").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("rationweight"),BigDecimal.valueOf(1000),3), font, 11f);
                    pdfAcroForm.getField("cargename").setValue(ObjectUtil.objectToString(resultSet.getString("cargename")), font, 11f);
                    pdfAcroForm.getField("packagetype").setValue(ObjectUtil.objectToString(resultSet.getString("packagetype")), font, 11f);
                    pdfAcroForm.getField("rationpiece").setValue(ObjectUtil.stringToBigDecimalString(resultSet.getString("rationpiece"),0), font, 11f);
                    pdfAcroForm.getField("workWeight").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("weightValue"),BigDecimal.valueOf(1000),3), font, 11f);
//                    pdfAcroForm.getField("workPiece").setValue(ObjectUtil.stringToBigDecimalDivideString(resultSet.getString("workPiece"),BigDecimal.valueOf(1000),3), font, 11f);
                    pdfAcroForm.getField("shipname").setValue(ObjectUtil.objectToString(resultSet.getString("shipname")), font, 11f);
                    pdfAcroForm.getField("workPiece").setValue(resultSet.getString("workPiece"), font, 11f);
                    ++count;
                }
            }

            pdfDocument.close();
        }

    }

    // 生成船舶告知书
    public void printCBGZS(String waterwaycargoid,OutputStream outputStream) throws Exception {
        log.info("船舶告知书触发生成："+waterwaycargoid);
        InputStream inputStream=new ClassPathResource("广州港船务有限公司来港小型船舶告知书.pdf").getInputStream();// 类路径下的输入流
        PdfDocument pdfDocument=new PdfDocument(new PdfReader(inputStream),new PdfWriter(outputStream));
        PdfAcroForm pdfAcroForm = PdfAcroForm.getAcroForm(pdfDocument, true);
        pdfAcroForm.setGenerateAppearance(true);
        PdfFont font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H",true);

        Pb6Waterwaycargo pb6Waterwaycargo=pb6WaterwaycargoService.getOne(Wrappers.<Pb6Waterwaycargo>query().eq("id",waterwaycargoid/*""*/));

        // 告知书内容
        MiniappStatementBO statementByWaterWayId = miniappStatementService.getStatementByWaterWayId(Long.valueOf(waterwaycargoid));
        // 时间 为createTime
        Date createTime = statementByWaterWayId.getCreateTime();
        // 时间格式化为 yyyy-MM-dd
        String dateTime = new java.text.SimpleDateFormat("yyyy年MM月dd日").format(createTime);

        //pdfAcroForm.getField("type").setFont(font).setValue(ObjectUtil.objectToString(pb8Weighrecord.getType())).setFontSizeAutoScale();
        pdfAcroForm.getField("shipName").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getShipName()), font, 11f);
        pdfAcroForm.getField("dateTime1").setValue(ObjectUtil.objectToString(dateTime), font, 11f);
        pdfAcroForm.getField("dateTime2").setValue(ObjectUtil.objectToString(dateTime), font, 11f);
        pdfAcroForm.getField("bargeCaptainSign").setValue(ObjectUtil.objectToString(statementByWaterWayId.getBargeCaptainSign()), font, 11f);

        pdfDocument.close();

    }

    // 生成安全提醒告知书
    public void printAQGZS(String waterwaycargoid,OutputStream outputStream) throws Exception {
        log.info("安全提醒告知书触发生成："+waterwaycargoid);
        InputStream inputStream=new ClassPathResource("安全提醒告示书.pdf").getInputStream();// 类路径下的输入流
        PdfDocument pdfDocument=new PdfDocument(new PdfReader(inputStream),new PdfWriter(outputStream));
        PdfAcroForm pdfAcroForm = PdfAcroForm.getAcroForm(pdfDocument, true);
        pdfAcroForm.setGenerateAppearance(true);
        PdfFont font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H",true);

        Pb6Waterwaycargo pb6Waterwaycargo=pb6WaterwaycargoService.getOne(Wrappers.<Pb6Waterwaycargo>query().eq("id",waterwaycargoid/*""*/));

        // 告知书内容
        MiniappStatementBO statementByWaterWayId = miniappStatementService.getStatementByWaterWayId(Long.valueOf(waterwaycargoid));
        // 时间 为createTime
        Date createTime = statementByWaterWayId.getCreateTime();
        // 时间格式化为 yyyy-MM-dd
        String dateTime = new java.text.SimpleDateFormat("yyyy-MM-dd").format(createTime);
        // 获得当前时间的年月日，用year，month，day接收
        String year = dateTime.substring(0, 4);
        String month = dateTime.substring(5, 7);
        String day = dateTime.substring(8, 10);

        //pdfAcroForm.getField("type").setFont(font).setValue(ObjectUtil.objectToString(pb8Weighrecord.getType())).setFontSizeAutoScale();
        pdfAcroForm.getField("bargeName").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getBargeName()), font, 11f);
        // 海事局
        pdfAcroForm.getField("maritimeBureau").setValue(ObjectUtil.objectToString(statementByWaterWayId.getMaritimeBureau()), font, 11f);
        // 海事处
        pdfAcroForm.getField("maritimeOffice").setValue(ObjectUtil.objectToString(statementByWaterWayId.getMaritimeOffice()), font, 11f);
        // 船舶签收栏
        pdfAcroForm.getField("shipSign").setValue(ObjectUtil.objectToString(statementByWaterWayId.getShipSign()), font, 11f);
        // 年
        pdfAcroForm.getField("year").setValue(ObjectUtil.objectToString(year), font, 11f);
        // 月
        pdfAcroForm.getField("month").setValue(ObjectUtil.objectToString(month), font, 11f);
        // 日
        pdfAcroForm.getField("day").setValue(ObjectUtil.objectToString(day), font, 11f);

        pdfDocument.close();
    }

    // 生成船长声明
    public void printCZSM(String waterwaycargoid,OutputStream outputStream) throws Exception {
        log.info("船长声明触发生成："+waterwaycargoid);
        InputStream inputStream=new ClassPathResource("船长声明.pdf").getInputStream();// 类路径下的输入流
        PdfDocument pdfDocument=new PdfDocument(new PdfReader(inputStream),new PdfWriter(outputStream));
        PdfAcroForm pdfAcroForm = PdfAcroForm.getAcroForm(pdfDocument, true);
        pdfAcroForm.setGenerateAppearance(true);
        PdfFont font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H",true);

        Pb6Waterwaycargo pb6Waterwaycargo=pb6WaterwaycargoService.getOne(Wrappers.<Pb6Waterwaycargo>query().eq("id",waterwaycargoid/*""*/));

        // 告知书内容
        MiniappStatementBO statementByWaterWayId = miniappStatementService.getStatementByWaterWayId(Long.valueOf(waterwaycargoid));
        // 时间 为createTime
        Date createTime = statementByWaterWayId.getCreateTime();
        // 时间格式化为 yyyy-MM-dd
        String dateTime = new java.text.SimpleDateFormat("yyyy-MM-dd").format(createTime);
        // 获得当前时间的年月日，用year，month，day接收
        String year = dateTime.substring(0, 4);
        String month = dateTime.substring(5, 7);
        String day = dateTime.substring(8, 10);

        // 预计抵达时间为createTime的后一天，格式化为 yyyy-MM-dd
        Date tomorrow = new Date(createTime.getTime() + 1000 * 60 * 60 * 24);
        String tomorrowTime = new java.text.SimpleDateFormat("yyyy-MM-dd").format(tomorrow);
        // 获得明天的年月日，用tomorrowYear，tomorrowMonth，tomorrowDay接收
        String tomorrowYear = tomorrowTime.substring(0, 4);
        String tomorrowMonth = tomorrowTime.substring(5, 7);
        String tomorrowDay = tomorrowTime.substring(8, 10);

        //pdfAcroForm.getField("type").setFont(font).setValue(ObjectUtil.objectToString(pb8Weighrecord.getType())).setFontSizeAutoScale();
        pdfAcroForm.getField("bargeName").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getBargeName()), font, 11f);
        // 泊位
        pdfAcroForm.getField("berth").setValue(ObjectUtil.objectToString(statementByWaterWayId.getBerth()), font, 11f);
        // 海事处
        pdfAcroForm.getField("maritimeOffice").setValue(ObjectUtil.objectToString(statementByWaterWayId.getMaritimeOffice()), font, 11f);
        // 货名
        pdfAcroForm.getField("cargoName").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getCargename()), font, 11f);
        // 货吨
        pdfAcroForm.getField("weight").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getRationweight()), font, 11f);
        // 到达港
        pdfAcroForm.getField("endPort").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getEndPort()), font, 11f);
        // 船长签名
        pdfAcroForm.getField("captainSign").setValue(ObjectUtil.objectToString(statementByWaterWayId.getCaptainSign()), font, 11f);
        // 年
        pdfAcroForm.getField("year1").setValue(ObjectUtil.objectToString(year), font, 11f);
        // 月
        pdfAcroForm.getField("month1").setValue(ObjectUtil.objectToString(month), font, 11f);
        // 日
        pdfAcroForm.getField("day1").setValue(ObjectUtil.objectToString(day), font, 11f);
        // 明天年
        pdfAcroForm.getField("year2").setValue(ObjectUtil.objectToString(tomorrowYear), font, 11f);
        // 明天月
        pdfAcroForm.getField("month2").setValue(ObjectUtil.objectToString(tomorrowMonth), font, 11f);
        // 明天日
        pdfAcroForm.getField("day2").setValue(ObjectUtil.objectToString(tomorrowDay), font, 11f);
        // 年
        pdfAcroForm.getField("year3").setValue(ObjectUtil.objectToString(year), font, 11f);
        // 月
        pdfAcroForm.getField("month3").setValue(ObjectUtil.objectToString(month), font, 11f);
        // 日
        pdfAcroForm.getField("day3").setValue(ObjectUtil.objectToString(day), font, 11f);

        pdfDocument.close();
    }

    // 生成安全项目检查表
    public void printACJC(String waterwaycargoid,OutputStream outputStream) throws Exception {
        log.info("安全检查表触发生成："+waterwaycargoid);
        InputStream inputStream=new ClassPathResource("安全检查项目表.pdf").getInputStream();// 类路径下的输入流
        PdfDocument pdfDocument=new PdfDocument(new PdfReader(inputStream),new PdfWriter(outputStream));
        PdfAcroForm pdfAcroForm = PdfAcroForm.getAcroForm(pdfDocument, true);
        pdfAcroForm.setGenerateAppearance(true);
        PdfFont font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H",true);

        Pb6Waterwaycargo pb6Waterwaycargo=pb6WaterwaycargoService.getOne(Wrappers.<Pb6Waterwaycargo>query().eq("id",waterwaycargoid/*""*/));

        // 检查表内容
        ShipAnchorageCheckBO shipAnchorageCheckBO = shipAnchorageCheckService.selectShipAnchorageCheckListByWaterWayId(waterwaycargoid);
        // 检查表项目
        List<ShipAnchorageCheckItemBO> shipAnchorageCheckItemBOList = shipAnchorageCheckBO.getItems();
        // 子问题答案
        ShipAnchorageCheckSubQuestions shipAnchorageCheckSubQuestions = shipAnchorageCheckMapper.selectSubQuestionsByCheckId(shipAnchorageCheckBO.getCheckId());

        // 时间为checkDate，其中分为日期和时间，日期格式化为 yyyy-MM-dd 时间格式化为 HH:mm
        Date checkDate = shipAnchorageCheckBO.getCheckDate();
        String checkDateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm").format(checkDate);
        // 取得日期和时间
        String checkDateStr = checkDateFormat.substring(0, 10);
        String checkTimeStr = checkDateFormat.substring(11, 16);

        // 船名
        pdfAcroForm.getField("bargeName").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getBargeName()), font, 11f);
        // 日期
        pdfAcroForm.getField("checkDate").setValue(ObjectUtil.objectToString(checkDateStr), font, 11f);
        // 锚地
        pdfAcroForm.getField("anchorageName").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getAnchorageName()), font, 11f);
        // 锚位
        pdfAcroForm.getField("anchorPosition").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getAnchorPosition()), font, 11f);
        // 锚位水深
        pdfAcroForm.getField("waterDepth").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getWaterDepth()), font, 11f);
        // 到港吃水
        pdfAcroForm.getField("draftOnArrival").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getDraftOnArrival()), font, 11f);
        // 水上高度
        pdfAcroForm.getField("freeboardOnArrival").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getFreeboardOnArrival()), font, 11f);
        // 计算出港吃水
        pdfAcroForm.getField("draftOnDeparture").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getDraftOnDeparture()), font, 11f);
        // 水上高度
        pdfAcroForm.getField("freeboardOnDeparture").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getFreeboardOnDeparture()), font, 11f);
        // 时间
        pdfAcroForm.getField("checkTime").setValue(ObjectUtil.objectToString(checkTimeStr), font, 11f);
        // 底部日期
        pdfAcroForm.getField("checkDate2").setValue(ObjectUtil.objectToString(checkDateStr), font, 11f);
        // 底部船名
        pdfAcroForm.getField("bargeName2").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getBargeName()), font, 11f);
        // 港口经营人
        pdfAcroForm.getField("portOperator").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getPortOperator()), font, 11f);
        // 船舶姓名及职务
        pdfAcroForm.getField("operatorNamePosition").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getOperatorNamePosition()), font, 11f);
        // 港口姓名及职务
        pdfAcroForm.getField("portOperatorNamePosition").setValue(ObjectUtil.objectToString(shipAnchorageCheckBO.getPortOperatorNamePosition()), font, 11f);
        // 通信方式
        pdfAcroForm.getField("communicationMethod").setValue(ObjectUtil.objectToString(shipAnchorageCheckSubQuestions.getCommunicationMethod()), font, 11f);
        // 语音
        pdfAcroForm.getField("language").setValue(ObjectUtil.objectToString(shipAnchorageCheckSubQuestions.getLanguage()), font, 11f);
        // 无线电话号码
        pdfAcroForm.getField("radioChannel").setValue(ObjectUtil.objectToString(shipAnchorageCheckSubQuestions.getRadioChannel()), font, 11f);
        // 船舶联络人员
        pdfAcroForm.getField("shipContact").setValue(ObjectUtil.objectToString(shipAnchorageCheckSubQuestions.getShipContact()), font, 11f);
        // 浮吊联络人员
        pdfAcroForm.getField("floatingCraneContact").setValue(ObjectUtil.objectToString(shipAnchorageCheckSubQuestions.getFloatingCraneContact()), font, 11f);
        // 位置
        pdfAcroForm.getField("position").setValue(ObjectUtil.objectToString(shipAnchorageCheckSubQuestions.getPosition()), font, 11f);
        // 浮吊1
        pdfAcroForm.getField("floatingCrane1").setValue(ObjectUtil.objectToString(shipAnchorageCheckSubQuestions.getFloatingCrane1()), font, 11f);
        // 浮吊2
        pdfAcroForm.getField("floatingCrane2").setValue(ObjectUtil.objectToString(shipAnchorageCheckSubQuestions.getFloatingCrane2()), font, 11f);
        // 计划副本持有人
        pdfAcroForm.getField("planHolder").setValue(ObjectUtil.objectToString(shipAnchorageCheckSubQuestions.getPlanHolder()), font, 11f);
        // 船舶勾选1-20
        // 港口勾选1-20
        // 备注1-20
        // 遍历检查项目
        for (int i = 0; i < shipAnchorageCheckItemBOList.size(); i++) {
            ShipAnchorageCheckItemBO shipAnchorageCheckItemBO = shipAnchorageCheckItemBOList.get(i);
            // 船舶勾选,如果为Y则赋值为字符串"√"，如果是N赋值为字符串"×"，否则赋值为不适用"-"
            if("Y".equals(shipAnchorageCheckItemBO.getShipResponse())){
                pdfAcroForm.getField("barge" + (i + 1)).setValue(ObjectUtil.objectToString("√"), font, 11f);
            }else if("N".equals(shipAnchorageCheckItemBO.getShipResponse())){
                pdfAcroForm.getField("barge" + (i + 1)).setValue(ObjectUtil.objectToString("×"), font, 11f);
            }else{
                pdfAcroForm.getField("barge" + (i + 1)).setValue(ObjectUtil.objectToString("——"), font, 11f);
            }
            // 港口勾选
            if("Y".equals(shipAnchorageCheckItemBO.getPortResponse())){
                pdfAcroForm.getField("port" + (i + 1)).setValue(ObjectUtil.objectToString("√"), font, 11f);
            }else if("N".equals(shipAnchorageCheckItemBO.getPortResponse())){
                pdfAcroForm.getField("port" + (i + 1)).setValue(ObjectUtil.objectToString("×"), font, 11f);
            }else{
                pdfAcroForm.getField("port" + (i + 1)).setValue(ObjectUtil.objectToString("——"), font, 11f);
            }
            // 备注
            pdfAcroForm.getField("remark" + (i + 1)).setValue(ObjectUtil.objectToString(shipAnchorageCheckItemBO.getRemarks()), font, 11f);
        }

        pdfDocument.close();
    }

    // 生成安全装货确认书
    public void printAQQRS(String waterwaycargoid,OutputStream outputStream) throws Exception {
        log.info("安全装货确认书触发生成："+waterwaycargoid);
        InputStream inputStream=new ClassPathResource("安全装货确认书.pdf").getInputStream();// 类路径下的输入流
        PdfDocument pdfDocument=new PdfDocument(new PdfReader(inputStream),new PdfWriter(outputStream));
        PdfAcroForm pdfAcroForm = PdfAcroForm.getAcroForm(pdfDocument, true);
        pdfAcroForm.setGenerateAppearance(true);
        PdfFont font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H",true);

        Pb6Waterwaycargo pb6Waterwaycargo=pb6WaterwaycargoService.getOne(Wrappers.<Pb6Waterwaycargo>query().eq("id",waterwaycargoid/*""*/));

        // 告知书内容
        MiniappStatementBO statementByWaterWayId = miniappStatementService.getStatementByWaterWayId(Long.valueOf(waterwaycargoid));
        // 时间 为confirmDate
        Date confirmDate = statementByWaterWayId.getConfirmDate();
        // 时间格式化为 yyyy-MM-dd
        String dateTime = new java.text.SimpleDateFormat("yyyy-MM-dd").format(confirmDate);
        // 获得当前时间的年月日，用year，month，day接收
        String year = dateTime.substring(0, 4);
        String month = dateTime.substring(5, 7);
        String day = dateTime.substring(8, 10);

        //pdfAcroForm.getField("type").setFont(font).setValue(ObjectUtil.objectToString(pb8Weighrecord.getType())).setFontSizeAutoScale();
        // 驳船名
        pdfAcroForm.getField("bargeName").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getBargeName()), font, 11f);
        // 码头
        pdfAcroForm.getField("port").setValue(ObjectUtil.objectToString(statementByWaterWayId.getPort()), font, 11f);
        // 货名
        pdfAcroForm.getField("cargoName").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getCargename()), font, 11f);
        // 载重吨
        pdfAcroForm.getField("weight").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getRationweight()), font, 11f);
        // 离开时间
        pdfAcroForm.getField("leaveTime").setValue(ObjectUtil.objectToString(statementByWaterWayId.getLeaveTime()), font, 11f);
        // 船方确认
        pdfAcroForm.getField("shipConfirm").setValue(ObjectUtil.objectToString(statementByWaterWayId.getShipConfirm()), font, 11f);
        // 年
        pdfAcroForm.getField("year").setValue(ObjectUtil.objectToString(year), font, 11f);
        // 月
        pdfAcroForm.getField("month").setValue(ObjectUtil.objectToString(month), font, 11f);
        // 日
        pdfAcroForm.getField("day").setValue(ObjectUtil.objectToString(day), font, 11f);

        pdfDocument.close();

    }

    // 生成水路运单文件
    public void printSLHWYD(String waterwaycargoid, OutputStream outputStream) throws Exception {
        log.info("水路运单触发生成："+waterwaycargoid);
        InputStream inputStream=new ClassPathResource("水路货物运单.pdf").getInputStream();// 类路径下的输入流
        PdfDocument pdfDocument=new PdfDocument(new PdfReader(inputStream),new PdfWriter(outputStream));
        PdfAcroForm pdfAcroForm = PdfAcroForm.getAcroForm(pdfDocument, true);
        pdfAcroForm.setGenerateAppearance(true);
        PdfFont font = PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H",true);

        Pb6Waterwaycargo pb6Waterwaycargo=pb6WaterwaycargoService.getOne(Wrappers.<Pb6Waterwaycargo>query().eq("id",waterwaycargoid/*"BBZ042007101030"*/));

        pdfAcroForm.getField("waterwaycargoid").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getWaterwaycargoid()), font, 11f);
        //pdfAcroForm.getField("type").setFont(font).setValue(ObjectUtil.objectToString(pb8Weighrecord.getType())).setFontSizeAutoScale();
        pdfAcroForm.getField("shipname").setValue(ObjectUtil.objectToString(pb6BargeinfoService.getBargeNameById(pb6Waterwaycargo.getBargeidid())), font, 11f);
        pdfAcroForm.getField("shipnumber").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getBargeNumber()), font, 11f);
        // 起运港
        pdfAcroForm.getField("comid").setValue(ObjectUtil.stringLast(pb6Waterwaycargo.getBeginPort(),7), font, 11f);
        // 到达港
        pdfAcroForm.getField("validdate").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getEndPort()), font, 11f);
        // if (pb6Waterwaycargo.getComid()!=3 && pb6Waterwaycargo.getComid()!=2){
        //     pdfAcroForm.getField("shipnumber").setValue(ObjectUtil.stringLast(pb6Waterwaycargo.getShipnumber(),7), font, 11f);
        // }
        // if (pb6Waterwaycargo.getComid()==16){
        //     pdfAcroForm.getField("comid").setValue(ObjectUtil.objectToString("南沙粮食通用码头"), font, 11f);
        // }else if(pb6Waterwaycargo.getComid()==2){
        //     pdfAcroForm.getField("comid").setValue(ObjectUtil.objectToString("黄埔老港"), font, 11f);
        // }else{
        //     pdfAcroForm.getField("comid").setValue(ObjectUtil.objectToString(pubCompanyService.getComsnameById(pb6Waterwaycargo.getComid())), font, 11f);
        // }
        pdfAcroForm.getField("midportid").setValue(ObjectUtil.objectToString(pubPortService.getPortcnameById(pb6Waterwaycargo.getMidportid())), font, 11f);
        // if(pb6Waterwaycargo.getValiddate().length()<=4){
        //     pdfAcroForm.getField("validdate").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getValiddate()), font, 11f);
        // }else{
        //     pdfAcroForm.getField("validdate").setFont(font).setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getValiddate())).setFontSizeAutoScale();
        // }
        pdfAcroForm.getField("chargebalancetype").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getChargebalancetype()), font, 11f);
        if (pb6Waterwaycargo.getConsigner().length()<=20){
            pdfAcroForm.getField("consigner").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getConsigner()), font, 11f);
        }else{
            pdfAcroForm.getField("consigner").setFont(font).setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getConsigner())).setFontSizeAutoScale();
        }
        if (pb6Waterwaycargo.getConsignee().length()<=17){
            pdfAcroForm.getField("consignee").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getConsignee()), font, 11f);
        }else {
            pdfAcroForm.getField("consignee").setFont(font).setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getConsignee())).setFontSizeAutoScale();
        }
        if(pb6Waterwaycargo.getCargosize().length() <= 12){
            pdfAcroForm.getField("cargosize").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getCargosize()), font, 11f);
        }else{
            pdfAcroForm.getField("cargosize").setFont(font).setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getCargosize())).setFontSizeAutoScale();
        }
        // 发货符号（改为大船船名，格式：大船名XXX）
        String flag =  pb6Waterwaycargo.getShipName();
        pdfAcroForm.getField("consignmentflag").setValue(ObjectUtil.objectToString(flag), font, 11f);
        pdfAcroForm.getField("cargename").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getCargename()), font, 11f);

        // pdfAcroForm.getField("rationpiece").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getRationpiece(),0), font, 11f);
        pdfAcroForm.getField("packagetype").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getPackagetype()), font, 11f);
        pdfAcroForm.getField("totalcharge").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getTotalcharge(),2), font, 11f);
        pdfAcroForm.getField("rationweight").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getRationweight()), font, 11f);

        pdfAcroForm.getField("businessagent").setValue(ObjectUtil.objectToString("代理费"), font, 11f);
        pdfAcroForm.getField("businessagentchargerate").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getBusinessagentchargerate(),2), font, 11f);
        pdfAcroForm.getField("businessagentcharge").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getBusinessagentcharge(),2), font, 11f);

        // pdfAcroForm.getField("cargoport").setValue(ObjectUtil.objectToString("货物港务费"), font, 11f);
        // pdfAcroForm.getField("cargoportchargerate").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getCargoportchargerate(),2), font, 11f);
        // pdfAcroForm.getField("cargoportcharge").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getCargoportcharge(),2), font, 11f);
        //
        // pdfAcroForm.getField("berth").setValue(ObjectUtil.objectToString("停泊费"), font, 11f);
        // pdfAcroForm.getField("berthchargerate").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getBerthchargerate(),2), font, 11f);
        // pdfAcroForm.getField("berthcharge").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getBerthcharge(),2), font, 11f);
        //
        // pdfAcroForm.getField("serviceagent").setValue(ObjectUtil.objectToString("围油栏使用费"), font, 11f);
        // pdfAcroForm.getField("serviceagentchargerate").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getServiceagentchargerate(),2), font, 11f);
        // pdfAcroForm.getField("serviceagentcharge").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getServiceagentcharge(),2), font, 11f);

        pdfAcroForm.getField("totalcharge").setValue(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getTotalcharge(),2), font, 11f);
        pdfAcroForm.getField("totalchargeCN").setValue(ObjectUtil.number2CNMontrayUnit(new BigDecimal(ObjectUtil.stringToBigDecimalString(pb6Waterwaycargo.getTotalcharge(),null))), font, 11f);

        // List<Pb3Coutform> coutformList=pb3CoutformService.findByCoutformId(pb6Waterwaycargo.getOutorinformid());
        // String uniqecode=coutformList.size()==1?coutformList.get(0).getUniqecode():"";
        // 特约事项还包括备注
        String specialNotice = pb6Waterwaycargo.getSpecialNotice();
        if(pb6Waterwaycargo.getCompleteRemark()!=null && !"".equals(pb6Waterwaycargo.getCompleteRemark())){
            // 如果specialNotice不为空，则拼接，如果是空，直接复制为CompleteRemark
            if(specialNotice!=null && !"".equals(specialNotice)){
                specialNotice = specialNotice + "，" + pb6Waterwaycargo.getCompleteRemark();
            }else{
                specialNotice = pb6Waterwaycargo.getCompleteRemark();
            }
        }

        if(specialNotice.length() <= 45){
            pdfAcroForm.getField("specialnotice").setValue(ObjectUtil.objectToString(specialNotice), font, 11f);
        }else{
            pdfAcroForm.getField("specialnotice").setFont(font).setValue(ObjectUtil.objectToString(specialNotice)).setFontSizeAutoScale();
        }



        pdfAcroForm.getField("consignflag").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getConsignflag()), font, 11f);

        pdfAcroForm.getField("maker").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getRationprincipal()), font, 11f);

        // 如果水路运单确认装货人 confrimloadpeople不为空，则赋值confirmLoadPeople为confrimloadpeople，否则赋值为updateBy
        if(pb6Waterwaycargo.getConfirmloadpeople() != null && !"".equals(pb6Waterwaycargo.getConfirmloadpeople())){
            pdfAcroForm.getField("confirmLoadPeople").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getConfirmloadpeople()), font, 11f);
        }else{
            pdfAcroForm.getField("confirmLoadPeople").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getUpdateBy()), font, 11f);
        }

        // InvoicePersonnel invoicePersonnel=invoicePersonnelService.getOne(Wrappers.<InvoicePersonnel>lambdaQuery().eq(InvoicePersonnel::getComid,pb6Waterwaycargo.getComid()));
        // pdfAcroForm.getField("checker").setValue(ObjectUtil.objectToString(invoicePersonnel.getResponsiblePerson()), font, 11f);
        pdfAcroForm.getField("rationdate").setValue(ObjectUtil.objectToString(pb6Waterwaycargo.getRationdate()), font, 11f);




        pdfDocument.close();
    }

    public XGPdfDTO searchXGPdfDTO(String waterwaycargoid){
        XGPdfDTO xgPdfDTO=new XGPdfDTO();
        List<XGPdfDTO> xgPdfDTOS= pb6WaterwaycargoMapper.searchXGPdf(waterwaycargoid);
        float lhszzl=0;//理货实装重量（吨）
        int lhszjs=0;//理货实装件数（件）
        float ckszzl=0;//仓库实装重量（吨）
        int ckszjs=0;//仓库实装件数（件）
        if (xgPdfDTOS.size()>0){
            for(int i=0;i<xgPdfDTOS.size();i++){
                if (xgPdfDTOS.get(i).getTrantool().equals("过水")){
                    if (xgPdfDTOS.get(i).getAmt()!=null){
                        lhszjs=lhszjs+Integer.parseInt(xgPdfDTOS.get(i).getAmt());
                    }
                    if (xgPdfDTOS.get(i).getWeightvalue()!=null){
                        lhszzl=lhszzl+Float.parseFloat(xgPdfDTOS.get(i).getWeightvalue());
                    }
                }
                if (xgPdfDTOS.get(i).getTrantool().equals("驳")){
                    if (xgPdfDTOS.get(i).getAmt()!=null){
                        ckszjs=ckszjs+Integer.parseInt(xgPdfDTOS.get(i).getAmt());
                    }
                    if (xgPdfDTOS.get(i).getWeightvalue()!=null){
                        ckszzl=ckszzl+Float.parseFloat(xgPdfDTOS.get(i).getWeightvalue());
                    }
                }
            }
        }
        Pb6Waterwaycargo pb6Waterwaycargo=pb6WaterwaycargoService.searchWaterwaycargoByWaterwaycargoid(waterwaycargoid);
        BigDecimal a=new BigDecimal(pb6Waterwaycargo.getRationweight());
        BigDecimal b=new BigDecimal(lhszzl);
        BigDecimal c=new BigDecimal(ckszzl);
        BigDecimal d=a.subtract(b);
        BigDecimal e=b.add(c);
        xgPdfDTO.setCkszjs(ckszjs);
        xgPdfDTO.setLhszjs(lhszjs);
        xgPdfDTO.setTotalAmt(lhszjs+ckszjs);
        if (lhszjs+ckszjs==0){
            xgPdfDTO.setCkszzl(ckszzl/1000);
            xgPdfDTO.setLhszzl(lhszzl/1000);
            xgPdfDTO.setTotalWeight(ObjectUtil.stringToBigDecimalDivideString(e.toString(),BigDecimal.valueOf(1000),3));
        }

        if (lhszjs+ckszjs>0){
            //纯过水
            if (lhszjs>0&&ckszjs==0&&ckszzl==0){
                xgPdfDTO.setLhszzl(Float.parseFloat(ObjectUtil.stringToBigDecimalDivideString(a.toString(),BigDecimal.valueOf(1000),3)));
                xgPdfDTO.setCkszzl(ckszzl/1000);
            }else{
                xgPdfDTO.setLhszzl(lhszzl/1000);
                xgPdfDTO.setCkszzl(Float.parseFloat(ObjectUtil.stringToBigDecimalDivideString(d.toString(),BigDecimal.valueOf(1000),3)));
            }
            xgPdfDTO.setTotalWeight(ObjectUtil.stringToBigDecimalDivideString(a.toString(),BigDecimal.valueOf(1000),3));

        }


        log.info(waterwaycargoid+"水路运单ckszjs:"+ckszjs+"ckszzl:"+ckszzl+"lhszjs:"+lhszjs+"lhszzl:"+lhszzl);
        return  xgPdfDTO;
    }

    private static String bCJJPZSql;
    private static String hWJJQDSql;
    static {
        try {
            bCJJPZSql=FileCopyUtils.copyToString(new InputStreamReader(new ClassPathResource("sql/bCJJPZSql.sql").getInputStream(),StandardCharsets.UTF_8));
            hWJJQDSql=FileCopyUtils.copyToString(new InputStreamReader(new ClassPathResource("sql/hWJJQDSql.sql").getInputStream(),StandardCharsets.UTF_8));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    class CustomButton extends AbstractElement<CustomButton> implements ILeafElement, IAccessibleElement {

        protected DefaultAccessibilityProperties accessibilityProperties;
        protected PdfButtonFormField button;
        protected String caption;
        protected ImageData image;
        protected com.itextpdf.kernel.geom.Rectangle rect;
        //protected Color borderColor = ColorConstants.BLACK;
        protected com.itextpdf.kernel.colors.Color borderColor = ColorConstants.WHITE;
        protected com.itextpdf.kernel.colors.Color buttonBackgroundColor = ColorConstants.WHITE;

        public CustomButton(PdfButtonFormField button) {
            this.button = button;
        }

        @Override
        protected IRenderer makeNewRenderer() {
            return new CustomButtonRenderer(this);
        }

        @Override
        public AccessibilityProperties getAccessibilityProperties() {
            if (accessibilityProperties == null) {
                accessibilityProperties = new DefaultAccessibilityProperties(StandardRoles.FIGURE);
            }
            return accessibilityProperties;
        }

        public PdfButtonFormField getButton() {
            return button;
        }

        public String getCaption() {
            return caption == null ? "" : caption;
        }

        public void setImage(ImageData image) {
            this.image = image;
        }

        public ImageData getImage() {
            return image;
        }

        public com.itextpdf.kernel.colors.Color getBorderColor() {
            return borderColor;
        }

        public void setBorderColor(com.itextpdf.kernel.colors.Color borderColor) {
            this.borderColor = borderColor;
        }

        public void setButtonBackgroundColor(com.itextpdf.kernel.colors.Color buttonBackgroundColor) {
            this.buttonBackgroundColor = buttonBackgroundColor;
        }
    }
    class CustomButtonRenderer extends AbstractRenderer {

        public CustomButtonRenderer(CustomButton button) {
            super(button);
        }

        @Override
        public LayoutResult layout(LayoutContext layoutContext) {
            LayoutArea area = layoutContext.getArea().clone();
            com.itextpdf.kernel.geom.Rectangle layoutBox = area.getBBox();
            applyMargins(layoutBox, false);
            CustomButton modelButton = (CustomButton) modelElement;
            occupiedArea = new LayoutArea(area.getPageNumber(), new com.itextpdf.kernel.geom.Rectangle(modelButton.button.getWidgets().get(0).getRectangle().toRectangle()));
            PdfButtonFormField button = ((CustomButton) getModelElement()).getButton();
            button.getWidgets().get(0).setRectangle(new PdfArray(occupiedArea.getBBox()));

            return new LayoutResult(LayoutResult.FULL, occupiedArea, null, null);
        }

        @Override
        public void draw(DrawContext drawContext) {
            CustomButton modelButton = (CustomButton) modelElement;
            com.itextpdf.kernel.geom.Rectangle rect = modelButton.button.getWidgets().get(0).getRectangle().toRectangle();
            occupiedArea.setBBox(rect);

            super.draw(drawContext);
            float width = occupiedArea.getBBox().getWidth();
            float height = occupiedArea.getBBox().getHeight();

            PdfStream str = new PdfStream();
            PdfCanvas canvas = new PdfCanvas(str, new PdfResources(), drawContext.getDocument());
            PdfFormXObject xObject = new PdfFormXObject(new com.itextpdf.kernel.geom.Rectangle(0, 0, width, height));

            canvas.
                    saveState().
                    setStrokeColor(modelButton.getBorderColor()).
                    setLineWidth(1).
                    rectangle(0, 0, occupiedArea.getBBox().getWidth(), occupiedArea.getBBox().getHeight()).
                    stroke().
                    setFillColor(modelButton.buttonBackgroundColor).
                    rectangle(0.5f, 0.5f, occupiedArea.getBBox().getWidth() - 1, occupiedArea.getBBox().getHeight() - 1).
                    fill().
                    restoreState();

            Paragraph paragraph = new Paragraph(modelButton.getCaption()).setFontSize(10).setMargin(0).setMultipliedLeading(1);

            new Canvas(canvas, new Rectangle(0, 0, width, height)).
                    showTextAligned(paragraph, 1, 1, TextAlignment.LEFT, VerticalAlignment.BOTTOM);

            PdfImageXObject imageXObject = new PdfImageXObject(modelButton.getImage());
            float imageWidth = imageXObject.getWidth();
            if (imageXObject.getWidth() > rect.getWidth()) {
                imageWidth = rect.getWidth();
            } else if (imageXObject.getHeight() > rect.getHeight()) {
                imageWidth = imageWidth * (rect.getHeight() / imageXObject.getHeight());
            }

            canvas.addXObject(imageXObject, 0.5f, 0.5f, imageWidth - 1);


            PdfButtonFormField button = modelButton.getButton();
            button.getWidgets().get(0).setNormalAppearance(xObject.getPdfObject());
            xObject.getPdfObject().getOutputStream().writeBytes(str.getBytes());
            xObject.getResources().addImage(imageXObject);

            PdfAcroForm.getAcroForm(drawContext.getDocument(), true).addField(button, drawContext.getDocument().getPage(1));
        }

        @Override
        public IRenderer getNextRenderer() {
            return null;
        }
    }
}

package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.databarge.domain.Pb6Bargeinfo;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.databarge.domain.dto.BargeCheckInSearchDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeCheckInVO;
import com.ruoyi.databarge.domain.vo.Pb6CargoconsignmentVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 驳船托运单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface Pb6CargoconsignmentdetailMapper extends BaseMapper<Pb6Cargoconsignmentdetail> {

    List<Pb6CargoconsignmentVO> searchPb6Cargoconsignment(@Param("pb6CargoconsignmentSearchDTO") Pb6CargoconsignmentSearchDTO pb6CargoconsignmentSearchDTO);

    List<Pb6CargoconsignmentDTO> searchApplyDateByConsignFlag(@Param("consignflag") String consignflag);

    List<String> searchPortByEndPortName(@Param("endPortName") String endPortName);
    List<Pb6Cargoconsignmentdetail> searchBargeByWxappointmenttime(@Param("wxappointmenttime") String wxappointmenttime, @Param("wxappointmenttime1") String wxappointmenttime1);
    IPage<BargeCheckInVO> searchBargeCheckINVO(Page<Pb6Bargeinfo> page, @Param("bargeCheckInSearchDTO") BargeCheckInSearchDTO bargeCheckInSearchDTO);
//    List<BargeCheckInVO> searchBargeCheckINVO(@Param("bargeCheckInSearchDTO") BargeCheckInSearchDTO bargeCheckInSearchDTO);

    /**
     * 查询新沙当前预约报到的船的信息
     * @return 船的信息
     */
    List<Pb6Cargoconsignmentdetail> searchBargeOrder(@Param("comid") Long comid);

    IPage<BargeCheckInVO> xgXjSearch(Page<Pb6Bargeinfo> page, @Param("bargeCheckInSearchDTO") BargeCheckInSearchDTO bargeCheckInSearchDTO);

    List<Pb6Cargoconsignmentdetail> searchByConsignId(@Param("consignid") Long consignid);

    // 参数驳船信息对象，在驳船信息表中查询所有驳船名+航次相同、并且大船计划不同的驳船信息
    // 修改为查询所有驳船名相同的驳船信息，不再限制航次，并且创建时间在当前时间前七天内
    List<Pb6Cargoconsignmentdetail> searchRepeatByBargeInfo(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail);

    // 参数驳船信息对象，在驳船信息表中查询所有驳船名+航次相同、并且大船计划不同的驳船信息
    List<Pb6Cargoconsignmentdetail> searchRepeatBarge(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail);

    // 参数驳船信息对象，在驳船信息表中查询所有驳船名+航次相同、并且驳船id不同的驳船信息
    List<Pb6Cargoconsignmentdetail> searchRepeatBargeExceptNow(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail);

    List<Pb6Cargoconsignmentdetail> searchByShipInfoId(@Param("shipInfoId") Long shipInfoId);
}

package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * @Description: 出库单表
 * <AUTHOR>
 * @Date 2020/3/13 9:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PB3_COUTFORM")
public class Pb3Coutform implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 出库单号
     */
    @TableField("COUTFORMID")
    private String coutformid;

    /**
     * 委托人ID(未使用)
     */
    @TableField("CUSTOMERID")
    private String customerid;

    /**
     * 委托人名称
     */
    @TableField("CUSTOMERNAME")
    private String customername;

    /**
     * 委托单位经办人电话
     */
    @TableField("CUSTOMERTEL")
    private String customertel;

    /**
     * 收货人
     */
    @TableField("CONSIGNEE")
    private String consignee;

    /**
     * 货物代码
     */
    @TableField("CARGOID")
    private String cargoid;

    /**
     * 货物名称
     */
    @TableField("CARGONAME")
    private String cargoname;

    /**
     * 合并提单号
     */
    @TableField("UNBLID")
    private String unblid;

    /**
     * 合同号
     */
    @TableField("CONTRACTID")
    private String contractid;

    /**
     * 补充协议号
     */
    @TableField("RECONTRACTID")
    private String recontractid;

    /**
     * 预结算编号
     */
    @TableField("PREBALANCEID")
    private String prebalanceid;

    /**
     * 到验号
     */
    @TableField("UNIQECODE")
    private String uniqecode;

    /**
     * 办单时地磅单号
     */
    @TableField("FORMLOADOMETERID")
    private String formloadometerid;

    /**
     * 堆存地点
     */
    @TableField("STACKNAME")
    private String stackname;

    /**
     * 货物流向
     */
    @TableField("CARGOFLOW")
    private String cargoflow;

    /**
     * 运输工具
     */
    @TableField("TRANSPORTTOOL")
    private String transporttool;

    /**
     * 标志规格
     */
    @TableField("MARK")
    private String mark;

    /**
     * 包装方式
     */
    @TableField("PACKAGETYPE")
    private String packagetype;

    /**
     * 办单件数
     */
    @TableField("AMT")
    private String amt;

    /**
     * 单重
     */
    @TableField("PIECEWEIGHT")
    private String pieceweight;

    /**
     * 办单重量
     */
    @TableField("WEIGHTVALUE")
    private String weightvalue;

    /**
     * 办单货物体积
     */
    @TableField("CARGOVOLUME")
    private String cargovolume;

    /**
     * 是否办保函
     */
    @TableField("ISPLEDGE")
    private String ispledge;

    /**
     * 保函换单日期
     */
    @TableField("PLEDGETRANDATE")
    private String pledgetrandate;

    /**
     * 办单人
     */
    @TableField("FORMMAN")
    private String formman;

    /**
     * 办单日期
     */
    @TableField("FORMDATE")
    private String formdate;

    /**
     * 结存件数
     */
    @TableField("REMAINAMT")
    private String remainamt;

    /**
     * 结存重量
     */
    @TableField("REMAINWEIGHT")
    private String remainweight;

    /**
     * 结存体积
     */
    @TableField("REMAINVOLUME")
    private String remainvolume;

    /**
     * 测算金额
     */
    @TableField("RECKONMNY")
    private String reckonmny;

    /**
     * 测算人
     */
    @TableField("RECKONMAN")
    private String reckonman;

    /**
     * 进库日期
     */
    @TableField("CINDATE")
    private String cindate;

    /**
     * 有效日期
     */
    @TableField("INVALIDDATE")
    private String invaliddate;

    /**
     * 免费堆存天数
     */
    @TableField("FREEDAYS")
    private String freedays;

    /**
     * 核发人
     */
    @TableField("ISSUEMAN")
    private String issueman;

    /**
     * 核发日期
     */
    @TableField("ISSUEDATE")
    private String issuedate;

    /**
     * 父出库单号
     */
    @TableField("DADCOFORMID")
    private String dadcoformid;

    /**
     * 换出库单号
     */
    @TableField("CHANGECFORMID")
    private String changecformid;

    /**
     * 换单日期
     */
    @TableField("CHANGEDATE")
    private String changedate;

    /**
     * 收单人（completeform的人）
     */
    @TableField("RECEIPTMAN")
    private String receiptman;

    /**
     * 收单日期
     */
    @TableField("RECEIPTDATE")
    private String receiptdate;

    /**
     * 销单人
     */
    @TableField("FORMCLOSEMAN")
    private String formcloseman;

    /**
     * 销单日期
     */
    @TableField("FORMCLOSEDATE")
    private String formclosedate;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 办单结存件数
     */
    @TableField("FREMAINAMT")
    private String fremainamt;

    /**
     * 办单结存重量
     */
    @TableField("FREMAINWEIGHT")
    private String fremainweight;

    /**
     * 办单结存体积
     */
    @TableField("FREMAINVOLUME")
    private String fremainvolume;

    /**
     * 动态结存重量
     */
    @TableField("DYNAMICWEIGHT")
    private String dynamicweight;

    /**
     * 动态结存件数
     */
    @TableField("DYNAMICAMT")
    private String dynamicamt;

    /**
     * 动态结存体积
     */
    @TableField("DYNAMICVOLUME")
    private String dynamicvolume;

    /**
     * 实际重量
     */
    @TableField("REALWEIGHT")
    private String realweight;

    /**
     * 实际件数
     */
    @TableField("REALAMT")
    private String realamt;

    /**
     * 实际体积
     */
    @TableField("REALVOLUME")
    private String realvolume;

    /**
     * 实际平均单重
     */
    @TableField("REALPWEIGHT")
    private String realpweight;

    /**
     * 结存平均单重
     */
    @TableField("REMAINPWEIGHT")
    private String remainpweight;

    /**
     * 与父单结算关系
     */
    @TableField("DADRELATION")
    private String dadrelation;

    /**
     * 重量单位
     */
    @TableField("WEIGHTUNIT")
    private String weightunit;

    /**
     * 大船名
     */
    @TableField("SHIPNAME")
    private String shipname;

    /**
     * 是否为危险品
     */
    @TableField("DANGEROUSCARGO")
    private String dangerouscargo;

    /**
     * 扣押方式
     */
    @TableField("PLEDGECLASS")
    private String pledgeclass;

    /**
     * 审核人
     */
    @TableField("CHECKMAN")
    private String checkman;

    /**
     * 审核日期
     */
    @TableField("CHECKDATE")
    private String checkdate;

    /**
     * 是否为保税货物
     */
    @TableField("BONDEDCARGO")
    private String bondedcargo;

    /**
     * 作业项目
     */
    @TableField("WORKSTYLE")
    private String workstyle;

    /**
     * 货物长度
     */
    @TableField("CARGOLENGTH")
    private String cargolength;

    /**
     * 货物高度
     */
    @TableField("CARGOHEIGHT")
    private String cargoheight;

    /**
     * 货物宽度
     */
    @TableField("CARGOWIDTH")
    private String cargowidth;

    /**
     * 结算方式
     */
    @TableField("SETTLEMENTTYPE")
    private String settlementtype;

    /**
     * 委托单位经办人
     */
    @TableField("CUSTOMERBUSINESSMAN")
    private String customerbusinessman;

    /**
     * 包装方式代理主键
     */
    @TableField("PACKAGETYPEID")
    private Long packagetypeid;

    /**
     * 运输工具代理主键
     */
    @TableField("TRANSPORTTOOLID")
    private Long transporttoolid;

    /**
     * 货物流向代理主键
     */
    @TableField("CARGOFLOWID")
    private Long cargoflowid;

    /**
     * 结算方式代理主键
     */
    @TableField("SETTLEMENTTYPEID")
    private Long settlementtypeid;

    /**
     * 作业项目代理主键
     */
    @TableField("WORKSTYLEID")
    private Long workstyleid;

    /**
     * 公司代理主键
     */
    @TableField("CORPORATIONID")
    private Long corporationid;

    /**
     * 部门代理主键
     */
    @TableField("DEPTID")
    private Long deptid;

    /**
     * 货名代理主键
     */
    @TableField("CARGOSERIALNUMBER")
    private Long cargoserialnumber;

    /**
     * 是否停单Y/N
     */
    @TableField("IMPAWN")
    private String impawn;

    /**
     * 停单重量
     */
    @TableField("IMPAWNNUMBER")
    private String impawnnumber;

    /**
     * 办单人id
     */
    @TableField("FORMMANID")
    private Long formmanid;

    /**
     * 测算人id
     */
    @TableField("RECKONMANID")
    private Long reckonmanid;

    /**
     * 核发人id
     */
    @TableField("ISSUREMANID")
    private Long issuremanid;

    /**
     * 收单人id
     */
    @TableField("RECEIPTMANID")
    private Long receiptmanid;

    /**
     * 销单人id
     */
    @TableField("FORMCLOSEMANID")
    private Long formclosemanid;

    /**
     * 是否计费
     */
    @TableField("ISRATING")
    private String israting;

    /**
     * 客户代理主键
     */
    @TableField("CUSTOMERSERIALNUMBER")
    private Long customerserialnumber;

    /**
     * 收货人代理主键
     */
    @TableField("CONSIGNEEID")
    private Long consigneeid;

    /**
     * 堆存地点代理主键
     */
    @TableField("STACKNAMESERIALID")
    private Long stacknameserialid;

    /**
     * 合并提单代理主键
     */
    @TableField("UNBLIDSERIALID")
    private Long unblidserialid;

    /**
     * 合同号代理主键
     */
    @TableField("CONTRACTSERIALID")
    private Long contractserialid;

    /**
     * 预结号代理主键
     */
    @TableField("PREBALANCEIDSERIALID")
    private Long prebalanceidserialid;

    /**
     * 到验号代理主键
     */
    @TableField("UNIQECODESERIALID")
    private Long uniqecodeserialid;

    /**
     * 地磅单代理主键
     */
    @TableField("LOADOMETERSERIALID")
    private Long loadometerserialid;

    /**
     * 父出库单代理主键
     */
    @TableField("DADSERIALID")
    private Long dadserialid;

    /**
     * 换出库单号代理主键
     */
    @TableField("CHANGESERIALID")
    private Long changeserialid;

    /**
     * 是否扣押Y/N
     */
    @TableField("DETAIN")
    private String detain;

    /**
     * 修改人id
     */
    @TableField("MENDERID")
    private Long menderid;

    /**
     * 修改人
     */
    @TableField("MENDER")
    private String mender;

    /**
     * 修改时间
     */
    @TableField("MENDERTIME")
    private String mendertime;

    /**
     * 是否完单Y/N（检算收单，出库单收单）
     */
    @TableField("COMPLETEFORM")
    private String completeform;

    /**
     * 是否计收工本费
     */
    @TableField("COUFROMCOST")
    private String coufromcost;

    /**
     * 是否销单(计费完毕)
     */
    @TableField("CLOSEFORM")
    private String closeform;

    /**
     * 作业区id
     */
    @TableField("WORKAREAID")
    private Long workareaid;

    /**
     * 是否为导入数据
     */
    @TableField("ISIMPORT")
    private String isimport;

    /**
     * 发货比例
     */
    @TableField("DELIVERYRATE")
    private String deliveryrate;

    /**
     * 是否已经中转
     */
    @TableField("ISTRAN")
    private String istran;

    /**
     * 原舱单货名
     */
    @TableField("PRECARGONAME")
    private String precargoname;

    /**
     * 转出口合同号
     */
    @TableField("TRANCONTRACTID")
    private String trancontractid;

    /**
     * 停单件数
     */
    @TableField("IMPAWNAMT")
    private String impawnamt;

    /**
     * 报关号
     */
    @TableField("DECLARATIONNUMBER")
    private String declarationnumber;

    /**
     * 是否代办发货
     */
    @TableField("ISAGENT")
    private String isagent;

    /**
     * 合单父出库单号（未使用）
     */
    @TableField("COMBINEDADFORMID")
    private String combinedadformid;

    /**
     * 商品车货卡TID号
     */
    @TableField("CARDTID")
    private String cardtid;

    /**
     * 商品车提货卡卡面号码
     */
    @TableField("CARDNUM")
    private String cardnum;

    /**
     * 对出库单修改的模块名称
     */
    @TableField("CHANGEPART")
    private String changepart;

    /**
     * 指令编号
     */
    @TableField("ORDERNUM")
    private String ordernum;

    /**
     * 修改原因
     */
    @TableField("REASON")
    private String reason;

    /**
     * 停单原因
     */
    @TableField("IMPAWNREASON")
    private String impawnreason;

    /**
     * 真实发货比例
     */
    @TableField("REALDELIVERYRATE")
    private String realdeliveryrate;

    /**
     * 是否核销(新港港建用)
     */
    @TableField("ISCANCEL")
    private String iscancel;

    /**
     * 公估重量（小虎使用单位为kg)
     */
    @TableField("PUBESTIMATIONWEIGHT")
    private String pubestimationweight;

    /**
     * 流向（小虎使用）
     */
    @TableField("DIRECTION")
    private String direction;

    /**
     * 仓租量（小虎使用）
     */
    @TableField("STACKRENT")
    private String stackrent;

    /**
     * 航次号
     */
    @TableField("SHIPVOPXY")
    private String shipvopxy;

    /**
     * 申请编号
     */
    @TableField("APPLYNO")
    private String applyno;

    /**
     * 新沙核销
     */
    @TableField("ISXSCANCEL")
    private String isxscancel;

    /**
     * 超配载
     */
    @TableField("OVERCARGO")
    private String overcargo;

    /**
     * 联系电话
     */
    @TableField("TELEPHONE")
    private String telephone;

    /**
     * 来源
     */
    @TableField("ONLINERESOURCE")
    private String onlineresource;

    /**
     * 分单是否被接受
     */
    @TableField("RECEIVED")
    private String received;

    /**
     * 出库单办理人对应的ID
     */
    @TableField("USERID")
    private Long userid;

    /**
     * 审核状态
     */
    @TableField("ISCHECK")
    private String ischeck;

    /**
     * 未通过原因
     */
    @TableField("NOPASSREASON")
    private String nopassreason;

    @TableField("ISFIXED")
    private String isfixed;

    /**
     * 网上营业厅疏运方式对应重量
     */
    @TableField("SYWEIGHT")
    private String syweight;

    /**
     * 出库单存上cargolistcontract的id
     */
    @TableField("IDCONTRACTID")
    private Long idcontractid;

    /**
     * 网上出库单办理联系人
     */
    @TableField("USERNAME")
    private String username;

    /**
     * 物权委托标志
     */
    @TableField("WQQT")
    private String wqqt;

    /**
     * 分单后费用划分归属
     */
    @TableField("FEEBELONG")
    private String feebelong;

    /**
     * 支付标识
     */
    @TableField("PAYCHECK")
    private String paycheck;

    /**
     * 网上办单备注
     */
    @TableField("ONLINEREMARK")
    private String onlineremark;

    /**
     * 是否电动车，新沙进口商品车用
     */
    @TableField("ISELECTRIC")
    private String iselectric;


}

package com.ruoyi.basic.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.basic.domain.BasicGoodsName;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【BASIC_GOODS_NAME】的数据库操作Service
* @createDate 2023-11-16 11:43:52
*/
public interface BasicGoodsNameService extends IService<BasicGoodsName> {

    // 货名List
    List<BasicGoodsName> selectBasicGoodsNameList(BasicGoodsName basicGoodsName);

    // 新增货名
    AjaxResult addBasicGoodsName(BasicGoodsName basicGoodsName);

    // 修改货名
    AjaxResult updateBasicGoodsName(BasicGoodsName basicGoodsName);

    // 删除货名
    AjaxResult deleteBasicGoodsName(String goodsNameId);

    // 根据货名查询货名信息
    BasicGoodsName selectBasicGoodsNameByName(String goodsName);

    // 根据货类查询货名信息
    List<BasicGoodsName> selectBasicGoodsNameByGoodsCategory(String goodsCategory);

    // 根据货类id删除所有货名
    AjaxResult deleteBasicGoodsNameByCategoryId(String goodsCategoryId);

}

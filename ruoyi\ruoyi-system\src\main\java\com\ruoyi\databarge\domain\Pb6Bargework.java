package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 驳船报到登记信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PB6_BARGEWORK")
@KeySequence("SEQ_PB6_BARGEWORK")
public class Pb6Bargework implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 水路货物运单编号
     */
    @TableField("WATERWAYCARGOID")
    private String waterwaycargoid;

    /**
     * 报到时间
     */
    @TableField("REGISTERTIME")
    private String registertime;

    /**
     * 报到负责人
     */
    @TableField("REGISTERPRINCIPAL")
    private String registerprincipal;

    /**
     * 作业泊位
     */
    @TableField("WORKBERTH")
    private String workberth;

    /**
     * 开工时间
     */
    @TableField("BEGINWORKTIME")
    private String beginworktime;

    /**
     * 完工时间
     */
    @TableField("ENDWORKTIME")
    private String endworktime;

    /**
     * 离港时间
     */
    @TableField("LEAVEPORTTIME")
    private String leaveporttime;

    /**
     * 离港操作人
     */
    @TableField("LEAVEPORTPRINCIPAL")
    private String leaveportprincipal;

    /**
     * 出货仓库员
     */
    @TableField("WAREHOUSEMAN")
    private String warehouseman;

    /**
     * 作业类型
     */
    @TableField("WORKTYPE")
    private String worktype;

    /**
     * 装驳方式
     */
    @TableField("LOADBARGETYPE")
    private String loadbargetype;

    /**
     * 是否编制指令
     */
    @TableField("FLAGFRAMEORDER")
    private String flagframeorder;

    /**
     * 出货堆位
     */
    @TableField("SHIPMENTSTACK")
    private String shipmentstack;

    /**
     * 水路货物运单代理主键
     */
    @TableField("WATERWAYCARGOIDID")
    private Long waterwaycargoidid;

    /**
     * 报到负责人代理主键
     */
    @TableField("REGISTERPRINCIPALID")
    private Long registerprincipalid;

    /**
     * 作业泊位代理主键
     */
    @TableField("WORKBERTHID")
    private Long workberthid;

    /**
     * 出货堆位代理主键
     */
    @TableField("SHIPMENTSTACKID")
    private Long shipmentstackid;

    /**
     * 离港操作人代理主键
     */
    @TableField("LEAVEPORTPRINCIPALID")
    private Long leaveportprincipalid;

    /**
     * 出货仓库员代理主键
     */
    @TableField("WAREHOUSEMANID")
    private Long warehousemanid;

    /**
     * 作业区代理主键
     */
    @TableField("WORKID")
    private Long workid;

    /**
     * 公司代理主键
     */
    @TableField("COMID")
    private Long comid;

    /**
     * 作业地点类型代理主键
     */
    @TableField("PLACETYPE")
    private Long placetype;

    /**
     * 是否直通或过水
     */
    @TableField("FLAGSTRAIGHT")
    private String flagstraight;

    /**
     * 联系人
     */
    @TableField("BARGELINKMAN")
    private String bargelinkman;

    /**
     * 联系电话
     */
    @TableField("CONTACTPHONE")
    private String contactphone;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 是否过磅
     */
    @TableField("FLAGLOADOMETER")
    private String flagloadometer;

    /**
     * 是否减载
     */
    @TableField("DELOADINGFLAG")
    private String deloadingflag;

    /**
     * 到验号
     */
    @TableField("UNIQUECODE")
    private String uniquecode;

    /**
     * 大船名
     */
    @TableField("SHIPNAME")
    private String shipname;

    /**
     * 发货比例
     */
    @TableField("SHIPMENTRATE")
    private String shipmentrate;

    /**
     * 顺序号
     */
    @TableField("FLAGSEQUENCE")
    private Long flagsequence;

    /**
     * 是否绑定工班
     */
    @TableField("ISBINDWORKCLASS")
    private String isbindworkclass;

    /**
     * 绑定班次
     */
    @TableField("APPLICATIONCLASSES")
    private Long applicationclasses;

    /**
     * 是否可以工作
     */
    @TableField("ISWORKING")
    private String isworking;

    /**
     * 过磅重量
     */
    @TableField("LOADWEIGHT")
    private String loadweight;

    /**
     * 直通重量
     */
    @TableField("STRAIGHTWEIGHT")
    private String straightweight;

    /**
     * 过磅件数
     */
    @TableField("LOADPIECE")
    private String loadpiece;

    /**
     * 直通件数
     */
    @TableField("STRAIGHTPIECE")
    private String straightpiece;

    /**
     * 作业部门
     */
    @TableField("DEPT")
    private String dept;

    /**
     * 是否配煤
     */
    @TableField("ISCOAL")
    private String iscoal;

    /**
     * 委托人
     */
    @TableField("CUSTOMER")
    private String customer;

    /**
     * 是否预扣，0为没有预扣，1为预扣
     */
    @TableField("FLAGYUKOU")
    private Long flagyukou;

    /**
     * 是否解扣，0为没有解扣，1为解扣
     */
    @TableField("FLAGJIEKOU")
    private Long flagjiekou;

    /**
     * 预扣吨数
     */
    @TableField("YUKOUWEIGHT")
    private String yukouweight;

    /**
     * 解扣吨数
     */
    @TableField("JIEKOUWEIGHT")
    private String jiekouweight;

    /**
     * 审核人
     */
    @TableField("CHECKMAN")
    private String checkman;

    /**
     * 是否审核:0未审核，1审核
     */
    @TableField("ISCHECK")
    private Long ischeck;


}

<!--pages/minepage/messagedetail/messagedetail.wxml-->
<view class="consign-detail" hover-class="none" hover-stop-propagation="false">
  <view class="message-detail-page" hover-class="none" hover-stop-propagation="false">
    <view class="message-title" hover-class="none" hover-stop-propagation="false">{{getRouteData.title}}</view>
    <text class="message-content" hover-class="none" hover-stop-propagation="false">{{getRouteData.content}}</text>
    <button bindtap="submitEvent" type="primary">{{buttonText}}</button>
  </view>
  <view class="pdf-button" hover-class="none" hover-stop-propagation="false" wx:if="{{mainItem != null}}">
    <text class="pdf-button" selectable="false" space="false" decode="false">驳卡详表附件操作</text>
    <button class="pdf-button" bindtap="openSendEmail">发送邮箱</button>
    <button class="pdf-button" bindtap="getPdf">查看</button>
  </view>
  <view class="detail-content" hover-class="none" hover-stop-propagation="false" wx:if="{{mainItem != null}}">
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">到验号： </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{mainItem.uniqeCode}}</text>
    </view>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">船名： </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{mainItem.shipName}}</text>
    </view>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">水路运单号： </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{mainItem.waterwayCargoId}}</text>
    </view>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">地磅单： </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{mainItem.loadoMeterId}}</text>
    </view>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">办单号： </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{mainItem.outFormId}}</text>
    </view>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">预结号： </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{mainItem.preBalanceId}}</text>
    </view>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title blno-title" selectable="false" space="false" decode="false">发货符号： </text>
      <textarea class="detail-value blno-value" auto-height disabled value="{{mainItem.ladingBill}}" />
      <!-- <text class="detail-value blno-value" selectable="false" space="false" decode="false">{{mainItem.ladingBill}}</text> -->
    </view>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">货名： </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{mainItem.cargoName}}</text>
    </view>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">委托人： </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{mainItem.customerName}}</text>
    </view>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">驳船名: </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{mainItem.trainNum}}</text>
    </view>
    <text>\n</text>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">累计重量： </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{total.weight}}</text>
    </view>
    <view class="data-detail-content" hover-class="none" hover-stop-propagation="false">
      <text class="detail-title" selectable="false" space="false" decode="false">累计车次： </text>
      <text class="detail-value" selectable="false" space="false" decode="false">{{total.num}}</text>
    </view>
  </view>
  <view class="table" wx:if="{{mainItem != null}}">
    <view class="tr bg-w">
      <view class="th">车号</view>
      <view class="th">重车</view>
      <view class="th">空车</view>
      <view class="th">净重</view>
      <view class="th">日期</view>
      <view class="th">件数</view>
    </view>
    <block wx:for="{{listData}}" wx:key="index">
      <view class="tr bg-g" wx:if="{{index % 2 == 0}}">
        <view class="td">{{item.carNum}}</view>
        <view class="td">{{item.heaWeight}}</view>
        <view class="td">{{item.empWeight}}</view>
        <view class="td">{{item.netWeight}}</view>
        <view class="td">{{item.heaTime}}</view>
        <view class="td">{{item.sanPieces == null ? "" : item.sanPieces}}</view>
      </view>
      <view class="tr" wx:else>
        <view class="td">{{item.carNum}}</view>
        <view class="td">{{item.heaWeight}}</view>
        <view class="td">{{item.empWeight}}</view>
        <view class="td">{{item.netWeight}}</view>
        <view class="td">{{item.heaTime}}</view>
        <view class="td">{{item.sanPieces == null ? "" : item.sanPieces}}</view>
      </view>
    </block>
  </view>
  <!-- 发送邮箱弹窗 -->
  <mp-dialog title="{{userEmail ? '您的邮箱如下':'账号尚未绑定邮箱，请填写邮箱'}}" show="{{sendEmailDialog}}" mask="true" mask-closable="false" bindbuttontap="tapSendEmailDialogButton" buttons="{{batchButtons}}">
  <input type="text" class="add-message-emailipt" placeholder="请输入邮箱" value="{{userEmail}}"  bindinput="emailInput"/>
</mp-dialog>
</view>
package com.ruoyi.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/28 10:54
 */
public enum UploadDataType {

    BARGE_RECORD_DATA("10","驳船备案材料", ""),
    SHIP_RECORD_DATA("20","船公司备案材料",""),
    OUTBOUND_DATA("30","出库单证明材料",""),
    CONSIGN_DATA("40", "托运单资料", ""),
    NOTICE_DATA("60", "告知书资料", ""),
    IDENTITY("50", "用户关联资料(身份证/委托书)", "");

    @EnumValue
    private final String type;

    private final String desc;

    private final String table;

    UploadDataType(String type, String desc, String table) {
        this.type = type;
        this.desc = desc;
        this.table = table;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public String getTable() {
        return table;
    }

    /**
     * 枚举值获取描述值
     * @param type
     * @return
     */
    public static String typeToValue(String type){
        if(Objects.nonNull(type)){
            for (UploadDataType status : UploadDataType.values()) {
                if (status.type.equals(type)){
                    return status.desc;
                }
            }
        }
        return null;
    }

    /**
     * 枚举值获取table
     * @param type
     * @return
     */
    public static String typeToTable(String type){
        if(Objects.nonNull(type)){
            for (UploadDataType status : UploadDataType.values()) {
                if (status.type.equals(type)){
                    return status.table;
                }
            }
        }
        return null;
    }

    /**
     * 描述值获取枚举值
     * @param desc
     * @return
     */
    public static String descToType(String desc){
        if(Objects.nonNull(desc)){
            for (UploadDataType status : UploadDataType.values()) {
                if (status.desc.equals(desc)){
                    return status.type;
                }
            }
        }
        return null;
    }
}

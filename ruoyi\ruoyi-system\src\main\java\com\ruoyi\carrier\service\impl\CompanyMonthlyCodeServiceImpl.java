package com.ruoyi.carrier.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.carrier.domain.CompanyMonthlyCode;
import com.ruoyi.carrier.domain.bo.CompanyMonthlyCodeBO;
import com.ruoyi.carrier.domain.vo.CompanyMonthlyCodeVO;
import com.ruoyi.carrier.mapper.CompanyMonthlyCodeMapper;
import com.ruoyi.carrier.service.CompanyMonthlyCodeService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Chen
 * @email <EMAIL>
 * @date 2020/12/24 11:31
 */
@Slf4j
@Service
public class CompanyMonthlyCodeServiceImpl implements CompanyMonthlyCodeService {

    @Autowired
    private CompanyMonthlyCodeMapper companyMonthlyCodeMapper;
    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    /**
     * 获取月结码列表
     * @param companyMonthlyCodeBO
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public AjaxResult getMonthlyCodeList(CompanyMonthlyCodeBO companyMonthlyCodeBO) {
        Long companyId = SecurityUtils.getLoginUser().getUser().getCompanyId();
        companyMonthlyCodeBO.setCompanyId(companyId);
        List<CompanyMonthlyCodeVO> list = companyMonthlyCodeMapper.getMonthlyCodeList(companyMonthlyCodeBO);
        return AjaxResult.success(list);
    }

    /**
     * 批量生成月结码
     * @param companyMonthlyCodeBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult createMonthlyCode(CompanyMonthlyCodeBO companyMonthlyCodeBO) {
        Long companyId = SecurityUtils.getLoginUser().getUser().getCompanyId();
        Integer codeNumbers = companyMonthlyCodeBO.getCodeNumbers();
        companyMonthlyCodeBO.setCompanyId(companyId);
        companyMonthlyCodeBO.setIsUsed("0");
        List<CompanyMonthlyCodeVO> list = companyMonthlyCodeMapper.getMonthlyCodeList(companyMonthlyCodeBO);
        List<String> codeList = getRandomCode(codeNumbers, list);
        codeList.forEach(item->{
            CompanyMonthlyCode companyMonthlyCode = new CompanyMonthlyCode();
            companyMonthlyCode.setCompanyId(companyId);
            companyMonthlyCode.setMonthlyCode(item);
            companyMonthlyCode.setCreateTime(DateUtils.getNowDate());
            companyMonthlyCodeMapper.insert(companyMonthlyCode);
        });
        return AjaxResult.success();
    }

    /**
     * 改变月结码状态
     * @param companyMonthlyCodeBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult updateMonthlyCode(CompanyMonthlyCodeBO companyMonthlyCodeBO) {

        UpdateWrapper<CompanyMonthlyCode> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("company_id", companyMonthlyCodeBO.getCompanyId())
                .eq("monthly_code", companyMonthlyCodeBO.getMonthlyCode());

        // 判断该月结码是否被使用
        CompanyMonthlyCode code = companyMonthlyCodeMapper.selectOne(updateWrapper);
        if (StringUtils.isNull(code)) {
            log.info("月结码"+companyMonthlyCodeBO.getMonthlyCode()+"已被使用");
            return AjaxResult.error("月结码"+companyMonthlyCodeBO.getMonthlyCode()+"已被使用");
        }

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        SysUserBarge userBarge = sysUserBargeMapper.selectOne(new QueryWrapper<SysUserBarge>().eq("USER_ID", sysUser.getUserId()));

        CompanyMonthlyCode monthlyCode = new CompanyMonthlyCode();
        monthlyCode.setCompanyId(companyMonthlyCodeBO.getCompanyId());
        monthlyCode.setMonthlyCode(companyMonthlyCodeBO.getMonthlyCode());
        monthlyCode.setUseUserId(sysUser.getUserId());
        monthlyCode.setUseCompanyId(sysUser.getCompanyId());
        monthlyCode.setUsedTime(DateUtils.getNowDate());
        monthlyCode.setIsUsed("1");
        assert userBarge != null;
        monthlyCode.setUseBargeId(userBarge.getBargeId());

        int result = companyMonthlyCodeMapper.updateCode(monthlyCode);
        if (result <= 0) {
            log.error("月结码"+companyMonthlyCodeBO.getMonthlyCode()+"已被使用");
            return AjaxResult.error("月结码"+companyMonthlyCodeBO.getMonthlyCode()+"已被使用");
        }
        return AjaxResult.success();
    }

    /**
     * 随机生成6位月结码
     * @param codeNumbers 生成个数
     * @param codeList 该公司现有未使用的月结码
     * @return 6位月结码
     */
    public static List<String> getRandomCode(int codeNumbers, List<CompanyMonthlyCodeVO> codeList) {
        Set<String> set = new HashSet<>();
        codeList.forEach(item -> set.add(item.getMonthlyCode()));
        List<String> newList = new ArrayList<>();
        for (int i = 0; i < codeNumbers; i++) {
            String num = RandomUtil.randomNumbers(6);
            set.add(num);
            if ((set.size() - codeList.size()) == i) {
                i = i-1;
            } else {
                newList.add(num);
            }
        }
        return newList;
    }

}

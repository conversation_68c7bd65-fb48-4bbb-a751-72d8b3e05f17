package com.ruoyi.consignor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.barge.domain.bo.BargeUserBO;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.carrier.domain.CompanyMonthlyCode;
import com.ruoyi.carrier.mapper.CompanyMonthlyCodeMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.*;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.bo.CargocmentBO;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.bo.CoutformBO;
import com.ruoyi.common.domain.dto.CargocmentDTO;
import com.ruoyi.common.domain.vo.*;
import com.ruoyi.common.enums.*;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.*;
import com.ruoyi.common.service.CargocmentService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.consignor.domain.vo.ConsignorConsignVO;
import com.ruoyi.consignor.service.ConsignorConsignService;
import com.ruoyi.databarge.domain.Pb1Customer;
import com.ruoyi.databarge.domain.Pb3Coutform;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.domain.vo.PortLoadingMsgVO;
import com.ruoyi.databarge.mapper.Pb1CustomerMapper;
import com.ruoyi.databarge.mapper.Pb6BargeCheckMessageMapper;
import com.ruoyi.databarge.mapper.ShipFddUserRelMapper;
import com.ruoyi.databarge.service.Pb3CoutformService;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 货主的托运业务层实现类
 * <AUTHOR>
 * @Date 2020/7/30  10:56
 */

@Service
@Slf4j
public class ConsignorConsignServiceImpl implements ConsignorConsignService {

    @Autowired
    private UploadAddressMapper uploadAddressMapper;

    @Autowired
    private BargeInfoMapper bargeInfoMapper;

    @Autowired
    private CargocmentMapper cargocmentMapper;

    @Autowired
    private CargocmentdetailMapper cargocmentdetailMapper;

    @Autowired
    private CoutformMapper coutformMapper;

    @Autowired
    private CargocmentService cargocmentService;

    @Autowired
    private CargoconsignmentdetailBakMapper cargoconsignmentdetailBakMapper;

    @Autowired
    private CargoconsignmentBakMapper cargoconsignmentBakMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private WaterwayCargoMapper waterwayCargoMapper;

    @Value("${dataLocal.cargo}")
    private String dataLocal;


    @Autowired
    private BargeCenterService bargeCenterService;

    @Autowired
    private BargeCheckNoteMapper bargeCheckNoteMapper;

    @Autowired
    private BargeCheckMessageMapper bargeCheckMessageMapper;

    @Autowired
    private CompanyMonthlyCodeMapper companyMonthlyCodeMapper;

    @Autowired
    private BargeCompanyLinkMapper bargeCompanyLinkMapper;

    @Autowired
    private BargeCompanyMapper bargeCompanyMapper;

    @Autowired
    private Pb1CustomerMapper pb1CustomerMapper;

    @Autowired
    private Pb6BargeCheckMessageMapper pb6BargeCheckMessageMapper;

    @Autowired
    private Pb3CoutformService pb3CoutformService;

    @Autowired
    private ShipFddUserRelMapper shipFddUserRelMapper;

    /**
     * 查询托运单列表
     *
     * @param status
     * @return
     */
    @Override
    public List<ConsignorConsignVO> selectConsignByStatus(Long status, String searchValue, String beginTime, String endTime) {

        // status: null.全部， 0.待审核， 4.进行中， 5.已完成， 11.差额退款审批， 1.待支付/预约，2.待确认， 10.待报到

        List<ConsignorConsignVO> cargoconsignmentDifferentStatusData = new ArrayList<>();
        // 获取当前的用户
        SysUser user = SecurityUtils.getLoginUser().getUser();

        if (StringUtils.isEmpty(user.getCompanyName())) {
            if (StringUtils.isNull(user.getCompanyId())) {
                throw new CustomException("当前用户没有所属公司");
            } else {
                Customer customer = customerMapper.selectById(user.getCompanyId());
                if (StringUtils.isEmpty(customer.getCFullName())) {
                    throw new CustomException("当前用户所属公司没有公司名称");
                } else {
                    user.setCompanyName(customer.getCFullName());
                }
            }
        }

        cargoconsignmentDifferentStatusData = cargocmentdetailMapper.waybillList(status, searchValue, user.getCompanyName(), user.getUserId(), beginTime, endTime);

        if (Long.valueOf(0).equals(status)) {
            // 去重
            cargoconsignmentDifferentStatusData = cargoconsignmentDifferentStatusData.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(ConsignorConsignVO::getConsignFlag))), ArrayList::new));
            cargoconsignmentDifferentStatusData = cargoconsignmentDifferentStatusData.stream().sorted(Comparator.comparing(ConsignorConsignVO::getConsignFlag).reversed()).collect(Collectors.toList());
        }

        return cargoconsignmentDifferentStatusData;
    }


    /**
     * 托运单详情
     *
     * @param consignId 托运单主键id
     * @return
     */
    @Override
    public CargocmentBO selectConsignById(Long consignId) {

        if (consignId == null) {
            throw new CustomException("托运单id不能为空");
        }
        Cargoconsignment cargoconsignment = cargocmentMapper.selectById(consignId);
        List<Cargoconsignmentdetail> cargoconsignmentdetails = cargocmentdetailMapper.selectList(new LambdaQueryWrapper<Cargoconsignmentdetail>()
                .eq(Cargoconsignmentdetail::getConsignId, consignId));
        ArrayList<BargeInfoBO> bargeInfoList = new ArrayList<>();
        for (Cargoconsignmentdetail cargoconsignmentdetail : cargoconsignmentdetails) {
            BargeInfoBO bargeInfo = new BargeInfoBO();
            bargeInfo.setCargoConsignmentDetailId(cargoconsignmentdetail.getId());
            bargeInfo.setId(cargoconsignmentdetail.getBargeId());
            bargeInfo.setBargeName(cargoconsignmentdetail.getBargeName());
            bargeInfo.setRationWeight(cargoconsignmentdetail.getRationWeight());
            bargeInfo.setWxRationContactNumber(cargoconsignmentdetail.getWxRationContactNumber());
            bargeInfo.setContactPhone(cargoconsignmentdetail.getBargeTel());
            bargeInfo.setCheckState(Integer.valueOf(cargoconsignmentdetail.getFlagBargeState()));
            List<BargeInfoVO> bargeInfoVOS = bargeInfoMapper.searchBarge(bargeInfo.getBargeName());
            bargeInfo.setBargeLoadA(StringUtils.isNotEmpty(bargeInfoVOS)?bargeInfoVOS.get(0).getBargeLoadA():null);
            bargeInfo.setBargeLoadB(StringUtils.isNotEmpty(bargeInfoVOS)?bargeInfoVOS.get(0).getBargeLoadB():null);
            bargeInfoList.add(bargeInfo);
        }
        // 获取关联的文件
        List<UploadAddress> uploadAddresses = uploadAddressMapper.selectList(new LambdaQueryWrapper<UploadAddress>()
                .eq(UploadAddress::getLinkId, consignId)
                .eq(UploadAddress::getLinkType, UploadDataType.CONSIGN_DATA.getType())
                .eq(UploadAddress::getStatus, 1)
                .eq(UploadAddress::getBakFlag, 1));
        CargocmentBO cargocmentBO = new CargocmentBO();

        Coutform coutform = coutformMapper.selectOne(new LambdaQueryWrapper<Coutform>()
                .eq(Coutform::getCoutformId, cargoconsignment.getOutOrInformId()));

        cargocmentBO.setWeightValue(coutform.getWeightValue());
        cargocmentBO.setAmt(coutform.getAmt());
        cargocmentBO.setPieceValue(coutform.getAmt());
        cargocmentBO.setBargeInfos(bargeInfoList);
        cargocmentBO.setCargoconsignment(cargoconsignment);
        List<PubPortVo> pubPortVos = cargocmentMapper.searchEndPort(cargoconsignment.getEndPort());
        cargocmentBO.setNavigatingZone(StringUtils.isNotEmpty(pubPortVos)?pubPortVos.get(0).getNavigatingZone():null);

        // 处理文件url
        uploadAddresses.forEach(uploadAddress -> {
                    if (StringUtils.isNotEmpty(uploadAddress.getUrl())) {
                        String[] split = uploadAddress.getUrl().split("/olorder");
                        uploadAddress.setUrl(dataLocal + split[1]);
                    }
                }
        );
        cargocmentBO.setUploadAddress(uploadAddresses);

        PortLoadingMsgVO portLoadingMsgVO = pb6BargeCheckMessageMapper.searchPortLoadingInfo(cargoconsignment.getWaterwayCargoId());
        if (portLoadingMsgVO != null) {
            cargocmentBO.setTscargoweightValue(portLoadingMsgVO.getTscargoweightValue());
        }

        return cargocmentBO;
    }

    /**
     * 修改托运单信息
     *
     * @param cargocmentBO
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void updateBookingNote(CargocmentBO cargocmentBO) {

        try {
            if (StringUtils.isNull(cargocmentBO) || StringUtils.isNull(cargocmentBO.getCargoconsignment().getId())) {
                throw new CustomException("托运单信息或托运单id不能为空");
            }

            SysUser user = SecurityUtils.getLoginUser().getUser();

            List<Cargoconsignmentdetail> cargoconsignmentdetailList = new ArrayList<>();

            // 获取传送过来的托运单新数据
            Cargoconsignment cargoconsignment = cargocmentBO.getCargoconsignment();

            // 获取传送过来的驳船新数据
            List<BargeInfoBO> bargeInfos = cargocmentBO.getBargeInfos();
            // 数据库查询的数据
            Cargoconsignment cargoconsignmentData = cargocmentMapper.selectById(cargoconsignment.getId());

            // 获取所关联的文件url
            List<UploadAddress> uploadAddresses = uploadAddressMapper.selectList(new LambdaQueryWrapper<UploadAddress>()
                    .eq(UploadAddress::getLinkId, cargoconsignment.getId())
                    .eq(UploadAddress::getLinkType, UploadDataType.CONSIGN_DATA.getType())
                    .eq(UploadAddress::getStatus, 1)
                    .eq(UploadAddress::getBakFlag, 1));
            // 获取文件id
            List<Long> uploadAddressIds = uploadAddresses.stream().map(UploadAddress::getId).collect(Collectors.toList());

            // 获取托运单明细数据
            List<Cargoconsignmentdetail> cargoconsignmentdetails = cargocmentdetailMapper.selectList(new LambdaQueryWrapper<Cargoconsignmentdetail>()
                    .eq(Cargoconsignmentdetail::getConsignId, cargoconsignment.getId()));
            // 获取托运单明细ids
            List<Long> detailIds = cargoconsignmentdetails.stream().map(Cargoconsignmentdetail::getId).collect(Collectors.toList());


            // 判断新数据是否是自己派船还是船公司派船
            if (cargoconsignment.getWxSelectShipType().equals(SelectShipType.SELECTSELF.getCode())) {
                // 自己派船

                // 获取托运单明细数据 （判断是否审核前重查一次）
                List<Cargoconsignmentdetail> newCargoconsignmentdetails = cargocmentdetailMapper.selectList(new LambdaQueryWrapper<Cargoconsignmentdetail>()
                        .eq(Cargoconsignmentdetail::getConsignId, cargoconsignment.getId()));
                // 获取已审核的托运单
                List<Cargoconsignmentdetail> flagBargeState = newCargoconsignmentdetails.stream()
                        .filter(cargoconsignmentdetail -> !(cargoconsignmentdetail.getFlagBargeState().equals(FlagBargeState.NO_CHECK.getCode()) || cargoconsignmentdetail.getFlagBargeState().equals(FlagBargeState.CHECK_FAIL.getCode()))).collect(Collectors.toList());

                // 判断是否有已审核的托运单
                if (StringUtils.isEmpty(flagBargeState)) {
                    // 进来 代表明细表没有含有已审核订单 可以修改托运单主表信息
                    if (StringUtils.isNotEmpty(bargeInfos)) {
                        if (StringUtils.isNotEmpty(detailIds)) {
                            // 有新增驳船 全删除明细表订单
                            cargocmentdetailMapper.deleteBatchIds(detailIds);
                        }
                        // 添加托运单明细表
                        cargocmentBO.setFlag(1);
                        cargocmentService.waybillsSelectShips(cargocmentBO);
                        // 修改保存
                        cargoconsignment.setModifyDate(DateUtils.getDate());
                        cargoconsignment.setModifyMan(user.getUserName());
                        cargoconsignment.setModifyManId(user.getUserId());
                        cargoconsignment.setWxCreateCompanyId(user.getCompanyId());
                        cargocmentMapper.updateBookingNoteInfo(cargoconsignment);
                    }
                } else {
                    // 含有已审核订单 不能修改主表信息
                    if (StringUtils.isNotEmpty(bargeInfos)) {

                        // 获取待审核和审核不通过的驳船
                        List<BargeInfoBO> BargeInfoBOs = new ArrayList<>();
                        for (BargeInfoBO bargeInfo : bargeInfos) {
                            if (StringUtils.isNull(bargeInfo.getCheckState())) {
                                BargeInfoBOs.add(bargeInfo);
                            } else {
                                if (bargeInfo.getCheckState().equals(Integer.valueOf(FlagBargeState.NO_CHECK.getCode()))
                                        || bargeInfo.getCheckState().equals(Integer.valueOf(FlagBargeState.CHECK_FAIL.getCode()))) {
                                    BargeInfoBOs.add(bargeInfo);
                                }
                            }
                        }

                        // 获取非审核 托运单明细ids
                        List<Long> newDetailIds = cargoconsignmentdetails.stream().filter(cargoconsignmentdetail -> cargoconsignmentdetail.getFlagBargeState()
                                .equals(FlagBargeState.NO_CHECK.getCode()) || cargoconsignmentdetail.getFlagBargeState()
                                .equals(FlagBargeState.CHECK_FAIL.getCode()))
                                .map(Cargoconsignmentdetail::getId)
                                .collect(Collectors.toList());
                        if (StringUtils.isNotEmpty(newDetailIds)) {
                            // 有新增驳船 全删除明细表订单
                            cargocmentdetailMapper.deleteBatchIds(newDetailIds);
                        }
                        cargocmentBO.setBargeInfos(BargeInfoBOs);
                        cargocmentBO.setFlag(1);
                        cargocmentService.waybillsSelectShips(cargocmentBO);

                    }
                }
            } else {
                // 船公司派船

                // 获取托运单明细数据 （判断是否审核前重查一次）
                List<Cargoconsignmentdetail> newCargoconsignmentdetails = cargocmentdetailMapper.selectList(new LambdaQueryWrapper<Cargoconsignmentdetail>()
                        .eq(Cargoconsignmentdetail::getConsignId, cargoconsignment.getId()));
                // 获取已审核的托运单
                List<Cargoconsignmentdetail> flagBargeState = newCargoconsignmentdetails.stream()
                        .filter(cargoconsignmentdetail -> !(cargoconsignmentdetail.getFlagBargeState().equals(FlagBargeState.NO_CHECK.getCode()) || cargoconsignmentdetail.getFlagBargeState().equals(FlagBargeState.CHECK_FAIL.getCode()))).collect(Collectors.toList());

                // 判断是否有托运单明细数据
                if (StringUtils.isNotEmpty(detailIds)) {

                    // 判断数据库是否有已审核的托运单
                    if (StringUtils.isEmpty(flagBargeState)) {
                        // 判断数据库中的订单是自己派船还是船公司派船 （自己派船就删除掉全部驳船信息）
                        if (cargoconsignmentData.getWxSelectShipType().equals(SelectShipType.SELECTSELF.getCode())) {
                            // 没有已审核托运单  全删除明细表订单
                            cargocmentdetailMapper.deleteBatchIds(detailIds);
                        }
                        cargoconsignment.setModifyDate(DateUtils.getDate());
                        cargoconsignment.setModifyMan(user.getUserName());
                        cargoconsignment.setModifyManId(user.getUserId());
                        cargoconsignment.setWxCreateCompanyId(null);
                        cargocmentMapper.updateBookingNoteInfo(cargoconsignment);
                    } else {
                        // 有已审核的托运单 （不用处理，主表信息不可更改，驳船信息不可更改）

                    }
                } else {
                    cargoconsignment.setModifyDate(DateUtils.getDate());
                    cargoconsignment.setModifyMan(user.getUserName());
                    cargoconsignment.setModifyManId(user.getUserId());
                    cargoconsignment.setWxCreateCompanyId(null);
                    cargocmentMapper.updateBookingNoteInfo(cargoconsignment);
                }
            }

            // 文件的失效与启用
            fileStartup(cargocmentBO, cargoconsignment, cargoconsignmentData, uploadAddressIds, cargoconsignment.getId());


            log.info("修改托运单信息成功！");
        } catch (CustomException e) {
            log.error("修改托运单信息失败！"+e.getMessage());
            throw new CustomException("修改托运单信息失败！");
        }
    }

    /**
     *   文件的失效与启用
     * @param cargocmentBO
     * @param cargoconsignment  前端传递的新数据
     * @param cargoconsignmentData  数据库查询的数据
     * @param uploadAddressIds  所关联的文件Ids
     */
    private void fileStartup(CargocmentBO cargocmentBO, Cargoconsignment cargoconsignment, Cargoconsignment cargoconsignmentData, List<Long> uploadAddressIds, Long linkId) {

        // 获取新文件数据
        List<UploadAddress> newUploadAddress = cargocmentBO.getUploadAddress();
        List<Long> newFileIds = null;
        if (StringUtils.isNotEmpty(newUploadAddress)) {
            // 收集文件id
            newFileIds = newUploadAddress.stream().map(UploadAddress::getId).collect(Collectors.toList());
        }

        // 新数据与旧数据都是他司办理才进来
        if (Long.valueOf(cargoconsignment.getWxOutorInformType()).equals(Long.valueOf(CoutformType.OTHER_COMPANY_DISPOSE.getCode()))
                && Long.valueOf(cargoconsignmentData.getWxOutorInformType()).equals(Long.valueOf(CoutformType.OTHER_COMPANY_DISPOSE.getCode()))) {
            // 切割旧文件id
            String fileId = cargocmentBO.getFileIds();
            String[] split = fileId.split(",");
            List<Long> ids = new ArrayList<>();
            for (String s : split) {
                ids.add(Long.valueOf(s));
            }

            Collections.sort(ids);
            Collections.sort(newFileIds);

            // 判断办理他司是否有重新上传文件
            if (!ids.equals(newFileIds)) {
                UploadAddress upload = new UploadAddress();
                upload.setStatus(0); // 改成失效
                int updateBefore = uploadAddressMapper.update(upload, new LambdaQueryWrapper<UploadAddress>()
                        .in(UploadAddress::getId, uploadAddressIds));
                upload.setStatus(1); // 改成生效
                upload.setLinkId(linkId); // 关联id
                int updateAfter = uploadAddressMapper.update(upload, new LambdaQueryWrapper<UploadAddress>()
                        .in(UploadAddress::getId, newFileIds));
                if (updateBefore <= 0 || updateAfter <= 0) {
                    throw new CustomException("从他司办理选择他司办理操作失败！");
                }
            }
            // 从他司办理选择本司办理
        } else if (Long.valueOf(cargoconsignmentData.getWxOutorInformType()) > Long.valueOf(cargoconsignment.getWxOutorInformType())) {
            UploadAddress uploadAddress = new UploadAddress();
            uploadAddress.setStatus(0); // 改成失效
            // 批量修改文件成失效
            int uploadAddressResult = uploadAddressMapper.update(uploadAddress, new LambdaQueryWrapper<UploadAddress>()
                    .in(UploadAddress::getId, uploadAddressIds));
            if (uploadAddressResult <= 0) {
                throw new CustomException("从他司办理选择本司办理操作失败！");
            }
        } else if (cargoconsignmentData.getWxOutorInformType().equals(CoutformType.COMPANY_DISPOSE.getCode()) && cargoconsignment.getWxOutorInformType().equals(CoutformType.OTHER_COMPANY_DISPOSE.getCode())) {
            // 从本司转到他司
            UploadAddress uploadAddress = new UploadAddress();
            uploadAddress.setLinkId(linkId); // 关联id
            // 批量修改文件关联id
            int uploadAddressResult = uploadAddressMapper.update(uploadAddress, new LambdaQueryWrapper<UploadAddress>()
                    .in(UploadAddress::getId, newFileIds));
            if (uploadAddressResult <= 0) {
                throw new CustomException("从本司办理选择他司办理操作失败！");
            }
        }

    }

    /**
     * 办理托运单
     *
     * @param cargocmentBO
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void transactBookingNote(CargocmentBO cargocmentBO) {

        try {

            Cargoconsignment cargoconsignment = cargocmentBO.getCargoconsignment();
            SysUser user = SecurityUtils.getLoginUser().getUser();
            long comid = cargocmentBO.getCargoconsignment().getComId() == null ? 1 : cargocmentBO.getCargoconsignment().getComId();

            // 保存信息到托运单主表
            cargoconsignment.setApplyDate(DateUtils.getTime()); // 申请日期
            cargoconsignment.setComId(comid); // 公司Id
            cargoconsignment.setShipperDept(user.getCompanyName()); // 托运人公司，和consigner一致
            cargoconsignment.setOnineresourse("3"); // 办理方式（1为网页端，2为app 端, 3为小程序）
            cargoconsignment.setWxCreateUserById(user.getUserId().intValue()); // 创建人id
            cargoconsignment.setWxCreateTime(DateUtils.getTime()); // 创建时间
            cargoconsignment.setWxCreateCompanyId(user.getCompanyId());

            String consignflag = cargocmentService.getNewSerialNo("TD", comid, DateUtils.dateTimeNow("yyMM"), 4);

             cargoconsignment.setConsignFlag(consignflag); // 托运单号

            int cargoconsignmentResult = cargocmentMapper.saveCargocment(cargoconsignment);

            if (cargoconsignmentResult <= 0) {
                // 重试一次
                String newConsignflag = cargocmentService.getNewSerialNo("TD", comid, DateUtils.dateTimeNow("yyMM"), 4);
                cargoconsignment.setConsignFlag(newConsignflag); // 新托运单号
                int newCargoconsignmentResult = cargocmentMapper.saveCargocment(cargoconsignment);
                if (newCargoconsignmentResult <= 0) {
                    log.error("托号已存在，请重新提交！");
                    throw new CustomException("托号已存在，请重新提交！");
                }
            }

            if (cargoconsignment.getWxOutorInformType().equals(CoutformType.OTHER_COMPANY_DISPOSE.getCode())) {
                // 设置文件关联id
                List<UploadAddress> uploadAddress = cargocmentBO.getUploadAddress();
                List<Long> uploadIds = uploadAddress.stream().map(UploadAddress::getId).collect(Collectors.toList());
                UploadAddress upload = new UploadAddress();
                upload.setLinkId(cargoconsignment.getId());
                int uploadResult = uploadAddressMapper.update(upload, new LambdaQueryWrapper<UploadAddress>()
                        .in(UploadAddress::getId, uploadIds));
                if (uploadResult <= 0) {
                    log.error("托运单相关文件修改关联失败！");
                    throw new CustomException("托运单相关文件修改关联失败");
                }
            }

            // 添加驳船信息 同时生成托运单明细
            if (StringUtils.isNotEmpty(cargocmentBO.getBargeInfos())) {
                cargocmentBO.setFlag(1);
                List<BargeInfoBO> bargeInfos = cargocmentBO.getBargeInfos();
                for (int i = 0; i < bargeInfos.size(); i++) {
                    bargeInfos.get(i).setSerialNumber(String.valueOf(i+1));
                }
                cargocmentService.waybillsSelectShips(cargocmentBO);
            }

            log.info("办理托运单成功！");
        } catch (Exception e) {
            throw new CustomException("办理托运单失败");
        }

    }


    /**
     * 托运单-改单/退单
     *
     * @param cargocmentDTO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void modificationBookingNote(CargocmentDTO cargocmentDTO) {

        if (StringUtils.isNull(cargocmentDTO) || StringUtils.isNull(cargocmentDTO.getCargoconsignment().getId())|| StringUtils.isNull(cargocmentDTO.getCargoconsignmentdetail().getId())) {
            throw new CustomException("改单/退单数据或者id不能为空");
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 取出数据
        Cargoconsignmentdetail detail = cargocmentDTO.getCargoconsignmentdetail();
        Cargoconsignment cargoconsignment = cargocmentDTO.getCargoconsignment();

        // 查出明细表
        Cargoconsignmentdetail newCargoconsignmentdetail = cargocmentdetailMapper.selectById(detail.getId());
        Cargoconsignment newCargoconsignment = cargocmentMapper.selectById(cargoconsignment.getId());
        log.info(String.valueOf(detail));
        log.warn("ConsignorConsignServiceImpl - modificationBookingNote - 水路运单编号：{} - 入参 - 退改单原因modifyReason：{}", newCargoconsignmentdetail.getWaterwayCargoId(), detail.getModifyreason());

        // 新建明细实体
        Cargoconsignmentdetail cargoconsignmentdetail = new Cargoconsignmentdetail();
        // 记录实体
        BargeCheckNode bargeCheckNote = new BargeCheckNode();
        BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();


        // 判断是退单审核
        if (cargocmentDTO.getState().equals(CheckStatusEnum.CHARGEBACK_CHECK.getCode())) {
            cargoconsignmentdetail.setId(detail.getId());
            cargoconsignmentdetail.setApplyModify(BookingNoteStatusEnum.CHARGEBACK_ING_CHECK.getCode().toString());
            cargoconsignmentdetail.setWxNode(WxNodeEnum.SUBMIT_AUDIT.getCode());
            cargoconsignmentdetail.setModifyreason(detail.getModifyreason());
            bargeCheckNote.setWxNodeFlag(0);
            bargeCheckNote.setWxNode(0);

            if (newCargoconsignmentdetail.getFlagBargeState().equals(FlagBargeState.REPORT.getCode())) {
                bargeCheckMessage.setMType(BargeCheckMessageType.CHARGEBACK_DOCK_AUDIT.getCode().shortValue());
            } else {
                bargeCheckMessage.setMType(BargeCheckMessageType.RETURNED_ORDER_LOGISTICS_COMPANY_CHECK.getCode().shortValue());
            }
        }

        // 判断是改单审核
        if (cargocmentDTO.getState().equals(CheckStatusEnum.MODIFICATION_CHECK.getCode())) {
            //2021.11.25 jinn 改单只能改同公司的出库单
            if(!cargoconsignment.getOutOrInformId().equals(newCargoconsignment.getOutOrInformId())){
                Pb3Coutform pb3Coutform = pb3CoutformService.findOneByCoutformId(cargoconsignment.getOutOrInformId());
                Pb3Coutform pb3Coutform1 = pb3CoutformService.findOneByCoutformId(newCargoconsignment.getOutOrInformId());
                if(!pb3Coutform.getCorporationid().equals(pb3Coutform1.getCorporationid())){

                    throw new CustomException("不允许修改为其他码头的出库单");
                }
            }
            // 获取文件信息
            List<UploadAddress> uploadAddress = cargocmentDTO.getUploadAddress();
            if (StringUtils.isNotEmpty(uploadAddress)) {
                // 文件不为空则进来 关联id 设置为备份文件 审核成功才恢复正式文件
                List<Long> ids = uploadAddress.stream().map(UploadAddress::getId).collect(Collectors.toList());

                // 判断是否有原来的文件
                List<UploadAddress> uploadAddresses = uploadAddressMapper.selectBatchIds(ids);
                for (UploadAddress address : uploadAddresses) {
                    if (StringUtils.isNotNull(address.getLinkId())) {
                        address.setId(null);
                        address.setLinkId(cargoconsignment.getId()); // 关联id
                        address.setBakFlag(2); // 改成是备份文件
                        address.setUploadUserId(user.getUserId());
                        address.setUploadTime(new Date());
                        int insert = uploadAddressMapper.insert(address);
                        if (insert <= 0) {
                            log.error("关联文件失败!");
                            throw new CustomException("关联文件失败！");
                        }
                    } else {
                        UploadAddress uploadAddres = new UploadAddress();
                        uploadAddres.setId(address.getId()); // 关联id
                        uploadAddres.setLinkId(cargoconsignment.getId()); // 关联id
                        uploadAddres.setBakFlag(2); // 改成是备份文件
                        int insert = uploadAddressMapper.updateById(uploadAddres);
                        if (insert <= 0) {
                            log.error("关联文件失败!");
                            throw new CustomException("关联文件失败！");
                        }
                    }
                }
            }

            // 备份托运单主表实体
            CargoconsignmentBak cargoconsignmentBak = new CargoconsignmentBak();
            // 备份托运单明细表实体
            CargoconsignmentdetailBak cargoconsignmentdetailBak = new CargoconsignmentdetailBak();

            // 保存备份托运单明细表
            BeanUtils.copyPropertiesIgnoreNull(detail, newCargoconsignmentdetail);
            BeanUtils.copyPropertiesIgnoreNull(newCargoconsignmentdetail, cargoconsignmentdetailBak);
            // 添加修改信息
            cargoconsignmentdetailBak.setWxUpdateById(user.getUserId());
            cargoconsignmentdetailBak.setWxUpdateTime(DateUtils.getTime());
            CargoconsignmentdetailBak cargoconsignmentdetailBak1 = cargoconsignmentdetailBakMapper.selectById(cargoconsignmentdetailBak.getId());

            // 判断是已报到还是已审核或已配载进度
            if (newCargoconsignmentdetail.getFlagBargeState().equals(FlagBargeState.REPORT.getCode())) {
                bargeCheckMessage.setMType(BargeCheckMessageType.CHANGE_DOCK_AUDIT.getCode().shortValue());
                cargoconsignmentdetailBak.setWxNode(WxNodeEnum.DIRECT_AUDIT_BY_LOGISTICS_COMPANY.getCode());
                cargoconsignmentdetailBak.setAuditStates(CheckEnum.WAIT_CHECK.getCode());
                cargoconsignmentdetail.setWxNode(WxNodeEnum.DIRECT_AUDIT_BY_LOGISTICS_COMPANY.getCode());
                cargoconsignmentdetail.setAuditStates(CheckEnum.WAIT_CHECK.getCode());
                bargeCheckNote.setWxNode(WxNodeEnum.DIRECT_AUDIT_BY_LOGISTICS_COMPANY.getCode());

            } else {
                bargeCheckMessage.setMType(BargeCheckMessageType.CHANGE_LOGISTICS_COMPANY_AUDIT.getCode().shortValue());
                cargoconsignmentdetailBak.setWxNode(WxNodeEnum.SUBMIT_AUDIT.getCode());
                cargoconsignmentdetailBak.setAuditStates(CheckEnum.WAIT_CHECK.getCode());
                cargoconsignmentdetail.setWxNode(WxNodeEnum.SUBMIT_AUDIT.getCode());
                cargoconsignmentdetail.setAuditStates(CheckEnum.WAIT_CHECK.getCode());
                bargeCheckNote.setWxNode(WxNodeEnum.SUBMIT_AUDIT.getCode());
            }

            // 如果是已审核的状态 修改驳船主电话（覆盖PB6_BARGEINFO表的驳船电话）
            if (FlagBargeState.YES_CHECK.getCode().equals(newCargoconsignmentdetail.getFlagBargeState())) {
                    if (newCargoconsignmentdetail.getBargeId() != null && StringUtils.isNotEmpty(newCargoconsignmentdetail.getBargeTel())) {
                        BargeInfo bargeInfoTemp = new BargeInfo();
                        bargeInfoTemp.setId(newCargoconsignmentdetail.getBargeId());
                        bargeInfoTemp.setContactPhone(newCargoconsignmentdetail.getBargeTel());
                        bargeInfoMapper.updateById(bargeInfoTemp);
                    }
            }

                if (StringUtils.isNotNull(cargoconsignmentdetailBak1)) {
                cargoconsignmentdetailBakMapper.updateById(cargoconsignmentdetailBak);
            } else {
                cargoconsignmentdetailBakMapper.insert(cargoconsignmentdetailBak);
            }

            // 保存备份托运单主表
            BeanUtils.copyPropertiesIgnoreNull(cargoconsignment, newCargoconsignment);
            BeanUtils.copyPropertiesIgnoreNull(newCargoconsignment, cargoconsignmentBak);
            if(!cargoconsignmentBak.getEndPort().contains("其他")){
                cargoconsignmentBak.setOtherCoastalInlandPort("");
            }
            CargoconsignmentBak cargoconsignmentBak1 = cargoconsignmentBakMapper.selectById(cargoconsignmentBak.getId());
            if (StringUtils.isNotNull(cargoconsignmentBak1)) {
                cargoconsignmentBakMapper.updateById(cargoconsignmentBak);
            } else {
                cargoconsignmentBakMapper.insert(cargoconsignmentBak);
            }

            // 设置改单申请
            cargoconsignmentdetail.setId(detail.getId());
            cargoconsignmentdetail.setApplyModify(BookingNoteStatusEnum.MODIFICATION_ING_CHECK.getCode().toString());
            cargoconsignmentdetail.setModifyreason(detail.getModifyreason());
            // 保存操作记录
            bargeCheckNote.setWxNodeFlag(1);
        }
        cargoconsignmentdetail.setNewWaybill("0");

        cargoconsignmentdetail.setModifyreason(detail.getModifyreason());
        log.warn(String.valueOf(cargoconsignmentdetail));
        log.info("ConsignorConsignServiceImpl - modificationBookingNote - 水路运单编号：{} - 更新前 - 退改单原因modifyreason：{}",
                cargoconsignmentdetail.getWaterwayCargoId(), cargoconsignmentdetail.getModifyreason());
        cargocmentdetailMapper.updateById(cargoconsignmentdetail);

        //  保存改单/退单 BargeCheckNote表操作记录
        bargeCheckNote.setWxApplyMan(user.getUserName());
        bargeCheckNote.setWxApplyTime(DateUtils.getTime());
        bargeCheckNote.setAuditStates(Integer.valueOf(FlagBargeState.NO_CHECK.getCode()));
        bargeCheckNote.setLinkId(detail.getId());
        int insert = bargeCheckNoteMapper.insert(bargeCheckNote);

        //  保存改单/退单 BargeCheckMessage表操作记录
        bargeCheckMessage.setApplyManId(user.getUserId());
        bargeCheckMessage.setApplyMan(user.getUserName());
        bargeCheckMessage.setApplyTime(new Date());
        bargeCheckMessage.setAuditFlag(Short.valueOf(MessageIsRead.UNREAD.getCode()));
        bargeCheckMessage.setBargeName(newCargoconsignmentdetail.getBargeName());
        bargeCheckMessage.setWaterWayCargoId(newCargoconsignmentdetail.getWaterwayCargoId());
        bargeCheckMessage.setPb6CargoConsignmentDetailId(newCargoconsignmentdetail.getId());
        bargeCheckMessage.setConsignFlag(newCargoconsignmentdetail.getConsignFlag());
        int insert1 = bargeCheckMessageMapper.insert(bargeCheckMessage);

        Cargoconsignmentdetail logDetail = cargocmentdetailMapper.selectById(cargoconsignmentdetail.getId());
        log.warn(String.valueOf(logDetail));
        if (StringUtils.isNotNull(logDetail)) {
            log.info("ConsignorConsignServiceImpl - modificationBookingNote - 水路运单编号：{} - 更新后 - 退改单原因modifyReason：{}",
                    logDetail.getWaterwayCargoId(), logDetail.getModifyreason());
        }


        if (insert <= 0 && insert1 <= 0) {
            log.info("保存改单/退单 操作记录失败");
            throw new CustomException("保存改单/退单 操作记录失败");
        }
        log.info("改单/退单操作成功");

    }



    /**
     * 匹配出库单
     *
     * @param coutformBO
     * @return
     */
    @Override
    public List<CoutformVO> selectCoutformByCoutformId(CoutformBO coutformBO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        coutformBO.setCompanyId(user.getCompanyId());
        List<CoutformVO> newCoutformList = new ArrayList<>();

        //List<CoutformVO> vo = new ArrayList<>();

        if (StringUtils.isBlank(coutformBO.getWxOutorinformType())) {
            coutformBO.setWxOutorinformType(CoutformType.COMPANY_DISPOSE.getCode().toString());
        }

        // 他司办理 没有输入出库单返回空
        if (coutformBO.getWxOutorinformType().equals(CoutformType.OTHER_COMPANY_DISPOSE.getCode().toString()) && StringUtils.isEmpty(coutformBO.getCoutformId())) {
            return newCoutformList;
        }

        // 查询出库单
        List<CoutformVO> coutformList = coutformMapper.findCoutform(coutformBO);

        /*if (StringUtils.isNotEmpty(coutformList)) {
            // 同个出库单，组合地磅单号
            *//*coutformList.parallelStream().collect(Collectors.groupingBy(o -> (o.getId()),
                    Collectors.toList())).forEach(
                    (id, temp) -> {
                        temp.stream().reduce((cout1, cout2) -> new CoutformVO(cout1.getId(),
                                cout1.getCoutformId(), cout1.getConsigneeId(), cout1.getConsignee()
                                , cout1.getUnblId(), cout1.getWeightValue(), cout1.getCargoName()
                                , cout1.getPackageType(), (cout1.getFormloadometerId() + "," + cout2.getFormloadometerId()), cout1.getCorporationId()
                        )).ifPresent(newCoutformList::add);
                    }
            );*//*

        } else {
            return vo;
        }

        if (StringUtils.isNotEmpty(newCoutformList)) {
            // 倒叙
            vo = newCoutformList.stream().sorted(Comparator.comparing(CoutformVO::getId).reversed()).collect(Collectors.toList());
        }*/
        coutformList.forEach(item->{
            List<String> list = item.getFormloadometerIdList();
            if (StringUtils.isNotNull(list)) {
                item.setFormloadometerId(CollUtil.join(list,","));
            }
        });

        return coutformList;
    }

    /**
     *  查询历史收货人
     * @return
     */
    @Override
    public List<String> consignee() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 查询历史收货人
        List<Cargoconsignment> cargoconsignments = cargocmentMapper.selectList(new LambdaQueryWrapper<Cargoconsignment>()
                .select(Cargoconsignment::getConsignee)
                .eq(Cargoconsignment::getWxCreateUserById, user.getUserId()));

        List<Cargoconsignment> collect = cargoconsignments.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                        new TreeSet<>(Comparator.comparing(Cargoconsignment::getConsignee))), ArrayList::new));

        List<String> consigneeLists = collect.stream()
                .map(Cargoconsignment::getConsignee)
                .collect(Collectors.toList());

        return consigneeLists;

    }



    /**
     * 匹配船公司
     *
     * @param shipCompanyName
     * @return
     */
    @Override
    public List<Customer> selectShipCompanyByShipCompanyName(String shipCompanyName) {

        List<Customer> customers = customerMapper.selectShipCompany(shipCompanyName);
        return customers;
    }



    /**
     * 查询用户派过的驳船
     *
     * @return
     */
    @Override
    public List<BargeInfo> recordBarge() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();

        List<BargeInfo> bargeCapacityPublishVOS = cargocmentdetailMapper.recordBarge(userId);

        return bargeCapacityPublishVOS;
    }




    /**
     * 搜索驳船
     *
     * @param searchValue
     * @return
     */
    @Override
    public List<BargeInfoVO> searchBarge(String searchValue) {

        List<BargeInfoVO> bargeInfos = bargeInfoMapper.searchBarge(searchValue);

        return bargeInfos;
    }



    /**
     * 搜索目的港
     *
     * @param searchValue
     * @return
     */
    @Override
    public List<PubPortVo> searchEndPort(String searchValue) {

        List<PubPortVo> vo = cargocmentMapper.searchEndPort(searchValue.toUpperCase());

        return vo;
    }


    /**
     *  水路运单详情
     * @param id 托运单明细id
     * @param status 1 改单需要数据 2水路详情
     * @return
     */
    @Override
    public WaterwayCargoVO waybillDetail(Long id, Integer status) {
        WaterwayCargoVO waterwayCargoVO = new WaterwayCargoVO();
        // 封装托运单明细表数据
        Cargoconsignmentdetail cargoconsignmentdetail = cargocmentdetailMapper.selectById(id);
        waterwayCargoVO.setConsignDetailId(cargoconsignmentdetail.getId());
        waterwayCargoVO.setBargeName(cargoconsignmentdetail.getBargeName());
        waterwayCargoVO.setConsignFlag(cargoconsignmentdetail.getConsignFlag());
        waterwayCargoVO.setConsignId(cargoconsignmentdetail.getConsignId());
        waterwayCargoVO.setShippingCoName(cargoconsignmentdetail.getShippingCoName());
        waterwayCargoVO.setRationWeight(cargoconsignmentdetail.getRationWeight());
        waterwayCargoVO.setRationPiece(cargoconsignmentdetail.getRationPiece());
        waterwayCargoVO.setWxRationContactNumber(cargoconsignmentdetail.getWxRationContactNumber());
        waterwayCargoVO.setBargeTel(cargoconsignmentdetail.getBargeTel());
        waterwayCargoVO.setChargeBalanceType(cargoconsignmentdetail.getChargeBalanceType());
        waterwayCargoVO.setWxMonthChargeById(cargoconsignmentdetail.getWxMonthChargeById());
        waterwayCargoVO.setWxMonthChargeByName(cargoconsignmentdetail.getWxMonthChargeByName());
        waterwayCargoVO.setWorkWeight(cargoconsignmentdetail.getWorkWeight());
        waterwayCargoVO.setWxAppointmentTime(cargoconsignmentdetail.getWxAppointmentTime());
        waterwayCargoVO.setFlagBargeState(cargoconsignmentdetail.getFlagBargeState());
        waterwayCargoVO.setBargeId(cargoconsignmentdetail.getBargeId());
        waterwayCargoVO.setWxOperateState(cargoconsignmentdetail.getWxOperateState());
        waterwayCargoVO.setMthApplyCheckTime(cargoconsignmentdetail.getMthApplyCheckTime());

        // 封装托运单主表信息
        Cargoconsignment cargoconsignment = cargocmentMapper.selectById(cargoconsignmentdetail.getConsignId());
        waterwayCargoVO.setApplyDate(cargoconsignment.getApplyDate());
        waterwayCargoVO.setWxOutorInformType(cargoconsignment.getWxOutorInformType());
        waterwayCargoVO.setOutOrInformId(cargoconsignment.getOutOrInformId());
        waterwayCargoVO.setCargeName(cargoconsignment.getCargeName());
        waterwayCargoVO.setConsigner(cargoconsignment.getConsigner());
        waterwayCargoVO.setConsignee(cargoconsignment.getConsignee());
        waterwayCargoVO.setBeginPort(cargoconsignment.getBeginPort());
        waterwayCargoVO.setEndPort(cargoconsignment.getEndPort());
        waterwayCargoVO.setMidPort(cargoconsignment.getMidPort());
        waterwayCargoVO.setShipmentPlace(cargoconsignment.getShipmentPlace());
        waterwayCargoVO.setShipmentunPlace(cargoconsignment.getShipmentunPlace());
        waterwayCargoVO.setLoadometerId(cargoconsignment.getLoadometerId());
        waterwayCargoVO.setConsignmentFlag(cargoconsignment.getConsignmentFlag());
        waterwayCargoVO.setPackageType(cargoconsignment.getPackageType());
        waterwayCargoVO.setSpecialProceeding(cargoconsignment.getSpecialProceeding());
        waterwayCargoVO.setWxSelectShipByName(cargoconsignment.getWxSelectShipByName());
        waterwayCargoVO.setWxSelectShipById(cargoconsignment.getWxSelectShipById());
        waterwayCargoVO.setWxSelectShipState(cargoconsignment.getWxSelectShipState());
        waterwayCargoVO.setWxSelectShipType(cargoconsignment.getWxSelectShipType());
        waterwayCargoVO.setComId(cargoconsignment.getComId());
        waterwayCargoVO.setWxApplyUserType(cargoconsignment.getWxApplyUserType());
        waterwayCargoVO.setOtherCoastalInlandPort(cargoconsignment.getOtherCoastalInlandPort());
        List<PubPortVo> pubPortVos = cargocmentMapper.searchEndPort(cargoconsignment.getEndPort());
        waterwayCargoVO.setNavigatingZone(StringUtils.isNotEmpty(pubPortVos)?pubPortVos.get(0).getNavigatingZone():null);

        // 获取月结码
        if (StringUtils.isNotNull(cargoconsignmentdetail.getMonthlyCodeId())) {
            CompanyMonthlyCode monthlyCode = companyMonthlyCodeMapper.selectById(cargoconsignmentdetail.getMonthlyCodeId());
            waterwayCargoVO.setMonthlyCode(monthlyCode.getMonthlyCode());
        }

        //获取挂靠公司名称
        if (StringUtils.isNotNull(cargoconsignmentdetail.getBargeId())) {
            //2022-09-02 托运单联系人支付获取抬头改为印章的公司
            /*String name = shipFddUserRelMapper.selectTtByBargeId(cargoconsignmentdetail.getBargeId());
            if(org.apache.commons.lang3.StringUtils.isNotBlank(name)){
                waterwayCargoVO.setCompanyName(name);
            }*/
            QueryWrapper<BargeCompanyLink> linkQueryWrapper = new QueryWrapper<>();
            linkQueryWrapper.eq("BARGEID",cargoconsignmentdetail.getBargeId())
                    .eq("STATUS",1)
                    .eq("ISDELETE",1);
            BargeCompanyLink link = bargeCompanyLinkMapper.selectOne(linkQueryWrapper);
            Pb1Customer customer = null;
            if (StringUtils.isNotNull(link) && StringUtils.isNotNull(link.getCompanyId())) {
                customer = pb1CustomerMapper.selectById(link.getCompanyId());
            }
            if (StringUtils.isNotNull(link) &&
                    (StringUtils.isNull(link.getCompanyId()) || StringUtils.isNull(customer))) {
                waterwayCargoVO.setCompanyName(link.getCompanyName());
            }
        }

        // 获取出库单的
        List<Coutform> coutforms = coutformMapper.selectList(new LambdaQueryWrapper<Coutform>()
                .eq(Coutform::getCoutformId, cargoconsignment.getOutOrInformId()));
        if (StringUtils.isNotEmpty(coutforms)) {
            waterwayCargoVO.setWeightValue(coutforms.get(0).getWeightValue());
            waterwayCargoVO.setPieceValue(coutforms.get(0).getAmt());
        }

        // 驳船A、B级取最大
        //退单时修改目的港出现配载重量问题是出现like查询的多条船取的第一个记录，改为=查询

        /** 20220825修改，hxl让改成按托运单明细中的驳船id查询若查不到则按船名查询 **/
        BargeInfo bargeInfo = bargeInfoMapper.selectById(cargoconsignmentdetail.getBargeId());
        BargeInfoVO bargeInfoVOS = new BargeInfoVO();
        if(bargeInfo != null){
            BeanUtils.copyProperties(bargeInfo,bargeInfoVOS);
        }
        else {
            bargeInfoVOS = bargeInfoMapper.searchBargeByName(cargoconsignmentdetail.getBargeName());
        }

        waterwayCargoVO.setBargeLoadA(StringUtils.isNotNull(bargeInfoVOS)?bargeInfoVOS.getBargeLoadA():null);
        waterwayCargoVO.setBargeLoadB(StringUtils.isNotNull(bargeInfoVOS)?bargeInfoVOS.getBargeLoadB():null);


        if (StringUtils.isNotNull(cargoconsignmentdetail.getWaterwayCargoId())) {
            // 封装水路运单表信息
            WaterwayCargo waterwayCargo = waterwayCargoMapper.selectList(new LambdaQueryWrapper<WaterwayCargo>()
                    .eq(WaterwayCargo::getWaterwayCargoId, cargoconsignmentdetail.getWaterwayCargoId()))
                    .get(0);
            waterwayCargoVO.setRecordDate(waterwayCargo.getRecordDate()); // 申请日期
            waterwayCargoVO.setTransportCharge(waterwayCargo.getTransportCharge()); // 运费
            waterwayCargoVO.setCargoPortCharge(waterwayCargo.getCargoPortCharge()); // 货物港务费
            waterwayCargoVO.setBerthCharge(waterwayCargo.getBerthCharge()); // 停泊费
            waterwayCargoVO.setServiceAgentCharge(waterwayCargo.getServiceAgentCharge()); // 围油栏费
            waterwayCargoVO.setBusinessAgentCharge(waterwayCargo.getTotalCharge()); // 代理费
            waterwayCargoVO.setTotalCharge(waterwayCargo.getTotalCharge()); // 总费用
            waterwayCargoVO.setWaterwayCargoId(waterwayCargo.getWaterwayCargoId()); // 水路运单号
        }


        List<UploadAddress> uploadAddresses = new ArrayList<>();
        if (status.equals(1)) {
            // 获取出库单关联的文件
            uploadAddresses = uploadAddressMapper.selectList(new LambdaQueryWrapper<UploadAddress>()
                    .eq(UploadAddress::getLinkId, cargoconsignment.getId())
                    .eq(UploadAddress::getLinkType, UploadDataType.CONSIGN_DATA.getType())
                    .eq(UploadAddress::getStatus, 1)
                    .eq(UploadAddress::getBakFlag, 1));
        } else {
            // 获取水路运单关联的文件
            uploadAddresses = uploadAddressMapper.selectList(new LambdaQueryWrapper<UploadAddress>()
                    .eq(UploadAddress::getLinkId, id)
                    .eq(UploadAddress::getLinkType, UploadDataType.CONSIGN_DATA.getType())
                    .eq(UploadAddress::getStatus, 1)
                    .eq(UploadAddress::getBakFlag, 1));
        }


        // 处理文件url
        uploadAddresses.forEach(uploadAddress -> {
                    if (StringUtils.isNotEmpty(uploadAddress.getUrl())) {
                        String[] split = uploadAddress.getUrl().split("/olorder");
                        uploadAddress.setUrl(dataLocal + split[1]);
                    }
                }
        );
        waterwayCargoVO.setUploadAddress(uploadAddresses);

        PortLoadingMsgVO portLoadingMsgVO = pb6BargeCheckMessageMapper.searchPortLoadingInfo(waterwayCargoVO.getWaterwayCargoId());
        if (portLoadingMsgVO != null) {
            waterwayCargoVO.setTscargoweightValue(portLoadingMsgVO.getTscargoweightValue());
        }

        // 查找驳船挂靠公司名
        BargeCompanyVO bargeCompany = bargeCompanyMapper.queryCompanyByBargeId(cargoconsignmentdetail.getBargeId(), null);
        if (StringUtils.isNotNull(bargeCompany)) {
            waterwayCargoVO.setCompanyName(bargeCompany.getCFullName());
        }

        ShipFddUserRel shipFddUserRel1=shipFddUserRelMapper.selectOne(Wrappers.<ShipFddUserRel>lambdaQuery().
                eq(ShipFddUserRel::getShipId,cargoconsignmentdetail.getBargeId()).eq(ShipFddUserRel::getType,1).eq(ShipFddUserRel::getReviewStatus,1));
        if (shipFddUserRel1!=null){
            waterwayCargoVO.setCompanyName(shipFddUserRel1.getShipCompanyName());
        }else{
            ShipFddUserRel shipFddUserRel=shipFddUserRelMapper.selectOne(Wrappers.<ShipFddUserRel>lambdaQuery().
                    eq(ShipFddUserRel::getShipId,cargoconsignmentdetail.getBargeId()).eq(ShipFddUserRel::getType,4).eq(ShipFddUserRel::getReviewStatus,1));
            if (ObjectUtil.isNotNull(shipFddUserRel)){
                waterwayCargoVO.setCompanyName(shipFddUserRel.getShipCompanyName());
            }
        }

        return waterwayCargoVO;
    }



    /**
     *  删除托运单
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void deleteBookingNote(CargocmentBO cargocmentBO) {

        // 删除标记
        Boolean flag = false;

        // 获取托运单明显表ids
        List<Long> ids = cargocmentBO.getIds();

        Cargoconsignment cargoconsignment = cargocmentBO.getCargoconsignment();


        List<Cargoconsignmentdetail> cargoconsignmentdetail = cargocmentdetailMapper.selectList(new LambdaQueryWrapper<Cargoconsignmentdetail>()
                .eq(Cargoconsignmentdetail::getConsignId, cargoconsignment.getId()));

        // 证明托运单是船公司派船，没有驳船审核
        if (StringUtils.isNotNull(cargoconsignment.getId()) && StringUtils.isEmpty(cargoconsignmentdetail)) {
            cargocmentMapper.deleteById(cargoconsignment.getId());
        } else {
            // 验证是外面点击删除
            if (StringUtils.isNotNull(cargoconsignment.getId()) && StringUtils.isEmpty(ids)) {
                // 遍历是否包含已审核驳船
                if (StringUtils.isNotEmpty(cargoconsignmentdetail)) {
                    for (Cargoconsignmentdetail detail : cargoconsignmentdetail) {
                        // 判断有已审核 修改标志
                        if (detail.getFlagBargeState().equals(FlagBargeState.YES_CHECK.getCode())
                        || detail.getFlagBargeState().equals(FlagBargeState.CHARGEBACK.getCode())
                        || detail.getFlagBargeState().equals(FlagBargeState.ALTER.getCode())) {
                            flag = true;
                        }
                    }
                    if (flag == true) {
                        log.error("托运单删除失败，含有已审核或已改单/已退单驳船");
                        throw new CustomException("托运单删除失败，含有已审核或已改单/已退单驳船");
                    } else {
                        List<Long> collect = cargoconsignmentdetail.stream()
                                .map(Cargoconsignmentdetail::getId)
                                .collect(Collectors.toList());
                        cargocmentdetailMapper.deleteBatchIds(collect);
                        cargocmentMapper.deleteById(cargoconsignment.getId());
                    }
                }

            }
        }

    }


    /**
     *    取消改单/退单操作
     * @param cargocmentdetailBO  stateOperation 1.取消改单, 2.取消退单
     */
    @Override
    public void cancelOperation(CargocmentdetailBO cargocmentdetailBO) {

        if (StringUtils.isNull(cargocmentdetailBO.getStateOperation()) || StringUtils.isNull(cargocmentdetailBO.getId())) {
            log.error("明细单ID 或 (退/改单)取消状态不能为空");
            throw new CustomException("明细单ID 或 (退/改单)取消状态不能为空");
        }


        Cargoconsignmentdetail cargoconsignmentdetail = cargocmentdetailMapper.selectById(cargocmentdetailBO.getId());
        if (StringUtils.isNotNull(cargoconsignmentdetail)) {
            if (cargoconsignmentdetail.getFlagBargeState().equals(FlagBargeState.ALTER.getCode()) || cargoconsignmentdetail.getFlagBargeState().equals(FlagBargeState.CHARGEBACK.getCode())) {
                log.error("托运单已经改单成功或退款成功");
                throw new CustomException("托运单已经改单成功或退款成功");
            }
        }

        // 查询是否已经码头审核过了
        List<BargeCheckMessage> bargeCheckMessages = bargeCheckMessageMapper.selectList(new LambdaQueryWrapper<BargeCheckMessage>()
                .eq(BargeCheckMessage::getPb6CargoConsignmentDetailId, cargocmentdetailBO.getId()));

        for (BargeCheckMessage bargeCheckMessage : bargeCheckMessages) {
            if (Long.valueOf(bargeCheckMessage.getAuditFlag()).equals(1L)) {
                log.error("码头已经审核成功由物流公司审核，不能取消改单/退单");
                throw new CustomException("码头已经审核成功由物流公司审核，不能取消改单/退单");
            }
        }

        // 删除Messages记录
        List<Long> ids = bargeCheckMessages.stream().map(BargeCheckMessage::getId).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(ids)) {
            bargeCheckMessageMapper.deleteBatchIds(ids);
        }

        // 取消改单
        if (cargocmentdetailBO.getStateOperation().equals(CancelOperationEnum.CANCEL_TO_CHANGE_SINGLE.getCode())) {
            cargocmentdetailMapper.cancelOperation(cargocmentdetailBO);
        }

        // 取消退单
        if (cargocmentdetailBO.getStateOperation().equals(CancelOperationEnum.CANCEL_OUT_THE_SINGLE.getCode())) {
            cargocmentdetailMapper.cancelOperation(cargocmentdetailBO);
        }

        // 备份文件改成失效
        List<UploadAddress> uploadAddresses = uploadAddressMapper.selectList(new LambdaQueryWrapper<UploadAddress>()
                .eq(UploadAddress::getLinkId, cargoconsignmentdetail.getConsignId())
                .eq(UploadAddress::getBakFlag, "2"));
        if (StringUtils.isNotEmpty(uploadAddresses)) {
            List<Long> addressIds = uploadAddresses.stream().map(UploadAddress::getId).collect(Collectors.toList());
            UploadAddress uploadAddress = new UploadAddress();
            uploadAddress.setStatus(0);
            int i = uploadAddressMapper.update(uploadAddress, new LambdaQueryWrapper<UploadAddress>()
                    .in(UploadAddress::getId, addressIds));
            if (i <= 0) {
                log.error("改单修改文件失效失败");
                throw new CustomException("改单修改文件失效失败");
            }
        }

        // 记录取消申请的记录
        SysUser user = SecurityUtils.getLoginUser().getUser();
        BargeCheckNode bargeCheckNode = new BargeCheckNode();
        bargeCheckNode.setWxNodeFlag(2);
        bargeCheckNode.setWxApplyAuditMan(user.getUserName());
        bargeCheckNode.setWxApplyAuditTime(DateUtils.getTime());
        bargeCheckNode.setAuditStates(CheckEnum.WAIT_CHECK.getCode());
        bargeCheckNode.setLinkId(cargocmentdetailBO.getId());

        int insert = bargeCheckNoteMapper.insert(bargeCheckNode);


        if (insert <= 0) {
            log.error("(退/改单)取消申请记录保存失败");
            throw new CustomException("(退/改单)取消申请记录保存失败");
        }


        log.info("(退/改单)取消成功");

    }

    /**
     *  修改个人信息
     * @param bargeUserBO
     * @return
     */
    @Override
    public AjaxResult personRecord(BargeUserBO bargeUserBO) {
        return bargeCenterService.personRecord(bargeUserBO);
    }

    @Override
    public AjaxResult recordDetail() {

        Map<String, Object> map = new HashMap<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysUser personRecord = sysUserMapper.selectUserById(user.getUserId());

        map.put("personRecordDetail",personRecord);
        return AjaxResult.success("获取个人详情成功",map);
    }

    /**
     * 查询出库单件数
     * @param outOrInFormId
     * @return
     */
    @Override
    public String checkAmt(String outOrInFormId) {
        return coutformMapper.checkAmt(outOrInFormId);
    }
}

package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.ShipInvoiceHistory;
import com.ruoyi.databarge.domain.dto.ShipInvoiceHistorySearchDTO;
import com.ruoyi.databarge.domain.vo.ShipInvoiceHistoryVO;

/**
 * <p>
 * 发票开票记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
public interface ShipInvoiceHistoryService extends IService<ShipInvoiceHistory> {
    public IPage<ShipInvoiceHistoryVO> searchPage(ShipInvoiceHistorySearchDTO shipInvoiceHistorySearchDTO);
}

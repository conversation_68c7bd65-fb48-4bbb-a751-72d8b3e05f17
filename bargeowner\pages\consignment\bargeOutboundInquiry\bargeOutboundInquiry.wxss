/* pages/consignment/consigndetail/bargeOutboundInquiry.wxss */
.outbount-page{
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
}
.search-area{
  height: 70rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 710rpx;
}
.search-warp{
  position: fixed;
  top: 0rpx;
  left: 0rpx;
  right: 0rpx;
  height: 300rpx;
  background: #ffffff;
  z-index: 100;
  border-bottom: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  padding: 20rpx 0;
}
.search-warp .publish-btn{
  width: 150rpx;
  border: none;
  border-radius: 10rpx;
  height: 60rpx;
  line-height: 60rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
  padding: 0rpx;
  background: #00426B;
  color: #fff;
  font-weight: normal;
}
.search-right{
  margin-left: 10rpx;
}
.search-warp .publish-btn.search-btn {
  width: 120rpx;
}
.search-ipt{
  height: 70rpx;
  line-height: 70rpx;
  flex: 1;
  width: 100%;
  padding: 0 20rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
}
.search-data-box{
  height: 70rpx;
  line-height: 66rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  flex-shrink: 0;
  flex: 1;
}
.padding-text{
  padding: 0 5rpx;
  flex-shrink: 0;
}
.search-date-text{
  height: 100%;
  width: 100%;
  padding: 0 20rpx;
  color: #808080;
}
.search-date-selected{
  height: 100%;
  width: 100%;
  padding: 0 20rpx;
  color: #333;
}
.search-ipt{
  height: 70rpx;
  line-height: 70rpx;
  flex: 1;
  width: 100%;
  padding: 0 20rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
}
.data-list{
  padding: 10rpx 20rpx;
  margin-top: 300rpx;
  background: #f5f5f5;
}
.data-item {
  border: 1px solid #ddd;
  margin-top: 30rpx;
  background: #ffffff;
  border-radius: 10rpx;
}
.data-item-title{
  height: 80rpx;
  border-bottom: 1px solid #ddd;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx;
}
.detail-icon{
  flex-shrink: 0;
  padding: 0 5rpx;
}
.data-item-text{
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

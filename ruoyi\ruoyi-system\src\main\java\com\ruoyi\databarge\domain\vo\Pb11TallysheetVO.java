package com.ruoyi.databarge.domain.vo;


import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Pb11TallysheetVO {
    private String stime;//开始时间
    private String etime;//结束时间
    private String operationclass;//作业班次
    private String weightvalue;//重量
    private String argovolume;//体积
    private String amt;//件数
    private String ischeck;//检算标志
    private String oplace;//作业地点
    private String outformid;//出入库单号
    private String loadometerid;//地磅单号
    private String blno;//提单号运单号
    private String isweigh;//是否过磅
    private String outportno;//工具标识
    private String omname;//操作方法
    private String packagetype;//包装方式
    private String worktype;//作业类型
    private String stackname;//堆位
    private String opname;//装卸过程
    private String trantool;//出货工具
    private String precargoname;//货名
    private String wman;//仓库员
}

/* eslint-disable */
var touchstart = function(event, ownerInstance) {
    var ins = event.instance
    var st = ins.getState()
    if (st.disable) return // disable的逻辑
    // console.log('touchstart st', JSON.stringify(st))
    if (!st.size) return
    // console.log('touchstart', JSON.stringify(event))
    st.isMoving = true
    st.startX = event.touches[0].pageX
    st.startY = event.touches[0].pageY
    st.firstAngle = 0
}
var touchmove = function(event, ownerInstance) {
    var ins = event.instance
    var st = ins.getState()
    if (!st.size || !st.isMoving) return
    // console.log('touchmove', JSON.stringify(event))
    var pagex = event.touches[0].pageX - st.startX
    var pagey = event.touches[0].pageY - st.startY
    // 左侧45度角为界限，大于45度则允许水平滑动
    if (st.firstAngle === 0) {
        st.firstAngle = Math.abs(pagex) - Math.abs(pagey)
    }
    if (st.firstAngle < 0) {
        return
    }
    var movex = pagex > 0 ? Math.min(st.max, pagex) : Math.max(-st.max, pagex)
    // 往回滑动的情况
    if (st.out) {
        // 已经是划出来了，还要往左滑动，忽略
        if (movex < 0) return
        ins.setStyle({
            'transform': 'translateX(' + (st.transformx + movex) + 'px)',
            'transition': ''
        })
        var btns = ownerInstance.selectAllComponents('.btn')
        var transformTotal = 0
        var len = btns.length
        var i = len - 1;
        for (;i >= 0; i--) {
            var transform = st.size.buttons[i].width / st.max * movex
            var transformx = st.size.buttons[i].max - Math.min(st.size.buttons[i].max, transform + transformTotal)
            btns[i].setStyle({
                'transform': 'translateX(' + (-transformx) + 'px)',
                'transition': ''
            })
            transformTotal += transform
        }
        return false
    }
    if (movex > 0) movex = 0
    ins.setStyle({
        'transform': 'translateX(' + movex + 'px)',
        'transition': ''
    })
    st.transformx = movex
    var btns = ownerInstance.selectAllComponents('.btn')
    var transformTotal = 0
    var len = btns.length
    var i = len - 1;
    for (;i >= 0; i--) {
        var transform = st.size.buttons[i].width / st.max * movex
        var transformx = Math.max(-st.size.buttons[i].max, transform + transformTotal)
        btns[i].setStyle({
            'transform': 'translateX(' + transformx + 'px)',
            'transition': ''
        })
        st.size.buttons[i].transformx = transformx
        transformTotal += transform
    }
    return false // 禁止垂直方向的滑动
}
var touchend = function(event, ownerInstance) {
    var ins = event.instance
    var st = ins.getState()
    if (!st.size || !st.isMoving) return
    // 左侧45度角为界限，大于45度则允许水平滑动
    if (st.firstAngle < 0) {
        return
    }
    var duration = st.duration / 1000
    st.isMoving = false
    // console.log('touchend', JSON.stringify(event))
    var btns = ownerInstance.selectAllComponents('.btn')
    var len = btns.length
    var i = len - 1
    // console.log('len size', len)
    if (Math.abs(event.changedTouches[0].pageX - st.startX) < st.throttle || event.changedTouches[0].pageX - st.startX > 0) { // 方向也要控制
        st.out = false
        ins.setStyle({
            'transform': 'translate3d(0px, 0, 0)',
            'transition': 'transform ' + (duration) + 's'
        })
        for (;i >= 0; i--) {
            btns[i].setStyle({
                'transform': 'translate3d(0px, 0, 0)',
                'transition': 'transform ' + (duration) + 's'
            })
        }
        ownerInstance.callMethod('hide')
        return
    }
    showButtons(ins, ownerInstance, duration)
    ownerInstance.callMethod('show')
}
var REBOUNCE_TIME = 0.2
var showButtons = function(ins, ownerInstance, withDuration) {
    var st = ins.getState()
    if (!st.size) return
    var rebounceTime = st.rebounce ? REBOUNCE_TIME : 0
    var movex = st.max
    st.out = true
    var btns = ownerInstance.selectAllComponents('.btn')
    var rebounce = st.rebounce || 0
    var len = btns.length
    var i = len - 1
    ins.setStyle({
        'transform': 'translate3d(' + (-movex - rebounce) + 'px, 0, 0)',
        'transition': 'transform ' + (withDuration) + 's'
    })
    st.transformx = -movex
    var transformTotal = 0
    for (;i >= 0; i--) {
        var transform = st.size.buttons[i].width / st.max * movex
        var transformx = (-(transform + transformTotal))
        btns[i].setStyle({
            'transform': 'translate3d(' + transformx + 'px, 0, 0)',
            'transition': 'transform ' + (withDuration ? withDuration + rebounceTime : withDuration) + 's'
        })
        st.size.buttons[i].transformx = transformx
        transformTotal += transform
    }
}
var innerHideButton = function(ownerInstance) {
    var ins = ownerInstance.selectComponent('.left')
    var st = ins.getState()
    if (!st.size) return
    var duration = st.duration ? st.duration / 1000 : 0
    var btns = ownerInstance.selectAllComponents('.btn')
    var len = btns.length
    var i = len - 1
    ins.setStyle({
        'transform': 'translate3d(0px, 0, 0)',
        'transition': 'transform ' + (duration) + 's'
    })
    st.transformx = 0
    for (;i >= 0; i--) {
        btns[i].setStyle({
            'transform': 'translate3d(0px, 0, 0)',
            'transition': 'transform ' + (duration) + 's'
        })
        st.size.buttons[i].transformx = 0
    }
}
var hideButton = function(event, ownerInstance) {
    innerHideButton(ownerInstance)
    ownerInstance.callMethod('buttonTapByWxs', {index: event.currentTarget.dataset.index, data: event.currentTarget.dataset.data})
    return false
}
var sizeReady = function(newVal, oldVal, ownerInstance, ins) {
    var st = ins.getState()
    // st.disable = newVal && newVal.disable
    if (newVal && newVal.button && newVal.buttons) {
        st.size = newVal
        st.transformx = 0
        // var min = newVal.button.width
        var max = 0
        var len = newVal.buttons.length
        var i = newVal.buttons.length - 1;
        var total = 0
        for (; i >= 0; i--) {
            max += newVal.buttons[i].width
            // if (min > newVal.buttons[i]) {
            //     min = newVal.buttons[i].width
            // }
            total += newVal.buttons[i].width
            newVal.buttons[i].max = total
            newVal.buttons[i].transformx = 0
        }
        st.throttle = st.size.throttle || 40 // 固定值
        st.rebounce = st.size.rebounce
        st.max = max
        ownerInstance.selectComponent('.right').setStyle({
            'line-height': newVal.button.height + 'px',
            left: (newVal.button.width) + 'px',
            width: max + 'px'
        })
        // console.log('st size', JSON.stringify(newVal))
        if (!st.size.disable && st.size.show) {
            showButtons(ins, ownerInstance)
        }
    }
}
var disableChange = function(newVal, oldVal, ownerInstance, ins) {
    var st = ins.getState()
    st.disable = newVal
}
var durationChange = function(newVal, oldVal, ownerInstance, ins) {
    var st = ins.getState()
    st.duration = newVal || 400
}
var showChange = function(newVal, oldVal, ownerInstance, ins) {
    var st = ins.getState()
    st.show = newVal
    if (st.disable) return
    // console.log('show change')
    if (st.show) {
        showButtons(ins, ownerInstance, st.duration)
    } else {
        innerHideButton(ownerInstance)
    }
}
var rebounceChange = function(newVal, oldVal, ownerInstance, ins) {
    var st = ins.getState()
    // console.log('rebounce', st.rebounce)
    st.rebounce = newVal
}
var transitionEnd = function(event, ownerInstance) {
    // console.log('transition')
    var ins = event.instance
    var st = ins.getState()
    // 回弹效果
    if (st.out && st.rebounce) {
        // console.log('transition rebounce', st.rebounce)
        ins.setStyle({
            'transform': 'translate3d(' + (-st.max) + 'px, 0, 0)',
            'transition': 'transform ' + REBOUNCE_TIME +'s'
        })
    }
}
module.exports = {
    touchstart: touchstart,
    touchmove: touchmove,
    touchend: touchend,
    hideButton: hideButton,
    sizeReady: sizeReady,
    disableChange: disableChange,
    durationChange: durationChange,
    showChange: showChange,
    rebounceChange: rebounceChange,
    transitionEnd: transitionEnd
}
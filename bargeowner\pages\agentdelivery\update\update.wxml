<!--pages/consignment/consignmodify/consignmodify.wxml-->
<view class="consign-detail" hover-class="none" hover-stop-propagation="false">
  <view class="consign-top" hover-class="none" hover-stop-propagation="false">
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false"><text class="star"></text>出库单号：</view>
      <input class="message-value" value="{{form.coutFormId}}" bindtap="coutFormIdInput" class="weui-input" placeholder="请输入出库单号"/>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false"><text class="star"></text>发货编号：</view>
      <input class="message-value" value="{{form.deliveryNo}}" bindinput="deliveryNoInputChange" class="weui-input" placeholder="请输入发货编号"/>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false"><text class="star"></text>驳船名：</view>
      <input class="message-value" disabled value="{{form.bargeName}}" class="weui-input" placeholder="请输入驳船名"/>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false"><text class="star"></text>计划吨数：</view>
      <input class="message-value" value="{{form.planWeight}}" bindinput="planWeightInputChange" class="weui-input" placeholder="请输入计划吨数"/>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false"><text class="star"></text>附件：</view>
      <button class="message-value btn" bindtap="takePhoto">点击上传</button>
    </view>
  </view>
  <image class="image" wx:for="{{images}}" wx:for-item="item" mode="widthFix" src="{{item}}" data-item="{{index}}" bindtap="deletePhoto" wx:if="{{item != null}}"></image>
  <view class="detail-oper" hover-class="none" hover-stop-propagation="false">
    <button class="oper-btn" bindtap="handleConfirm">确定</button>
  </view>

    <!-- 出库单号搜索窗口 -->
    <mp-halfScreenDialog show="{{showHalfDialog}}" maskClosable="{{false}}" mask="{{true}}" class='orderNum-dialog'>
      <view slot='title'>请选择出库单号</view>
      <view slot='desc'>
        <view class="search-wrap" hover-class="none" hover-stop-propagation="false">
          <input type="text" value="{{coutFormIdSearch}}" bindinput="coutFormIdSearchInputChange"  maxlength="20"  class="dialog-searchkey" placeholder='请输入出库单后4位搜索'></input>
          <button class="add-btn" bindtap="handleSearchCoutformIds">搜索</button>
        </view>
        <view class="dialog-content" hover-class="none" hover-stop-propagation="false">
          <radio-group class='orderNum-list' bindchange="handleCoutformIdChange">
            <label class="orderNum-item" wx:for="{{coutforms}}" wx:key="index">
              <view class="">
                <radio value="{{index}}" />
              </view>
              <view class="orderNum-formid">{{item}}</view>
            </label>
          </radio-group>
        </view>
      </view>
      <view slot="footer" class="dialog-wrap" hover-class="none" hover-stop-propagation="false">
        <button class="dialog-btn" bindtap="handleSelectCoutformId">确定</button>
      </view>
  </mp-halfScreenDialog>
</view>

package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value ="PUB_COUTFORMREDUCERECORD")
@KeySequence("SEQ_PUB_COUTFORMREDUCERECORD")
public class PubCoutformreducerecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 指令编号/车牌号
     */
    @TableField("ORDERNUM")
    private String ordernum;

    /**
     * 消减重量/加回重量
     */
    @TableField("REDUCEWEIGHT")
    private String reduceweight;

    /**
     * 操作类型
     */
    @TableField("OPERATETYPE")
    private String operatetype;

    /**
     * 操作模块
     */
    @TableField("OPERATEPART")
    private String operatepart;

    /**
     * 操作时间
     */
    @TableField("OPERATETIME")
    private String operatetime;

    /**
     * 扣减量
     */
    @TableField("YUKOUWEIGHT")
    private String yukouweight;

    /**
     * 加回量
     */
    @TableField("JIEKOUWEIGHT")
    private String jiekouweight;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 出库单号
     */
    @TableField("COUTFORMNUM")
    private String coutformnum;

    /**
     * 到验号
     */
    @TableField("UNIQECODE")
    private String uniqecode;

    /**
     * 船名
     */
    @TableField("SHIPNAME")
    private String shipname;

    /**
     * 静态结存
     */
    @TableField("REMAINWEIGHT")
    private String remainweight;

    /**
     * 动态结存
     */
    @TableField("DYNAMICWEIGHT")
    private String dynamicweight;

    /**
     * 出库单修改原因
     */
    @TableField("COUTFORMREASON")
    private String coutformreason;

    /**
     * 停单原因
     */
    @TableField("IMPAWNREASON")
    private String impawnreason;

    /**
     * 停单量
     */
    @TableField("IMPAWNNUMBER")
    private String impawnnumber;

    /**
     * 是否停单
     */
    @TableField("IMPAWN")
    private String impawn;

    /**
     * 货名
     */
    @TableField("CARGONAME")
    private String cargoname;

    /**
     * 公司id
     */
    @TableField("CORPORATIONID")
    private String corporationid;


}

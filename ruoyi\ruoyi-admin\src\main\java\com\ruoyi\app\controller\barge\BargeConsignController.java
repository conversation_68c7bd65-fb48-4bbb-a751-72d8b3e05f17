package com.ruoyi.app.controller.barge;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.app.controller.databarge.Pb6BargeworkController;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.ftp.BargeSftpUtil;
import com.ruoyi.common.utils.ftp.ReceivableBargeSftpUtil;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.barge.service.BargeConsignService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.*;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.dto.CargocmentDTO;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.DataType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.service.CustomerService;
import com.ruoyi.common.service.UploadAddressService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.consignor.service.ConsignorConsignService;
import com.ruoyi.contacts.service.ContactsConsignService;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.vo.PortLoadingMsgVO;
import com.ruoyi.databarge.mapper.Pb6WaterwaycargoMapper;
import com.ruoyi.databarge.service.*;
import com.ruoyi.databarge.service.impl.Pb6WaterwaycargoServiceImpl;
import com.ruoyi.databarge.wechat.MpMessageDTO;
import com.ruoyi.databarge.wechat.MpMessageResult;
import com.ruoyi.databarge.wechat.MpUtil;
import com.ruoyi.framework.web.service.WebSocketServer;

import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.web.utils.BargeUtils;
import com.ruoyi.wechat.service.WechatMpAccessTokenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/29 21:23
 */
@Api("驳船主-托运-api")
@Slf4j
@RestController
@RequestMapping("/barge/consignment")
public class BargeConsignController {

    @Autowired
    private BargeConsignService bargeConsignService;
    @Autowired
    private ConsignorConsignService consignorConsignService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private ContactsConsignService contactsConsignService;
    @Autowired
    private UploadAddressService uploadAddressService;
    @Autowired
    private BargeCenterService bargeCenterService;
    @Autowired
    private FddCommonService fddCommonService;
    @Autowired
    private CargocmentdetailService cargocmentdetailService;
    @Autowired
    private Pb6BargeCheckMessageService pb6BargeCheckMessageService;
    @Autowired
    private Pb6WaterwaycargoMapper pb6WaterwaycargoMapper;


    @Autowired
    private Pb6BargeworkController pb6BargeworkController;

    @Autowired
    private Pb6CargoconsignmentdetailService cargoconsignmentdetailService;

    @Autowired
    private Pb6CargoconsignmentService cargoconsignmentService;

    @Autowired
    private BargeConsignController bargeConsignController;

    @Autowired
    private ReceivableBargeSftpUtil receivableBargeSftpUtil;
    @Autowired
    private BargeSftpUtil bargeSftpUtil;

    @Autowired
    Pb6CargoconsignmentService pb6CargoconsignmentService;

    @Autowired
    private ShipFddUserRelService shipFddUserRelService;



    /**
     * 新增托运单
     * @return 手动新增托运单
     */
    @PostMapping("/addConsign")
    public AjaxResult addConsign(@RequestBody Pb6Cargoconsignment cargoconsignment) {
        return pb6CargoconsignmentService.addConsign(cargoconsignment);
    }

    /**
     * 新增托运单
     * @return 手动修改托运单
     */
    @PostMapping("/editConsign")
    public AjaxResult editConsign(@RequestBody Pb6Cargoconsignment cargoconsignment) {
        return pb6CargoconsignmentService.editConsign(cargoconsignment);
    }

    /**
     * 获取托运单列表
     *
     * @return 托运单列表
     */
    @ApiOperation(value = "获取托运单列表")
    @PostMapping("/getConsignmentList")
    public AjaxResult getConsignList(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        return bargeConsignService.getConsignList(cargocmentdetailBO);
    }

    /**
     * 获取托运单详情
     *
     * @param cargocmentdetailBO 托运单明细id
     * @return 托运单详情实体
     */
    @PostMapping("/getConsignDetail")
    public AjaxResult getConsignDetail(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        if (null == cargocmentdetailBO.getId()) {
            log.info("托运单明细id不能为空");
            return AjaxResult.error("支付失败，托运单明细id不能为空");
        }
        return bargeConsignService.getConsignDetail(cargocmentdetailBO);
    }

    /**
     * 支付（月结、现结） 驳船主
     *
     * @return 支付状态
     */
    //@PreAuthorize("@ss.hasPermi('weChat:barge:defray')")
    @PostMapping("/defray")
    public AjaxResult defray(@RequestBody CargocmentdetailBO cargocmentdetailBO) {

        if (StringUtils.isNull(cargocmentdetailBO.getId())) {
            log.info("托运单明细id不能为空");
            return AjaxResult.error("支付失败，托运单明细id不能为空");
        }
        if (StringUtils.isBlank(cargocmentdetailBO.getChargeBalanceType())) {
            log.error("支付方式不能为空");
            return AjaxResult.error("支付失败，支付方式不能为空");
        }

        return bargeConsignService.defray(cargocmentdetailBO);
    }

    /**
     * 支付（月结、现结） 托运单联系人
     *
     * @return 支付状态
     */
    @PostMapping("/contactsDefray")
    public AjaxResult contactsDefray(@RequestBody CargocmentdetailBO cargocmentdetailBO) throws InterruptedException {
        AjaxResult ajaxResult = this.defray(cargocmentdetailBO);
        if (HttpStatus.SUCCESS == Convert.toInt(ajaxResult.get("code"))) {
            CargocmentdetailBO cargocmentdetailBO1 = Convert.convert(CargocmentdetailBO.class, ajaxResult.get(AjaxResult.DATA_TAG));
            log.info("doDefrayAfterReturning - 返回参数：{}", cargocmentdetailBO1);
            if (StringUtils.isNotNull(cargocmentdetailBO1)) {
                log.warn("水路运单编号：{}", cargocmentdetailBO1.getWaterwayCargoId());
                if (StringUtils.isNotBlank(cargocmentdetailBO1.getWaterwayCargoId())) {
                    Thread.sleep(2000);
                    BargeUtils.defrayStamp(cargocmentdetailBO1.getWaterwayCargoId(), BargeUtils.WATERWAYCARGO_FILENAME);
                }
            }
        }
        return ajaxResult;
    }

    /**
     * 获取月结公司列表
     *
     * @return 月结公司列表
     */
    @PostMapping("/getMonthCompany")
    public AjaxResult getMonthCompany(@RequestBody Customer customer) {
        List<Customer> customerList = customerService.getMothCompanyList(customer);
        return AjaxResult.success(customerList);
    }

    /**
     * 获取船公司列表
     *
     * @return 船公司列表
     */
    @PostMapping("/getCarrierList")
    public AjaxResult getCarrierList(@RequestBody Customer customer) {
        List<Customer> customerList = customerService.getShipCompanyList(customer);
        return AjaxResult.success(customerList);
    }

    /**
     * 预约、取消预约
     *
     * @return 预约状态
     */
    @PreAuthorize("@ss.hasPermi('weChat:barge:reservation')")
    @PostMapping("/reservation")
    public AjaxResult reservation(@RequestBody CargocmentdetailBO cargocmentdetailBO) {

        Long id = cargocmentdetailBO.getId();
        String wxAppOintmentTime = cargocmentdetailBO.getWxAppOintmentTime();
        Integer wxOperateState = cargocmentdetailBO.getWxOperateState();

        if (null == cargocmentdetailBO.getId()) {
            log.info("托运单明细id不能为空");
            return AjaxResult.error("预约失败，托运单明细id不能为空");
        }

        try {
            AjaxResult result = bargeConsignService.reservation(id, wxAppOintmentTime, wxOperateState);
            //判断是否绿色驳船
            Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = cargoconsignmentdetailService.getOne(new LambdaQueryWrapper<Pb6Cargoconsignmentdetail>().eq(Pb6Cargoconsignmentdetail::getId, id));
            Pb6Cargoconsignment pb6Cargoconsignment = cargoconsignmentService.getOne(new LambdaQueryWrapper<Pb6Cargoconsignment>().eq(Pb6Cargoconsignment::getConsignflag, pb6Cargoconsignmentdetail.getConsignflag()));
            if (pb6BargeworkController.isGreenBarge(pb6Cargoconsignment.getComid(), pb6Cargoconsignmentdetail.getBargename())) {
                return result;
            } else {
                return AjaxResult.success("预约成功请申报绿色驳船");
            }
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 托运单确认
     *
     * @return 确认状态
     */
    // @PreAuthorize("@ss.hasPermi('weChat:barge:confirmConsign')")
    @PostMapping("/confirmConsign")
    public AjaxResult confirmConsign(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        if (null == cargocmentdetailBO.getId()) {
            log.error("托运明细id不能为空");
            return AjaxResult.error("id为空");
        }
        if (null == cargocmentdetailBO.getState()) {
            log.error("提交类型不能为空");
            return AjaxResult.error("提交类型不能为空");
        }
        // if (StringUtils.isBlank(cargocmentdetailBO.getChargeBalanceType())) {
        //     log.error("支付方式不能为空");
        //     return AjaxResult.error("支付方式不能为空");
        // }

        log.info("BargeConsignController - confirmConsign - 确认托运单（入参实体）：{}", cargocmentdetailBO);
        AjaxResult result = bargeConsignService.confirmConsign(cargocmentdetailBO);
        log.info("BargeConsignController - confirmConsign - 确认托运单（结果）：{}", result);

        String bcmId = (String) result.get("data");
        // 发消息给物流公司
        // try {
        //     WebSocketServer.sendInfo(bcmId, SecurityUtils.getLoginUser().getUser().getUserId().toString());
        // } catch (IOException e) {
        //     e.printStackTrace();
        //     return AjaxResult.error("发送消息给物流公司失败");
        // }
        return result;
    }

    /**
     * @param
     * @return
     * @description 托运单取消确认
     * <AUTHOR>
     * @date 2024/2/21 10:33
     */
    @PostMapping("/cancelConfirmConsign")
    public AjaxResult cancelConfirmConsign(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        if (null == cargocmentdetailBO.getId()) {
            log.error("托运明细id不能为空");
            return AjaxResult.error("id为空");
        }

        log.info("BargeConsignController - confirmConsign - 确认托运单（入参实体）：{}", cargocmentdetailBO);
        AjaxResult result = bargeConsignService.cancelConfirmConsign(cargocmentdetailBO);
        log.info("BargeConsignController - confirmConsign - 确认托运单（结果）：{}", result);
        return result;
    }


    /**
     * 确认实装数
     *
     * @param waterwayCargo 确认是否实装： Y 确认实装， N是没有确认实装
     * @return 确认状态
     */
    @PostMapping("/confirmLoadingOver")
    public AjaxResult confirmLoadingOver(@RequestBody WaterwayCargo waterwayCargo) {
        if (StringUtils.isBlank(waterwayCargo.getConfirmloadingover())) {
            log.info("确认状态为空");
            return AjaxResult.error("确认状态不能为空");
        }

        AjaxResult result = bargeCenterService.confirmLoadingOver(waterwayCargo);

        int code = Convert.toInt(result.get(AjaxResult.CODE_TAG));
        log.info("确认实装数[code：{}]", code);

        if (HttpStatus.SUCCESS == code) {
            // 异步开票
            BargeUtils.invoicing(waterwayCargo.getWaterwayCargoId());
            // 异步盖章
            BargeUtils.defrayStamp(waterwayCargo.getWaterwayCargoId(), BargeUtils.ALL_FILE);
        }

        return result;
    }

    // 发送生成水路运单文件消息
    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    @Autowired
    private WechatMpUserService wechatMpUserService;

    @Autowired
    SysUserMapper sysUserMapper;

    @Autowired
    private WechatMpAccessTokenService wechatMpAccessTokenService;

    @Autowired
    private MpUtil mpUtil;


    // 新运单生成通知，发送公众号消息
    public void sendWaterWayFileMessage(Pb6Waterwaycargo pb6Waterwaycargo, Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) {
        Map<String, Object> params = new HashMap<>();
        // 船名航次
        String bargeVoyage = pb6Waterwaycargo.getBargeName() +"/" + pb6Waterwaycargo.getBargeNumber();
        params.put("thing1",bargeVoyage);
        // 大船船名
        params.put("thing2",pb6Waterwaycargo.getShipName());
        // 运单编号
        params.put("character_string3",pb6Waterwaycargo.getWaterwaycargoid());
        // 生成时间，格式为：2021-01-01 12:00
        params.put("time4", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm"));

        List<SysUser> sysUserList = sysUserBargeMapper.queryAllUserByBargeId(pb6Cargoconsignmentdetail.getBargeid());

        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWxrationcontactnumber())){
            List<SysUser> ration = sysUserMapper.selectCargoConsignPersonByPhone(pb6Cargoconsignmentdetail.getWxrationcontactnumber());
            sysUserList.addAll(ration);
        }

        for(SysUser sysUser: sysUserList){

            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(sysUser.getUnionId())) {

                // 查询前，先根据openId去查询用户信息
                wechatMpUserService.getUserInfo(sysUser.getUnionId());

                // 2、发公众号消息
                List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                if (wechatMpUserList.size() == 0) {
                    log.warn("该用户未关注广州港船务有限公司!");
//                    throw new CustomException("5");
                } else if (wechatMpUserList.size() == 1) {
                    String accessToken = wechatMpAccessTokenService.getAccessToken();
                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                            .template_id("iB5Eo9c73FqcxlxRIkWndW28YNRPuojtTBRGtAs-EGs")
                            .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing1").toString()).color("#000000").build())
                            .data("thing2", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing2").toString()).color("#000000").build())
                            .data("character_string3", MpMessageDTO.MpMessageDataField.builder().value(params.get("character_string3").toString()).color("#000000").build())
                            .data("time4", MpMessageDTO.MpMessageDataField.builder().value(params.get("time4").toString()).color("#000000").build())
                            .build();
                    System.out.println("W H Y");
                    System.out.println(mpMessageDTO);
                    MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                    System.out.println("水路运单生成通知");
                    System.out.println(mpMessageResult);
                } else {
                    throw new CustomException("数据库数据错误!");
                }
            } else {
                log.warn("该用户没有unionid，发送水路运单生成通知失败！");
//                throw new CustomException("4");
            }
        }
    }

    /**
     * @param
     * @return
     * @description 确认实装数，生成水路运单
     * <AUTHOR>
     * @date 2024/1/27 21:55
     */
    @PostMapping("/confirmLoadingOverForApp")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult confirmLoadingOverForApp(@RequestBody WaterwayCargo waterwayCargo) {

        // 查询托运单明细
        Cargoconsignmentdetail detail = cargocmentdetailService.getOne(new QueryWrapper<Cargoconsignmentdetail>()
                .eq("waterwaycargoid", waterwayCargo.getID().toString()));

        if(StringUtils.isNull(detail)){
            return AjaxResult.error("托运单明细不存在");
        }

        if(StringUtils.isNull(detail.getBargeId())){
            return AjaxResult.error("船id为空");
        }

        // 查询印章是否存在，如果不存在则提示，“印章不存在，请联系管理员”
        List<ShipFddUserRel> shipFddUserRelList=shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                .eq(ShipFddUserRel::getType,"1")
                .eq(ShipFddUserRel::getReviewStatus,"1")
                .eq(ShipFddUserRel::getShipId,detail.getBargeId()));

        // 印章不存在
        if(shipFddUserRelList.size()==0){
            return AjaxResult.error("印章不存在，请联系管理员");
        }

        // 水路运单盖章
        BargeUtils.defrayStamp(waterwayCargo.getID().toString(), BargeUtils.WATERWAYCARGO_FILENAME);

        // 安全装货书盖章
        BargeUtils.defrayStampSafetyLoad(waterwayCargo.getID().toString());

        // 文件生成完后，
        // 托运单详情 驳船状态改为已离港
        // 根据waterwaycargoid查询托运单详情

        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = cargoconsignmentdetailService.getOne(new QueryWrapper<Pb6Cargoconsignmentdetail>().eq("waterwaycargoid", waterwayCargo.getID().toString()));
        pb6Cargoconsignmentdetail.setFlagbargestate("5");

        // 设置生成时间
        pb6Cargoconsignmentdetail.setWxGenerateTime(new Date());

        Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoMapper.selectById(waterwayCargo.getID());

        // 发送生成水路运单文件消息
        this.sendWaterWayFileMessage(pb6Waterwaycargo,pb6Cargoconsignmentdetail);

        cargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
        return AjaxResult.success();


    }

    /**
     * @param
     * @return
     * @description 重新生成水路运单
     * @date 2024/1/27 21:55
     */
    @PostMapping("/reGenerateWaterWayCargoApp")
    public AjaxResult reGenerateWaterWayCargoApp(@RequestBody WaterwayCargo waterwayCargo) {

        // 重新生成水路运单
        BargeUtils.defrayStamp(waterwayCargo.getID().toString(), BargeUtils.WATERWAYCARGO_FILENAME);

        return AjaxResult.success();
    }

    /**
     * @param
     * @return
     * @description 安全装货书盖章
     * <AUTHOR>
     * @date 2024/12/27 8:48
     */
    @PostMapping("/defrayStampForSafetyLoad")
    public AjaxResult defrayStampForSafetyLoad(@RequestBody WaterwayCargo waterwayCargo) {
        // 生成安全装货书
        BargeUtils.defrayStampSafetyLoad(waterwayCargo.getID().toString());
        return AjaxResult.success();
    }



    /**
     * 确认实装数后的操作（开发票、三个文件盖章）
     *
     * @param waterwayCargoId 水路运单编号
     * @return 响应状态
     */
    @GetMapping("/stamp")
    public AjaxResult stamp(@RequestParam(value = "waterwayCargoId", required = false) String waterwayCargoId) {
        if (StringUtils.isBlank(waterwayCargoId)) {
            log.error("BargeConsignController - stamp - 水路运单号不能为空");
            return AjaxResult.error("水路运单号不能为空");
        }

        // 根据水路运单号查找托运单明细
        Cargoconsignmentdetail cargoconsignmentdetail = cargocmentdetailService.getOne(new QueryWrapper<Cargoconsignmentdetail>().eq("waterwaycargoid", waterwayCargoId));

        if (StringUtils.isNull(cargoconsignmentdetail)) {
            log.error("BargeConsignController - stamp - 水路运单号{}的托运单明细不存在", waterwayCargoId);
            return AjaxResult.error("水路运单号" + waterwayCargoId + "的托运单明细不存在");
        }

        // 异步开票
        BargeUtils.invoicing(waterwayCargoId);

        // 异步盖章
        BargeUtils.defrayStamp(waterwayCargoId, BargeUtils.ALL_FILE);

        return AjaxResult.success();
    }

    /**
     * 重新开票和盖章方法
     *
     * @param waterwayCargoId 水路运单编号
     * @param invoicing       1-开票
     * @param stamp           BargeUtils.ALL_FILE
     * @return 响应状态
     */
    @GetMapping("/againStamp")
    public AjaxResult againStamp(@RequestParam(value = "waterwayCargoId", required = false) String waterwayCargoId,
                                 @RequestParam(value = "invoicing", required = false) Long invoicing,
                                 @RequestParam(value = "defrayStamp", required = false) String stamp) {
        if (StringUtils.isBlank(waterwayCargoId)) {
            log.error("BargeConsignController - stamp - 水路运单号不能为空");
            return AjaxResult.error("水路运单号不能为空");
        }
        if (stamp.equals("水路货物运单.pdf") == false) {
            PortLoadingMsgVO portLoadingMsgVO = pb6BargeCheckMessageService.searchPortLoadingInfo(waterwayCargoId);
            if (StringUtils.isEmpty(portLoadingMsgVO.getTscargoweightValue())) {
                return AjaxResult.error("没有实装数，无法生成‘驳船装货交接凭证’和‘货物交接清单’");
            }
        }


        // 根据水路运单号查找托运单明细
        Cargoconsignmentdetail cargoconsignmentdetail = cargocmentdetailService.getOne(new QueryWrapper<Cargoconsignmentdetail>().eq("waterwaycargoid", waterwayCargoId));

        if (StringUtils.isNull(cargoconsignmentdetail)) {
            log.error("BargeConsignController - stamp - 水路运单号{}的托运单明细不存在", waterwayCargoId);
            return AjaxResult.error("水路运单号" + waterwayCargoId + "的托运单明细不存在");
        }

        if (StringUtils.isNotNull(invoicing) && 1 == invoicing) {
            // 异步开票
            BargeUtils.invoicing(waterwayCargoId);
        }

        if (StringUtils.isNotBlank(stamp)) {
            // 异步盖章
            BargeUtils.defrayStamp(waterwayCargoId, stamp);
        }


        return AjaxResult.success();
    }

    /**
     * 退单/改单
     *
     * @return 响应状态
     */
    @PostMapping("/chargeback")
    public AjaxResult chargeback(@RequestBody CargocmentDTO cargocmentDTO) {
        consignorConsignService.modificationBookingNote(cargocmentDTO);
        return AjaxResult.success("成功");
    }

    /**
     * 差额退款
     *
     * @return 响应状态
     */
    @PostMapping("/bargeRefund")
    public AjaxResult bargeRefund(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        Long id = cargocmentdetailBO.getId();
        if (id == null) {
            log.error("托运单明细id不能为空");
            return AjaxResult.error("托运单id不能为空");
        }
        return contactsConsignService.balanceRefund(cargocmentdetailBO);
    }

    /**
     * 获取盖章文件列表
     *
     * @param cargocmentdetailBO waterwayCargoId
     * @return 盖章文件列表
     */
    @PostMapping("/waterwayCargoFiles")
    public AjaxResult waterwayCargoFiles(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        if (StringUtils.isBlank(cargocmentdetailBO.getWaterwayCargoId())) {
            log.error("水路运单编号为空");
            throw new CustomException("水路运单编号不能为空");
        }

        return AjaxResult.success(BargeUtils.getStampFiles(cargocmentdetailBO.getWaterwayCargoId()));
    }

    /**
     * 获取文件流
     *
     * @param id       文件id
     * @param response 请求
     * @throws Exception 异常
     */
    @GetMapping("/getStampFile")
    public void getStampFile(@RequestParam("id") Long id, HttpServletResponse response) throws Exception {
        if (StringUtils.isNull(id)) {
            log.error("文件id不能为空");
            throw new CustomException("文件id不能为空");
        }

        UploadAddress address = uploadAddressService.getById(id);

        String fileName = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        if (address != null) {
            String url = address.getUrl();
            fileName = url.substring(url.lastIndexOf("/"));
            // 获取文件流
            byteArrayOutputStream = fddCommonService.downloadContract(address.getContractNo());
        }

        if (StringUtils.isNotNull(byteArrayOutputStream)) {
            ServletOutputStream outputStream = response.getOutputStream();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.flush();
            // 关闭流
            IoUtil.close(byteArrayOutputStream);
            IoUtil.close(outputStream);
        }
    }

    /**
     * 获取 水路货物运单.pdf 货物交接清单.docx 广州港新沙港务有限公司驳船装货交接凭证.docx 文件
     *
     * @param waterwayCargoId 水路运单编号
     * @param type            文件类型 1-广州港新沙港务有限公司驳船装货交接凭证.docx
     *                        2-货物交接清单.docx 3-水路货物运单.pdf
     */
    @GetMapping("/getPdfOrWord")
    public void getPdfOrWord(@RequestParam("waterwayCargoId") String waterwayCargoId,
                             @RequestParam("type") Integer type,
                             HttpServletResponse response) throws Exception {

        if (StringUtils.isBlank(waterwayCargoId)) {
            log.error("水路运单编号为空");
            throw new CustomException("水路运单编号不能为空");
        }

        // 首先查询
        UploadAddress address = null;
        List<UploadAddress> list = uploadAddressService.getUploadAddressList(null, waterwayCargoId);
        Integer dataType =
                type == 1 ? DataType.CONSIGN_CERTIFICATE.getCode()
                        : type == 2 ? DataType.CONSIGN_HANDOVER.getCode()
                        : type == 3 ? DataType.CONSIGN_WATER_ORDER.getCode()
                        : 0;
        for (UploadAddress uploadAddress : list) {
            if (dataType.equals(uploadAddress.getDataType())) {
                address = uploadAddress;
                break;
            }
        }

        String fileName = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        if (address != null) {
            String url = address.getUrl();
            fileName = url.substring(url.lastIndexOf("/"));
            // 获取文件流
            byteArrayOutputStream = fddCommonService.downloadContract(address.getContractNo());
        }

        if (StringUtils.isNotNull(byteArrayOutputStream)) {
            ServletOutputStream outputStream = response.getOutputStream();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.flush();
            // 关闭流
            IoUtil.close(byteArrayOutputStream);
            IoUtil.close(outputStream);
        }
    }

    @GetMapping("/waterwayCargoIdStamp")
    public AjaxResult waterwayCargoIdStamp() {
//        List<String> waterwayCargoIdList=pb6WaterwaycargoService.waterwayCargoIdStamp("2022-02-28 01:23:44");
        List<String> waterwayCargoIdList = pb6WaterwaycargoMapper.waterwayCargoIdStampaaa();
        System.out.println(waterwayCargoIdList.size());
        System.out.println(waterwayCargoIdList);
        if (waterwayCargoIdList.size() > 0) {
            for (String a : waterwayCargoIdList) {
                try {
                    BargeUtils.defrayStamp(a, "ALL");
                } catch (Exception e) {
                    System.out.println(a);
                }

            }
        }
        return AjaxResult.success("生成完成");
    }

    @Log(title = "散货帐龄盖章", businessType = BusinessType.UPDATE)
    @GetMapping("/PDFStamp")
    public AjaxResult PDFStamp(@RequestParam("url") String url, @RequestParam("port") int port,String fileName) throws Exception {
        log.info(url);
        try {
            HttpRequest relad = HttpRequest.get(url)
                    .header("Content-Type", "application/json");
            HttpURLConnection httpUrl = (HttpURLConnection) new URL(relad.getUrl()).openConnection();
            httpUrl.connect();
            InputStream ins = httpUrl.getInputStream();
            File file = new File(System.getProperty("java.io.tmpdir") + File.separator + "teast.pdf");//System.getProperty("java.io.tmpdir")缓存
            if (file.exists()) {
                file.delete();//如果缓存中存在该文件就删除
            }
            OutputStream os = new FileOutputStream(file);
            int bytesRead;
            int len = 8192;
            byte[] buffer = new byte[len];
            while ((bytesRead = ins.read(buffer, 0, len)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();

            // 开始盖章
            String docNo = "YD" + IdUtil.objectId();
            log.info("外部开始调用FDD：docNo=" + docNo + ";cargoFile=" + file);
            log.info("url:" + url + ";port:" + port);
            int a = 789;

            switch (port) {
                case 1:
                    // 新沙公司1.上传并创建合同
                    fddCommonService.uploadAndSaveContract(null, "e194eccf8d364ac68d3b4352f91c1c42", docNo, file);
                    // 2.自动签署
                    fddCommonService.autoSign(docNo, null, "e194eccf8d364ac68d3b4352f91c1c42", "广州港新沙港务有限公司", docNo, 595.0f, 912.0f);
                    break;
                case 2:
                    // 黄埔公司1.上传并创建合同
                    fddCommonService.uploadAndSaveContract(null, "8571c49ac2d748bab3e40f9abd718a80", docNo, file);
                    // 2.自动签署
                    fddCommonService.autoSign(docNo, null, "8571c49ac2d748bab3e40f9abd718a80", "广州港股份有限公司黄埔港务分公司", docNo, 595.0f, 912.0f);
                    break;
                case 3:
                    // 新港公司1.上传并创建合同
                    fddCommonService.uploadAndSaveContract(null, "0a1a18f6bbee4ba486afde5fbe53bc1d", docNo, file);
                    // 2.自动签署
                    fddCommonService.autoSign(docNo, null, "0a1a18f6bbee4ba486afde5fbe53bc1d", "广州港股份有限公司新港港务分公司", docNo, 595.0f, 912.0f);
                    break;
                case 27:
                    // 茂名1.上传并创建合同
                    fddCommonService.uploadAndSaveContract(null, "9f8d544f22144ae3848521d312bfc915", docNo, file);
                    // 2.自动签署
                    fddCommonService.autoSign(docNo, null, "9f8d544f22144ae3848521d312bfc915", "茂名广港码头有限公司（行政公章）", docNo, 595.0f, 912.0f);
                    break;
                case 6:
                    // 石油化工1.上传并创建合同
                    fddCommonService.uploadAndSaveContract(null, "b64267eb9d5b4f49a6d114511fa33254", docNo, file);
                    // 2.自动签署
                    fddCommonService.autoSign(docNo, null, "b64267eb9d5b4f49a6d114511fa33254", "石油化工", docNo, 595.0f, 912.0f);
                    break;
                case 13:
                    // 小虎码头1.上传并创建合同
                    fddCommonService.uploadAndSaveContract(null, "cd4ffab5837a4afab4f3b913ea6712bb", docNo, file);
                    // 2.自动签署
                    fddCommonService.autoSign(docNo, null, "cd4ffab5837a4afab4f3b913ea6712bb", "公司章", docNo, 595.0f, 912.0f);
                    break;
                case 16:
                    // 粮食码头1.上传并创建合同
                    fddCommonService.uploadAndSaveContract(null, "6010d5647d894640a4e35f45b3e74652", docNo, file);
                    // 2.自动签署
                    fddCommonService.autoSign(docNo, null, "6010d5647d894640a4e35f45b3e74652", "广州港股份有限公司南沙粮食通用码头分公司印章", docNo, 595.0f, 912.0f);
                    break;
                default:
                    a = port;
                    break;
            }

            log.info("1.上传并创建合同");
        if (a!=789){
            return AjaxResult.error(port+"码头id有误");
        }
            // 3.查看合同
            log.info("2.查看合同");
            log.info(fddCommonService.viewContract(docNo));
            // 4.下载合同
            log.info("3.FDD盖章流程结束");
            ByteArrayOutputStream bos = fddCommonService.downloadContract(docNo);
            InputStream inputStream = new ByteArrayInputStream(bos.toByteArray());
            TimeInterval timer = DateUtil.timer();
            try {
                String path= receivableBargeSftpUtil.uploadFile(inputStream, fileName);
                Thread.sleep(Long.parseLong("3000"));
                return AjaxResult.success(path);
            } catch (Exception e) {
                log.error("- waterwayCargoId:{} - 花费时间:{}ms",  timer.interval());
                log.error("上传文件失败", e);
                return AjaxResult.error(e.toString());
            } finally {
                IoUtil.close(bos);
                IoUtil.close(inputStream);
            }
//            response.setContentLength(bos.size());
//            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
//            response.setHeader("Content-Disposition","attachment;filename="+new String(fileName.getBytes(), "ISO-8859-1"));
//            response.getOutputStream().write(bos.toByteArray());
        } catch (Exception e) {
            return AjaxResult.error(e.toString());
        }
    }



}

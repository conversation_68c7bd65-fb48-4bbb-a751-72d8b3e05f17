package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("PB6_BARGE_COMPANY")
@KeySequence("SEQ_PB6_BARGE_COMPANY")
public class Pb6BargeCompany implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 驳船id
     */
    @TableField("BARGEID")
    private Long bargeid;

    /**
     * 挂靠公司id
     */
    @TableField("COMPANYID")
    private Long companyid;

    /**
     * 挂靠类型（（1-自有、2-临时挂靠、3-长期挂靠，4-其他））
     */
    @TableField("BINDINGTYPE")
    private Integer bindingtype;

    /**
     * 是否生效(挂靠审核使用)  1-生效  0-无效
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 修改挂靠类型（（1-自有、2-临时挂靠、3-长期挂靠，4-其他））
     */
    @TableField("UPDATEBINDINGTYPE")
    private Integer updatebindingtype;

    /**
     * 审核人
     */
    @TableField("AUDITBYID")
    private Long auditbyid;

    /**
     * 审核时间
     */
    @TableField("AUDITTIME")
    private String audittime;

    /**
     * 挂靠审核（0-修改挂靠审核，1-挂靠审核）
     */
    @TableField("ISAUDIT")
    private Integer isaudit;

    /**
     * 创建人
     */
    @TableField("CREATEBYID")
    private Long createbyid;

    /**
     * 创建时间
     */
    @TableField("CREATETIME")
    private String createtime;

    /**
     * 修改人
     */
    @TableField("UPDATEBYID")
    private Long updatebyid;

    /**
     * 修改时间
     */
    @TableField("UPDATETIME")
    private String updatetime;

    /**
     * "挂靠审核（0：待审核，1：审核通过 2.审核不通过）"
     */
    @TableField("AUDITSTATUS")
    private Integer auditstatus;

    /**
     * 审核人名称
     */
    @TableField("AUDITBYNAME")
    private String auditByName;

    /**
     * 创建人名称
     */
    @TableField("CREATEBYNAME")
    private String createByName;

    /**
     * 修改人名称
     */
    @TableField("UPDATEBYNAME")
    private String updateByName;

    /**
     * 是否已删除标识（0-已删除，1-生效）
     */
    @TableField("ISDELETE")
    private Integer isDelete;

    /**
     * 挂靠公司名称
     */
    @TableField("COMPANYNAME")
    private String companyName;
}

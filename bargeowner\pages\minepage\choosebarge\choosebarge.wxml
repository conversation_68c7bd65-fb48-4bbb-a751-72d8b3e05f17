<!--pages/minepage/choosebarge/choosebarge.wxml-->
<view class="choose-page" hover-class="none" hover-stop-propagation="false">
  <view class="search-warp" hover-class="none" hover-stop-propagation="false">
    <view class="search-area" hover-class="none" hover-stop-propagation="false">
      <input type="text" class="search-ipt" placeholder="驳船名称"  value="{{searchValue}}" bindinput="searchHandle"/>
      <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-icon" wx:if="{{searchValue}}" bindtap="deleteSearchValue"></mp-icon>
      <mp-icon class="search-icon" type="field" icon="search" color="#aaaaaa" size="{{30}}" wx:if="{{!searchValue}}"></mp-icon>
    </view>
    <button class="add-btn" bindtap="handleAddBarge" wx:if="{{userInfos.userType == 14}}">新增驳船</button>
  </view>
  <view class="barge-list" hover-class="none" hover-stop-propagation="false">
    <radio-group bindchange="radioChange">
      <label class="barge-item" wx:for="{{bargeList}}" wx:key="id" hover-class="none" hover-stop-propagation="false" data-item="{{item}}" id="each-radio">
        <radio value="{{item.id}}" checked="{{item.checked}}"></radio>
        <view class="iconfont iconchuan main-icon" hover-class="none" hover-stop-propagation="false"></view>
        <view class="barge-name" hover-class="none" hover-stop-propagation="false">{{item.bargeName}}</view>
      </label>
    </radio-group>
  </view>
  <view class="search-bottom" hover-class="none" hover-stop-propagation="false">
    <button class="confirm-btn" bindtap="handleConfirm">确定</button>
  </view>
</view>

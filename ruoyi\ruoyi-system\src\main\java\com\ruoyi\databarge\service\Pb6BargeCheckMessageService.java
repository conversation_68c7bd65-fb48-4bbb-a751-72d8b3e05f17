package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.Pb6BargeCheckMessage;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.domain.dto.Pb6BargeCheckMessageSearchDTO;
import com.ruoyi.databarge.domain.dto.XGPdfDTO;
import com.ruoyi.databarge.domain.vo.Pb6BargeCheckMessageVO;
import com.ruoyi.databarge.domain.vo.PortLoadingMsgVO;

import java.net.URISyntaxException;

/**
 * <p>
 * 驳船审核消息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
public interface Pb6BargeCheckMessageService extends IService<Pb6BargeCheckMessage> {

    IPage<Pb6BargeCheckMessage> searchPage(Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO);

    IPage<Pb6BargeCheckMessage> searchPagePort(Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO);

    //查询可配载的消息数
    int stowageMsgNum();

    //物流公司审核驳船备案、挂靠申请通过，发送给驳船主或船公司备案申请人
    void sendMessageForAddOrUpdateBargeInfo(Pb6BargeCheckMessage pb6BargeCheckMessage) throws URISyntaxException;

    //码头实装后发消息给到驳船主（发给预约人）(公众号和小程序都要发)
    void sendMessageForPortLoadingOver(String waterWayCargoId, XGPdfDTO xgPdfDTO) throws URISyntaxException;

    //退单改单审核后给申请人发消息
    String sendMessageForRefundOrUpdateCargoConsignment(Pb6BargeCheckMessage pb6BargeCheckMessage) throws URISyntaxException;

    //查询水路运单实装数
    PortLoadingMsgVO searchPortLoadingInfo(String waterWayCargoId);
    //查询水路运单实装件数
    Long searchPortLoadingAmt(String waterwaycargoid);

    // 物流公司审核托运单通过后，发消息给驳船主管理员和业务员
    void sendMessageForCargoConsignmentChecked(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail, Long bargeid) throws URISyntaxException;

    //码头消息列表
    IPage<Pb6BargeCheckMessageVO> searchPortMessagePage(Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO);

    //码头审核代理发货信息，发消息给申请人
    void sendMessageForAgentDeliveryChecked(Pb6BargeCheckMessage pb6BargeCheckMessage) throws URISyntaxException;

    //报到时给预约人发绿色驳船申请
    void sendMessageForGreenBarge(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail)throws URISyntaxException;
}

package com.ruoyi.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.domain.Customer;
import com.ruoyi.common.domain.bo.CustomerBO;
import com.ruoyi.common.mapper.CustomerMapper;
import com.ruoyi.common.service.CustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 公司业务层实现类
 * <AUTHOR>
 * @Date 2020/8/10  17:02
 */
@Service
public class CustomerServiceImpl implements CustomerService {
    @Autowired
    private CustomerMapper customerMapper;
    @Override
    public List<Customer> getCustomerList(Customer customer) {
        List<Customer> customers = customerMapper.selectList(
                new QueryWrapper<Customer>()
                        .like("cfullname",customer.getCFullName())
                        .le("rownum",100)
                        .le("ctype", "船公司")
        );
        return customers;
    }

    /**
     * 获取船公司列表
     * @param customer
     * @return
     */
    @Override
    public List<Customer> getShipCompanyList(Customer customer) {
        /*List<Customer> customers = customerMapper.selectList(
                new QueryWrapper<Customer>()
                        .like("cfullname",customer.getCFullName())
                        .le("rownum",100)
                        .le("ctype", "船公司")
        );*/
        CustomerBO customerBO = new CustomerBO();
        customerBO.setCFullName(customer.getCFullName());
        List<Customer> customers = customerMapper.getShipCompanyList(customerBO);
        return customers;
    }

    /**
     * 获取月结客户公司
     * @param customer
     * @return
     */
    @Override
    public List<Customer> getMothCompanyList(Customer customer) {
        /*List<Customer> customers = customerMapper.selectList(
                new QueryWrapper<Customer>()
                        .like("cfullname",customer.getCFullName())
                        .le("rownum",100)
                        .le("specialuse", "0")
        );*/
        CustomerBO customerBO = new CustomerBO();
        customerBO.setCFullName(customer.getCFullName());
        customerBO.setSpecialuse("0");
        List<Customer> customers = customerMapper.getShipCompanyList(customerBO);
        return customers;
    }
}

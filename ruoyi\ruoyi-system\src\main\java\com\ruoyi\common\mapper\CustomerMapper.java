package com.ruoyi.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.domain.Customer;
import com.ruoyi.common.domain.bo.CustomerBO;

import java.util.List;

/**
 * @Description 客户实体（公司）持久层接口
 * <AUTHOR>
 * @Date 2020/8/10  17:02
 */
public interface CustomerMapper extends BaseMapper<Customer> {
    // 匹配船公司
    List<Customer> selectShipCompany(String shipCompanyName);

    List<Customer> getShipCompanyList(CustomerBO customerBO);
}

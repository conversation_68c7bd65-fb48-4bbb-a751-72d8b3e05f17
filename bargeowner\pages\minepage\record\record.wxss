/* pages/minepage/record/record.wxss */
.record-head{
  position: fixed;
  left: 70rpx;
  right: 70rpx;
  height: 80rpx;
  width: 70%;
  border: 1px solid #00426B;
  border-radius: 15rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
  z-index:1000;
  background: #fff;
}
page {
  height: 100%;
  width: 100%;
  background: #f8f8f8;
}
.record-page{
  padding-top: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: auto;
  width: 100%;
  margin-bottom: 300rpx;
  background: #f8f8f8;
}
.record-head-item{
  height: 100%;
  flex: 1;
  line-height: 76rpx;
  text-align: center;
  font-size: 30rpx;
  color: #00426B;
}
.record-head-item.head-left{
  border-right: 1px solid #ddd;
}
.record-head-item.active-tab{
  background: #00426B;
  color: #ffffff;
}
.record-content{
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-top:100rpx;
}
.add-message-item{
  min-height: 100rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px solid #ddd;
  background: #fff;
  padding: 0 10px;
}
.add-message-title{
  color: #00426B;
  font-size: 30rpx;
  flex-shrink: 0;
}
.add-message-ipt{
  flex: 1;
  width: 100%;
  height: 100%;
  color: #333;
  padding: 0 20rpx;
  font-size: 30rpx;
}
.add-message-ipt1{
  flex: 1;
  width: 100%;
  height: 100%;
  color: #333;
  padding: 0 20rpx;
  font-size: 30rpx;
}
.box-right{
  display: inline-block;
  color: #333;
  padding: 0 20rpx;
  font-size: 30rpx;
}
.person-record{
  flex: 1;
}
.person-operate{
  height: 200rpx;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.person-operate .person-btn{
  position: fixed;
  left: 70rpx;
  right: 70rpx;
  bottom: 50rpx;
  z-index: 1000;
  width: 80%;
  height: 90rpx;
  padding: 0rpx;
  margin: 0rpx;
  border-radius: 10rpx;
  border: none;
  line-height: 90rpx;
  color: #fff;
  background: rgb(6, 143, 13);
  font-weight: normal;
  font-size: 30rpx;
}
.barge-value{
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 10rpx;
}
.barge-value1{
  flex: 1;
  height: 100%;
  text-align: left;
  line-height: 96rpx;
  padding: 0 10rpx;
}
.detail-icon{
  flex-shrink: 0;
  color:#aaa;
  font-size: 40rpx;
  font-weight: bold;
}
.search-date-selected{
  height: 100%;
  width: 100%;
  color: #333;
}
.search-date-text{
  height: 100%;
  width: 100%;
  color: #808080;
}
.upload-btn{
  width: 100rpx;
  height: 60rpx;
  padding: 0;
  margin: 0;
  background: #fff;
  color:#00426B;
  border: 2rpx solid #00426B;
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  font-weight: 500;
}
.red{
  color: red;
}
.img-wrap{
  width: 100%;
  padding: 10rpx 10rpx 10rpx 25rpx;
  display: flex;
  flex-wrap: wrap;
}
.img-item{
  flex-shrink: 0;
  margin: 15rpx 8rpx 0rpx 8rpx;
  position: relative;
}
.img-list{
  background: #fff;
  margin-top: 20rpx;
}
.close-icon1{
  position: absolute;
  right: 3rpx;
  top: -5rpx;
}
.consign-top{
  height: auto;
  width: 100%;
  margin-top: 20rpx;
  background: #fff;
}
.consign-title{
  width:100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  padding: 0 20rpx;
  border-bottom: 1px solid #ddd;
}
.mask1{
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  background:rgba(103, 103, 103,.5);
}
.search-dialog{
  position: fixed;
  left: 0;
  top: 150rpx;
  right: 0;
  bottom: 0;
  z-index: 5000;
  width: 100%;
  height: auto;
  background:#FFF;
} 
.contract-text-wrap {
  border-bottom: 1rpx solid #ddd;
  line-height: 50rpx;
  padding: 20rpx;
} 
.contract-wrap{
  height: 100rpx;
  display: flex;
  align-items: center;
  margin-top: 20px;
  border-bottom: 1rpx solid #ddd;
  background: #fff;
  padding: 0 20px;
  font-size: 30rpx;
  color: #333;
}
.privacy-title{
  font-size: 34rpx;
  text-align: center;
  line-height: 50rpx;
  font-weight: bold;
}
.important-text{
  font-weight: bold;
  text-indent: 2em;
  font-size: 28rpx;
}
.normal-text{
  text-indent: 2em;
  font-size: 28rpx;
  word-break: break-all;
}
.star{
  width: 10rpx;
  flex-shrink: 0;
  margin:0 5rpx;
  display: inline-block;
  color:red;
}
#canvas {
  width: 166px;
  height: 166px;
}
.underline-text{
  text-decoration: underline;
}
.myCanvas {
  position: fixed;
  background-color: #f99;
  left: -303px;
}
.cargo-tip {
  padding: 20rpx 30rpx 10rpx;
  display: flex;
  align-items: center;
}

.tip-text {
  color: #ff0000;
  font-size: 24rpx;
  font-weight: bold;
  margin-left: 10rpx;
}
package com.ruoyi.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.domain.BargeCompany;
import com.ruoyi.common.domain.vo.BargeCompanyVO;
import org.apache.ibatis.annotations.Param;

/**
 * 驳船-公司 mapper
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/15 11:26
 */
public interface BargeCompanyMapper extends BaseMapper<BargeCompany> {
    int insertBargeCompany(BargeCompany record);

    int insertSelective(BargeCompany record);

    /**
     * 根据驳船Id查询公司信息
     * @param bargeId
     * @return
     */
    BargeCompanyVO queryCompanyByBargeId(@Param("bargeId") Long bargeId,@Param("userId") Long userId);
}
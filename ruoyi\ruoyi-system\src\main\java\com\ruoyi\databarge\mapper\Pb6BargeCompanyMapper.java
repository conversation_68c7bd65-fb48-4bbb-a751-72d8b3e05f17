package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.databarge.domain.Pb6BargeCompany;
import com.ruoyi.databarge.domain.dto.Pb6BargeinfoAuditSearchDTO;
import com.ruoyi.databarge.domain.vo.Pb6BargeCompanyVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/9/1.
 * @Date: 2020/9/1 11:37
 */
public interface Pb6BargeCompanyMapper extends BaseMapper<Pb6BargeCompany> {

    List<Pb6BargeCompanyVO> searchByBargeId(@Param("bargeid") Long bargeid);

    Pb6BargeCompanyVO searchById(@Param("id") Long id);

    /**
     * 通过船公司ID，查找所有挂靠在该公司下的所有船信息
     * @param companyid 船公司ID
     * @return 挂靠列表
     */
    List<Pb6BargeCompanyVO> searchByCompanyId(@Param("companyid") Long companyid);

    @Update("update pb6_barge_company set BARGEID=#{bargeId},COMPANYID=null,BINDINGTYPE=null,STATUS=0,UPDATEBINDINGTYPE=null,AUDITBYID=null,AUDITTIME=null,ISAUDIT=null,UPDATEBYID=#{updatebyid},UPDATETIME=#{updatetime},AUDITSTATUS=null,AUDITBYNAME=null,UPDATEBYNAME=#{updateByName},ISDELETE=0,COMPANYNAME=#{companyName} where id=#{id}")
    boolean updateCompanyName(@Param("bargeId") Long bargeId,@Param("updatebyid") Long updatebyid,@Param("updatetime") String updatetime,@Param("updateByName") String updateByName,@Param("companyName") String companyName,@Param("id") Long id);
}

package com.ruoyi.common.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.domain.*;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.bo.CargocmentBO;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.vo.CargocmentVO;
import com.ruoyi.common.enums.CoutformType;
import com.ruoyi.common.enums.FlagBargeState;
import com.ruoyi.common.enums.PayWayEnum;
import com.ruoyi.common.enums.SelectShipType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.BargeInfoMapper;
import com.ruoyi.common.mapper.CargocmentMapper;
import com.ruoyi.common.mapper.CargocmentdetailMapper;
import com.ruoyi.common.mapper.UploadAddressMapper;
import com.ruoyi.common.service.BargeInfoAuditService;
import com.ruoyi.common.service.CargocmentService;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.consignor.domain.vo.ConsignorConsignVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * @Description 运单实现
 * <AUTHOR>
 * @Date 2020/8/5 9:53
 */
@Service
public class CargocmentServiceImpl extends ServiceImpl<CargocmentMapper, Cargoconsignment> implements CargocmentService {
    @Autowired
    private CargocmentdetailService cargocmentdetailService;
    @Autowired
    private BargeInfoAuditService bargeInfoAuditService;
    @Autowired
    private UploadAddressMapper uploadAddressMapper;

    @Autowired
    private CargocmentdetailMapper cargocmentdetailMapper;
    @Autowired
    private BargeInfoMapper bargeInfoMapper;


    @Override
    @Transactional
    public void waybillsSelectShips(CargocmentBO cargocmentBO) {

        // 查询是否已有托运单驳船信息

        List<BargeInfoBO> bargeInfos = cargocmentBO.getBargeInfos();

        // 判断是否是派船后再修改信息
        if (StringUtils.isNotEmpty(bargeInfos)) {
            boolean flag = true;
            for (int i = 0; i < bargeInfos.size(); i++) {
                BargeInfoBO bargeInfoBO = bargeInfos.get(i);
                if (StringUtils.isNotNull(bargeInfoBO.getCargoConsignmentDetailId()) && (StringUtils.isNotNull(bargeInfoBO.getBargeLoadA()) || StringUtils.isNotNull(bargeInfoBO.getBargeLoadB()))) {
                    if ((!bargeInfoBO.getCheckState().equals(Integer.valueOf(FlagBargeState.NO_CHECK.getCode())) || bargeInfoBO.getCheckState().equals(Integer.valueOf(FlagBargeState.CHECK_FAIL.getCode())))) {
                        bargeInfos.remove(i);
                        --i;
                    }
                    if (flag) {
                        List<Cargoconsignmentdetail> cargoconsignmentdetails = cargocmentdetailMapper.selectList(new LambdaQueryWrapper<Cargoconsignmentdetail>()
                                .eq(Cargoconsignmentdetail::getConsignId, cargocmentBO.getCargoconsignment().getId())
                                .eq(Cargoconsignmentdetail::getFlagBargeState, FlagBargeState.NO_CHECK.getCode())
                                .or()
                                .eq(Cargoconsignmentdetail::getConsignId, cargocmentBO.getCargoconsignment().getId())
                                .eq(Cargoconsignmentdetail::getFlagBargeState, FlagBargeState.CHECK_FAIL.getCode()));
                        List<Long> ids = cargoconsignmentdetails.stream().map(Cargoconsignmentdetail::getId).collect(Collectors.toList());
                        if (StringUtils.isNotEmpty(ids)) {
                            cargocmentdetailMapper.deleteBatchIds(ids);
                        }
                        flag = false;
                    }
                }
            }
        }
        // 修改驳船主电话（覆盖PB6_BARGEINFO表的驳船电话）
        if (StringUtils.isNotEmpty(bargeInfos)) {
            for (BargeInfoBO bargeInfo : bargeInfos) {
                if (bargeInfo.getId() != null && StringUtils.isNotEmpty(bargeInfo.getContactPhone())) {
                    BargeInfo bargeInfoTemp = new BargeInfo();
                    bargeInfoTemp.setId(bargeInfo.getId());
                    bargeInfoTemp.setContactPhone(bargeInfo.getContactPhone());
                    bargeInfoMapper.updateById(bargeInfoTemp);
                }
            }
        }

        List<Cargoconsignmentdetail> list=new ArrayList<>();
        Cargoconsignment cargoconsignment = cargocmentBO.getCargoconsignment();

        // 获取托运单明细数据
        List<Cargoconsignmentdetail> newCargoconsignmentdetails = cargocmentdetailMapper.selectList(new LambdaQueryWrapper<Cargoconsignmentdetail>()
                .eq(Cargoconsignmentdetail::getConsignId, cargoconsignment.getId()));
        String serialNumber = null;
        if (StringUtils.isNotEmpty(newCargoconsignmentdetails)) {
            newCargoconsignmentdetails.sort((p1,p2) -> p1.getSerialNumber().compareTo(p2.getSerialNumber()));
            // 获取最大序列号
            serialNumber = newCargoconsignmentdetails.get(newCargoconsignmentdetails.size()-1).getSerialNumber();
        }
        // 判断驳船序列号是否为空
        if (StringUtils.isNotEmpty(bargeInfos)) {
            if (StringUtils.isEmpty(serialNumber)) {
                for (int i = 0; i < bargeInfos.size(); i++) {
                    bargeInfos.get(i).setSerialNumber(String.valueOf(i+1));
                }
            } else {
                Integer aLong = Integer.valueOf(serialNumber);
                for (int i = 0; i < bargeInfos.size(); i++) {
                    if (StringUtils.isEmpty(bargeInfos.get(i).getSerialNumber())) {
                        bargeInfos.get(i).setSerialNumber(String.valueOf((aLong+i+1)));
                    }
                }
            }
        }

        for(BargeInfoBO bargeInfo:bargeInfos){
            Cargoconsignmentdetail cargoconsignmentdetail = new Cargoconsignmentdetail();
            if(bargeInfo.getId()==null){//新的驳船,走驳船审批流程
                bargeInfo.setCheckFlag(1);
                bargeInfo.setRecordCheck(0);
                BargeInfoAudit bargeInfoAudit=new BargeInfoAudit();
                BeanUtils.copyBeanProp(bargeInfoAudit,bargeInfo);
                bargeInfoAuditService.addCarrierBarge(bargeInfoAudit);
            }

            cargoconsignmentdetail.setBargeName(bargeInfo.getBargeName());
            cargoconsignmentdetail.setConsignFlag(cargoconsignment.getConsignFlag());
            cargoconsignmentdetail.setConsignId(cargoconsignment.getId());
            cargoconsignmentdetail.setRationPiece(bargeInfo.getRationPiece());
            cargoconsignmentdetail.setRationWeight(bargeInfo.getRationWeight());
            cargoconsignmentdetail.setMmsi(bargeInfo.getMmsi());
            // 驳船的顺序号
            cargoconsignmentdetail.setSerialNumber(bargeInfo.getSerialNumber());
            // 给驳船id
            cargoconsignmentdetail.setBargeId(bargeInfo.getId());

            SysUser user = SecurityUtils.getLoginUser().getUser();

            if (ObjectUtil.isNotNull(user)){
                cargoconsignmentdetail.setCustomerUserId(StringUtils.isNotEmpty(user.getPhonenumber()) ? user.getPhonenumber() : "");
            }


            cargoconsignmentdetail.setOnineresourse("3");
            cargoconsignmentdetail.setFlagBargeState("0");
            cargoconsignmentdetail.setBargeTel(bargeInfo.getContactPhone());
            cargoconsignmentdetail.setWxRationContactNumber(bargeInfo.getWxRationContactNumber());
            cargoconsignmentdetail.setApplyTime(DateUtils.getTime());
            cargoconsignmentdetail.setChargeBalanceType(StringUtils.isNotEmpty(bargeInfo.getChargeBalanceType())?bargeInfo.getChargeBalanceType():cargoconsignment.getChargeBalanceType());
            if (cargoconsignment.getChargeBalanceType()!=null && cargoconsignment.getChargeBalanceType().equals(PayWayEnum.MONTHLY_PAY.getCodeName())) {
                cargoconsignmentdetail.setWxMonthChargeById(cargocmentBO.getWxMonthChargeById());
                cargoconsignmentdetail.setWxMonthChargeByName(cargocmentBO.getWxMonthChargeByName());
            }else {
                cargoconsignmentdetail.setWxMonthChargeById(bargeInfo.getWxMonthChargeById()!=null?Long.valueOf(bargeInfo.getWxMonthChargeById()):null);
                cargoconsignmentdetail.setWxMonthChargeByName(bargeInfo.getWxMonthChargeById()!=null?bargeInfo.getWxMonthChargeByName():null);
            }

            list.add(cargoconsignmentdetail);
        }

        //插入到数据库
        cargocmentdetailService.saveBatch(list);
        if (StringUtils.isNull(cargocmentBO.getFlag())) {
            //更新派船状态
            cargoconsignment.setWxSelectShipState(SelectShipType.DONESELECT.getCode());

            this.updateById(cargoconsignment);
        }
    }

    /**
     * 船公司办理托运单
     * @param cargocmentBO
     */
    @Override
    @Transactional
    public void addWaybills(CargocmentBO cargocmentBO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Integer wxselectshiptype = cargocmentBO.getCargoconsignment().getWxSelectShipType();//派船类型
        long comid = cargocmentBO.getCargoconsignment().getComId() == null ? 1 : cargocmentBO.getCargoconsignment().getComId();
        //生成托运单号
        String consignflag = this.getNewSerialNo("TD", comid, DateUtils.dateTimeNow("yyMM"), 4);

        if(wxselectshiptype.intValue()== SelectShipType.SELECTCOMPANY.getCode()){//船公司派船
            //只选择公司无船，运单待派船状态
            Cargoconsignment cargoconsignment = cargocmentBO.getCargoconsignment();
            cargoconsignment.setComId(comid); // 公司Id
            cargoconsignment.setShipperDept(loginUser.getUser().getCompanyName()); // 托运人公司，和consigner一致

            cargoconsignment.setOnineresourse("3");
            cargoconsignment.setConsignFlag(consignflag);
            cargoconsignment.setWxCreateUserById(loginUser.getUser().getUserId().intValue());
            cargoconsignment.setWxCreateTime(DateUtils.getTime());
            cargoconsignment.setWxCreateCompanyId(loginUser.getUser().getCompanyId());
            cargoconsignment.setWxApplyUserType(2);
            cargoconsignment.setWxSelectShipType(wxselectshiptype);
            cargoconsignment.setWxSelectShipState(0);
            cargoconsignment.setComId(comid);
            cargoconsignment.setWxSelectShipById(cargocmentBO.getCargoconsignment().getWxSelectShipById());
            cargoconsignment.setWxSelectShipByName(cargocmentBO.getCargoconsignment().getWxSelectShipByName());
            cargoconsignment.setApplyDate(DateUtils.getTime());
            //需要规则生成
            //cargoconsignment.setConsignflag();
            int insert = baseMapper.insert(cargoconsignment);
            if(insert==0){
                insert = baseMapper.insert(cargoconsignment);
            }
            if(insert>0){
                if (cargoconsignment.getWxOutorInformType().equals(CoutformType.OTHER_COMPANY_DISPOSE.getCode())) {//它司出库单则需要上传资料
                    // 设置文件关联id
                    List<UploadAddress> uploadAddress = cargocmentBO.getUploadAddress();
                    List<Long> uploadIds = uploadAddress.stream().map(UploadAddress::getId).collect(Collectors.toList());
                    UploadAddress upload = new UploadAddress();
                    upload.setLinkId(cargoconsignment.getId());
                    int uploadResult = uploadAddressMapper.update(upload, new LambdaQueryWrapper<UploadAddress>()
                            .in(UploadAddress::getId, uploadIds));
                    if (uploadResult <= 0) {
                        log.error("托运单相关文件修改关联失败！");
                        throw new CustomException("托运单相关文件修改关联失败");
                    }
                }
            }
        }
        if(wxselectshiptype.intValue()== SelectShipType.SELECTSELF.getCode()){//自己公司派船
            //自己公司派船则优先生成运单，然后生成运单明细
            //只选择公司无船，运单待派船状态
            Cargoconsignment cargoconsignment = cargocmentBO.getCargoconsignment();
            cargoconsignment.setComId(comid); // 公司Id
            cargoconsignment.setShipperDept(loginUser.getUser().getCompanyName()); // 托运人公司，和consigner一致

            cargoconsignment.setOnineresourse("3");
            cargoconsignment.setConsignFlag(consignflag);
            cargoconsignment.setWxCreateUserById(loginUser.getUser().getUserId().intValue());
            cargoconsignment.setWxCreateTime(DateUtils.getTime());
            cargoconsignment.setWxCreateCompanyId(loginUser.getUser().getCompanyId());
            cargoconsignment.setWxApplyUserType(2);
            cargoconsignment.setWxSelectShipType(wxselectshiptype);
            cargoconsignment.setWxSelectShipState(1);
            cargoconsignment.setComId(comid);
            cargoconsignment.setWxSelectShipById(cargocmentBO.getCargoconsignment().getWxSelectShipById());
            cargoconsignment.setWxSelectShipByName(cargocmentBO.getCargoconsignment().getWxSelectShipByName());
            cargoconsignment.setApplyDate(DateUtils.getTime());
            //需要规则生成
            //cargoconsignment.setConsignflag();
            int insert = baseMapper.insert(cargoconsignment);
            if(insert==0){//重试
                insert=baseMapper.insert(cargoconsignment);
            }
            if(insert>0){
                if (cargoconsignment.getWxOutorInformType().equals(CoutformType.OTHER_COMPANY_DISPOSE.getCode())) {//它司出库单则需要上传资料
                    // 设置文件关联id
                    List<UploadAddress> uploadAddress = cargocmentBO.getUploadAddress();
                    List<Long> uploadIds = uploadAddress.stream().map(UploadAddress::getId).collect(Collectors.toList());
                    UploadAddress upload = new UploadAddress();
                    upload.setLinkId(cargoconsignment.getId());
                    int uploadResult = uploadAddressMapper.update(upload, new LambdaQueryWrapper<UploadAddress>()
                            .in(UploadAddress::getId, uploadIds));
                    if (uploadResult <= 0) {
                        log.error("托运单相关文件修改关联失败！");
                        throw new CustomException("托运单相关文件修改关联失败");
                    }
                }

                //插入明细
                // 添加驳船信息 同时生成托运单明细
                if (StringUtils.isNotEmpty(cargocmentBO.getBargeInfos())) {
                    cargocmentBO.setFlag(1);
                    List<BargeInfoBO> bargeInfos = cargocmentBO.getBargeInfos();
                    for (int i = 0; i < bargeInfos.size(); i++) {
                        bargeInfos.get(i).setSerialNumber(String.valueOf(i+1));
                    }
                }
                this.waybillsSelectShips(cargocmentBO);
            }

        }


    }
    @Override
    public List<ConsignorConsignVO> billslist(CargocmentdetailBO cargocmentdetailBO) {

        List<ConsignorConsignVO> vo = new ArrayList<>();

        SysUser user = SecurityUtils.getLoginUser().getUser();
        cargocmentdetailBO.setCompanyName(user.getCompanyName());
        List<CargocmentVO> billslist = baseMapper.billslist(cargocmentdetailBO);
       if (StringUtils.isNotEmpty(billslist)) {
           List<Long> ids = billslist.stream().map(Cargoconsignment::getId).collect(Collectors.toList());
           List<Cargoconsignmentdetail> cargoconsignmentdetails = cargocmentdetailMapper.selectList(new LambdaQueryWrapper<Cargoconsignmentdetail>()
                   .in(Cargoconsignmentdetail::getConsignId, ids).orderByDesc(Cargoconsignmentdetail::getWxCreateTime));
           for (CargocmentVO cargocmentVO : billslist) {
               List<Cargoconsignmentdetail> detailTemp = new ArrayList<>();
               for (Cargoconsignmentdetail cargoconsignmentdetail : cargoconsignmentdetails) {
                   if (cargocmentVO.getId().equals(cargoconsignmentdetail.getConsignId())) {
                       detailTemp.add(cargoconsignmentdetail);

                   }
               }
               cargocmentVO.setCargoconsignmentdetailList(detailTemp);
           }

           for (int i = 0; i < billslist.size(); i++) {
               CargocmentVO cargocmentVO = billslist.get(i);
               if ("待派船".equals(cargocmentVO.getToAuditFlag()) || "待审核".equals(cargocmentVO.getToAuditFlag())) {
                   List<Cargoconsignmentdetail> cargoconsignmentdetailList = cargocmentVO.getCargoconsignmentdetailList();
                   if (StringUtils.isNotEmpty(cargoconsignmentdetailList)) {
                       int size = cargoconsignmentdetailList.size();
                       for (Cargoconsignmentdetail cargoconsignmentdetail : cargoconsignmentdetailList) {
                           if (!("0".equals(cargoconsignmentdetail.getFlagBargeState()) || ("2".equals(cargoconsignmentdetail.getFlagBargeState())))) {
                               --size;
                           }
                       }
                       if (size==0) {
                           billslist.remove(i);
                           --i;
                       }
                   }
               }
           }

           for (CargocmentVO cargocmentVO : billslist) {
               ConsignorConsignVO consignorConsignVO = new ConsignorConsignVO();
               List<Cargoconsignmentdetail> cargoconsignmentdetailList = cargocmentVO.getCargoconsignmentdetailList();
               if (StringUtils.isNotEmpty(cargoconsignmentdetailList)) {
                   for (Cargoconsignmentdetail cargoconsignmentdetail : cargoconsignmentdetailList) {
                       if (cargoconsignmentdetail.getConsignId().equals(cargocmentVO.getId())) {
                           if (FlagBargeState.NO_CHECK.getCode().equals(cargoconsignmentdetail.getFlagBargeState()) || FlagBargeState.CHECK_FAIL.getCode().equals(cargoconsignmentdetail.getFlagBargeState())) {
                               consignorConsignVO.setBargeName(cargoconsignmentdetail.getBargeName());
                               consignorConsignVO.setRationWeight(cargoconsignmentdetail.getRationWeight());
                               consignorConsignVO.setConsignDetailId(cargoconsignmentdetail.getId());
                               consignorConsignVO.setFlagBargeState(cargoconsignmentdetail.getFlagBargeState());
                               consignorConsignVO.setWxOperateState(cargoconsignmentdetail.getWxOperateState());
                               consignorConsignVO.setApplyModify(cargoconsignmentdetail.getApplyModify());
                               consignorConsignVO.setChargeBalanceType(cargoconsignmentdetail.getChargeBalanceType());
                               consignorConsignVO.setNewWaybill(cargoconsignmentdetail.getNewWaybill());
                           }
                       }
                   }
               }
               consignorConsignVO.setCargeName(cargocmentVO.getCargeName());
               consignorConsignVO.setConsignId(cargocmentVO.getId());
               consignorConsignVO.setConsignFlag(cargocmentVO.getConsignFlag());
               consignorConsignVO.setBeginPort(cargocmentVO.getBeginPort());
               consignorConsignVO.setEndPort(cargocmentVO.getEndPort());
               consignorConsignVO.setConsignee(cargocmentVO.getConsignee());
               consignorConsignVO.setWxSelectShipState(cargocmentVO.getWxSelectShipState());
               consignorConsignVO.setWxOutorInformType(cargocmentVO.getWxOutorInformType());
               consignorConsignVO.setWxCreateUserById(cargocmentVO.getWxCreateUserById());
               consignorConsignVO.setWxSelectShipType(cargocmentVO.getWxSelectShipType());
               consignorConsignVO.setWxApplyUserType(cargocmentVO.getWxApplyUserType());
               consignorConsignVO.setWxSelectShipById(cargocmentVO.getWxSelectShipById());
               consignorConsignVO.setWxSelectShipByName(cargocmentVO.getWxSelectShipByName());
               consignorConsignVO.setWxCreateCompanyId(cargocmentVO.getWxCreateCompanyId());
               vo.add(consignorConsignVO);
           }

       }

        if (StringUtils.isNotEmpty(vo)) {
            // 去重
            vo = vo.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(ConsignorConsignVO::getConsignFlag))), ArrayList::new));

            vo = vo.stream().sorted(Comparator.comparing(ConsignorConsignVO::getConsignId).reversed()).collect(Collectors.toList());

        }



        return vo;
    }

    @Override
    public String getNewSerialNo(String businessType, Long comId, String date, int length) {
        String companyNo="";
        if(comId ==3L){
            companyNo="02";
        }else if(comId == 4L){
            companyNo="03";
        }else if(comId == 1L){
            companyNo="01";
        }else if(comId == 6L){
            companyNo="06";
        }else if(comId == 16L){
            companyNo="16";
        }else{
            companyNo="00";
        }
        String serialNoLike=businessType+companyNo+date;
        String newSerialNo = baseMapper.getNewSerialNo(serialNoLike, length);
        if(StringUtils.isNotEmpty(newSerialNo)){
            int sn = Integer.valueOf(newSerialNo.substring(newSerialNo.length() - length));
            sn+=1;
            return  businessType.toUpperCase()+companyNo+date+StringUtils.leftPad(String.valueOf(sn),length,"0");
        }else{
            return  businessType.toUpperCase()+companyNo+date+StringUtils.leftPad("1",length,"0");
        }
    }
}

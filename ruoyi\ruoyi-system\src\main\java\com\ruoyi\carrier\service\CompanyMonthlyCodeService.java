package com.ruoyi.carrier.service;

import com.ruoyi.carrier.domain.bo.CompanyMonthlyCodeBO;
import com.ruoyi.common.core.domain.AjaxResult;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/12/24 11:30
 */
public interface CompanyMonthlyCodeService {

    /**
     * 获取月结码列表
     * @param companyMonthlyCodeBO
     * @return
     */
    AjaxResult getMonthlyCodeList(CompanyMonthlyCodeBO companyMonthlyCodeBO);

    /**
     * 批量生成月结码
     * @param companyMonthlyCodeBO
     * @return
     */
    AjaxResult createMonthlyCode(CompanyMonthlyCodeBO companyMonthlyCodeBO);

    /**
     * 改变月结码状态
     * @param companyMonthlyCodeBO
     * @return
     */
    AjaxResult updateMonthlyCode(CompanyMonthlyCodeBO companyMonthlyCodeBO);
}

package com.ruoyi.app.controller.databarge;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.AgentDelivery;
import com.ruoyi.databarge.domain.Pb6BargeCheckMessage;
import com.ruoyi.databarge.domain.dto.AgentDeliveryCheckDTO;
import com.ruoyi.databarge.domain.dto.AgentDeliverySearchDTO;
import com.ruoyi.databarge.domain.dto.Pb1CustomerSearchDTO;
import com.ruoyi.databarge.service.AgentDeliveryService;
import com.ruoyi.databarge.service.Pb6BargeCheckMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.sql.Timestamp;

/**
 * Description:
 *
 * @Author: ChenJin on 2021/7/13.
 * @Date: 2021/7/13 9:51
 */
@Api("代理发货信息 - api")
@RestController
@RequestMapping("/barge/agentDelivery")
public class AgentDeliveryController {

    @Autowired
    private AgentDeliveryService agentDeliveryService;

    @Autowired
    private Pb6BargeCheckMessageService pb6BargeCheckMessageService;

    @ApiOperation("代理发货信息列表")
    @PostMapping("/list")
    public AjaxResult selectList(@RequestBody AgentDeliverySearchDTO agentDeliverySearchDTO) {
        return AjaxResult.success(agentDeliveryService.selectList(agentDeliverySearchDTO.getSearchValue()));
    }

    @PostMapping("/add")
    public AjaxResult addAgentDelivery(@RequestBody AgentDelivery agentDelivery){
        return AjaxResult.success(agentDeliveryService.addAgentDelivery(agentDelivery));
    }

    @PostMapping("/update")
    public AjaxResult updateAgentDelivery(@RequestBody AgentDelivery agentDelivery){
        agentDeliveryService.updateAgentDelivery(agentDelivery);
        return AjaxResult.success();
    }

    @PostMapping("/selectUserBargeName")
    public AjaxResult selectUserBargeName(){
        return AjaxResult.success(agentDeliveryService.selectUserBargeName());
    }

    @PostMapping("/agentDeliveryById")
    public AjaxResult agentDeliveryById(@RequestBody Long agentDeliveryId ) { return  AjaxResult.success(agentDeliveryService.getById(agentDeliveryId));}


    @PostMapping("/auditAgentDelivery")
    public AjaxResult auditAgentDelivery(@RequestBody AgentDeliveryCheckDTO agentDeliveryCheckDTO)throws URISyntaxException {
        AgentDelivery agentDelivery=agentDeliveryService.getById(agentDeliveryCheckDTO.getAgentDeliveryId());
        agentDelivery.setAuditTime(Timestamp.valueOf(DateUtils.getTime())) ;
        agentDelivery.setAuditSign(agentDeliveryCheckDTO.getCheckStatus());
        Pb6BargeCheckMessage pb6BargeCheckMessage=pb6BargeCheckMessageService.getById(agentDeliveryCheckDTO.getPb6BargeCheckMessageId());
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        pb6BargeCheckMessage.setAuditmanid(sysUser.getUserId());
        pb6BargeCheckMessage.setAuditman(sysUser.getUserName());
        pb6BargeCheckMessage.setAudittime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        pb6BargeCheckMessage.setAuditflag(agentDeliveryCheckDTO.getCheckStatus());
        if (StringUtils.isNotEmpty(agentDeliveryCheckDTO.getFailureReasons())){
            pb6BargeCheckMessage.setFailurereasons(agentDeliveryCheckDTO.getFailureReasons());
        }
        agentDeliveryService.updateById(agentDelivery);
        pb6BargeCheckMessageService.updateById(pb6BargeCheckMessage);
        pb6BargeCheckMessageService.sendMessageForAgentDeliveryChecked(pb6BargeCheckMessage);
        return AjaxResult.success();
    }

    @PostMapping("/deleteById")
    public AjaxResult deleteById(@RequestBody Long id){
        agentDeliveryService.deleteAgentDelivery(id);
        return AjaxResult.success();
    }
}

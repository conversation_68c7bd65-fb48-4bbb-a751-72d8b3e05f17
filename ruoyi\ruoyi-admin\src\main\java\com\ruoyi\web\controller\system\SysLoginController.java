package com.ruoyi.web.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.domain.BargeInfoAudit;
import com.ruoyi.common.enums.CheckEnum;
import com.ruoyi.common.enums.UserType;
import com.ruoyi.common.mapper.BargeInfoAuditMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private BargeCenterService bargeCenterService;

    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    @Autowired
    private BargeInfoAuditMapper bargeInfoAuditMapper;

    @Autowired
    private ISysUserService iSysUserService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        SysUser sysUser = iSysUserService.selectUserByUserName(loginBody.getUsername());
        if(sysUser != null && sysUser.getLockTime() != null && sysUser.getLockTime().compareTo(DateUtils.getNowDate()) > 0){
            return AjaxResult.error("该账户已被锁定，请联系超级管理员");
        }
        else {
            AjaxResult ajax = AjaxResult.success();
            loginBody.setLoginType("pc");
            // 生成令牌
            String token = loginService.login(loginBody.getLoginType(),loginBody.getUserType(),loginBody.getWxCode(),loginBody.getPhoneNumber(),loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                    loginBody.getUuid(),9999);
            ajax.put(Constants.TOKEN, token);
            return ajax;
        }
    }

    @GetMapping("/login/prodBusinessSystem")
    public void loginProdBusinessSystem(HttpServletResponse response)
    {
        SysUser sysUser=iSysUserService.selectUserByUserName("xsadmin");
        LoginUser loginUser=new LoginUser(sysUser,new HashSet<>());// 权限为空
        String generateToken=tokenService.createToken(loginUser);

        try {
//            response.sendRedirect("http://localhost/auth-redirect?"+generateToken);
            response.sendRedirect("http://************/auth-redirect?"+generateToken);
        }catch (Exception e2){
            e2.printStackTrace();
        }
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = new HashSet<>();
        
        if(user.getUserId() == null && user.getUserType().equals(UserType.PUBUSER.getCode())){
            //为生产系统用户，角色直接返回 user 的
            for (SysRole perm : user.getRoles())
            {
                if (StringUtils.isNotNull(perm))
                {
                    roles.add(perm.getRoleKey().trim());
                }
            }
        } else {
            roles = permissionService.getRolePermission(user);
        }
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);

        // 驳船主获取是否备案
        if (UserType.BARGEADMIN.getCode().equals(user.getUserType())) {
            // 当前备案是否成功
            AjaxResult result = bargeCenterService.checkRecordIsSuccess();
            boolean isRecord = (Boolean) result.get("isRecord");
            if (isRecord) {
                //BargeInfoVO bargeInfo = bargeCenterService.checkRecord(user.getUserId());
                BargeInfoAudit bargeInfoAudit = (BargeInfoAudit) result.get("barge");
                user.setBargeId(bargeInfoAudit.getPb6bargeInfoId());
                ajax.put("barge", bargeInfoAudit);
            }
            //ajax.put("isRecord", isRecord);

            QueryWrapper<SysUserBarge> sub = new QueryWrapper<>();
            sub.eq("USER_ID", user.getUserId());
            SysUserBarge sysUserBarge = sysUserBargeMapper.selectOne(sub);

            // 这条驳船以前是否备案成功
            boolean flag = false;
            if (sysUserBarge != null) {
                QueryWrapper<BargeInfoAudit> query = new QueryWrapper<>();
                query.eq("RECORDERID", user.getUserId())
                        .eq("PB6BARGEINFOID", sysUserBarge.getBargeId())
                        .eq("ISDELETE", 0)
                        .eq("RECORDCHECK", CheckEnum.PASS_CHECK.getCode());
                BargeInfoAudit audit = bargeInfoAuditMapper.selectOne(query);

                QueryWrapper<BargeInfoAudit> wrapper = new QueryWrapper<>();
                wrapper.eq("RECORDERID", user.getUserId())
                        .eq("PB6BARGEINFOID", sysUserBarge.getBargeId())
                        .eq("ISDELETE", 0)
                        .eq("UPDATECHECK", CheckEnum.PASS_CHECK.getCode());
                List<BargeInfoAudit> list = bargeInfoAuditMapper.selectList(wrapper);
                if (audit != null || list.size() > 0) {
                    flag = true;
                }
            }
            ajax.put("isRecorded", flag);
        }

        // 驳船业务员 返回是否绑定驳船
        if (UserType.BARGEUSER.getCode().equals(user.getUserType())) {
            QueryWrapper<SysUserBarge> wrapper = new QueryWrapper<>();
            wrapper.eq("USER_ID", user.getUserId());
            boolean bindBarge = sysUserBargeMapper.selectOne(wrapper)!=null;
            ajax.put("bindBarge", bindBarge);
        }

        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 用户信息
        SysUser user = loginUser.getUser();

        List<SysMenu> menus = new ArrayList<>();
        if(user.getUserId() == null && user.getUserType().equals(UserType.PUBUSER.getCode())){
            //没有用户ID且类型为 pubuser 说明是生产系统账号，需作为仓库员使用   201为仓库员角色
            menus = menuService.selectMenuTreeByRoleId(201L);
        } else {
            menus = menuService.selectMenuTreeByUserId(user.getUserId());
        }
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    @PostMapping("/outLogin")
    public AjaxResult outLogin() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //如果是网厅用户则清除openId
        String userType = iSysUserService.selectOnlineUser(loginUser.getUser().getUserId());
        if(!userType.equals("11")){
            SysUser sysUser = new SysUser();
            sysUser.setUserId(loginUser.getUser().getUserId());
            iSysUserService.cleanOpenByUserId(loginUser.getUser());
        }
        /*// 清除openId
        SysUser sysUser = new SysUser();
        sysUser.setUserId(loginUser.getUser().getUserId());
        iSysUserService.cleanOpenByUserId(loginUser.getUser());*/
        // 删除登录信息
        tokenService.delLoginUser(loginUser.getToken());
        return AjaxResult.success();
    }
}

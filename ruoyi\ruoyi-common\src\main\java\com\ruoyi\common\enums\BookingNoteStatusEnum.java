package com.ruoyi.common.enums;

/**
 *  申请改单/退单状态
 *
 * @Description 申请改单/退单状态
 * <AUTHOR>
 * @Date 2020/8/10  10:56
 */
public enum BookingNoteStatusEnum {

    /**
     *  申请改单/退单（0为申请退单中，1为申请退单通过，2为申请退单失败,3为申请改单中，4为申请改单成功，5为申请改单失败）
     */
    CHARGEBACK_ING_CHECK(0, "申请退单中"),
    CHARGEBACK_YES_CHECK(1, "申请退单通过"),
    CHARGEBACK_NO_CHECK(2, "申请退单失败"),
    MODIFICATION_ING_CHECK(3, "申请改单中"),
    MODIFICATION_YES_CHECK(4, "申请改单成功"),
    MODIFICATION_NO_CHECK(5, "申请改单失败");

    private final Integer code;

    private final String codeName;

    BookingNoteStatusEnum(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

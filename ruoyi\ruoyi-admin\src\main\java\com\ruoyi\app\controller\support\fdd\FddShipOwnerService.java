package com.ruoyi.app.controller.support.fdd;

import com.fdd.api.client.dto.*;
import com.fdd.api.client.release.base.ClientFactory;
import com.fdd.api.client.res.RestResult;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2020/10/16 11:25
 * @Description: FDD驳船主服务类
 */
@Service
@AllArgsConstructor
public class FddShipOwnerService {

    private ClientFactory clientFactory;

    /**
     * 驳船主注册
     * @param account 账号
     * @return Fdd账户customerId
     */
    public String shipOwnerRegister(String account) {
        RegisterDTO registerDTO=new RegisterDTO();
        registerDTO.setType(1);// 1为个人
        registerDTO.setAccount(account);// 需要注册的账号

        try {
            RestResult restResult=clientFactory.accountClient().register(registerDTO);// RestResult{code='1', msg='添加成功', data=c261f3d7cb434a43b4b9013b302f6f38}，data就是个人客户编号
            if("1".equals(restResult.getCode())){
                return (String)restResult.getData();
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 驳船主hash存证
     * @param customerId Fdd账户customerId
     * @param preservationName 存证名称
     * @param preservationDesc 存证描述
     * @param fileName 文件名
     * @param noPerTime 文件最后修改时间
     * @param fileSize 文件大小
     * @param transactionId 交易号
     * @return 存证编号
     */
    public String shipOwnerSaveHashEvidence(String customerId,String preservationName,String preservationDesc,String fileName,String noPerTime,String fileSize,String originalSha256,String transactionId){
        HashEvidenceDTO hashEvidenceDTO=new HashEvidenceDTO();
        hashEvidenceDTO.setCustomerId(customerId);// 客户编号
        hashEvidenceDTO.setIsRepeatVerified(1);// 是否重复存证，1-首次存证，2-重新存证
        hashEvidenceDTO.setType(1);// 存证类型，1-个人，2-企业
        hashEvidenceDTO.setPreservationName(preservationName);// 存证名称
        hashEvidenceDTO.setPreservationDesc(preservationDesc);// 存证描述
        hashEvidenceDTO.setFileName(fileName);// 文件名
        hashEvidenceDTO.setNoPerTime(noPerTime);// 文件最后修改时间，unix时间，单位s
        hashEvidenceDTO.setFileSize(fileSize);// 文件大小
        hashEvidenceDTO.setOriginalSha256(originalSha256);// 文件hash值
        hashEvidenceDTO.setTransactionId(transactionId);// 交易号

        try {
            RestResult restResult=clientFactory.evidenceClient().saveHashEvidence(hashEvidenceDTO);// RestResult{code='1', msg='操作成功', data=20200814111348328912861}，data就是存证编号
            if("1".equals(restResult.getCode())){
                return (String)restResult.getData();
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 驳船主存证申请编号证书
     * @param customerId Fdd账户customerId
     * @param evidenceNo 存证编号
     * @return 个人客户编号
     */
    public String applyEvidenceNumberCert(String customerId,String evidenceNo) {
        EvidenceNumberCertDTO evidenceNumberCertDTO=new EvidenceNumberCertDTO();
        evidenceNumberCertDTO.setCustomerId(customerId);// 客户编号
        evidenceNumberCertDTO.setEvidenceNo(evidenceNo);// 存证编号

        try {
            RestResult restResult=clientFactory.certificateClient().applyEvidenceNumberCert(evidenceNumberCertDTO);// RestResult{code='1', msg='添加成功', data=b3a147c8e58d4fcd9e07a81036bfbdd9}，data就是个人客户编号
            if("1".equals(restResult.getCode())){
                return (String)restResult.getData();
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }
}

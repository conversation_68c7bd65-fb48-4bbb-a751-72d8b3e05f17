// pages/scancollect/chooseprojects/searchprojects/searchprojects.js
const app =  getApp();
Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    //获取父组件的选中月结单位列表数组
    selectedProjectsArray:{
      type:Array,
      default:()=>[]
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    //搜索关键字
    searchKey:"",
    //可选择项目列表
    projectsArray1:[],
    //选中的月结单位数组-判断使用
    selectedProjectsArray1:[],
  },

  //生命周期
  lifetimes: {
    attached: function() {
      // 在组件实例进入页面节点树时执行(页面出现时获取父组件数据赋值给子组件)
      let that = this 
      that.getProjectList()

      
    },
    detached: function() {
      // 在组件实例被从页面节点树移除时执行（页面移除时将子组件数据赋值给父组件）
      let that = this

    },
  },

  pageLifetimes: {
    // 组件所在页面的生命周期函数
    //(页面出现时获取父组件数据赋值给子组件)
    show: function () {  
      let that = this 
      that.getProjectList()

    },
    //（页面隐藏时将子组件数据赋值给父组件）
    hide: function () {
      let that = this

    },
    resize: function () { },
  },


  /**
   * 组件的方法列表
   */
  methods: {
      /**
   * 判断是否存在，不存在返回空字符串或其他
   * @param {传进来的变量} empty 
   * @param {为空时返回的值} replace 
   */
    emptyReturn(empty,replace){
      if(!replace || replace==null){
        replace = ""
      }
      if(empty == null || !empty){
        return replace
      }else{
        return empty
      }
    },

    //清空关键字
    deleteSearchKey(){
      this.setData({
        "searchKey":""
      })
    },
    //关闭
    // closeDialog() {
    //   let that = this 
    //   let detailObj = {} //传给父组件的数据
    //   that.triggerEvent('closeDialog',detailObj) 
    // },
    //搜索关键字
    searchInput1(e){
      let that = this 
      that.setData({
        searchKey:e.detail.value
      })
      setTimeout(()=>{
        that.getProjectList()
      },2000)
      
    },
    //确定-目前只可选择一行
    submitHandle(){
      let that = this 
      let selectedArr = [] //选中项目数组
      let projectArr = that.data.projectsArray1 //月结单位的可选项目列表
    
      //选中状态的传给父组件
      selectedArr = projectArr.filter(item1=>{
        return item1.checked
      })
      let detailObj = {selectedArr:selectedArr} //传给父组件的数据
      // if(selectedArr && selectedArr.length==0){
      //   app.$message("请选择月结单位")
      //   return
      // }
      that.triggerEvent('closeDialog',detailObj) 
    },

    //获取可选择项目列表
    getProjectList(options){
      let that = this
      let searchKey = that.data.searchKey //关键字(项目名称)
      let selectedProjectsArray = that.data.selectedProjectsArray //获取父组件选中的数组
      let selectedProjectsArray1 = that.data.selectedProjectsArray1  //当前页最新选中的项目数组
      app.$post(app.$url.consign.getMonthCompany,{cFullName:searchKey}).then(res=>{
        if(res.code == 200){
          if(res.data){
            let compareArr = [] //比较数组
            //判断当前页最新选中的项目数组与父组件选中的项目数组是否一致，不一致，选中当前页面最新选中的项目数组比较
            if(selectedProjectsArray.length>0 && selectedProjectsArray1.length>0 && selectedProjectsArray[0].id !== selectedProjectsArray1[0].id){
              compareArr = selectedProjectsArray1
            }else if(selectedProjectsArray.length>0 && selectedProjectsArray1.length==0){
              compareArr = selectedProjectsArray
            }else if(selectedProjectsArray.length==0 && selectedProjectsArray1.length>0){
              compareArr = selectedProjectsArray1
            }
            //回选选中
            for(let b = 0; b < res.data.length; b++){
              res.data[b].checked = false //默认取消选中状态
              for(let a = 0; a < compareArr.length; a++){
                if(res.data[b].id === compareArr[a].id){
                  res.data[b].checked = true
                }
                
              }
            }
            this.setData({
              projectsArray1:res.data
            })
          }
        }else {
        app.$message(res.msg)
        }
      })
    },
    
    //选中或取消当前行-目前只可选择一行
    currentCheck(e){
      let that = this 
      let item = e.currentTarget.dataset.item //当前对象
      let index = e.currentTarget.dataset.index //索引
      let projectsArray1 = that.data.projectsArray1 //可选列表数组
      let checkedArray1 = that.data.selectedProjectsArray //选中列表数组
      //状态取反-判断使用
      item.checked = !item.checked
     
      //若该行属于选中状态时，先取消所有选中状态,然后重新选中该行
      if(item.checked){
        projectsArray1.map((item1,index1)=>{
          item1.checked = false
        })
      }
      //状态取反-赋值使用
      projectsArray1[index].checked = !projectsArray1[index].checked 
      
      //返回选中状态的数组
      let selectedProjectsArray1 = projectsArray1.filter(item=>{
        return item.checked
      })
      that.setData({
        projectsArray1,
        selectedProjectsArray1,
      })

     
    }
  }
})

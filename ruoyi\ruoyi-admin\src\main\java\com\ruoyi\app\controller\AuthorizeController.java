package com.ruoyi.app.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.AuthorizeService;
import com.ruoyi.system.service.ISysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/28 9:11
 */
@Api(value = "授权相关api")
@Slf4j
@RestController
@RequestMapping("/authorization")
public class AuthorizeController {

    @Autowired
    private AuthorizeService authorizeService;
    @Autowired
    private ISysMenuService iSysMenuService;

    /**
     * 管理员菜单列表
     * @param menu
     * @return
     */
    @PostMapping("/adminMenu")
    public AjaxResult list(@RequestBody SysMenu menu)
    {
        Integer userId = (Integer) menu.getParams().get("userId");
        Long loginUserId = SecurityUtils.getLoginUser().getUser().getUserId();
        Long sysUserId;
        if (userId == null) {
            sysUserId = loginUserId;
        } else {
            sysUserId = userId.longValue();
        }

        List<SysMenu> menus = iSysMenuService.selectMenuList(menu, sysUserId);
        return AjaxResult.success(menus);
    }

    /**
     * 船公司业务员列表
     * @param user
     * @return
     */
    @PostMapping("/userAuthList")
    public AjaxResult getAuthList(@RequestBody SysUser user) {

        if (user.getUserId() == null) {
            return AjaxResult.error("被授权用户id不能为空");
        }

        return authorizeService.getAuthList(user.getUserId());
    }

//    /**
//     * 获取授权菜单按钮列表
//     * @param user 被授权用户id
//     * @return
//     */
//    @ApiOperation(value = "获取授权菜单按钮列表", tags = "获取授权菜单按钮列表")
//    @PreAuthorize("@ss.hasPermi('weChat:authorization:authList')")
//    @PostMapping("/getAuthList")
//    public AjaxResult getAuthList(@RequestBody SysUser user) {
//
//        if (user.getUserId() == null) {
//            return AjaxResult.error("被授权用户id不能为空");
//        }
//
//        return authorizeService.getAuthList(user.getUserId());
//    }

    /**
     * 管理员授权
     */
    @ApiOperation(value = "管理员授权", tags = "管理员授权")
    //@PreAuthorize("@ss.hasPermi('weChat:authorization:authorize')")
    @PostMapping("/authorize")
    public AjaxResult authorize(@RequestBody SysUser sysUser) {

        if (sysUser.getUserId() == null) {
            log.error("AuthorizeController - authorize - 被授权用户id不能为空");
            AjaxResult.error("被授权用户id不能为空");
        }
        return AjaxResult.success(authorizeService.authorize(sysUser.getUserId(), sysUser.getBargeId(), sysUser.getMenus()));
    }

    /**
     * 赋予角色权限
     * @param sysUser
     * @return
     */
    @PostMapping("/authRole")
    public AjaxResult authRole(@RequestBody SysUser sysUser) {

        if (sysUser.getUserId() == null) {
            log.error("AuthorizeController - authorize - 被授权用户id不能为空");
            AjaxResult.error("被授权用户id不能为空");
        }
        return AjaxResult.success(authorizeService.authRole(sysUser.getUserId(), sysUser.getMenus()));
    }
}

package com.ruoyi.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.BargeInfoAudit;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.vo.BargeInfoVO;

import java.util.List;

/**
 * @Description 驳船接口
 * <AUTHOR>
 * @Date 2020/8/5 9:53
 */
public interface BargeInfoService extends IService<BargeInfo> {

     //void addCarrierBarge(BargeInfoAudit bargeInfoAudit);

     //Boolean bargeAudit(BargeInfoAudit bargeInfoAudit);

     List<BargeInfoVO> getBargeList(BargeInfoBO bargeInfoBO);

     List<BargeInfoVO> getBargeListByCompanyId(BargeInfoBO bargeInfoBO);

     List<BargeInfoVO> getBargeListByCompanyUser(BargeInfoBO bargeInfoBO);

     List<BargeInfoVO> getBargeListByNoCompany(BargeInfoBO bargeInfoBO);




}

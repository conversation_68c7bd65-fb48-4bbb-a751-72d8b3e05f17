SELECT DISTINCT
	pb6_bargework.id,
	pb6_bargework.waterwaycargoidid,
	pb6_bargework.waterwaycargoid,
	pb6_bargeInfo.bargeid,
	pb6_bargeInfo.bargename,
	pb6_waterwaycargo.outorinformid,
	pb6_waterwaycargo.cargename AS cargename,
	pb6_waterwaycargo.rationweight,
	pb6_waterwaycargo.rationpiece,
	( SELECT sum( pb6_xsorderdetail.planworkweight ) FROM pb6_xsorderdetail WHERE pb6_xsorderdetail.waterwaycargoid = pb6_bargework.waterwaycargoid ) AS planWeight,
	( SELECT sum( pb6_xsorderdetail.planworkpiece ) FROM pb6_xsorderdetail WHERE pb6_xsorderdetail.waterwaycargoid = pb6_bargework.waterwaycargoid ) AS planPiece,
	pb6_bargework.registertime,
	pb6_bargework.registerprincipalid,
	pb6_bargework.registerprincipal,
	pb6_bargework.workberthid,
	pb6_bargework.workberth,
	pb6_bargework.shipmentstackid,
	pb6_bargework.shipmentstack,
	pb6_bargework.beginworktime,
	pb6_bargework.endworktime,
	pb6_bargework.leaveporttime,
	pb6_bargework.leaveportprincipalid,
	pb6_bargework.leaveportprincipal,
	pb6_bargework.warehousemanid,
	pb6_bargework.warehouseman,
	pb6_bargework.worktype,
	pb6_bargework.loadbargetype,
	pb6_bargework.flagframeorder,
	pb6_waterwaycargo.flagBargeState,
	pb6_bargework.workid,
	pb6_bargework.comid,
	pb6_waterwaycargo.outOrInFormIdId,
	pb6_waterwaycargo.bargeIdId,
	pb6_bargework.placeType,
	pb6_bargework.flagStraight,
	pb6_bargework.uniqueCode,
	pb6_bargework.shipName,
	pb6_bargework.bargeLinkman,
	pb6_bargework.contactPhone,
	pb6_waterwaycargo.flagLoadOrUnload,
	pb6_bargework.remark,
	pb6_bargework.flagLoadometer,
	PB6_BARGEWORK.DELOADINGFLAG,
	pb6_waterwaycargo.DISRATIONWEIGHT,
	pb6_waterwaycargo.disrationpiece,
	pb6_waterwaycargo.loadometeridid,
	pb6_waterwaycargo.loadometerid,
	pb6_waterwaycargo.consignee,pb6_waterwaycargo.consigner as customer,
/*CASE

	WHEN pb6_waterwaycargo.comid = 1 THEN
	(
SELECT
sum( PB8_TRAINWEIGHRECORD.cargoWeight )
FROM
	PB8_TRAINWEIGHRECORD
WHERE
	PB8_TRAINWEIGHRECORD.loadometerbillno = pb6_xsloadbargeorder.loadometerid
	AND PB8_TRAINWEIGHRECORD.ISDELETED <> 'Y'
	AND REPLACE ( PB8_TRAINWEIGHRECORD.Noticeno, '*', '' ) = pb6_bargework.waterwaycargoid
	) ELSE (
	SELECT
		sum( PB8_TRAINWEIGHRECORD.cargoWeight )
	FROM
		PB8_TRAINWEIGHRECORD
	WHERE
		PB8_TRAINWEIGHRECORD.loadometerbillno = pb6_waterwaycargo.loadometerid
		AND PB8_TRAINWEIGHRECORD.ISDELETED <> 'Y'
		AND REPLACE ( PB8_TRAINWEIGHRECORD.Noticeno, '*', '' ) = pb6_bargework.waterwaycargoid
	)
	END AS loadworkweight,
CASE

		WHEN pb6_waterwaycargo.comid = 1 THEN
		(
		SELECT
			sum( PB8_TRAINWEIGHRECORD.cargoQuantity )
		FROM
			PB8_TRAINWEIGHRECORD
		WHERE
			PB8_TRAINWEIGHRECORD.loadometerbillno = pb6_xsloadbargeorder.loadometerid
			AND PB8_TRAINWEIGHRECORD.ISDELETED <> 'Y'
			AND REPLACE ( PB8_TRAINWEIGHRECORD.Trainnum, '*', '' ) = pb6_bargeInfo.Bargename
			AND PB8_TRAINWEIGHRECORD.Heatime >= pb6_bargework.registertime
			AND PB8_TRAINWEIGHRECORD.Heatime <= pb6_bargework.endworktime
			) ELSE (
		SELECT
			sum( PB8_TRAINWEIGHRECORD.cargoQuantity )
		FROM
			PB8_TRAINWEIGHRECORD
		WHERE
			PB8_TRAINWEIGHRECORD.loadometerbillno = pb6_waterwaycargo.loadometerid
			AND PB8_TRAINWEIGHRECORD.ISDELETED <> 'Y'
			AND REPLACE ( PB8_TRAINWEIGHRECORD.Trainnum, '*', '' ) = pb6_bargeInfo.Bargename
			AND PB8_TRAINWEIGHRECORD.Heatime >= pb6_bargework.registertime
			AND PB8_TRAINWEIGHRECORD.Heatime <= pb6_bargework.endworktime
		)
	END AS workPiece,*/
	pb6_bargework.Shipmentrate,
	pb6_waterwaycargo.flagManyFormId,
	pb6_bargework.flagsequence,
	pb6_bargework.isbindworkclass,
	pb6_bargework.APPLICATIONCLASSES,
	pb6_bargework.isWorking,
	pb6_waterwaycargo.packagetype,
	pb6_waterwaycargo.isApplyUniquecode,
	pb6_waterwaycargo.totalcharge,
	pb6_waterwaycargo.WATERWAYCARGONO,
CASE

		WHEN pb6_bargework.comid = 4 THEN
		nvl (
			ROUND( TO_NUMBER ( to_date ( pb6_bargework.endworktime, 'yyyy-MM-dd hh24:mi:ss' ) - to_date ( pb6_bargework.beginworktime, 'yyyy-MM-dd hh24:mi:ss' ) ) * 24 * 60 ),
			0
			) ELSE nvl (
			ROUND( TO_NUMBER ( to_date ( pb6_bargework.endworktime, 'yyyy-MM-dd hh24:mi:ss' ) - to_date ( pb6_bargework.registertime, 'yyyy-MM-dd hh24:mi:ss' ) ) * 24 * 60 ),
			0
		)
	END,
CASE

		WHEN pb6_waterwaycargo.flagbargestate = '2' THEN
		nvl (
			ROUND( TO_NUMBER ( to_date ( pb6_bargework.endworktime, 'yyyy-mm-dd hh24:mi:ss' ) - to_date ( pb6_bargework.registertime, 'yyyy-MM-dd hh24:mi:ss' ) ) * 24 * 60 ),
			0
			) ELSE nvl (
			ROUND(
				TO_NUMBER ( to_date ( TO_CHAR ( sysdate, 'yyyy-mm-dd hh24:mi:ss' ), 'yyyy-MM-dd hh24:mi:ss' ) - to_date ( pb6_bargework.registertime, 'yyyy-MM-dd hh24:mi:ss' ) ) * 24 * 60
			),
			0
		)
	END,
	pb6_bargework.loadWeight,
	pb6_bargework.straightWeight,
	pb6_bargework.loadPiece,
	pb6_bargework.straightPiece,
	pb6_waterwaycargo.cargosize,
	pb6_bargework.dept,
CASE

		WHEN pb6_bargework.comid = 4 THEN
		(
		SELECT
			round( sum( to_number ( xs2.workweight ) / to_number ( pb6_bargework.shipmentrate ) ) )
		FROM
			pb6_xsloadbargeorder xs1,
			pb6_xsorderdetail xs2
		WHERE
			xs1.orderid = xs2.orderid
			AND xs2.waterwaycargoid = pb6_bargework.waterwaycargoid
			) ELSE (
		SELECT
			sum( xs2.workweight )
		FROM
			pb6_xsloadbargeorder xs1,
			pb6_xsorderdetail xs2
		WHERE
			xs1.orderid = xs2.orderid
			AND xs2.waterwaycargoid = pb6_bargework.waterwaycargoid
			AND xs1.type = '4'
		)
	END AS workWeight,
	pb6_waterwaycargo.validDate AS endport,
	(
	SELECT
		cast( sum( cast( tscargoite1_.weightValue AS DOUBLE PRECISION ) ) AS varchar2 ( 255 CHAR ) )
	FROM
		PB11_TALLYSHEET_LOADITEM tallysheet0_,
		PB11_TSCARGOITEM tscargoite1_
	WHERE
		tallysheet0_.ciid = tscargoite1_.ciid
		AND tallysheet0_.noticeNo = pb6_bargework.waterwaycargoid
		AND tallysheet0_.outFormid = pb6_waterwaycargo.outorinformid
	) AS weightValue,
	(
	SELECT
		cast( sum( cast( tscargoite1_.amt AS DOUBLE PRECISION ) ) AS varchar2 ( 255 CHAR ) )
	FROM
		PB11_TALLYSHEET_LOADITEM tallysheet0_,
		PB11_TSCARGOITEM tscargoite1_
	WHERE
		tallysheet0_.ciid = tscargoite1_.ciid
		AND tallysheet0_.noticeNo = pb6_bargework.waterwaycargoid
		AND tallysheet0_.outFormid = pb6_waterwaycargo.outorinformid
	) AS amt,
		( select sum(i.amt) from PB11_TALLYSHEET_LOADITEM t
		left join pb11_tscargoitem i on t.ciid = i.ciid
		where t.noticeno=pb6_bargework.waterwaycargoid )as workPiece,
	pb6_bargework.ISCOAL,
-- 	pb6_bargework.customer,
	pb6_waterwaycargo.flow,
	pb6_bargework.flagyukou,
	pb6_bargework.flagjiekou,
	pb6_bargework.yukouweight,
	pb6_bargework.jiekouweight,
	pb6_bargework.IsCheck,
	pb6_bargework.checkMan
FROM
	pb6_bargework
	LEFT JOIN pb6_waterwaycargo ON pb6_waterwaycargo.id = pb6_bargework.waterwaycargoidid
	LEFT JOIN pb3_coutForm ON pb3_coutForm.id = pb6_waterwaycargo.outOrInFormIdId
	LEFT JOIN pb6_bargeInfo ON pb6_bargeInfo.id = pb6_waterwaycargo.bargeidid
	LEFT JOIN pb4_eighthoursplan ON pb4_eighthoursplan.id = pb6_bargework.APPLICATIONCLASSES
	LEFT JOIN pb6_xsorderdetail ON pb6_xsorderdetail.waterwaycargoid = pb6_bargework.waterwaycargoid
	LEFT JOIN pb6_xsloadbargeorder ON pb6_xsloadbargeorder.orderid = pb6_xsorderdetail.orderid
WHERE
	1 = 1
AND pb6_bargework.id = ?

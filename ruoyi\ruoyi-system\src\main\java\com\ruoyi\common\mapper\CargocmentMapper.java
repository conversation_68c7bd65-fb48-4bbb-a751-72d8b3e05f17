package com.ruoyi.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.domain.Cargoconsignment;
import com.ruoyi.common.domain.bo.CargocmentBO;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.vo.CargocmentVO;
import com.ruoyi.common.domain.vo.PubPortVo;

import java.util.List;

public interface CargocmentMapper extends BaseMapper<Cargoconsignment> {

    /**
     *  搜索目的港
     * @param searchValue
     */
    List<PubPortVo> searchEndPort(String searchValue);


    String getNewSerialNo(String serialNoLike, int length);

    // 保存托运单
    int saveCargocment(Cargoconsignment Cargoconsignment);


    // 修改托运单信息
    int updateBookingNoteInfo(Cargoconsignment cargoconsignment);

    //查询待审核tab（包含待派船任务+待审核的托运单）
    List<CargocmentVO> billslist(CargocmentdetailBO cargocmentdetailBO);
}

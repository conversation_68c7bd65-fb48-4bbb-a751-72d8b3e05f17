package com.ruoyi.common.service.impl;


import com.ruoyi.common.domain.bo.CoutformBO;
import com.ruoyi.common.domain.vo.CoutformVO;
import com.ruoyi.common.mapper.CoutformMapper;
import com.ruoyi.common.service.CoutformService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 出库单业务层实现类
 * <AUTHOR>
 * @Date 2020/8/10  17:02
 */
@Service
public class CoutformServiceImpl implements CoutformService {
    @Autowired
    private CoutformMapper coutformMapper;
    @Override
    public List<CoutformVO> getCoutformList(CoutformBO coutformBO) {
        return coutformMapper.getCoutformList(coutformBO);
    }
}

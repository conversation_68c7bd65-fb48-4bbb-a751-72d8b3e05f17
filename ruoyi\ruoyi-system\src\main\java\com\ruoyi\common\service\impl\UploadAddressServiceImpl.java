package com.ruoyi.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.domain.UploadAddress;
import com.ruoyi.common.enums.UploadDataType;
import com.ruoyi.common.mapper.UploadAddressMapper;
import com.ruoyi.common.service.UploadAddressService;
import com.ruoyi.common.utils.sign.Md5Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UploadAddressServiceImpl extends ServiceImpl<UploadAddressMapper, UploadAddress> implements UploadAddressService {

    @Autowired
    private UploadAddressMapper uploadAddressMapper;

    @Override
    public List<UploadAddress> getUploadAddressList(Long pb6BargeworkId, String waterwayCargoId) {
        UploadAddress uploadAddress = new UploadAddress();
        //uploadAddress.setLinkId(pb6BargeworkId);
        uploadAddress.setLinkType(UploadDataType.CONSIGN_DATA.getType());
        uploadAddress.setWaterwayCargoId(Md5Utils.MD5EncodeUtf8(waterwayCargoId));
        return uploadAddressMapper.getBargeRecordData(uploadAddress);
    }
}

package com.ruoyi.databarge.domain.vo;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2020/8/5 15:50
 */
@Data
public class Pb6CargoconsignmentVO {

    private Long mainId;
    private String consignflagFromMain;
    private String loadometerid;
    private String outorinformid;
    private String cargename;
    private String packagetype;
    private String consigner;
    private String consignee;
    private String consignmentflag;
    private String shippingconame;
    private String beginport;
    private String midport;
    private String endport;
    private String othercoastalinlandport;
    private String applydate;
    private String modifyman;
    private String modifydate;
    private String shipperdept;
    private String specialproceeding;

    private String settlementStatus;
    private Long settlementId;
    // 结算单位
    private String settlementCompany;

    // 提单号
    private String billNo;

    // 总吨数
    private BigDecimal totalTonnage;

    // 金额
    private BigDecimal amount;

    // 积载因素
    private String stowageFactor;

    // 费率
    private String rate;

    // 货物类别
    private String cargoType;

    private List<Pb6Cargoconsignmentdetail> pb6CargoconsignmentdetailList;
}

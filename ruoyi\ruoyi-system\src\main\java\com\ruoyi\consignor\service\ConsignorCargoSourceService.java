package com.ruoyi.consignor.service;

import com.ruoyi.barge.domain.bo.BargeCapacityPublishBO;
import com.ruoyi.barge.domain.vo.BargeCapacityPublishVO;
import com.ruoyi.consignor.domain.bo.CargoSourceBO;
import com.ruoyi.consignor.domain.vo.CargoSourceVO;
import org.springframework.validation.BindingResult;

import java.util.List;

/**
 * @Description 货主的货源业务层接口
 * <AUTHOR>
 * @Date 2020/7/29  17:00
 */
public interface ConsignorCargoSourceService {

    // 新增货源
    void saveCargoSource(CargoSourceBO cargoSourceBO, BindingResult result);

    // 修改货源
    void updateCargoSource(CargoSourceBO cargoSourceBO, BindingResult result);


    // 根据id删除货源
    void deleteCargoSourceById(Long id);

    // 查询货源
    List<CargoSourceVO> selectCargoSource(CargoSourceBO cargoSourceBO);

    // 查询动力数据
    List<BargeCapacityPublishVO> selectImpetusData(BargeCapacityPublishBO bargeCapacityPublishBO);
}


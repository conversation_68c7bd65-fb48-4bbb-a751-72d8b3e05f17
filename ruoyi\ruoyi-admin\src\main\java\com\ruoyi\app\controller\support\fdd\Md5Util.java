package com.ruoyi.app.controller.support.fdd;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @Date 2020/10/16 11:31
 * @Description:
 */
public class Md5Util {
    public static byte[] encryptMD5(String code,String charsetName) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(code.getBytes(charsetName));
            return md5.digest();
            //return DigestUtils.md5Digest(code.getBytes("UTF-8"));
        }catch (NoSuchAlgorithmException e){
            e.printStackTrace();
        }catch (UnsupportedEncodingException e){
            e.printStackTrace();
        }
        return null;
    }
    private final static String[] hexDigits = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};// 16进制数字数组
    public static String byteArrayToHexString(byte[] b) {// 将byte[]转换成16进制字符串
        StringBuilder resultSb = new StringBuilder();
        for (byte aB : b) {
            resultSb.append(byteToHexString(aB));
        }
        return resultSb.toString();
    }
    private static String byteToHexString(byte b) {// 将byte转为16进制字符串
        int n = b;
        if (n < 0) {
            n = 256 + n;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }
}


// pages/minepage/record/record.js
const app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 提交驳船审批参数
    submitData: null,
    // 协议是否勾选
    isContractChecked: false,
    // 初始详情数据-用于保存对比
    initBargeDetailData: null,
    // 是否重新渲染页面-预览图片和上传图片会调onshow接口
    isunRefresh: false,
    // 是否显示印章
    canvaShow: false,
    // 删除已上传图片数组
    deleteUploadList: [],
    //是否显示查找租用公司的弹窗
    showSearchDialog:false,
    //选中的租用公司数组
    selectedProjectsArray:[],
    // 租用方式索引
    bingingTypeIndex: "",
    // 船支类型索引
    bargeTypeIndex: "",
    // 船支类型数组
    bargeTypeArray:[{
      label: "租用船",
      value: 2
    },],
    // 租用方式数组
    bingingTypeArray:[/*{
      label: "自有",
      value: 1
    },*/{
      label: "临时租用",
      value: 2
    },{
      label: "长期租用",
      value: 3
    },/*{
      label: "其他",
      value: 4
    }*/],
    // 是否申请租用
    isApplyBind: false,
    //管理员之前是否成功备案，true成功，false失败,首次登录没有isRecorded属性代表没有备案
    isAlreadyRecorded :"",
    //用户类型userType：00系统用户， 01-注册用户，10-货主，11-托运单联系人，12-船公司管理员，13-船公司业务员，14-驳船管理员，15-驳船业务员
    userInfos:"",//用户信息
    activeTab: 1,
    showIdentityPositiveImg:false,//是否显示驳船主身份证正面图片
    showIdentityNegativeImg:false,//是否显示驳船主身份证反面图片
    IdentityPositiveImgArr:[],//驳船主身份证正面图片数组-临时存储图片数组
    IdentityPositiveUploadArr:[],//驳船主身份证正面图片数组-上传返回的图片数组
    IdentityNegativeImgArr:[],//驳船主身份证反面图片数组-临时存储图片数组
    IdentityNegativeUploadArr:[],//驳船主身份证反面图片数组-上传返回的图片数组
    //个人备案
    personForm:{
      nickName:"",//姓名
      phonenumber:"",//手机
      // userName:"",//账号
      // wxAccount:"",//微信账号（不传）
      email:"",//邮箱
      identityId:"",//身份证号
      
    },
    // 驳船备案数据
    bargeParam: {
      id: '',// 驳船id主键
      barge: '', // 已选驳船(驳船名称和驳船标识Id)
      bargeName: '', // 驳船名称
      bargeId: '', // 驳船识别号
      mmsi: '', // MMSI标识
      bargeWeight: '', // 驳船净吨
      bargeLoadA: '', // 船舶载货量（A级）
      bargeLoadB: '', // 船舶载货量（B级）
      validSailDate: '', //证书有效期
      belongArea: '', //船籍港
      bargeOwner: '',//驳船所有人
      contactPhone: '',//联系电话
      bargeOperator: '',//驳船经营人
      auditStatus: '',//租用状态
      bargeType: '',//船支类型
      bingingType: '',//租用方式
      companyName: '',//租用公司

      loadingWeightMin: '', // 装载重量最小
      loadingWeightMax: '', // 装载重量最小
      cargoNotAllowed: '', //不允许装载货物
      flag:false
    },
     
    coverBargeImgArr:[],//船舶检验证书簿封面-临时存储图片数组
    coverBargeImgUploadArr:[],//船舶检验证书簿封面-上传返回的图片数组
    bargeMainItemImgArr:[],//船舶主要项目内容-临时存储图片数组
    bargeMainItemUploadArr:[],//船舶主要项目内容-上传返回的图片数组
    seaworthinessCertificateImgArr:[],//船舶适航证书-临时存储图片数组
    seaworthinessCertificateUploadArr:[],//船舶适航证书-上传返回的图片数组
    automaticAisImgArr:[],//船舶自动识别系统AIS证书-临时存储图片数组
    automaticAisUploadArr:[],//船舶自动识别系统AIS证书-上传返回的图片数组
    powerOfAttorneyImgArr:[],//委托书-临时存储图片数组
    powerOfAttorneyUploadArr:[],//委托书-上传返回的图片数组

    //选中驳船数组（校验使用）
    selectedBargeArr: [] ,
    //船舶检验证书簿封面-图片列表是否折叠
    showCoverImg:false,
    //船舶主要项目内容-图片列表是否折叠
    showMainItemImg:false,
    //船舶适航证书-图片列表是否折叠
    showSeaworthinessImg:false,
    //船舶自动识别系统AIS证书-图片列表是否折叠
    showAutomaticImg:false,
    //委托书-图片列表是否折叠
    showAttorneyImg:false,

    //印章图片
    sealImg: '',
    //印章图片-显示
    sealImg1: '',

    // 禁装货物列表
    cargoNotAllowedOptions: [], // 下拉框选项列表
    cargoNotAllowedIndex: 0 ,// 当前选中的值的索引，默认为第一个选项
    selectedCargoNotAllowed: {} ,// 选中的禁装货物字符串
    selectedValues:{} //选中的禁装货物
  },

  /**
   * 
   * 方法
   */
  /**
   * 判断是否存在，不存在返回空字符串或其他
   * @param {传进来的变量} empty 
   * @param {为空时返回的值} replace 
   */
  emptyReturn(empty,replace){
    if(!replace || replace==null){
      replace = ""
    }
    if(empty == null || !empty){
      return replace
    }else{
      return empty
    }
  },

  // 获取电子签章详情
  getSealDetail(shipId,shipUserId) {
    let that = this 
    let param = {
      shipId,
      shipUserId,
    }
    if(shipId && shipUserId) {
      app.$post(app.$url.mine.getSeal, param).then(res=>{
        if(res.code == 200){
          that.setData({
            sealImg1:'data:image/png;base64,'+res.data.fileBase64
          })
        }else {
          app.$message(res.msg)
        }
      })
    }
    
  },

  // 点击切换tab
  handleChangeTab(e) {
    if(e.target.dataset.tabid == 2) {
      this.personSave()
    }
    else if(e.target.dataset.tabid == 3) {
      this.shipSave()
    }else{
      this.setData({
        'activeTab': e.target.dataset.tabid
      })
      // 使页面滚动到顶部
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      })
    }
   

  },
  // 点击驳船选择
  handleChooseBarge() {
    //选中的驳船
    let selectedBargeArr = JSON.stringify(this.data.selectedBargeArr)
    let bargeId = this.data.bargeParam.id // 选择的驳船id
    wx.navigateTo({
      url: '/pages/minepage/choosebarge/choosebarge?selectedBargeArr='+selectedBargeArr+'&bargeId='+bargeId,
      success: (result) => {
        
      },
      fail: () => {},
      complete: () => {}
    });
  },

  //最终提交
  async submitFinal() {
    let that = this
    if(that.data.powerOfAttorneyImgArr && that.data.powerOfAttorneyImgArr.length == 0) {
      app.$message("请上传委托书")
      return
    }
    if(!that.data.isContractChecked) {
      app.$message("请勾选协议,否则无法成功备案")
      return
    }
    let powerOfAttorneyImgArr = that.data.powerOfAttorneyImgArr //委托书-临时存储图片数组
    let powerOfAttorneyUploadArr = [] //委托书-上传返回的图片数组
    // 委托书
    for(let i = 0; i < powerOfAttorneyImgArr.length; i++) {
      let item = powerOfAttorneyImgArr[i];
      let obj = {
        url:item.url,//路径
        dataType: "53",//资料类型
      }
      // https路径图片不重复上传
      if(obj.url.indexOf("https") == -1) {
        let result = await that.uploadImgEvent(obj.url, obj.dataType)
        if(result != "fail") {
          powerOfAttorneyUploadArr.push(result)
        }else if(result == "fail"){
          return 'fail';
        }
      }
    }
    let dataList = that.data.submitData.dataList.concat(powerOfAttorneyUploadArr)
    that.setData({
      "submitData.dataList": dataList
    });
     
    let deleteUploadList = that.data.deleteUploadList // 删除数组
    for(let i = 0; i < deleteUploadList.length; i++) {
      if(typeof deleteUploadList[i] === "object") {
        continue
      }else {
        deleteUploadList[i] = {
          id: deleteUploadList[i]
        };
      }
    }
    that.setData({
      'submitData.delDataList': that.data.deleteUploadList
    })
    //首次登录时，绑船失败，停留备案页面，成功直接返回登录页面
    console.log(that.data.bargeParam.flag)
    if(!that.data.bargeParam.flag){
        app.$post(app.$url.mine.bargeRecord,that.data.submitData).then(res=>{
            if(res.code == 200){
              app.$message("提交备案成功,请耐心等待审批")
              setTimeout(()=>{
                wx.reLaunch({ url: '/pages/login/login' })
              },1000)
            }else {
              app.$message(res.msg)
            }
          })
    }
    //修改时新增驳船
    else{
        app.$post(app.$url.mine.bargeRecordAdd,that.data.submitData).then(res=>{
            if(res.code == 200){
              app.$message("提交备案成功,请耐心等待审批")
              setTimeout(()=>{
                wx.reLaunch({ url: '/pages/login/login' })
              },1000)
            }else {
              app.$message(res.msg)
            }
          })
    }
  },

  // 跳转电子签章tab
  shipNext() {
    wx.showLoading({
      title: '加载中',
      mask: true,
    })
    this.shipSave()
  },

  // 勾选协议
  checkContractChange(val) {
    this.setData({
      isContractChecked:!this.data.isContractChecked
    })
  },

  //关闭搜索月结单位弹窗,获取子组件选中的值
  closeSearchDialog(e){
    let that = this
    let selectedArr = e.detail.selectedArr //获取子组件选中月结单位的数组
    if(selectedArr && selectedArr.length>0){
      that.setData({
        "bargeParam.companyId":selectedArr[0].id, // 租用公司id
        "bargeParam.companyName":selectedArr[0].cfullName, // 租用公司名称
      })
    }else{
      that.setData({
        "bargeParam.companyId":"", // 租用公司id
        "bargeParam.companyName":"", // 租用公司名称
      })
    }
    that.setData({
      showSearchDialog : !that.data.showSearchDialog,
      selectedProjectsArray:selectedArr,
    })
  },

  // 租用状态format auditStatus（1-已租用，2/0/null-未租用）
  auditStatusFormat(val) {
    switch(val){
      case 0: return "未租用"; break;
      case 1: return "已租用"; break;
      case 2: return "未租用"; break;
      default : return "未租用"; break;
    }
  },

  //打开搜索租用公司弹窗
  wxMonthChargeByNameInput(e){
    this.setData({
      showSearchDialog : !this.data.showSearchDialog,
    })
  },

  // 租用方式change
  bingingTypeChange(e) {
    let index = e.detail.value //索引
    this.setData({
      bingingTypeIndex: index,
      "bargeParam.bingingType": this.data.bingingTypeArray[index].value
    })
  },
  
  // 船支类型change
  bargeTypeChange(e) {
    // 船只类型为租用船-显示租用方式；船只类型为自有船-不显示租用方式，并清空租用方式的值
    let index = e.detail.value //索引
    // 为自有船时
    if(index == 0) {
      this.setData({
        bingingTypeIndex: "",
        "bargeParam.bingingType": ""
      })
    }
    this.setData({
      bargeTypeIndex: index,
      "bargeParam.bargeType": this.data.bargeTypeArray[index].value
    })
  },
  
  // 申请租用change
  checkBindChange(e) {
    let isApplyBind = !this.data.isApplyBind
    // 选中状态
    if(isApplyBind) {
      // 默认船支类型、租用方式
      this.setData({
        isApplyBind,
        "bargeParam.auditStatus":"已租用",
        bargeTypeIndex: 0,
        "bargeParam.bargeType": this.data.bargeTypeArray[0].value,
        // bingingTypeIndex: 0,
        // "bargeParam.bingingType": this.data.bingingTypeArray[0].value
      })
   
    }else {
    // 取消选中，清空船支类型、租用方式、租用公司
      this.setData({
        isApplyBind,
        "bargeParam.auditStatus":"未租用",
        bargeTypeIndex: "",
        "bargeParam.bargeType": "",
        bingingTypeIndex: "",
        "bargeParam.bingingType": "",
        "bargeParam.companyId": "",
        "bargeParam.companyName": ""
      })
    }
  },


  //删除照片
  deleteImg(e){
    let that = this
    let index = e.currentTarget.dataset.index //索引
    let item = e.currentTarget.dataset.item //当前对象 
    let datatype = e.currentTarget.dataset.datatype //当前对象的资料类型

    let coverBargeImgArr = that.data.coverBargeImgArr//船舶检验证书簿封面-临时存储图片数组
    let bargeMainItemImgArr = that.data.bargeMainItemImgArr//船舶主要项目内容-临时存储图片数组
    let seaworthinessCertificateImgArr = that.data.seaworthinessCertificateImgArr//船舶适航证书-临时存储图片数组
    let automaticAisImgArr = that.data.automaticAisImgArr//船舶自动识别系统AIS证书-临时存储图片数组
    let powerOfAttorneyImgArr = that.data.powerOfAttorneyImgArr //委托书-临时存储图片数组

    let deleteUploadList = []// 删除已上传图片数组

    let IdentityPositiveImgArr = that.data.IdentityPositiveImgArr //驳船主身份证正面图片数组-临时存储图片数组
    let IdentityNegativeImgArr = that.data.IdentityNegativeImgArr //驳船主身份证反面图片数组-临时存储图片数组


    //资料细分类型 11-驳船检验证书封面 12-驳船主要项目内容 13-驳船适航证书 14-驳船自动识别系统ASI证书 53-委托书图片 51驳船主身份证正面 52 驳船主身份证反面
    switch(datatype){
      case "11": 
        if(coverBargeImgArr[index].url.indexOf("https")>-1) {
          deleteUploadList.push(coverBargeImgArr[index].id)
        }
        coverBargeImgArr.splice(index,1)
      break;
      case "12": 
        if(bargeMainItemImgArr[index].url.indexOf("https")>-1) {
          deleteUploadList.push(bargeMainItemImgArr[index].id)
        }
        bargeMainItemImgArr.splice(index,1)

      break;
      case "13": 
        if(seaworthinessCertificateImgArr[index].url.indexOf("https")>-1) {
          deleteUploadList.push(seaworthinessCertificateImgArr[index].id)
        }
        seaworthinessCertificateImgArr.splice(index,1)
      break;
      case "14": 
        if(automaticAisImgArr[index].url.indexOf("https")>-1) {
          deleteUploadList.push(automaticAisImgArr[index].id)
        }
        automaticAisImgArr.splice(index,1)
      break;
      /*case "15":
        if(powerOfAttorneyImgArr[index].url.indexOf("https")>-1) {
          deleteUploadList.push(powerOfAttorneyImgArr[index].id)
        }
        powerOfAttorneyImgArr.splice(index,1)
      break;*/
      case "53":
      if(powerOfAttorneyImgArr[index].url.indexOf("https")>-1) {
        deleteUploadList.push(powerOfAttorneyImgArr[index].id)
      }
      powerOfAttorneyImgArr.splice(index,1)
      break;

      case "51": 
      if(IdentityPositiveImgArr[index].url.indexOf("https")>-1) {
        if(IdentityPositiveImgArr[index].id) {
            deleteUploadList.push(IdentityPositiveImgArr[index].id)
        }
      }
      IdentityPositiveImgArr.splice(index,1)
      break;
      case "52": 
      if(IdentityNegativeImgArr[index].url.indexOf("https")>-1) {
        if(IdentityNegativeImgArr[index].id) {
          deleteUploadList.push(IdentityNegativeImgArr[index].id)
        }
      }
      IdentityNegativeImgArr.splice(index,1)
      break;
      default:break;
    }
    deleteUploadList = that.data.deleteUploadList.concat(deleteUploadList)
    that.setData({
      coverBargeImgArr,
      bargeMainItemImgArr,
      seaworthinessCertificateImgArr,
      automaticAisImgArr,
      powerOfAttorneyImgArr,
      deleteUploadList,
      IdentityPositiveImgArr,
      IdentityNegativeImgArr
    })
    
  },
  

  //是否触发图片折叠展开功能
  foldImg(e){
    /**
     *资料细分类型 11-驳船检验证书封面 12-驳船主要项目内容 13-驳船适航证书 14-驳船自动识别系统ASI证书 41-出库单图片 42-委托书图片 51驳船主身份证正面 52 驳船主身份证反面
     */
    
    let datatype = e.currentTarget.dataset.datatype //当前对象的资料类型
    switch(datatype){
      case "11": this.setData({showCoverImg:!this.data.showCoverImg})
      break;
      case "12": this.setData({showMainItemImg:!this.data.showMainItemImg})
      break;
      case "13": this.setData({showSeaworthinessImg:!this.data.showSeaworthinessImg})
      break;
      case "14": this.setData({showAutomaticImg:!this.data.showAutomaticImg})
      break;
      /*case "15": this.setData({showAttorneyImg:!this.data.showAttorneyImg})
      break;*/
      case "53": this.setData({showAttorneyImg:!this.data.showAttorneyImg})
      break;
      case "51": this.setData({showIdentityPositiveImg:!this.data.showIdentityPositiveImg})
      break;
      case "52": this.setData({showIdentityNegativeImg:!this.data.showIdentityNegativeImg})
      break;
      default:break;
    }
    console.log(111,'展开');
  },

  //上传图片-dataType资料类型、path路径
  uploadImgEvent(path,dataType){
    let that = this
    let linkType = ""
    if(dataType == 51 || dataType == 52 || dataType == 53) {
      linkType = "50"
    } else {
      linkType = "10"
    }
    return new Promise((resolve,reject)=>{
      wx.showLoading({
        title: '加载中',
        mask:true
      })
      console.log(path)

      wx.uploadFile({
        url: app.$url.mine.weChatUploadFile,
        filePath: path,
        name:"fileName",
        header: {'Authorization':wx.getStorageSync('token')},
        formData: {
          'fileName': 'bargeImg',//文件
          'linkId': "",//驳船id
          'linkType':linkType,//资料类型 10-驳船备案资料 50-驳船主身份证正反面
          'linkCompanyId':"",//公司id 不是必填，有些驳船没有公司归属
          'dataType':dataType?dataType:"",//资料细分类型 11-驳船检验证书封面 12-驳船主要项目内容 13-驳船适航证书 14-驳船自动识别系统ASI证书 41-出库单图片 42-委托书图片 51驳船主身份证正面 52 驳船主身份证反面
        },
        success(res) {
          // wx.hideLoading()
          res = JSON.parse(res.data)
          //说明是成功返回了图片
          if(res.code == 200){
            if(res.data){
              resolve(res.data) 
            }
            
            console.log("res.data",res.data)
            console.log("biaodan",that.data)
          }else{
            app.$message("上传图片出错！")
            reject("fail")
      
          }
        },
        fail(res){
          console.log(res)
          if(res.errMsg.indexOf('timeout')) {
            // app.$message("网络不好，请重试")
            wx.hideLoading();
            setTimeout(()=>{
              wx.showToast({
                title: "网络不好，请重试",
                icon: 'none',
                mask: true,
                success:function(){
                  setTimeout(() => {
                    wx.hideToast();
                  }, 2000)
                }
              });
            },200);
          } else if (res.errMsg.indexOf('read')) {
            wx.hideLoading();
              setTimeout(()=>{
                wx.showToast({
                  title: "读取图片失败,请重试",
                  icon: 'none',
                  mask: true,
                  success:function(){
                    setTimeout(() => {
                      wx.hideToast();
                    }, 2000)
                  }
                });
              },200);
          }
          // app.$message(res.errMsg)
          reject("fail")
        },
        complete(){
          wx.hideLoading()
        }
      })
    })
  },

  //压缩并获取图片，这里用了递归的方法来解决canvas的draw方法延时的问题
 getCanvasImg (index,failNum, tempFilePaths,dataType){
  var that = this;
  if (index < tempFilePaths.length){
   const ctx = wx.createCanvasContext('attendCanvasId');
   ctx.drawImage(tempFilePaths[index], 0, 0, 300, 150);
   ctx.draw(true, function () {
    index = index + 1;//上传成功的数量，上传成功则加1
    wx.canvasToTempFilePath({
     canvasId: 'attendCanvasId',
     success: function success(res) {
      that.uploadImgEvent(res.tempFilePath,dataType);
      that.getCanvasImg(index,failNum,tempFilePaths);
      console.log(111,'压缩成功',res);
     }, fail: function (e) {
      failNum += 1;//失败数量，可以用来提示用户
      that.getCanvasImg(inedx,failNum,tempFilePaths);
     }
    });
   });
  }
 },

 // 下载委托书模板
 downloadBargeImg(e){
    wx.downloadFile({
      url: 'https://scbs.gzport.com/bargeRecord/0/weituoshu.docx',
      success: function (res) {
        const filePath = res.tempFilePath
        wx.openDocument({
          filePath: filePath,
          showMenu: true,
          success: function (res) {
            console.log('打开文档成功')
          }
        })
      }
    })
 },


  //选择图片
  chooseBargeImg(e){
    let dataType = e.currentTarget.dataset.datatype //资料类型
    // wx.showLoading({
    //   title: '加载中',
    //   mask:true
    // })
    // setTimeout(() => {
    //   wx.hideLoading()
    // }, 3000);
    let that = this
    that.setData({
      isunRefresh: true
    })
    let coverBargeImgArr = that.data.coverBargeImgArr//船舶检验证书簿封面-临时存储图片数组
    let bargeMainItemImgArr = that.data.bargeMainItemImgArr//船舶主要项目内容-临时存储图片数组
    let seaworthinessCertificateImgArr = that.data.seaworthinessCertificateImgArr//船舶适航证书-临时存储图片数组
    let automaticAisImgArr = that.data.automaticAisImgArr//船舶自动识别系统AIS证书-临时存储图片数组
    let powerOfAttorneyImgArr = that.data.powerOfAttorneyImgArr //委托书-临时存储图片数组

    let deleteUploadList = that.data.deleteUploadList // 删除已上传图片数组

    let IdentityPositiveImgArr = that.data.IdentityPositiveImgArr //驳船主身份证正面图片数组-临时存储图片数组
    let IdentityNegativeImgArr = that.data.IdentityNegativeImgArr //驳船主身份证反面图片数组-临时存储图片数组

 
    // 每种类型只能传一张图片，最新的图片覆盖旧的，目前每种类型只传一张-上传成功赋值前将含有https放到删除数组（先删除后赋值）
    wx.chooseImage({
      count:1,
      sizeType:['original','compressed'],
      sourceType: ['album', 'camera'],
      success(res){
        console.log(res.tempFilePaths,'上传图片')
        that.getCanvasImg(0,0,res.tempFilePaths,dataType)// 进行压缩
            console.log("chuan",res,dataType)
            switch(dataType){
              case "11": 
              if(coverBargeImgArr.length && coverBargeImgArr[0].url && coverBargeImgArr[0].url.indexOf("https")>-1) {
                deleteUploadList.push(coverBargeImgArr[0].id)
              }
              coverBargeImgArr = [{url:res.tempFiles[0].path}]
              break;
              case "12": 
              if(bargeMainItemImgArr.length>0 && bargeMainItemImgArr[0].url && bargeMainItemImgArr[0].url.indexOf("https")>-1) {
                deleteUploadList.push(bargeMainItemImgArr[0].id)
              }
              bargeMainItemImgArr = [{url:res.tempFiles[0].path}]
              break;
              case "13": 
              if(seaworthinessCertificateImgArr.length>0 && seaworthinessCertificateImgArr[0].url && seaworthinessCertificateImgArr[0].url.indexOf("https")>-1) {
                deleteUploadList.push(seaworthinessCertificateImgArr[0].id)
              }
              seaworthinessCertificateImgArr = [{url:res.tempFiles[0].path}]
              break;
              case "14": 
              if(automaticAisImgArr.length>0 && automaticAisImgArr[0].url && automaticAisImgArr[0].url.indexOf("https")>-1) {
                deleteUploadList.push(automaticAisImgArr[0].id)
              }
              automaticAisImgArr = [{url:res.tempFiles[0].path}]
              break;
              /*case "15":
              if(powerOfAttorneyImgArr.length>0 && powerOfAttorneyImgArr[0].url && powerOfAttorneyImgArr[0].url.indexOf("https")>-1) {
                deleteUploadList.push(powerOfAttorneyImgArr[0].id)
              }
              powerOfAttorneyImgArr = [{url:res.tempFiles[0].path}]
              break;*/
              case "53":
              if(powerOfAttorneyImgArr.length>0 && powerOfAttorneyImgArr[0].url && powerOfAttorneyImgArr[0].url.indexOf("https")>-1) {
                deleteUploadList.push(powerOfAttorneyImgArr[0].id)
              }
              powerOfAttorneyImgArr = [{url:res.tempFiles[0].path}]
              break;

              case "51": 
              if(IdentityPositiveImgArr.length>0 && IdentityPositiveImgArr[0].url && IdentityPositiveImgArr[0].url.indexOf("https")>-1) {
                deleteUploadList.push(IdentityPositiveImgArr[0].id)
              }
              IdentityPositiveImgArr = [{url:res.tempFiles[0].path}]
              break;
              case "52": 
              if(IdentityNegativeImgArr.length>0 && IdentityNegativeImgArr[0].url && IdentityNegativeImgArr[0].url.indexOf("https")>-1) {
                deleteUploadList.push(IdentityNegativeImgArr[0].id)
              }
              IdentityNegativeImgArr = [{url:res.tempFiles[0].path}]
              break;
              default:break;
            }
            that.setData({
              deleteUploadList,
              coverBargeImgArr,
              bargeMainItemImgArr,
              seaworthinessCertificateImgArr,
              automaticAisImgArr,
              powerOfAttorneyImgArr,
              IdentityPositiveImgArr,
              IdentityNegativeImgArr
            });
            console.log(that.data.deleteUploadList);
          
      },
      
      // },
      fail(res){
        wx.hideLoading()
        this.$message("上传图片失败！")
        console.log("上传图片失败！")
      },
      complete(){
        // wx.hideLoading()
      }
    })

  },

  //获取个人备案详情和驳船备案详情
  getBargeDetail(){
    let that = this
    //驳船id
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息

    let coverBargeImgArr = that.data.coverBargeImgArr//船舶检验证书簿封面-临时存储图片数组
    let bargeMainItemImgArr = that.data.bargeMainItemImgArr//船舶主要项目内容-临时存储图片数组
    let seaworthinessCertificateImgArr = that.data.seaworthinessCertificateImgArr//船舶适航证书-临时存储图片数组
    let automaticAisImgArr = that.data.automaticAisImgArr//船舶自动识别系统AIS证书-临时存储图片数组
    let powerOfAttorneyImgArr = that.data.powerOfAttorneyImgArr //委托书-临时存储图片数组


    // if(!userInfos.bargeId){
    //   app.$message("驳船id参数缺失")
    //   return
    // }
    console.log(userInfos)
    app.$post(app.$url.mine.recordDetail,{id:userInfos.bargeId}).then(res=>{
      console.log(res)
      if(res.code == 200){
        //驳船备案
        if(res.data.bargeRecordDetail){
          //校验去null
          for(let key1 in res.data.bargeRecordDetail){
            res.data.bargeRecordDetail[key1] = that.emptyReturn(res.data.bargeRecordDetail[key1])
          }
          let {id,bargeName,bargeId,mmsi,bargeWeight,bargeLoadA,bargeLoadB,validSailDate,belongArea,bargeOwner,contactPhone,bargeOperator,dataList,
            auditStatus,bindingType,companyId,companyName,isAudit,fdd,loadingWeightMax,loadingWeightMin,cargoNotAllowed} = res.data.bargeRecordDetail
          // bargeLoadA和bargeLoadB 提交值和返回值都是kg,但是页面需要显示吨，需将kg转为吨
          console.log(res.data.bargeRecordDetail,'750res.data.bargeRecordDetail');
          
          if(bargeLoadA,bargeLoadB) {
            bargeLoadA = Number(bargeLoadA)*100/100000
            bargeLoadB = Number(bargeLoadB)*100/100000
          }

          console.log(bargeLoadA,bargeLoadB,'738行修改后');

          // 初始详情数据-用于保存对比
          that.setData({
            initBargeDetailData: JSON.parse(JSON.stringify({companyId,bargeId}))
          })
          
          let isApplyBind = "" // 是否申请租用
          let bargeType1 = "" // 回显的船支类型
          let bargeTypeIndex = that.data.bargeTypeIndex // 船支类型索引 
          let bingingType1 = "" // 回显的租用方式
          let bingingTypeIndex = that.data.bingingTypeIndex // 租用方式索引
         
          if(dataList && dataList.length>0){
            dataList.map(item=>{
              switch(item.dataType){
                case 11: coverBargeImgArr = [item]
                break;
                case 12: bargeMainItemImgArr = [item]
                break;
                case 13: seaworthinessCertificateImgArr = [item]
                break;
                case 14: automaticAisImgArr = [item]
                break;
                /*case 15: powerOfAttorneyImgArr = [item]
                break;*/
                case 53: powerOfAttorneyImgArr = [item]
                break;
                default:break;
              }
            })
          }
          
          that.setData({
            coverBargeImgArr,
            bargeMainItemImgArr,
            seaworthinessCertificateImgArr,
            automaticAisImgArr,
            powerOfAttorneyImgArr
          })
          
          // 申请租用 isAudit （0/1-申请，2-不申请）
          isApplyBind = (isAudit == 1 || isAudit === 0|| isAudit === "0") ? true : false
 
          // 根据返回bindingType字段回显船支类型和租用方式:  1、租用方式bingingType 1-自有船， 2-临时租用，3-长期租用
          // 是否申请租用
          if(isAudit == 1 || isAudit === 0|| isAudit === "0") {
            // bindingType 1-自有船，驳船类型为自有船
            // if(bindingType == 1 ) {
            //   bargeTypeIndex = 0
            //   bargeType1 = 1
           
            // }
            if(bindingType == 2 ) {
            // bindingType 2-临时租用，驳船类型为租用船，租用方式为临时租用
              // bargeTypeIndex = 1
              bargeTypeIndex = 0
              bargeType1 = 2
              bingingTypeIndex = 0
              bingingType1 = 2
            }else if(bindingType == 3 ) {
              // bindingType 3-长期租用，驳船类型为租用船，租用方式为长期租用
              // bargeTypeIndex = 1
              bargeTypeIndex = 0
              bargeType1 = 2
              bingingTypeIndex = 1
              bingingType1 = 3
            }
          }else {
          // 取消选中，清空船支类型、租用方式、租用公司
            bargeTypeIndex = ""
            bargeType1 = ""
            bingingTypeIndex = ""
            bingingType1 = ""
            companyId = ""
            companyName = ""
          }
          auditStatus = that.auditStatusFormat(auditStatus)
          that.setData({ 
            bargeParam:{
              id,// 驳船id主键
              bargeName, // 驳船名称
              bargeId, // 驳船识别号
              mmsi, // MMSI标识
              bargeWeight, //驳船净吨
              bargeLoadA, // 船舶载货量（A级）
              bargeLoadB, // 船舶载货量（B级）
              validSailDate, //证书有效期
              belongArea, //船籍港
              bargeOwner,//驳船所有人
              contactPhone,//联系电话
              bargeOperator,//驳船经营人
              auditStatus,//租用状态
              bargeType: bargeType1,//船支类型
              bingingType: bingingType1,//租用方式
              companyId,//租用公司id
              companyName,//租用公司名称
              // sealImg1:fdd, // 电子签章

              loadingWeightMin, // 装载重量最小
              loadingWeightMax, // 装载重量最小
              cargoNotAllowed //不允许装载货物
            },
            isApplyBind, // 是否申请租用
            bargeTypeIndex, // 船支类型索引
            bingingTypeIndex, // 租用方式索引

          })
          console.log(that.data.bargeParam,'setdata后');
          console.log(that.data.bargeParam.bargeLoadA,'setdata后');
          console.log(that.data.bargeParam.bargeLoadB,'setdata后');
    
          // 获取电子签章
          that.getSealDetail(fdd.shipId,fdd.shipUserId)
         
        } 
        //个人备案
        if(res.data.personRecordDetail){
          let personRecordDetail = res.data.personRecordDetail //个人驳船详情
        
          //校验去null
          for(let key1 in personRecordDetail){
            personRecordDetail[key1] = that.emptyReturn(personRecordDetail[key1])
          }

          let IdentityPositiveImgArr = personRecordDetail.identityPositiveUrl ? [{url: personRecordDetail.identityPositiveUrl}] :[] //驳船主身份证正面图片数组
          let IdentityNegativeImgArr = personRecordDetail.identityNegativeUrl ? [{url: personRecordDetail.identityNegativeUrl}] :[]//驳船主身份证反面图片数组
          that.setData({
            "personForm.nickName":personRecordDetail.nickName,
            "personForm.phonenumber":personRecordDetail.phonenumber,
            // "personForm.userName":personRecordDetail.userName,
            "personForm.email":personRecordDetail.email,
            "personForm.identityId":personRecordDetail.identityId,
            IdentityPositiveImgArr,
            IdentityNegativeImgArr
          })
          
        }
       

      }else {
        app.$message(res.msg)
      }
    })
  },

  //驳船识别号input
  bargeIdInput(e){
    this.setData({
      "bargeParam.bargeId": e.detail.value
    })
  },
  
  //MMSI标识input
  mmsiInput(e){
    this.setData({
      "bargeParam.mmsi": e.detail.value
    })
  },
  //驳船净吨input
  bargeWeightInput(e){
    this.setData({
      "bargeParam.bargeWeight": e.detail.value
    })
  },
  // 装载重量最小input
  loadingWeightMinInput(e){
    this.setData({
      "bargeParam.loadingWeightMin": e.detail.value
    })
  },
  // 装载重量最大input
  loadingWeightMaxInput(e){
    this.setData({
      "bargeParam.loadingWeightMax": e.detail.value
    })
  },
  // 不允许装载货物input
  cargoNotAllowedInput(e){
    this.setData({
      "bargeParam.cargoNotAllowed": e.detail.value
    })
  },
  // 不允许装载货物select
  cargoNotAllowedChange:function(e){
    console.log(e.detail)
    // this.setData({
    //   cargoNotAllowedIndex: e.detail.value, // 更新当前选中的值的索引
    //   'bargeParam.cargoNotAllowed': this.data.cargoNotAllowedOptions[e.detail.value]
    // });
    const selectedValues = e.detail.value; // 获取选中的值
    const selectedCargoNotAllowed = {};
    const selectedCargoNotAllowedString = selectedValues.join(', '); // 将选中的值拼接为字符串
    
    // 设置选中的货物
    selectedValues.forEach(item => {
      selectedCargoNotAllowed[item] = true;
    });

    console.log(selectedCargoNotAllowedString)

    this.setData({
      selectedCargoNotAllowed: selectedCargoNotAllowedString,
      selectedValues:selectedCargoNotAllowed,
      "bargeParam.cargoNotAllowed": selectedCargoNotAllowedString
    });
  },
  //船舶载货量（A级）input
  bargeLoadAInput(e){
    this.setData({
      "bargeParam.bargeLoadA": e.detail.value
    })
  },
  //船舶载货量（B级）input
  bargeLoadBInput(e){
    this.setData({
      "bargeParam.bargeLoadB": e.detail.value
    })
  },
  //证书有效期input
  validSailDateInput(e){
    this.setData({
      "bargeParam.validSailDate": e.detail.value
    })
  },
  //船籍港input
  belongAreaInput(e){
    this.setData({
      "bargeParam.belongArea": e.detail.value
    })
  },
  //驳船所有人input
  bargeOwnerInput(e){
    this.setData({
      "bargeParam.bargeOwner": e.detail.value
    })
  },
  //联系电话input
  contactPhoneInput(e){
    this.setData({
      "bargeParam.contactPhone": e.detail.value
    })
  },
  //驳船经营人input
  bargeOperatorInput(e){
    this.setData({
      "bargeParam.bargeOperator": e.detail.value
    })
    console.log(this.data)
  },

  
  //个人备案
  async personSave(){
    let that = this 
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):"";//获取用户信息
    let personForm = that.data.personForm //个人备案表单

    let IdentityPositiveImgArr = that.data.IdentityPositiveImgArr //驳船主身份证正面图片数组-临时存储图片数组
    let IdentityPositiveUploadArr = [] //驳船主身份证正面图片数组-上传返回的图片数组
    let IdentityNegativeImgArr = that.data.IdentityNegativeImgArr //驳船主身份证反面图片数组-临时存储图片数组
    let IdentityNegativeUploadArr = [] //驳船主身份证反面图片数组-上传返回的图片数组

    if(!personForm.nickName){
      app.$message("请输入姓名")
      return
    }
    if(!personForm.phonenumber){
      app.$message("请输入手机")
      return
    }
    //手机校验
    let phoneReg = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[2-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/
    if(personForm.phonenumber && !phoneReg.test(personForm.phonenumber)){
      app.$message("手机输入格式不正确")
      return;
    }
    //邮箱校验格式
    let emailReg = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    let email = personForm.email //邮箱
    if (email && !emailReg.test(email)) {
      app.$message("你输入的邮箱格式不正确!")
      return;
    }

    if(!personForm.identityId){
      app.$message("请输入身份证号码")
      return
    }
    
    if(IdentityPositiveImgArr.length == 0 ) {
      app.$message("请上传驳船主身份证正面图片")
      return
    }

    if(IdentityNegativeImgArr.length == 0 ) {
      app.$message("请上传驳船主身份证反面图片")
      return
    }


    //添加资料类型、上传图片(上传图片只传新增的，否则保错)
    //驳船主身份证正面图片
    for(let i = 0; i < IdentityPositiveImgArr.length; i++) {
      let item = IdentityPositiveImgArr[i];
      let obj = {
        url:item.url,//路径
        dataType: "51",//资料类型
      }
      // https路径图片不重复上传
      if(obj.url.indexOf("https") == -1) {
        let result = await that.uploadImgEvent(obj.url,obj.dataType)
        if(result != "fail"){
          IdentityPositiveUploadArr.push(result)
          console.log("result",result)
        }else if(result == "fail"){
          return
        }
      }
    }

    //添加资料类型、上传图片(上传图片只传新增的，否则保错)
    //驳船主身份证反面图片
    for(let i = 0; i < IdentityNegativeImgArr.length; i++) {
      let item = IdentityNegativeImgArr[i];
      let obj = {
        url:item.url,//路径
        dataType: "52",//资料类型
      }
      // https路径图片不重复上传
      if(obj.url.indexOf("https") == -1) {
        let result = await that.uploadImgEvent(obj.url,obj.dataType)
        if(result != "fail"){
          IdentityNegativeUploadArr.push(result)
          console.log("result",result)
        }else if(result == "fail"){
          return
        }
      }
    }
    
    
    // let deleteUploadList = that.data.deleteUploadList // 删除数组
    // deleteUploadList = deleteUploadList.map(item=>{
    //   return {id:item}
    // })
    let dataList = IdentityPositiveUploadArr.concat(IdentityNegativeUploadArr)
     
    let param = {
      userId:userInfos.userId,//用户id
      ...that.data.personForm,
      // delDataList: deleteUploadList,// 个人备案删除图片
	    dataList,//已上传的文件数组
      
    }
    // delete param.wxAccount
    app.$post(app.$url.mine.personRecord,param).then(res=>{
      if(res.code == 200){
        if(userInfos.userType == 14){
          that.setData({
            'activeTab':2
          })
          // 使页面滚动到顶部
          wx.pageScrollTo({
            scrollTop: 0,
            duration: 0
          });
        }else{
          wx.navigateBack()
        }
      }else {
        app.$message(res.msg)
      }
    })
    //普通用户返回前一页，驳船主切换驳船备案tab
  },
  
  // 保存备案的委托书数据
  async saveAttorneyImg() {
    let that = this
    let powerOfAttorneyImgArr = that.data.powerOfAttorneyImgArr //委托书-临时存储图片数组
    let powerOfAttorneyUploadArr = [] //委托书-上传返回的图片数组
    // 委托书
    for(let i = 0; i < powerOfAttorneyImgArr.length; i++) {
      let item = powerOfAttorneyImgArr[i];
      let obj = {
        url:item.url,//路径
        dataType: "53",//资料类型
      }
      // https路径图片不重复上传
      if(obj.url.indexOf("https") == -1) {
        let result = await that.uploadImgEvent(obj.url, obj.dataType)
        if(result != "fail") {
          powerOfAttorneyUploadArr.push(result)
        }else if(result == "fail"){
          return 'fail';
        }
      }
    }
    let dataList = that.data.submitData.dataList.concat(powerOfAttorneyUploadArr)
    that.setData({
      "submitData.dataList": dataList
    });
  },

  //驳船备案-先上传所有图片后再调保存接口
  async shipSave(){
    let that = this
    let coverBargeImgArr = that.data.coverBargeImgArr//船舶检验证书簿封面-临时存储图片数组
    let bargeMainItemImgArr = that.data.bargeMainItemImgArr//船舶主要项目内容-临时存储图片数组
    let seaworthinessCertificateImgArr = that.data.seaworthinessCertificateImgArr//船舶适航证书-临时存储图片数组
    let automaticAisImgArr = that.data.automaticAisImgArr//船舶自动识别系统AIS证书-临时存储图片数组
    let powerOfAttorneyImgArr = that.data.powerOfAttorneyImgArr //委托书-临时存储图片数组


    let coverBargeImgUploadArr = []//船舶检验证书簿封面-上传返回的图片数组
    let bargeMainItemUploadArr = []//船舶主要项目内容-上传返回的图片数组
    let seaworthinessCertificateUploadArr = []//船舶适航证书-上传返回的图片数组
    let automaticAisUploadArr = []//船舶自动识别系统AIS证书-上传返回的图片数组
    let powerOfAttorneyUploadArr = [] //委托书-上传返回的图片数组
    
    // 必须将校验放在前面，避免新增图片重复上传
    let selectedBargeArr = that.data.selectedBargeArr //选中的驳船
    let bargeParam = that.data.bargeParam //驳船备案表单
    if((selectedBargeArr && selectedBargeArr.length==0 ) && (!bargeParam.bargeName)){
      app.$message("请选择驳船")
      return
    }

    //驳船备案表单
    let {id,bargeName,bargeId,mmsi,bargeWeight,bargeLoadA,bargeLoadB,validSailDate,belongArea,bargeOwner,contactPhone,bargeOperator,bargeType,bingingType,companyId,companyName,loadingWeightMax,loadingWeightMin,cargoNotAllowed} = that.data.bargeParam 
    // bargeLoadA和bargeLoadB 提交值和返回值都是kg,但是页面需要显示吨，需将吨转为kg
    bargeLoadA = bargeLoadA*1000
    bargeLoadB = bargeLoadB*1000

    let isApplyBind = that.data.isApplyBind // 是否申请租用
    bargeId = bargeId.trim()
    let bingingType1 = "" // 船支类型和租用方式参数（将bargeType和bingingType传参数时合并成一个字段）
    if(!bargeId) {
      app.$message("请填写船舶识别号")
      return
    }
    if(!mmsi) {
      app.$message("请填写MMSI（AIS）标识")
      return
    }
    if(!bargeWeight) {
      app.$message("请填写驳船净吨")
      return
    }
    if(!bargeLoadA) {
      app.$message("请填写船舶载货量（A级）")
      return
    }
    if(!bargeLoadB) {
      app.$message("请填写船舶载货量（B级）")
      return
    }
    if(!validSailDate) {
      app.$message("请填写证书有效期")
      return
    }
    if(!belongArea) {
      app.$message("请填写船籍港")
      return
    }
    if(!bargeOwner) {
      app.$message("请填写驳船所有人")
      return
    }
    if(!contactPhone) {
      app.$message("请填写联系电话")
      return
    }
    if(!bargeOperator) {
      app.$message("请填写驳船经营人")
      return
    }
    // if(!that.data.bargeParam.loadingWeightMin){
    //   app.$message("请填写最小装载重量")
    //   return
    // }

    // if(!that.data.bargeParam.loadingWeightMax){
    //   app.$message("请填写最大装载重量")
    //   return
    // }

    // 校验“船舶识别号”是否是CN开头的，如果不是CN开头的，请提示“船舶识别号必须以CN开头”
    if(bargeId && bargeId.indexOf("CN") == -1) {
      app.$message("船舶识别号必须以CN开头")
      return
    }
   
    //手机校验
    let phoneReg = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/
    if(contactPhone && !phoneReg.test(contactPhone)){
      app.$message("联系电话输入格式不正确")
      return;
    }
    console.log("isApplyBind",isApplyBind)

    // 目前将bargeType和bingingType传参数时合并成一个字段bingingType:  1、租用方式bingingType 1-自有船， 2-临时租用，3-长期租用

    //申请租用选中时校验
    if(isApplyBind) {
      // 船支类型为自有船时，必填：租用公司；船支类型选择租用船，必填：租用方式和租用公司
      // bargeType - 船支类型 : 1-自有船 2-租用船
      // （1）船支类型为1-自有船时， 则bingingType1为1
      if(bargeType == 1) {
        bingingType1 = 1
        if(!companyId) {
          app.$message("请选择租用公司")
          return
        }
      
      }else if(bargeType == 2) {
        // （2）船支类型为2-租用船时：租用方式bingingType为2-临时租用，则 bingingType1为2；租用方式bingingType为3-长期租用，则 bingingType1为3
        if(!bingingType) {
          app.$message("请选择租用方式")
          return
        }
        if(!companyId) {
          app.$message("请选择租用公司")
          return
        }
        bingingType1 = JSON.parse(JSON.stringify(bingingType))
      }
    }else {
      // 取消选中，清空船支类型、租用方式、租用公司
      that.setData({
        bargeTypeIndex: "",
        "bargeParam.bargeType": "",
        bingingTypeIndex: "",
        "bargeParam.bingingType": "",
        "bargeParam.companyId": "",
        "bargeParam.companyName": ""
      })
    }

    // 显示的图片每种类型都要上传
    if(coverBargeImgArr.length == 0) {
      app.$message("请上传船舶检验证书簿（船舶总吨净吨页）")
      return
    }
    if(bargeMainItemImgArr.length == 0) {
      app.$message("请上传船舶主要项目内容")
      return
    }
    if(seaworthinessCertificateImgArr.length == 0) {
      app.$message("请上传船舶适航证书")
      return
    }
    if(automaticAisImgArr.length == 0) {
      app.$message("请上传船舶自动识别系统AIS证书")
      return
    }


    // //勾选协议
    // if(!that.data.isContractChecked) {
    //   app.$message("请勾选协议,否则无法成功备案")
    //   return
    // }

    // 驳船备案生成印章要满足条件驳船名称和租用名称与详情获取的数据不相同时、或者没印章时才生成印章(id是唯一值判断)，避免印章浪费
    let sealImg = "" // 印章
    console.log(that.data.initBargeDetailData)
    if(that.data.initBargeDetailData.bargeId !=  bargeId || that.data.initBargeDetailData.companyId != companyId || !that.data.sealImg1 || isApplyBind || !isApplyBind) {
      // 生成印章
      // 满足首要条件：申请租用状态改变时：（1）只选择船公司时，船公司名称放在上面；（2）同时选择船公司和租用公司时，上面显示公司，下面驳船名称；
      if(bargeId && companyId) {
        sealImg = await this.createSeal(this.data.bargeParam.bargeName,"");
        // sealImg = await this.createSeal(this.data.bargeParam.companyName, this.data.bargeParam.bargeName);
      }else if((bargeId && !companyId) || !that.data.sealImg1) {
        sealImg = await this.createSeal(this.data.bargeParam.bargeName,"");
      }
    
      if(!sealImg || sealImg && sealImg == "fail") {
        app.$message("印章生成失败")
        return
      }
     
      console.log("sealImg",sealImg)
      that.setData({
        sealImg,
        sealImg1: sealImg ? JSON.parse(JSON.stringify(sealImg)) : that.data.sealImg1,
        canvaShow: false,
      })
    }

    

    //添加资料类型、上传图片(上传图片只传新增的，否则保错)
    //船舶检验证书簿封面
    for(let i = 0; i < coverBargeImgArr.length; i++) {
      console.log(i)
      let item = coverBargeImgArr[i];
      let obj = {
        url:item.url,//路径
        dataType: "11",//资料类型
      }
      // https路径图片不重复上传
      if(obj.url.indexOf("https") == -1) {
        let result = await that.uploadImgEvent(obj.url,obj.dataType)
        if(result != "fail"){
          coverBargeImgUploadArr.push(result)
          console.log("result",result)
        }else if(result == "fail"){
          return
        }
      }
    }
    
  
    //船舶主要项目内容
    for(let i = 0; i < bargeMainItemImgArr.length; i++) {
      let item = bargeMainItemImgArr[i];
      let obj = {
        url:item.url,//路径
        dataType: "12",//资料类型
      }
      // https路径图片不重复上传
      if(obj.url.indexOf("https") == -1) {
        let result = await that.uploadImgEvent(obj.url,obj.dataType)
        if(result != "fail"){
          bargeMainItemUploadArr.push(result)
          console.log("result",result)
        }else if(result == "fail"){
          return
        }
      }
    }
    
    //船舶适航证书
    for(let i = 0; i < seaworthinessCertificateImgArr.length; i++) {
      console.log(i)
      let item = seaworthinessCertificateImgArr[i];
      let obj = {
        url:item.url,//路径
        dataType: "13",//资料类型
      }
      // https路径图片不重复上传
      if(obj.url.indexOf("https") == -1) {
        let result = await that.uploadImgEvent(obj.url,obj.dataType)
        if(result != "fail"){
          seaworthinessCertificateUploadArr.push(result)
          console.log("result",result)
        }else if(result == "fail"){
          return
        }
      }
    }

    //船舶自动识别系统AIS证书
    for(let i = 0; i < automaticAisImgArr.length; i++) {
      let item = automaticAisImgArr[i];
      let obj = {
        url:item.url,//路径
        dataType: "14",//资料类型
      }
      // https路径图片不重复上传
      if(obj.url.indexOf("https") == -1) {
        let result = await that.uploadImgEvent(obj.url,obj.dataType)
        if(result != "fail"){
          automaticAisUploadArr.push(result)
        }else if(result == "fail"){
          return
        }
      }
    }

    // 委托书
    for(let i = 0; i < powerOfAttorneyImgArr.length; i++) {
      let item = powerOfAttorneyImgArr[i];
      let obj = {
        url:item.url,//路径
        dataType: "53",//资料类型
      }
      // https路径图片不重复上传
      if(obj.url.indexOf("https") == -1) {
        let result = await that.uploadImgEvent(obj.url,obj.dataType)
        if(result != "fail"){
          powerOfAttorneyUploadArr.push(result)
        }else if(result == "fail"){
          return
        }
      }
    }

    let deleteUploadList = that.data.deleteUploadList // 删除数组
    deleteUploadList = deleteUploadList.map(item=>{
      return {id:item}
    })
    let dataList = coverBargeImgUploadArr.concat(bargeMainItemUploadArr,seaworthinessCertificateUploadArr,automaticAisUploadArr,
      powerOfAttorneyUploadArr
    )
   
    let param = {
      id,// 驳船id主键
      bargeName, // 驳船名称
      bargeId, // 驳船识别号
      mmsi, // MMSI标识
      bargeWeight, //驳船净吨
      bargeLoadA, // 船舶载货量（A级）
      bargeLoadB, // 船舶载货量（B级）
      validSailDate, //证书有效期
      belongArea, //船籍港
      bargeOwner,//驳船所有人
      contactPhone,//联系电话
      bargeOperator,//驳船经营人
      dataList,//已上传的文件数组
      bindingType: bingingType1,//船支类型和租用方式参数
      companyId,//租用公司id
      companyName,//租用公司名称
      delDataList: deleteUploadList,// 备案删除图片
      sealImg: that.data.sealImg1, // 印章
      loadingWeightMin: that.data.bargeParam.loadingWeightMin, // 最小装载重量
      loadingWeightMax: that.data.bargeParam.loadingWeightMax, // 最大装载重量
      cargoNotAllowed: that.data.bargeParam.cargoNotAllowed, // 不允许装载货物
    }
    param.isAudit = isApplyBind ? 1 : 2 // 申请租用（0/1-申请，2-不申请）


    // let pages = getCurrentPages() //获取加载的页面
    // console.log("pages",pages)
    console.log("param",param)
  
    that.setData({
      submitData: JSON.parse(JSON.stringify(param))
    })
    wx.hideLoading()
    that.setData({
      activeTab: 3
    })

    // 使页面滚动到顶部
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    })
    //首次登录时，绑船失败，停留备案页面，成功直接返回登录页面
    // app.$post(app.$url.mine.bargeRecord,param).then(res=>{
    //   if(res.code == 200){
    //     app.$message("备案成功")
    //     setTimeout(()=>{
    //       wx.reLaunch({ url: '/pages/login/login' })
    //     },1000)
    //   }else {
    //     app.$message(res.msg)
    //   }
    // })
   
  },

  //获取用户个人信息
  getPersonInfo() {
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):"";//获取用户信息
    this.setData({
      "personForm.nickName":userInfos.nickName,
      "personForm.phonenumber":userInfos.phonenumber,
      // "personForm.userName":userInfos.userName,
      // "personForm.wxAccount":userInfos.wxAccount,
      "personForm.email":userInfos.email,

    })
    
  },

  // 身份证input
  identityIdInput(e){
    this.setData({
      "personForm.identityId":e.detail.value
    })
  },

  //姓名input
  nickNameInput(e){
    this.setData({
      "personForm.nickName":e.detail.value
    })
  },

  //手机号input
  phonenumberInput(e){
    this.setData({
      "personForm.phonenumber":e.detail.value
    })
  },

  //账号input
  userNameInput(e){
    this.setData({
      "personForm.userName":e.detail.value
    })
  },

  //微信号input
  wxAccountInput(e){
    this.setData({
      "personForm.wxAccount":e.detail.value
    })
  },

  //邮箱input
  emailInput(e){
    this.setData({
      "personForm.email":e.detail.value
    })
  },

  //照片放大功能
  showBigImg1(e){
    this.setData({
      isunRefresh:true
    })
    let currentUrl = e.currentTarget.dataset.currurl //当前路径
    let urlList=[currentUrl];
    wx.previewImage({
      urls:urlList,
      current:currentUrl
    })
  },


  //照片放大功能
  showBigImg(e){
    this.setData({
      isunRefresh:true
    })
    let currentUrl = e.currentTarget.dataset.currurl //当前路径
    let imgList = e.currentTarget.dataset.arr //当前图片数组路径
    let urlList=[];
    for(let i=0;i<imgList.length;i++){
      urlList.push(imgList[i].url)
    }
    wx.previewImage({
      urls:urlList,
      current:currentUrl
    })
  },

  /**
   * 生成印章
   * @param mainText 公司名称
   * @param subText 船名
   */
  createSeal(mainText,subText){
    const query = wx.createSelectorQuery();
    let that = this;
    return new Promise((resolve, reject) => {
      let color = "#f00" // "#f00" 红色; rgb(255, 255, 255,0) 透明色
      that.setData({
        canvaShow:true
      })

      query.select('#canvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvas = res[0].node;

          //canvas.height = canvas.height;// 清空canvas，canvas每当高度或宽度被重设时，画布内容就会被清空
          const context = canvas.getContext('2d');
          // mainText = ''
          let angleJustify=3.5,rotateJustify=5.5
          if(mainText && mainText.length<=5){
            angleJustify=2
            rotateJustify=7
          }

          // 绘制印章边框
          const dpr = wx.getSystemInfoSync().pixelRatio;
          canvas.width = res[0].width;
          canvas.height = res[0].height;
          const width = canvas.width / 2;
          const height = canvas.height / 2;
          context.lineWidth=4.5;
          context.strokeStyle=color;
          context.beginPath();
          context.arc(width,height,80,0,Math.PI*2);
          context.stroke();

          // 画五角星
          that.create5star(context,width,height,20,color,0);

          // 绘制印章名称
          context.font = '22px Helvetica';
          context.textBaseline = 'middle';// 设置文本的垂直对齐方式
          context.textAlign = 'center';// 设置文本的水平对对齐方式
          context.lineWidth= 3;
          context.fillStyle = color;
          context.fillText(subText,width,height+43.5);

          // 绘制印章单位
          context.translate(width,height);// 平移到此位置,
          context.font = '22px Helvetica'
          const count = mainText.length;// 字数
          const angle = angleJustify * Math.PI / (3 * (count - 1));// 字间角度
          const chars = mainText.split("");
          let c;
    
          for (let i = 0; i < count; i++){
            c = chars[i];// 需要绘制的字符
            if(i==0) {
              context.rotate(rotateJustify*Math.PI/6);
              context.save();
              context.translate(60, 0);// 平移到此位置,此时字和x轴垂直
              context.rotate(Math.PI/2);// 旋转90度,让字平行于x轴
              context.fillText(c,0, 5);// 此点为字的中心点
              context.restore();
            }
            else {
              context.rotate(angle);
              context.save();
              context.translate(60, 0);// 平移到此位置,此时字和x轴垂直
              context.rotate(Math.PI/2);// 旋转90度,让字平行于x轴
              context.fillText(c,0, 5);// 此点为字的中心点
              context.restore();
            }
          }

          console.log("canvas",canvas)
          let sealImg = canvas.toDataURL("image/png", 1) //图片质量
          console.log("sealImg1",sealImg)
          if(sealImg) {
            resolve(sealImg)
            // wx.hideLoading()
          }else {
            reject("fail")
          }
        });
    })
    
  },

   /**
   * 绘制一个五角星形状. 该五角星的中心坐标为(sx,sy),中心到顶点的距离为radius,rotate=0时一个顶点在对称轴上
   * rotate:绕对称轴旋转rotate弧度
   */
  create5star(context,sx,sy,radius,color,rotato){
    context.save();
    context.fillStyle=color;
    context.translate(sx,sy);// 移动坐标原点
    context.rotate(Math.PI+rotato);// 旋转
    context.beginPath();// 创建路径
    let x = Math.sin(0);
    let y = Math.cos(0);
    const dig = Math.PI / 5 * 4;
    for(let i = 0; i< 5; i++){// 画五角星的五条边
      x = Math.sin(i * dig);
      y = Math.cos(i * dig);
      context.lineTo(x*radius,y*radius);
    }
    context.closePath();
    context.stroke();
    context.fill();
    context.restore();
  },

  // 获取下拉框选项列表
  fetchCargoNotAllowedOptions(){
    app.$post(app.$url.mine.getGoodsCategorylist).then(res=>{
      console.log(res)
      if(res.code == 200){
        let list = res.data
        this.setData({
          cargoNotAllowedOptions:list
        })
        console.log(this.data.cargoNotAllowedOptions)
      }else {
        app.$message(res.msg)
      }
    })
  },


  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    //初次从用户信息获取个人备案资料
    this.getPersonInfo()
    console.log("备案onload")
    const res = wx.getSystemInfoSync()
    console.log(res,this.data)
    // 页面加载时调用后端接口获取下拉框选项列表
    this.fetchCargoNotAllowedOptions();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    //选中了驳船，避免详情数据覆盖
    if(this.data.selectedBargeArr && this.data.selectedBargeArr.length==0 && this.data.isunRefresh != true){
      //获取备案详情
      this.getBargeDetail()

    }
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息
    let isAlreadyRecorded = wx.getStorageSync('isRecorded') ? JSON.parse(wx.getStorageSync('isRecorded')) : "" //管理员之前是否成功备案，true成功，false失败,首次登录没有isRecorded属性代表没有备案
    this.setData({
      userInfos,
      isAlreadyRecorded,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.dto.BargeInfoDTO;
import com.ruoyi.databarge.domain.dto.CompanyDTO;
import com.ruoyi.databarge.domain.dto.Pb6BargeinfoAuditSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeCompanyResultVO;
import com.ruoyi.databarge.mapper.Pb6BargeinfoMapper;
import com.ruoyi.databarge.service.*;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.geom.Point2D;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/8/27.
 * @Date: 2020/8/27 16:20
 */
@Service
public class Pb6BargeinfoServiceImpl extends ServiceImpl<Pb6BargeinfoMapper, Pb6Bargeinfo> implements Pb6BargeinfoService {

    @Autowired
    private Pb1CustomerService pb1CustomerService;

    @Autowired
    private Pb6BargeCompanyService pb6BargeCompanyService;

    @Autowired
    private SysUserInfoService sysUserInfoService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private Pb6BargeinfoAuditService auditService;

    @Override
    public String getBargeNameById(Long id) {
        return super.baseMapper.getBargeNameById(id);
    }

    @Override
    public IPage<BargeCompanyResultVO> searchPagePb6Bargeinfo(Pb6BargeinfoAuditSearchDTO pb6BargeinfoAuditSearchDTO) {
        Long pageNum = Optional.ofNullable(pb6BargeinfoAuditSearchDTO.getPageNum()).orElse(0L);
        Long pageSize = Optional.ofNullable(pb6BargeinfoAuditSearchDTO.getPageSize()).orElse(-1L);

        IPage<BargeCompanyResultVO> bargeCompanyResultVOIPage = super.baseMapper.searchPagePb6Bargeinfo(new Page<>(pageNum, pageSize), pb6BargeinfoAuditSearchDTO);

        List<BargeCompanyResultVO> bargeCompanyResultVOList = new ArrayList<>();
        for(BargeCompanyResultVO bargeCompanyResultVO: bargeCompanyResultVOIPage.getRecords()){
            if(StringUtils.isNull(bargeCompanyResultVO.getNickName()) && StringUtils.isNull(bargeCompanyResultVO.getIdentityId()) && StringUtils.isNull(bargeCompanyResultVO.getPhonenumber())){
                SysUserInfo sysUserInfo = sysUserInfoService.searchOneByBargeId(bargeCompanyResultVO.getId());
                if(sysUserInfo != null){
                    bargeCompanyResultVO.setNickName(sysUserInfo.getUsername());
                    bargeCompanyResultVO.setIdentityId(sysUserInfo.getIdentity());
                    bargeCompanyResultVO.setPhonenumber(sysUserInfo.getPhone());
                }
            }
            bargeCompanyResultVOList.add(bargeCompanyResultVO);
        }
        bargeCompanyResultVOIPage.setRecords(bargeCompanyResultVOList);
        return bargeCompanyResultVOIPage;
    }

    @Override
    public BargeCompanyResultVO searchPb6BargeinfoById(Long pb6BargeInfoId) {
        return super.baseMapper.searchPb6BargeinfoById(pb6BargeInfoId);
    }

    @Override
    @Transactional
    public boolean updateBargeInfoAndCompanyBind(BargeInfoDTO bargeInfoDTO) {
        String nowDateTime = DateUtils.getTime();
        Pb6Bargeinfo pb6Bargeinfo = super.getById(bargeInfoDTO.getId());
        pb6Bargeinfo.setBargeid(bargeInfoDTO.getBargeid());
        pb6Bargeinfo.setBargename(bargeInfoDTO.getBargename());
        pb6Bargeinfo.setBargeoperator(bargeInfoDTO.getBargeoperator());
        pb6Bargeinfo.setMmsi(bargeInfoDTO.getMmsi());
        pb6Bargeinfo.setBargeweight(bargeInfoDTO.getBargeweight());

        pb6Bargeinfo.setBargeowner(bargeInfoDTO.getBargeowner());

        BigDecimal mul = new BigDecimal(1000);
        pb6Bargeinfo.setBargeloada(new BigDecimal(bargeInfoDTO.getBargeloada()).multiply(mul).toString());
        pb6Bargeinfo.setBargeloadb(new BigDecimal(bargeInfoDTO.getBargeloadb()).multiply(mul).toString());
        pb6Bargeinfo.setValidsaildate(bargeInfoDTO.getValidsaildate());
        pb6Bargeinfo.setBelongarea(bargeInfoDTO.getBelongarea());
        pb6Bargeinfo.setBargelinkman(bargeInfoDTO.getBargelinkman());
        pb6Bargeinfo.setContactphone(bargeInfoDTO.getContactphone());

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        pb6Bargeinfo.setModifyman(StringUtils.isNotBlank(sysUser.getUserName()) ? sysUser.getUserName() : "物流公司");
        pb6Bargeinfo.setModifydate(nowDateTime);

        // 最小散货密度
        pb6Bargeinfo.setLoadingWeightMin(bargeInfoDTO.getLoadingWeightMin());
        // 最大散货密度
        pb6Bargeinfo.setLoadingWeightMax(bargeInfoDTO.getLoadingWeightMax());
        // 禁装货物
        pb6Bargeinfo.setCargoNotAllowed(bargeInfoDTO.getCargoNotAllowed());

        //原数据是未使用
        if(StringUtils.isNotBlank(pb6Bargeinfo.getIsuse()) && "N".equals(pb6Bargeinfo.getIsuse())){
            //改成使用中
            if(StringUtils.isBlank(bargeInfoDTO.getIsuse()) || "Y".equals(bargeInfoDTO.getIsuse())){
                String bargeId = pb6Bargeinfo.getBargeid().replace("停用", "");
                LambdaQueryWrapper<Pb6Bargeinfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Pb6Bargeinfo::getBargeid, bargeId)
                        .eq(Pb6Bargeinfo::getIsuse, "Y");
                List<Pb6Bargeinfo> pb6BargeinfoList = list(queryWrapper);
                if(pb6BargeinfoList.size() > 0){
                    throw new CustomException("已有该驳船标识，无法改成使用中！");
                }
                pb6Bargeinfo.setBargeid(bargeId);
            }
        } else {
            //原数据是使用中
            //改成未使用
            if(StringUtils.isNotBlank(bargeInfoDTO.getIsuse()) && "N".equals(bargeInfoDTO.getIsuse())){
                if(!pb6Bargeinfo.getBargeid().contains("停用")){
                    pb6Bargeinfo.setBargeid(pb6Bargeinfo.getBargeid() + "停用");
                }
                //将备份表中的bargeid置为停用 20231012 ztw
                auditService.update(new UpdateWrapper<Pb6BargeinfoAudit>()
                        .set("bargeid",pb6Bargeinfo.getBargeid().contains("停用") ? pb6Bargeinfo.getBargeid() : pb6Bargeinfo.getBargeid() + "停用" )
                        .eq("bargeid",pb6Bargeinfo.getBargeid().contains("停用") ? pb6Bargeinfo.getBargeid().substring(0,pb6Bargeinfo.getBargeid().length()-2) : pb6Bargeinfo.getBargeid()));
            }
        }
        pb6Bargeinfo.setIsuse(bargeInfoDTO.getIsuse());
        super.updateById(pb6Bargeinfo);

        SysUser sysUserBargeOwner = sysUserInfoService.searchUserByBargeId(pb6Bargeinfo.getId());
        if(sysUserBargeOwner == null){
            SysUserInfo sysUserInfo = new SysUserInfo();
            sysUserInfo.setBargeid(pb6Bargeinfo.getId());
            sysUserInfo.setIdentity(bargeInfoDTO.getIdentityId());
            sysUserInfo.setPhone(bargeInfoDTO.getPhonenumber());
            sysUserInfo.setUsername(bargeInfoDTO.getNickName());
            sysUserInfoService.save(sysUserInfo);
        } else {
            sysUserBargeOwner.setIdentityId(bargeInfoDTO.getIdentityId());
            sysUserBargeOwner.setPhonenumber(bargeInfoDTO.getPhonenumber());
            sysUserBargeOwner.setNickName(bargeInfoDTO.getNickName());
            iSysUserService.updateUserProfile(sysUserBargeOwner);
        }

        //只查询没有删掉的数据
        Pb6BargeCompany company = pb6BargeCompanyService.searchPb6BargeCompanyByBargeId(bargeInfoDTO.getId());
        if(bargeInfoDTO.getComid() != null){
            Pb1Customer pb1Customer = pb1CustomerService.getById(bargeInfoDTO.getComid());
            if(pb1Customer == null){
                throw new CustomException("没有找到指定客户信息，请核对！");
            }
            if(company == null){
                Pb6BargeCompany pb6BargeCompany = new Pb6BargeCompany();
                pb6BargeCompany.setBargeid(bargeInfoDTO.getId());
                pb6BargeCompany.setCompanyid(bargeInfoDTO.getComid());
                pb6BargeCompany.setBindingtype(bargeInfoDTO.getBindingtype());
                pb6BargeCompany.setStatus(1);
                pb6BargeCompany.setAuditbyid(sysUser.getUserId() == null ? 1L : sysUser.getUserId());
                pb6BargeCompany.setAuditByName(StringUtils.isNotBlank(sysUser.getUserName()) ? sysUser.getUserName() : "物流公司");
                pb6BargeCompany.setAudittime(nowDateTime);
                pb6BargeCompany.setIsaudit(1);

                pb6BargeCompany.setCreatebyid(sysUser.getUserId() == null ? 1L : sysUser.getUserId());
                pb6BargeCompany.setCreateByName(StringUtils.isNotBlank(sysUser.getUserName()) ? sysUser.getUserName() : "物流公司");
                pb6BargeCompany.setCreatetime(nowDateTime);
                pb6BargeCompany.setAuditstatus(1);
                pb6BargeCompany.setIsDelete(1);
                pb6BargeCompany.setCompanyName(pb1Customer.getCfullname());
                pb6BargeCompanyService.save(pb6BargeCompany);
            } else {
                company.setCompanyid(bargeInfoDTO.getComid());
                company.setBindingtype(bargeInfoDTO.getBindingtype());
                company.setUpdatebyid(sysUser.getUserId() == null ? 1L : sysUser.getUserId());
                company.setUpdateByName(StringUtils.isNotBlank(sysUser.getUserName()) ? sysUser.getUserName() : "物流公司");
                company.setUpdatetime(nowDateTime);
                company.setCompanyName(pb1Customer.getCfullname());
                pb6BargeCompanyService.updateById(company);
            }
        } else { //删除挂靠
            if(company != null){
                company.setUpdatebyid(sysUser.getUserId() == null ? 1L : sysUser.getUserId());
                company.setUpdateByName(StringUtils.isNotBlank(sysUser.getUserName()) ? sysUser.getUserName() : "物流公司");
                company.setUpdatetime(nowDateTime);
                company.setIsDelete(0);
                pb6BargeCompanyService.updateById(company);
            }
        }
        return true;
    }

    @Override
    public List<Pb6Bargeinfo> searchForUpdateCheck(String bargeid) {
        QueryWrapper<Pb6Bargeinfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb6Bargeinfo::getBargeid, bargeid)
                .eq(Pb6Bargeinfo::getIsuse, "Y");
        return super.list(queryWrapper);
    }

    @Override
    public void insertById(Pb6Bargeinfo pb6Bargeinfo) {
        Integer i = super.baseMapper.insertById(pb6Bargeinfo);
        if (!i.equals(1)) {
            throw new CustomException("插入驳船基本信息出错！");
        }
        // if(baseMapper.insert(pb6Bargeinfo) != 1){
        //     throw new CustomException("插入驳船基本信息出错！");
        // }
    }

    @Override
    public List<Pb6Bargeinfo> searchByBargeName(String bargeName) {
        QueryWrapper<Pb6Bargeinfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb6Bargeinfo::getBargename, bargeName)
                .ne(Pb6Bargeinfo::getIsuse, "N");
        List<Pb6Bargeinfo> pb6BargeinfoList = super.list(queryWrapper);
        pb6BargeinfoList.forEach( i -> {
            i.setBargeloada(String.valueOf(Double.parseDouble(i.getBargeloada()) / 1000));
            i.setBargeloadb(String.valueOf(Double.parseDouble(i.getBargeloadb()) / 1000));
        });
        return pb6BargeinfoList;
    }

    @Override
    public IPage<Pb6Bargeinfo> searchListByBargeName(String bargeName) {

        Page<Pb6Bargeinfo> page = new Page<>(1, 10);
        QueryWrapper<Pb6Bargeinfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(Pb6Bargeinfo::getBargename, bargeName)
                .orderByDesc(Pb6Bargeinfo::getId);
        return super.page(page, queryWrapper);
    }

    @Override
    public Pb6Bargeinfo searchPb6BargeinfoByPb6BargeCompanyId(Long pb6BargeInfoAuditId) {
        Pb6Bargeinfo pb6Bargeinfo = super.baseMapper.searchPb6BargeinfoByPb6BargeCompanyId(pb6BargeInfoAuditId);
        if (pb6Bargeinfo == null) {
            throw new CustomException("没有找到驳船基本信息!");
        }
        return pb6Bargeinfo;
    }

    /**
     * 判断点是否在新沙画的多边形内
     *
     * @param point 检测点  均为保留6位小数的平面坐标，并去掉小数点
     * @return 点在多边形内返回true，否则返回false
     */
    @Override
    public boolean IsPtInPoly(Point2D.Double point, List<Point2D.Double> polygon) {

        int N = polygon.size();
        boolean boundOrVertex = true;//如果点位于多边形的顶点或边上，也算做点在多边形内，直接返回true
        int intersectCount = 0;//cross points count of x--交叉点计数X
        double precision = 2e-10;//浮点类型计算时候与0比较时候的容差
        Point2D.Double p1, p2;//neighbour bound vertices--临近绑定顶点
        Point2D.Double p = point;//当前点

        p1 = polygon.get(0);//left vertex--左顶点
        for (int i = 1; i <= N; ++i) {//check all rays--检查所有射线
            if (p.equals(p1)) {
                return boundOrVertex;//p is an vertex--p是一个顶点
            }

            p2 = polygon.get(i % N);//right vertex--右顶点
            if (p.x < Math.min(p1.x, p2.x) || p.x > Math.max(p1.x, p2.x)) {//ray is outside of our interests--射线不在我们的兴趣范围之内
                p1 = p2;
                continue;//next ray left point--下一条射线的左边点
            }

            if (p.x > Math.min(p1.x, p2.x) && p.x < Math.max(p1.x, p2.x)) {//ray is crossing over by the algorithm(common part of)--射线被算法穿越(常见的一部分)
                if (p.y <= Math.max(p1.y, p2.y)) {//x is before of ray--x在射线之前
                    if (p1.x == p2.x && p.y >= Math.min(p1.y, p2.y)) {//overlies on a horizontal ray--在一条水平射线上
                        return boundOrVertex;
                    }

                    if (p1.y == p2.y) {//ray is vertical--射线是垂直的
                        if (p1.y == p.y) {//overlies on a vertical ray--覆盖在垂直光线上
                            return boundOrVertex;
                        } else {//before ray--射线之前
                            ++intersectCount;
                        }

                    } else {//cross point on the left side--左边的交叉点
                        double xinters = (p.x - p1.x) * (p2.y - p1.y) / (p2.x - p1.x) + p1.y;//cross point of y--y的交叉点
                        if (Math.abs(p.y - xinters) < precision) {//overlies on a ray--覆盖在射线
                            return boundOrVertex;
                        }

                        if (p.y < xinters) {//before ray--射线之前
                            ++intersectCount;
                        }
                    }
                }
            } else {//special case when ray is crossing through the vertex--特殊情况下，当射线穿过顶点
                if (p.x == p2.x && p.y <= p2.y) {//p crossing over p2--p交叉p2
                    Point2D.Double p3 = polygon.get((i + 1) % N);//next vertex--下一个顶点
                    if (p.x >= Math.min(p1.x, p3.x) && p.x <= Math.max(p1.x, p3.x)) {//p.x lies between p1.x & p3.x--p.x在p1.x和p3.x之间
                        ++intersectCount;
                    } else {
                        intersectCount += 2;
                    }
                }
            }
            p1 = p2;//next ray left point--下一条射线的左边点
        }
        //偶数在多边形外
        if (intersectCount % 2 == 0) {
            return false;
        } else {
            //奇数在多边形内
            return true;
        }
    }


    @Override
    public String searchMmsiByBargeName(String bargeName){
        return super.baseMapper.searchMmsiByBargeName(bargeName);
    }
}

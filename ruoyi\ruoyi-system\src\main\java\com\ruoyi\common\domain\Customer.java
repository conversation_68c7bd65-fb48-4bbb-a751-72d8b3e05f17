package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 客户实体（公司）
 * <AUTHOR>
 * @Date 2020/8/12 11:43
 */
@Setter
@Getter
@ToString
@TableName(value = "PB1_CUSTOMER")
public class Customer {
    private Long id;
    @TableField(value = "customerid")
    private String customerId;
    @TableField(value = "cshortname")
    private String cShortName;
    @TableField(value = "cfullname")
    private String cFullName;
    @TableField(value = "isregister")
    private String isRegiester;

    public void setId(Long id) {
        this.id = id;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public void setcShortName(String cShortName) {
        this.cShortName = cShortName;
    }

    public void setcFullName(String cFullName) {
        this.cFullName = cFullName;
    }

    public String getIsRegiester() {
        return isRegiester;
    }

    public void setIsRegiester(String isRegiester) {
        this.isRegiester = isRegiester;
    }
}

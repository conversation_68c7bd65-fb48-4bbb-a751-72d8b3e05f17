package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 水路货物运单信息表
 * </p>
 *
 * <AUTHOR> @since 2020-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PB6_WATERWAYCARGO")
@KeySequence("SEQ_PB6_WATERWAYCARGO")
public class Pb6Waterwaycargo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 水路货物运单编号
     */
    @TableField("WATERWAYCARGOID")
    private String waterwaycargoid;

    /**
     * 出入库单号
     */
    @TableField("OUTORINFORMID")
    private String outorinformid;

    /**
     * 地磅单号
     */
    @TableField("LOADOMETERID")
    private String loadometerid;

    /**
     * 航次
     */
    @TableField("barge_number")
    private String bargeNumber;

    /**
     * 发货人
     */
    @TableField("CONSIGNER")
    private String consigner;

    /**
     * 收货人
     */
    @TableField("CONSIGNEE")
    private String consignee;

    /**
     * 装货地点
     */
    @TableField("SHIPMENTPLACE")
    private String shipmentplace;

    /**
     * 航区
     */
    @TableField("SAILAREA")
    private String sailarea;

    /**
     * 发货符号
     */
    @TableField("CONSIGNMENTFLAG")
    private String consignmentflag;

    /**
     * 配载件数
     */
    @TableField("RATIONPIECE")
    private String rationpiece;

    /**
     * 配载重量
     */
    @TableField("RATIONWEIGHT")
    private String rationweight;

    /**
     * 费用结算方式
     */
    @TableField("CHARGEBALANCETYPE")
    private String chargebalancetype;

    /**
     * 计费吨
     */
    @TableField("CHARGEWEIGHT")
    private String chargeweight;

    /**
     * 运费
     */
    @TableField("TRANSPORTCHARGE")
    private String transportcharge;

    /**
     * 货港费
     */
    @TableField("CARGOPORTCHARGE")
    private String cargoportcharge;

    /**
     * 服务代理费
     */
    @TableField("SERVICEAGENTCHARGE")
    private String serviceagentcharge;

    /**
     * 业务代理费
     */
    @TableField("BUSINESSAGENTCHARGE")
    private String businessagentcharge;

    /**
     * 费用总合计
     */
    @TableField("TOTALCHARGE")
    private String totalcharge;

    /**
     * 特约事项
     */
    @TableField("SPECIALPROCEEDING")
    private String specialproceeding;

    /**
     * (用于存显示的港口endportshow)
     */
    @TableField("VALIDDATE")
    private String validdate;

    /**
     * 配载日期
     */
    @TableField("RATIONDATE")
    private String rationdate;

    /**
     * 配载负责人
     */
    @TableField("RATIONPRINCIPAL")
    private String rationprincipal;

    /**
     * 退单负责人
     */
    @TableField("CANCELRATIONPRINCIPAL")
    private String cancelrationprincipal;

    /**
     * 退单日期
     */
    @TableField("CANCELRATIONDATE")
    private String cancelrationdate;

    /**
     * 出入库单号代理主键
     */
    @TableField("OUTORINFORMIDID")
    private Long outorinformidid;

    /**
     * 地磅单号代理主键
     */
    @TableField("LOADOMETERIDID")
    private Long loadometeridid;

    /**
     * 驳船基本信息代理主键
     */
    @TableField("BARGEIDID")
    private Long bargeidid;

    /**
     * 起运港代理主键
     */
    @TableField("BEGINPORTID")
    private Long beginportid;

    /**
     * 中转港代理主键
     */
    @TableField("MIDPORTID")
    private Long midportid;

    /**
     * 目的港代理主键
     */
    @TableField("ENDPORTID")
    private Long endportid;

    /**
     * 货物名称代理主键
     */
    @TableField("CARGENAMEID")
    private Long cargenameid;

    /**
     * 配载人代理主键
     */
    @TableField("RATIONPRINCIPALID")
    private Long rationprincipalid;

    /**
     * 退单负责人代理主键
     */
    @TableField("CANCELRATIONPRINCIPALID")
    private Long cancelrationprincipalid;

    /**
     * 作业区代理主键
     */
    @TableField("WORKID")
    private Long workid;

    /**
     * 公司代理主键
     */
    @TableField("COMID")
    private Long comid;

    /**
     * 修改人代理主键
     */
    @TableField("MODIFYMANID")
    private Long modifymanid;

    /**
     * 修改人
     */
    @TableField("MODIFYMAN")
    private String modifyman;

    /**
     * 修改日期
     */
    @TableField("MODIFYDATE")
    private String modifydate;

    /**
     * 工本费
     */
    @TableField("DOCHARGE")
    private String docharge;

    /**
     * 驳船状态，0：已配载，没有报到信息 1：已报到；2：已离港；3：已取消报到，处于配载状态，并且已有报到信息 4：已退单 5:已停止
     */
    @TableField("FLAGBARGESTATE")
    private String flagbargestate;

    /**
     * 装卸类型
     */
    @TableField("FLAGLOADORUNLOAD")
    private String flagloadorunload;

    /**
     * 运费费率
     */
    @TableField("TRANSPORTCHARGERATE")
    private String transportchargerate;

    /**
     * 货港费率
     */
    @TableField("CARGOPORTCHARGERATE")
    private String cargoportchargerate;

    /**
     * 服务代理费率
     */
    @TableField("SERVICEAGENTCHARGERATE")
    private String serviceagentchargerate;

    /**
     * 业务代理费率
     */
    @TableField("BUSINESSAGENTCHARGERATE")
    private String businessagentchargerate;

    /**
     * 托号
     */
    @TableField("CONSIGNFLAG")
    private String consignflag;

    /**
     * 一单多票
     */
    @TableField("FLAGMANYFORMID")
    private String flagmanyformid;

    /**
     * 水路货物运单编号no
     */
    @TableField("WATERWAYCARGONO")
    private String waterwaycargono;

    /**
     * 是否分量配载
     */
    @TableField("FLAGREG")
    private String flagreg;

    /**
     * 包装方式
     */
    @TableField("PACKAGETYPE")
    private String packagetype;

    /**
     * 包装方式代理主键
     */
    @TableField("PACKAGETYPEID")
    private Long packagetypeid;

    /**
     * 货物名称
     */
    @TableField("CARGENAME")
    private String cargename;

    /**
     * 分量配载货重
     */
    @TableField("DISRATIONWEIGHT")
    private String disrationweight;

    /**
     * 分量配载件数
     */
    @TableField("DISRATIONPIECE")
    private String disrationpiece;

    /**
     * 是否申请到验号
     */
    @TableField("ISAPPLYUNIQUECODE")
    private String isapplyuniquecode;

    /**
     * 是否为集装箱
     */
    @TableField("ISCONTAINER")
    private String iscontainer;

    /**
     * 录入时间
     */
    @TableField("RECORDDATE")
    private String recorddate;

    /**
     * 统计标记
     */
    @TableField("STAS_REMARK")
    private String stasRemark;

    /**
     * 货物体积(收款人)
     */
    @TableField("CARGOSIZE")
    private String cargosize;

    /**
     * 是否为大船
     */
    @TableField("FLAGSHIP")
    private String flagship;

    /**
     * 是否为沿海船
     */
    @TableField("COASTALVESSEL")
    private String coastalvessel;

    /**
     * 系解缆费
     */
    @TableField("MOORINGCHARGE")
    private String mooringcharge;

    /**
     * 开关仓费
     */
    @TableField("OCHATCHCHARGE")
    private String ochatchcharge;

    /**
     * 停泊费
     */
    @TableField("BERTHCHARGE")
    private String berthcharge;

    /**
     * 甚高频费
     */
    @TableField("VHFCHARGE")
    private String vhfcharge;

    /**
     * 系解缆费率
     */
    @TableField("MOORINGCHARGERATE")
    private String mooringchargerate;

    /**
     * 开关仓费率
     */
    @TableField("OCHATCHCHARGERATE")
    private String ochatchchargerate;

    /**
     * 停泊费率
     */
    @TableField("BERTHCHARGERATE")
    private String berthchargerate;

    /**
     * 甚高频费率
     */
    @TableField("VHFCHARGERATE")
    private String vhfchargerate;

    /**
     * 停泊天数
     */
    @TableField("BERTHDAYS")
    private String berthdays;

    /**
     * 流向
     */
    @TableField("FLOW")
    private String flow;

    /**
     * 驳船名
     */
    @TableField("barge_name")
    private String bargeName;

    /**
     * 世德申请水路货物运单编号
     */
    @TableField("APPLYCARGOCONSIGNMENT")
    private String applycargoconsignment;

    /**
     * 是否核销(Y;N)
     */
    @TableField("ISVERIFICATE")
    private String isverificate;

    /**
     * 核销原因
     */
    @TableField("VERIFICATEREASON")
    private String verificatereason;

    /**
     * 保存发送支付信息的电话号码
     */
    @TableField("SENDCELLNUM")
    private Long sendcellnum;


    /**
     * saveworkweight
     */
    @TableField("WORKWEIGHT")
    private String workweight;

    /**
     * 确认是否实装： Y 确认实装， N是没有确认实装
     */
    @TableField("CONFIRMLOADINGOVER")
    private String confirmloadingover;

    /**
     * 确认实装的仓库员
     */
    @TableField("CONFIRMLOADPEOPLE")
    private String confirmloadpeople;

    /**
     * 确认实装的时间
     */
    @TableField("CONFIRMTIME")
    private String confirmtime;

    /**
     * 确认实装的驳船主id（sys_user.user_id）
     */
    @TableField("CONFIRMBARGEEID")
    private BigDecimal confirmbargeeid;

    /**
     * N为不需要申报
     */
    @TableField("ISREPORTING")
    private String isreporting;

    // 起运港
    @TableField("BEGIN_PORT")
    private String beginPort;

    // 目的港
    @TableField("END_PORT")
    private String endPort;

    @TableField("special_notice")
    private String specialNotice;

    @TableField(exist = false)
    private int uploadaddresscount;

    // 货物类别
    @TableField(exist = false)
    private String cargoType;

    @TableLogic
    private String delFlag;

    // 大船名
    private String shipName;

    // 大船航次
    private String shipNumber;

    // 完船时间
    private Timestamp completeTime;

    // 完船备注
    private String completeRemark;

    // 是否生成告知书
    private String isStampNotice;

    // 是否放行
    private String isRelease;

    // 放行时间
    private String releaseTime;

    // 贸易类型
    @TableField(exist = false)
    private String tradeType;



}

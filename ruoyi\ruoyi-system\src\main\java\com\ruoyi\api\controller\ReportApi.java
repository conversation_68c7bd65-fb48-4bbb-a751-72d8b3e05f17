package com.ruoyi.api.controller;

import com.ruoyi.api.domain.CountResult;
import com.ruoyi.api.domain.BargeParam;
import com.ruoyi.api.service.impl.ReportApiServiceImpl;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.databarge.domain.Pb6Cargoconsignment;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.ship.domain.ShipInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/6/13 14:12
 */
@RestController
@RequestMapping("/api/report")
public class ReportApi extends BaseController {

    @Autowired
    private ReportApiServiceImpl reportApiService;


    // 返回散货开单系统数据，用于报表展示
    // 先做测试，看能否访问到系统
    @PostMapping("/getBusinessInfo")
    public CountResult getBusinessInfo(@RequestBody Map<String, Object> params) {

        // 返回结果
        CountResult countResult = new CountResult();
        // 从params中获取所需参数
        BargeParam bargeParam = new BargeParam();
        bargeParam.setBeginTime(params.get("beginTime").toString());
        bargeParam.setEndTime(params.get("endTime").toString());
        bargeParam.setWorkPoint(params.get("workPoint").toString());
        bargeParam.setShipCompanyId(params.get("shipCompanyId").toString());
        bargeParam.setCusType(params.get("cusType").toString());
        bargeParam.setLoadTerminalId(params.get("loadTerminalId").toString());
        bargeParam.setDisTerminalId(params.get("disTerminalId").toString());

        // 调用mapper查询数据
        // 大船数据
        List<ShipInfo> shipCount = reportApiService.getShipCount(bargeParam);
        // 驳船数据
        List<Pb6Cargoconsignmentdetail> bargeCount = reportApiService.getBargeCount(bargeParam);

        // 大船吨数
        BigDecimal importTd = new BigDecimal(0);
        // 驳船吨数
        BigDecimal exportTd = new BigDecimal(0);
        // 大船收入
        BigDecimal income = new BigDecimal(0);

        // 遍历大船数据
        for (ShipInfo shipInfo : shipCount) {
            // 票-大船吨数
            importTd = importTd.add(shipInfo.getTotalTonnage());
            // 托运单信息
            List<Pb6Cargoconsignment> pb6Cargoconsignments = reportApiService.selectByShipInfoId(shipInfo.getShipInfoId());
            for (Pb6Cargoconsignment pb6Cargoconsignment : pb6Cargoconsignments) {
                // 收入-大船收入
                income = income.add(pb6Cargoconsignment.getAmount());
            }
        }

        // 遍历驳船数据
        for (Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail : bargeCount) {
            // 票-驳船吨数
            exportTd = exportTd.add(new BigDecimal(pb6Cargoconsignmentdetail.getRationweight()));
        }

        // 设置返回结果
        countResult.setImportTdCount(importTd);
        countResult.setExportTdCount(exportTd);
        countResult.setInCome(income);

        // 合计
        countResult.setSummation(income);

        // 艘次为大船数量
        countResult.setImportSC(shipCount.size());
        // 航次为驳船数量
        countResult.setExportSC(bargeCount.size());
        // 总艘次
        countResult.setTotalSC(countResult.getImportSC() + countResult.getExportSC());

        return countResult;
    }

}

// pages/mainpage/main/supplycom/supply.js
const app = getApp()
Component({
  options: {
    addGlobalClass: true, // 使全局css生效
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
 
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    //货源列表数组
    listData:[],
    // 查询条件参数
    form: {
      search:"",//搜索关键字
      startTime: '',//开始时间
      endTime: '',//结束时间
    },
    
  },

  lifetimes: {
    attached: function() {
      // 在组件实例进入页面节点树时执行
      this.getSupplyData()
    },
    detached: function() {
      // 在组件实例被从页面节点树移除时执行
    },
  },

  pageLifetimes: {
    show: function() {
      // 页面被展示
      this.getSupplyData()
    },
    hide: function() {
      // 页面被隐藏
    },
    resize: function(size) {
      // 页面尺寸变化
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
      /**
   * 判断是否存在，不存在返回空字符串或其他
   * @param {传进来的变量} empty 
   * @param {为空时返回的值} replace 
   */
    emptyReturn(empty,replace){
      if(!replace || replace==null){
        replace = ""
      }
      if(empty == null || !empty){
        return replace
      }else{
        return empty
      }
    },

    //清空货源名称关键字
    deleteSearch(){
      this.setData({
        "form.search":""
      })
    },

    //清空开始时间
    deleteStartTime(){
      this.setData({
        "form.startTime":""
      })
    },

    //清空截止时间
    deleteEndTime(){
      this.setData({
        "form.endTime":""
      })
    },

    
    //获取货源信息列表
    getSupplyData() {
      let that = this 
      let param = {
        ...that.data.form
      }
      param.cargoName = param.search
      delete param.search
      app.$post(app.$url.main.getCargoSourceList,param).then(res=>{
        if(res.code == 200){
          res.data.map(item=>{
            item.cargoName = that.emptyReturn(item.cargoName)
            item.placeOfDeparture = that.emptyReturn(item.placeOfDeparture)
            item.destination = that.emptyReturn(item.destination)
            item.startTime = that.emptyReturn(item.startTime)
            item.endTime = that.emptyReturn(item.endTime)
            //方便其他页面使用
            item.cargoSourceId = that.emptyReturn(item.cargoSourceId)
            item.cargoNumber = that.emptyReturn(item.cargoNumber)
            item.cargoWeight = that.emptyReturn(item.cargoWeight)
            item.cargoBulk = that.emptyReturn(item.cargoBulk)
            item.consignor = that.emptyReturn(item.consignor)
            item.phone = that.emptyReturn(item.phone)
            item.remark = that.emptyReturn(item.remark)
          })
          this.setData({
            listData:res.data
          })
        }else {
        app.$message(res.msg)
        }
      })
    },

    //搜索
    handleSearch(){
      this.getSupplyData()
    },
    
    //关键字input
    searchInput(e){
      this.setData({
        "form.search":e.detail.value
      })
    },

    // 跳转到详情页面
    handleSupplyDetail(e) {
      let supplyObj = JSON.stringify(e.currentTarget.dataset.item) //当前货源对象
      wx.navigateTo({
        url: '/pages/mainpage/supplydetail/supplydetail?supplyObj='+supplyObj,
        success: (result)=>{
          
        },
        fail: ()=>{},
        complete: ()=>{}
      });
    },

    // 开始时间回调
    handleBeginDateChange(e) {
      //时间撮比较日期大小
      let beginTime = new Date(e.detail.value).getTime() //开始时间
      let endTime =  new Date(this.data.form.endTime).getTime()//结束时间
      if(beginTime && endTime && beginTime > endTime){
        app.$message("开始时间需小于或等于截止时间")
        this.setData({
          'form.startTime':""
        })
        return
      }
      this.setData({
        'form.startTime': e.detail.value
      })
    },

    // 结束时间回调
    handleEndDateChange(e) {
      //时间撮比较日期大小
      let endTime = new Date(e.detail.value).getTime()//结束时间
      let beginTime =  new Date(this.data.form.startTime).getTime()//开始时间
      if(beginTime && endTime && endTime < beginTime){
        app.$message("截止时间需大于或等于开始时间")
        this.setData({
          'form.endTime':""
        })
        return
      }
      this.setData({
        'form.endTime': e.detail.value
      })
    }
  }
})

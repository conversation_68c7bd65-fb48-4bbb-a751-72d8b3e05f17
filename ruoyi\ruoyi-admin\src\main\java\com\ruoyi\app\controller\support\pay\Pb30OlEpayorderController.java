package com.ruoyi.app.controller.support.pay;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.app.controller.support.fdd.Sha256Util;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.DataScope;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.dto.Pb30OlEpayorderDetailSearchDTO;
import com.ruoyi.databarge.domain.vo.Pb30OlEpayorderDetailVO;
import com.ruoyi.databarge.domain.vo.PortLoadingMsgVO;
import com.ruoyi.databarge.domain.vo.WaterCargoTagClickVO;
import com.ruoyi.databarge.pay.*;
import com.ruoyi.databarge.service.*;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.json.JSONException;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/11/17 9:54
 * @Description:
 */
@RestController
@RequestMapping("/barge/pay")
@Slf4j
@AllArgsConstructor
public class Pb30OlEpayorderController {

    private final Pb30OlEpayorderService pb30OlEpayorderService;
    private final Pb6WaterwaycargoService pb6WaterwaycargoService;
    private final Pb30OlEpaybargedetailService pb30OlEpaybargedetailService;
    private final Pb6BargeinfoService pb6BargeinfoService;
    private final PubCompanyService pubCompanyService;
    private final Pb6BargeCheckMessageService pb6BargeCheckMessageService;
    private final ISysDictDataService iSysDictDataService;
    private final ISysDictTypeService iSysDictTypeService;
    private final Pb6CargoconsignmentdetailService pb6CargoconsignmentdetailService;

    private final RestTemplate restTemplate = new RestTemplate();

    {
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON, MediaType.APPLICATION_OCTET_STREAM));
        restTemplate.getMessageConverters().add(mappingJackson2HttpMessageConverter);
    }

    /**
     * 驳船主小程序创建订单
     *
     * @param waterwaycargono 水路运单号
     * @return 跳转支付信息
     */
    @Log(title = "创建订单", businessType = BusinessType.INSERT)
    @PostMapping("/apply")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult apply(@RequestBody String waterwaycargono) {
        try{
            return AjaxResult.success(createOrder(waterwaycargono, PayUtil.CHAIN_ID, PayUtil.SECURITY_KEY, PayUtil.REDIRECT_URL));
        }
        catch (Exception e){
            return AjaxResult.success(e.getMessage());
        }
    }

    /**
     * 船公司（货主）创建订单
     *
     * @param waterwaycargono 水路运单号
     * @return 跳转支付信息
     */
    @Log(title = "创建订单", businessType = BusinessType.INSERT)
    @PostMapping("/applyBargeCompany")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult applyBargeCompany(@RequestBody String waterwaycargono) {
        try {
            return AjaxResult.success(createOrder(waterwaycargono, PayUtil.CHAIN_ID_BARGE_COMPANY, PayUtil.SECURITY_KEY_BARGE_COMPANY, PayUtil.REDIRECT_URL_BARGE_COMPANY));
        }
        catch (Exception c){
            return AjaxResult.error(c.getMessage());
        }

    }

    /**
     * 发起退款申请
     * waterwaycargono 退款单号
     * amount 退款金额，网页退款需填入金额，按照金额退
     *                  小程序退款只填单号，自动判断退款金额
     */
    @Log(title = "退款申请", businessType = BusinessType.INSERT)
    @PostMapping("/refundApply")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult refundApply(@RequestBody PayRefundApplyBO refundApplyBO){
        List<Pb6Waterwaycargo> pb6WaterwaycargoList = pb6WaterwaycargoService.list(Wrappers.<Pb6Waterwaycargo>query().eq("WATERWAYCARGONO", refundApplyBO.getWaterwaycargono()));
        if (pb6WaterwaycargoList.size() == 0) {
            throw new CustomException("水路运单不存在!");
        } else if (pb6WaterwaycargoList.size() > 1) {
            throw new CustomException("水路运单数据不唯一!");
        } else {
            Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoList.get(0);
            Pb6Bargeinfo pb6Bargeinfo = pb6BargeinfoService.getById(pb6Waterwaycargo.getBargeidid());
            String nowDate = DateUtils.getTime();

            Pb30OlEpayorder olEpayorderOrigin = pb30OlEpayorderService.searchOriginOrder(refundApplyBO.getWaterwaycargono());
            // 判断退款条件： 1、支付完成且成功
            if(olEpayorderOrigin == null){
                throw new CustomException("未支付或支付失败，无法退款");
            }
            List<Pb30OlEpaybargedetail> pb30OlEpaybargedetailList = pb30OlEpaybargedetailService.list(Wrappers.<Pb30OlEpaybargedetail>lambdaQuery().eq(Pb30OlEpaybargedetail::getOrderid, olEpayorderOrigin.getId()));
            if(pb30OlEpaybargedetailList.size() != 1){
                throw new CustomException("支付明细数据为空或不唯一");
            }

            // 判断退款条件： 2、现结
            if(!pb30OlEpaybargedetailList.get(0).getPaytype().equals("现结")){
                throw new CustomException("该支付不是现结，无法退款");
            }
            // 判断退款条件： 3、支付金额大于0
            if(new BigDecimal(olEpayorderOrigin.getAmount()).compareTo(new BigDecimal("0")) <= 0){
                throw new CustomException("该支付金额没有大于0，无法退款");
            }
            //定义退款金额
            BigDecimal amount = new BigDecimal("0");
            if(refundApplyBO.getAmount() == null || refundApplyBO.getAmount().compareTo(amount) == 0){
                // 没有传退款金额，或者退款金额为0
                PortLoadingMsgVO portLoadingMsgVO = pb6BargeCheckMessageService.searchPortLoadingInfo(refundApplyBO.getWaterwaycargono());

                // TODO: 2021/2/20 计算钱的实装去掉
                // 判断退款条件： 4、有实装数
                if(portLoadingMsgVO == null){
                    throw new CustomException("该单没有实装数，无法退款");
                }
                //实装数
                BigDecimal loadNum = new BigDecimal(portLoadingMsgVO.getTscargoweightValue());
//                BigDecimal actLoadNum = new BigDecimal("500");

                //配载数
                BigDecimal actLoadNum = new BigDecimal(portLoadingMsgVO.getRationweight());
//                BigDecimal loadNum = new BigDecimal("1000");

                //占比
                BigDecimal per = (loadNum.subtract(actLoadNum)).divide(loadNum);

                //获取设置的差额吨占比, dictCode 为 46 的数据存储的是差额吨占比
                SysDictData sysDictData = iSysDictDataService.selectDictDataById(46L);

                // 判断退款条件： 5、（（配载-实装）/配载）>长航填的参数
                if(per.compareTo(new BigDecimal(sysDictData.getDictValue())) > 0){
                    amount = per.multiply(new BigDecimal(pb6Waterwaycargo.getTotalcharge())).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else {
                    throw new CustomException("吨数不足，无法退款");
                }

            } else {
                amount = refundApplyBO.getAmount();
            }
            // 记录到支付表和支付明细表
            Pb30OlEpayorder pb30OlEpayorder = new Pb30OlEpayorder();
            pb30OlEpayorder.setOrderid(null);// 流水号,还未审核，此处没有值
            pb30OlEpayorder.setStarttime(nowDate);
            pb30OlEpayorder.setEndtime(null);
            pb30OlEpayorder.setOrderdescrip(refundApplyBO.getWaterwaycargono() + "，驳船 " + pb6Bargeinfo.getBargename() + "申请退款");

            pb30OlEpayorder.setAmount(amount.toString());
            pb30OlEpayorder.setStatus("4");// 4 待退款
            pb30OlEpayorder.setCustomerid("" + pb6Bargeinfo.getId());
            pb30OlEpayorder.setCustomername(pb6Bargeinfo.getBargename());
            pb30OlEpayorder.setComid("" + pb6Waterwaycargo.getComid());
            pb30OlEpayorder.setRemark(null);
            pb30OlEpayorder.setPaytype("1");// 1退款
            pb30OlEpayorder.setChanid(PayUtil.CHAIN_ID);
            pb30OlEpayorder.setEpayorderid(null); //支付平台订单号，需要审批通过后请求支付平台才会生成
            pb30OlEpayorder.setPaytime(null);
            pb30OlEpayorder.setRefundorderid(olEpayorderOrigin.getEpayorderid());
            pb30OlEpayorder.setRefundchanflow(olEpayorderOrigin.getChanflow());
            pb30OlEpayorder.setFinancecheck("N");
            pb30OlEpayorder.setPayperson(null); //这里填审批人，刚申请未审批，则为空
            pb30OlEpayorder.setContactcellphone(pb6Bargeinfo.getContactphone()); //登录用户的电话
            pb30OlEpayorder.setBusinesstype("4");// 1小程序支付
            pb30OlEpayorderService.save(pb30OlEpayorder);

            Pb30OlEpaybargedetail pb30OlEpaybargedetail = new Pb30OlEpaybargedetail();
            pb30OlEpaybargedetail.setConsignflag(pb6Waterwaycargo.getConsignflag());
            pb30OlEpaybargedetail.setOrderid(pb30OlEpayorder.getId());
            pb30OlEpaybargedetail.setOutorinformid(pb6Waterwaycargo.getOutorinformid());
            pb30OlEpaybargedetail.setWaterwaycargoid(refundApplyBO.getWaterwaycargono());
            pb30OlEpaybargedetail.setPaytime(nowDate);

            List<SysDictData> sysDictDataList = iSysDictTypeService.selectDictDataByType("ship_agency_department");
            SysDictData sysDictDataShipAgency = new SysDictData();
            for (SysDictData data: sysDictDataList){
                if(data.getDictValue().equals("" + pb6Waterwaycargo.getComid())){
                    sysDictDataShipAgency = data;
                }
            }
            if(StringUtils.isBlank(sysDictDataShipAgency.getDictLabel())){
                throw new CustomException("缺少船舶代理部信息！码头ID为： " + pb6Waterwaycargo.getComid());
            }
            pb30OlEpaybargedetail.setPayman(sysDictDataShipAgency.getDictLabel());
            pb30OlEpaybargedetail.setPaytype("现结");
            pb30OlEpaybargedetail.setCount(amount.toString());
            pb30OlEpaybargedetail.setComid("" + pb6Waterwaycargo.getComid());
            pb30OlEpaybargedetail.setStatus("4");// 4 待退款
            pb30OlEpaybargedetail.setActualmount("0");
            pb30OlEpaybargedetail.setContactphone(sysDictDataShipAgency.getRemark());
            pb30OlEpaybargedetail.setResourse("4");// 1小程序支付
            pb30OlEpaybargedetail.setRemark(refundApplyBO.getRemark());
            pb30OlEpaybargedetailService.save(pb30OlEpaybargedetail);

            return AjaxResult.success("发起退款申请成功！");
        }
    }

    /**
     * 退款申请审核
     * @return
     */
    @PostMapping("/refund")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult refund(@RequestBody PayRefundAuditReultBO payRefundAuditReultBO) {

        Pb30OlEpaybargedetail pb30OlEpaybargedetail = pb30OlEpaybargedetailService.getById(payRefundAuditReultBO.getPb30OlEpayBargeDetailId());
        Assert.notNull(pb30OlEpaybargedetail, "支付明细不存在！");
        if(!pb30OlEpaybargedetail.getStatus().equals("4")){
            throw new CustomException("当前订单不是待退款状态，不可退款！");
        }

        Pb30OlEpayorder pb30OlEpayorder = pb30OlEpayorderService.getById(pb30OlEpaybargedetail.getOrderid());
        Assert.notNull(pb30OlEpayorder, "支付不存在!");

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        Assert.notNull(sysUser, "没有用户登录!");
//        SysUser sysUser = new SysUser();
//        sysUser.setUserName("洪小林");

        if(payRefundAuditReultBO.getResult() == 1){
            //审批通过
            String nowDateEight = DateUtils.getNowEightDate();
            String nowDate = DateUtils.getTime();

            String count = getOrderIdByUUId();

            String oriChanflow = pb30OlEpayorder.getRefundchanflow();// 之前的流水号
            String newChanflow = PayUtil.CHAIN_ID + nowDateEight + count;// 新的流水号，chanflow=chain_id+8位日期+其他id，最多30位
            String amount = pb30OlEpayorder.getAmount();

            String companyId = "";
            if("1".equals(pb30OlEpaybargedetail.getComid())){
                companyId = PayUtil.COMPANY_ID_XS;
            } else if("16".equals(pb30OlEpaybargedetail.getComid())){
                companyId = PayUtil.COMPANY_ID_LS;
            } else if("3".equals(pb30OlEpaybargedetail.getComid()) || "4".equals(pb30OlEpaybargedetail.getComid())){
                companyId = PayUtil.COMPANY_ID_XG;
            } else if("2".equals(pb30OlEpaybargedetail.getComid()) ){
                companyId = PayUtil.COMPANY_ID_HP;
            }else  {
                throw new CustomException("不支持的港口！");
            }
            PayRefundDTO payRefundDTO = PayRefundDTO
                    .builder()
                    .reqmsg(PayRefundDTO.Reqmsg
                            .builder()
                            .reqhead(Reqhead
                                    .builder()
                                    .trancode(PayUtil.REFUND_TRAN_CODE)
                                    .sendtime(nowDate)
                                    .chanid(PayUtil.CHAIN_ID)
                                    .chanflow(newChanflow)// 新的流水号，不是之前的
                                    .build())
                            .cdtrid(companyId)
                            .refundamt(amount)
                            .chanflow(oriChanflow)// 之前的流水号，不是新的
                            .operuser(sysUser.getUserName())
                            .remark(pb30OlEpaybargedetail.getRemark())
                            .signature(Sha256Util.getSha256(PayUtil.CHAIN_ID + oriChanflow + amount + PayUtil.SECURITY_KEY))
                            .build())
                    .build();


            PayRefundVO payRefundVO = restTemplate.postForObject(PayUtil.BASE_URL, payRefundDTO, PayRefundVO.class);

            if (!"0000".equals(payRefundVO.getResmsg().getReshead().getProcd())) {
                throw new CustomException(payRefundVO.getResmsg().getReshead().getProinfo());
            }

            pb30OlEpayorder.setOrderid(count);
            pb30OlEpayorder.setStatus("7"); // 7退款中
            pb30OlEpayorder.setChanflow(newChanflow);
            pb30OlEpayorder.setEpayorderid(payRefundVO.getResmsg().getRefundid());
            pb30OlEpayorder.setPayperson(sysUser.getUserName());
            pb30OlEpayorderService.updateById(pb30OlEpayorder);

            pb30OlEpaybargedetail.setStatus("7"); // 7退款中
            pb30OlEpaybargedetailService.updateById(pb30OlEpaybargedetail);
            return AjaxResult.success(payRefundVO);
        } else {
            String nowDate = DateUtils.getTime();
            pb30OlEpayorder.setEndtime(nowDate);
            pb30OlEpayorder.setStatus("9"); // 9 订单关闭
            pb30OlEpayorder.setRemark("退款申请审批不通过!");
            pb30OlEpayorder.setPayperson(sysUser.getUserName());
            pb30OlEpayorderService.updateById(pb30OlEpayorder);

            pb30OlEpaybargedetail.setRemark((StringUtils.isNotBlank(pb30OlEpaybargedetail.getRemark()) ? pb30OlEpaybargedetail.getRemark() : "" ) + " 退款申请审批不通过!");
            pb30OlEpaybargedetail.setStatus("9"); // 7退款中
            pb30OlEpaybargedetailService.updateById(pb30OlEpaybargedetail);
            return AjaxResult.success("审批成功，审批状态： 不通过！");
        }
    }

    /**
     * 收款和退款的统一回调
     */
    @Log(title = "支付退款回调")
    @PostMapping("/redirect")
    public AjaxResult redirect(HttpServletRequest request) throws IOException {
        log.info("------------------回调了-----------------------");
        String result = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
        JSONObject jsonObject = JSONObject.parseObject(result);
        CallbackDTO callbackDTO = jsonObject.toJavaObject(CallbackDTO.class);

        String orgorderid = callbackDTO.getResmsg().getOrderid();
        log.info("订单号： {}", orgorderid);
        if (callbackDTO.getResmsg().getOrderid() != null) {

            Pb30OlEpayorder pb30OlEpayorder = pb30OlEpayorderService.getOne(Wrappers.<Pb30OlEpayorder>query().eq("EPAYORDERID", orgorderid));
            Assert.notNull(pb30OlEpayorder, "支付不存在!");

            Pb30OlEpaybargedetail pb30OlEpaybargedetail = pb30OlEpaybargedetailService.getOne(Wrappers.<Pb30OlEpaybargedetail>query().eq("ORDERID", pb30OlEpayorder.getId()));
            Assert.notNull(pb30OlEpaybargedetail, "支付明细不存在!");

            String nowDate = DateUtils.getTime();

            if(callbackDTO.getResmsg().getBiztype().equals("0")) {
                // 0为收款
                if(callbackDTO.getResmsg().getPaysts().equals("01") || callbackDTO.getResmsg().getPaysts().equals("02")){
                    // 01 成功   02 失败
                    if(callbackDTO.getResmsg().getPaysts().equals("01")){ // 支付成功

                        // 2021-06-04 jinn 发现一个订单多次回调，导致时间不正确，现修改为只有第一次回调才会改数据
                        if(StringUtils.isBlank(pb30OlEpayorder.getPaytime())){
                            pb30OlEpayorder.setEndtime(nowDate);
                            pb30OlEpayorder.setStatus("1"); // 1支付成功
                            pb30OlEpayorder.setRemark("处理成功");
                            pb30OlEpayorder.setPaytime(nowDate);
                            pb30OlEpayorderService.updateById(pb30OlEpayorder);

                            pb30OlEpaybargedetail.setPaytime(nowDate);
                            pb30OlEpaybargedetail.setRemark("支付成功");
                            pb30OlEpaybargedetail.setStatus("1");
                            pb30OlEpaybargedetail.setAlreadymount(pb30OlEpaybargedetail.getCount());
                            pb30OlEpaybargedetailService.updateById(pb30OlEpaybargedetail);

                            //修改小程序的状态
                            Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailService
                                    .getOne(new QueryWrapper<Pb6Cargoconsignmentdetail>().lambda()
                                            .eq(Pb6Cargoconsignmentdetail::getWaterwaycargoid, pb30OlEpaybargedetail.getWaterwaycargoid()));
                            pb6Cargoconsignmentdetail.setWxoperatestate(4); // 4 待驳船主预约
                            pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
//                            boolean a = pb6CargoconsignmentdetailService.update(pb6Cargoconsignmentdetail,
//                                    new UpdateWrapper<Pb6Cargoconsignmentdetail>().eq("ID", pb6Cargoconsignmentdetail.getId())
//                                            .eq("WXOPERATESTATE", 1));

                            Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.searchWaterwaycargoByWaterwaycargoid(pb30OlEpaybargedetail.getWaterwaycargoid());
                            if(pb6Waterwaycargo.getChargebalancetype() != null && "月结".equals(pb6Waterwaycargo.getChargebalancetype())){
                                pb6WaterwaycargoService.update(new LambdaUpdateWrapper<Pb6Waterwaycargo>().set(Pb6Waterwaycargo::getChargebalancetype, "现结")
                                    .set(Pb6Waterwaycargo::getCargosize, null)
                                    .eq(Pb6Waterwaycargo::getWaterwaycargoid, pb30OlEpaybargedetail.getWaterwaycargoid()));
                            }
                            log.info("第一次收到回调支付成功，更改状态！");
                        } else {
                            log.info("非第一次收到回调支付成功，不更改状态！");
                        }
                    } else {
                        pb30OlEpayorder.setEndtime(nowDate);
                        pb30OlEpayorder.setStatus("2"); // 1支付失败
                        pb30OlEpayorder.setRemark("处理失败");
                        pb30OlEpayorder.setPaytime(nowDate);
                        pb30OlEpayorderService.updateById(pb30OlEpayorder);

                        pb30OlEpaybargedetail.setPaytime(nowDate);
                        pb30OlEpaybargedetail.setRemark("支付失败");
                        pb30OlEpaybargedetail.setStatus("2");
                        pb30OlEpaybargedetailService.updateById(pb30OlEpaybargedetail);
                        log.info("收到回调支付失败，更改状态！");
                    }
                }
            } else if(callbackDTO.getResmsg().getBiztype().equals("1")) {
                // 1为退款
                if(callbackDTO.getResmsg().getPaysts().equals("01") || callbackDTO.getResmsg().getPaysts().equals("02")){
                    // 01 成功   02 失败
                    if(callbackDTO.getResmsg().getPaysts().equals("01")){ // 退款成功
                        pb30OlEpayorder.setEndtime(nowDate);
                        pb30OlEpayorder.setStatus("5"); // 5退款成功
                        pb30OlEpayorder.setRemark("处理成功");
                        pb30OlEpayorder.setPaytime(nowDate);
                        pb30OlEpayorderService.updateById(pb30OlEpayorder);

                        pb30OlEpaybargedetail.setPaytime(nowDate);
                        pb30OlEpaybargedetail.setRemark((StringUtils.isNotBlank(pb30OlEpaybargedetail.getRemark()) ? pb30OlEpaybargedetail.getRemark() : "") + " 退款成功");
                        pb30OlEpaybargedetail.setStatus("5");
                        pb30OlEpaybargedetail.setAlreadymount(pb30OlEpaybargedetail.getCount());
                        pb30OlEpaybargedetail.setActualmount(pb30OlEpaybargedetail.getCount());
                        pb30OlEpaybargedetailService.updateById(pb30OlEpaybargedetail);
                        log.info("收到回调退款成功，更改状态！");
                    } else { // 退款失败
                        pb30OlEpayorder.setEndtime(nowDate);
                        pb30OlEpayorder.setStatus("6"); // 6退款失败
                        pb30OlEpayorder.setRemark("处理失败");
                        pb30OlEpayorder.setPaytime(nowDate);
                        pb30OlEpayorderService.updateById(pb30OlEpayorder);

                        pb30OlEpaybargedetail.setPaytime(nowDate);
                        pb30OlEpaybargedetail.setRemark("退款失败");
                        pb30OlEpaybargedetail.setStatus("6");
                        pb30OlEpaybargedetailService.updateById(pb30OlEpaybargedetail);
                        log.info("收到回调支付失败，更改状态！");
                    }
                }
            } else {
                log.info("此回调非收款和退款！状态码: " + callbackDTO.getResmsg().getBiztype());
            }
        } else {
            log.info("没有取到订单号");
        }

        return AjaxResult.success();
    }


    @PostMapping("/search/page/detailVO")
    public AjaxResult searchPageDetailVO(@RequestBody @Validated Pb30OlEpayorderDetailSearchDTO
                                                 pb30OlEpayorderDetailSearchDTO) {
        DataScope.getDataScope("w", pb30OlEpayorderDetailSearchDTO, Boolean.FALSE);
        IPage<Pb30OlEpayorderDetailVO> page = pb30OlEpayorderService.searchPageDetailVO(pb30OlEpayorderDetailSearchDTO);

        return AjaxResult.success(page);
    }

    @PostMapping("/search/payDetails")
    public AjaxResult searchPayDetails(@RequestBody String waterwaycargono) {
        return AjaxResult.success(pb30OlEpayorderService.searchPayDetails(waterwaycargono));
    }

    @GetMapping("/download")
    public void download(@Validated Pb30OlEpayorderDetailSearchDTO
                                 pb30OlEpayorderDetailSearchDTO, HttpServletResponse response) throws Exception {
        DataScope.getDataScope("w", pb30OlEpayorderDetailSearchDTO, Boolean.FALSE);
        List<Pb30OlEpayorderDetailVO> pb30OlEpayorderDetailVOList = pb30OlEpayorderService.searchPageDetailVONoPage(pb30OlEpayorderDetailSearchDTO);

        List<Pb30OlEpayorderDetailVO> pb30OlEpayorderDetailVOListNoInvoice = pb30OlEpayorderDetailVOList.stream().filter(i -> i.getState() == null || i.getState() == 0 || i.getState() == 3).collect(Collectors.toList());
        List<Pb30OlEpayorderDetailVO> pb30OlEpayorderDetailVOListInvoice = pb30OlEpayorderDetailVOList.stream().filter(i -> i.getState() != null && (i.getState() == 1 || i.getState() == 2)).collect(Collectors.toList());
        TemplateExportParams templateExportParams = new TemplateExportParams("驳船移动支付记录.xls");

        LocalDateTime localDateTime = LocalDateTime.now();
        Map<String, Object> map = new HashMap<>();

        List<Map<String, String>> listNoInvoice = new ArrayList<>();
        List<Map<String, String>> listInvoice = new ArrayList<>();
        long total = 0;// 总个数
        BigDecimal totalCargoportcharge = BigDecimal.ZERO;// 总货港费
        BigDecimal totalBusinessagentcharge = BigDecimal.ZERO;// 总代理费
        BigDecimal totalBerthcharge = BigDecimal.ZERO;// 总停泊费
        BigDecimal totalServiceagentcharge = BigDecimal.ZERO;// 总围油栏费
        BigDecimal totalCount = BigDecimal.ZERO;// 总刷卡总金额
        BigDecimal totalAlreadymount = BigDecimal.ZERO;// 总发票
        BigDecimal totalIncome = BigDecimal.ZERO;// 总收入
        BigDecimal totalTax = BigDecimal.ZERO;// 总税款

        BigDecimal totalCargoportchargeNoInvoice = BigDecimal.ZERO;// 总货港费
        BigDecimal totalBusinessagentchargeNoInvoice = BigDecimal.ZERO;// 总代理费
        BigDecimal totalBerthchargeNoInvoice = BigDecimal.ZERO;// 总停泊费
        BigDecimal totalServiceagentchargeNoInvoice = BigDecimal.ZERO;// 总围油栏费
        BigDecimal totalCountNoInvoice = BigDecimal.ZERO;// 总刷卡总金额
//        BigDecimal totalAlreadymountNoInvoice = BigDecimal.ZERO;// 总发票
//        BigDecimal totalIncomeNoInvoice = BigDecimal.ZERO;// 总收入
//        BigDecimal totalTaxNoInvoice = BigDecimal.ZERO;// 总税款

        BigDecimal totalCargoportchargeInvoice = BigDecimal.ZERO;// 总货港费
        BigDecimal totalBusinessagentchargeInvoice = BigDecimal.ZERO;// 总代理费
        BigDecimal totalBerthchargeInvoice = BigDecimal.ZERO;// 总停泊费
        BigDecimal totalServiceagentchargeInvoice = BigDecimal.ZERO;// 总围油栏费
        BigDecimal totalCountInvoice = BigDecimal.ZERO;// 总刷卡总金额
        BigDecimal totalAlreadymountInvoice = BigDecimal.ZERO;// 总发票
        BigDecimal totalIncomeInvoice = BigDecimal.ZERO;// 总收入
        BigDecimal totalTaxInvoice = BigDecimal.ZERO;// 总税款

        for (Pb30OlEpayorderDetailVO pb30OlEpayorderDetailVO : pb30OlEpayorderDetailVOListNoInvoice) {
            total++;
            totalCargoportchargeNoInvoice = totalCargoportchargeNoInvoice.add(new BigDecimal(pb30OlEpayorderDetailVO.getCargoportcharge()));
            totalBusinessagentchargeNoInvoice = totalBusinessagentchargeNoInvoice.add(new BigDecimal(pb30OlEpayorderDetailVO.getBusinessagentcharge()));
            totalBerthchargeNoInvoice = totalBerthchargeNoInvoice.add(new BigDecimal(pb30OlEpayorderDetailVO.getBerthcharge()));
            totalServiceagentchargeNoInvoice = totalServiceagentchargeNoInvoice.add(new BigDecimal(pb30OlEpayorderDetailVO.getServiceagentcharge()));

            Map<String, String> temp = new HashMap<>();
            temp.put("no", String.valueOf(total));
            temp.put("payTime", pb30OlEpayorderDetailVO.getPaytime());
            temp.put("waterwaycargoid", pb30OlEpayorderDetailVO.getWaterwaycargoid());
            temp.put("customername", pb30OlEpayorderDetailVO.getCustomername());
            temp.put("cargoportcharge", pb30OlEpayorderDetailVO.getCargoportcharge());
            temp.put("businessagentcharge", pb30OlEpayorderDetailVO.getBusinessagentcharge());
            temp.put("berthcharge", pb30OlEpayorderDetailVO.getBerthcharge());
            temp.put("serviceagentcharge", pb30OlEpayorderDetailVO.getServiceagentcharge());
            temp.put("count", pb30OlEpayorderDetailVO.getCount());
//            temp.put("alreadymount", pb30OlEpayorderDetailVO.getAlreadymount());

            BigDecimal count = new BigDecimal(pb30OlEpayorderDetailVO.getCount());
            if(StringUtils.isBlank(pb30OlEpayorderDetailVO.getAlreadymount())){
                pb30OlEpayorderDetailVO.setAlreadymount("0");
            }
            BigDecimal alreadymount = new BigDecimal(pb30OlEpayorderDetailVO.getAlreadymount());
            BigDecimal income = alreadymount.divide(new BigDecimal("1.06"), 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal tax = alreadymount.subtract(income);

            totalCountNoInvoice = totalCountNoInvoice.add(count);
//            totalAlreadymountNoInvoice = totalAlreadymountNoInvoice.add(alreadymount);
//            totalIncomeNoInvoice = totalIncomeNoInvoice.add(income);
//            totalTaxNoInvoice = totalTaxNoInvoice.add(tax);

//            temp.put("income", income.stripTrailingZeros().toPlainString());
//            temp.put("tax", tax.stripTrailingZeros().toPlainString());
            temp.put("state", "待开票");

            listNoInvoice.add(temp);
        }

        total = 0;
        for (Pb30OlEpayorderDetailVO pb30OlEpayorderDetailVO : pb30OlEpayorderDetailVOListInvoice) {
            total++;
            totalCargoportchargeInvoice = totalCargoportchargeInvoice.add(new BigDecimal(pb30OlEpayorderDetailVO.getCargoportcharge()));
            totalBusinessagentchargeInvoice = totalBusinessagentchargeInvoice.add(new BigDecimal(pb30OlEpayorderDetailVO.getBusinessagentcharge()));
            totalBerthchargeInvoice = totalBerthchargeInvoice.add(new BigDecimal(pb30OlEpayorderDetailVO.getBerthcharge()));
            totalServiceagentchargeInvoice = totalServiceagentchargeInvoice.add(new BigDecimal(pb30OlEpayorderDetailVO.getServiceagentcharge()));

            Map<String, String> temp = new HashMap<>();
            temp.put("no", String.valueOf(total));
            temp.put("payTime", pb30OlEpayorderDetailVO.getPaytime());
            temp.put("waterwaycargoid", pb30OlEpayorderDetailVO.getWaterwaycargoid());
            temp.put("customername", pb30OlEpayorderDetailVO.getCustomername());
            temp.put("cargoportcharge", pb30OlEpayorderDetailVO.getCargoportcharge());
            temp.put("businessagentcharge", pb30OlEpayorderDetailVO.getBusinessagentcharge());
            temp.put("berthcharge", pb30OlEpayorderDetailVO.getBerthcharge());
            temp.put("serviceagentcharge", pb30OlEpayorderDetailVO.getServiceagentcharge());
            temp.put("count", pb30OlEpayorderDetailVO.getCount());
            temp.put("alreadymount", pb30OlEpayorderDetailVO.getAlreadymount());

            BigDecimal count = new BigDecimal(pb30OlEpayorderDetailVO.getCount());
            if(StringUtils.isBlank(pb30OlEpayorderDetailVO.getAlreadymount())){
                pb30OlEpayorderDetailVO.setAlreadymount("0");
            }
            BigDecimal alreadymount = new BigDecimal(pb30OlEpayorderDetailVO.getAlreadymount());
            BigDecimal income = alreadymount.divide(new BigDecimal("1.06"), 2, BigDecimal.ROUND_HALF_UP);
            BigDecimal tax = alreadymount.subtract(income);

            totalCountInvoice = totalCountInvoice.add(count);
            totalAlreadymountInvoice = totalAlreadymountInvoice.add(alreadymount);
            totalIncomeInvoice = totalIncomeInvoice.add(income);
            totalTaxInvoice = totalTaxInvoice.add(tax);

            temp.put("income", income.stripTrailingZeros().toPlainString());
            temp.put("tax", tax.stripTrailingZeros().toPlainString());
            temp.put("state", "已开票");

            temp.put("fphm", pb30OlEpayorderDetailVO.getFphm());

            listInvoice.add(temp);
        }

        // 获取根据字典值字典标签
        for (SysDictData sysDictData : DictUtils.getDictCache("ship_company_type")) {
            if (sysDictData.getDictValue().equals(pb30OlEpayorderDetailSearchDTO.getComid())) {
                map.put("company", sysDictData.getDictLabel());
                break;
            }
        }
        map.put("listnoinvoice", listNoInvoice);
        map.put("listinvoice", listInvoice);

        map.put("totalCargoportchargeNoInvoice", totalCargoportchargeNoInvoice.stripTrailingZeros().toPlainString());
        map.put("totalBusinessagentchargeNoInvoice", totalBusinessagentchargeNoInvoice);
        map.put("totalBerthchargeNoInvoice", totalBerthchargeNoInvoice);
        map.put("totalServiceagentchargeNoInvoice", totalServiceagentchargeNoInvoice);
        map.put("totalCountNoInvoice", totalCountNoInvoice);
        map.put("totalAlreadymountNoInvoice", 0);
        map.put("totalIncomeNoInvoice", 0);
        map.put("totalTaxNoInvoice", 0);

        map.put("totalCargoportchargeInvoice", totalCargoportchargeInvoice.stripTrailingZeros().toPlainString());
        map.put("totalBusinessagentchargeInvoice", totalBusinessagentchargeInvoice);
        map.put("totalBerthchargeInvoice", totalBerthchargeInvoice);
        map.put("totalServiceagentchargeInvoice", totalServiceagentchargeInvoice);
        map.put("totalCountInvoice", totalCountInvoice);
        map.put("totalAlreadymountInvoice", totalAlreadymountInvoice);
        map.put("totalIncomeInvoice", totalIncomeInvoice);
        map.put("totalTaxInvoice", totalTaxInvoice);

        totalCargoportcharge = totalCargoportchargeNoInvoice.add(totalCargoportchargeInvoice);
        totalBusinessagentcharge = totalBusinessagentchargeNoInvoice.add(totalBusinessagentchargeInvoice);
        totalBerthcharge = totalBerthchargeNoInvoice.add(totalBerthchargeInvoice);
        totalServiceagentcharge = totalServiceagentchargeNoInvoice.add(totalServiceagentchargeInvoice);
        totalCount = totalCountNoInvoice.add(totalCountInvoice);
        totalAlreadymount = totalAlreadymountInvoice;
        totalIncome = totalIncomeInvoice;
        totalTax = totalTaxInvoice;

        map.put("totalCargoportcharge", totalCargoportcharge.stripTrailingZeros().toPlainString());
        map.put("totalBusinessagentcharge", totalBusinessagentcharge);
        map.put("totalBerthcharge", totalBerthcharge);
        map.put("totalServiceagentcharge", totalServiceagentcharge);
        map.put("totalCount", totalCount);
        map.put("totalAlreadymount", totalAlreadymount);
        map.put("totalIncome", totalIncome);
        map.put("totalTax", totalTax);

        map.put("date", localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("man", SecurityUtils.getLoginUser().getUser().getUserName());

        Workbook workbook = ExcelExportUtil.exportExcel(templateExportParams, map);
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.close();
    }

    //点击水路运单界面的水路运单月结现结标签查询方法
    @PostMapping("/searchWaterCargoTagClickVO")
    public AjaxResult searchWaterCargoTagClickVO(@RequestBody String waterwaycargono){
        return AjaxResult.success(pb30OlEpayorderService.searchWaterCargoTagClickVO(waterwaycargono));
    }

    private PayApplyVO createOrder(String waterwaycargono, String chainId, String securityKey, String redirectUrl){
        List<Pb6Waterwaycargo> pb6WaterwaycargoList = pb6WaterwaycargoService.list(Wrappers.<Pb6Waterwaycargo>query().eq("WATERWAYCARGONO", waterwaycargono));
        if (pb6WaterwaycargoList.size() == 0) {
            throw new CustomException("水路运单不存在!");
        } else if (pb6WaterwaycargoList.size() > 1) {
            throw new CustomException("服务器数据库错误!");
        } else {
            List<Pb30OlEpaybargedetail> pb30OlEpaybargedetailList = pb30OlEpaybargedetailService
                    .list(new LambdaQueryWrapper<Pb30OlEpaybargedetail>().eq(Pb30OlEpaybargedetail::getWaterwaycargoid, waterwaycargono));
            if(pb30OlEpaybargedetailList.size() > 0){
                pb30OlEpaybargedetailList.forEach(i -> {
                    if("1".equals(i.getStatus())){
                        throw new CustomException("该水路运单线下已支付，无需重复支付！");
                    }
                });
            }
            Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoList.get(0);
            pb6WaterwaycargoService.canOrderOrForcast(pb6Waterwaycargo, 0);

            List<Pb6Cargoconsignmentdetail> pb6CargoconsignmentdetailList = pb6CargoconsignmentdetailService
                    .list(new LambdaQueryWrapper<Pb6Cargoconsignmentdetail>().eq(Pb6Cargoconsignmentdetail::getWaterwaycargoid, waterwaycargono));
            if(pb6CargoconsignmentdetailList.size() == 0){
                throw new CustomException("托运单不存在!");
            } else if(pb6CargoconsignmentdetailList.size() > 1){
                throw new CustomException("存在多条托运单!");
            }
            if(pb6CargoconsignmentdetailList.get(0).getIsoffline() == null || "N".equals(pb6CargoconsignmentdetailList.get(0).getIsoffline())){
                throw new CustomException("非小程序支付");
            }
            String nowDateEight = DateUtils.getNowEightDate();
            String nowDate = DateUtils.getTime();

            String count = getOrderIdByUUId();

            String chanflow = chainId + nowDateEight + count;// chanflow=chain_id+8位日期+其他id，最多30位

            String amount = new BigDecimal(pb6Waterwaycargo.getTotalcharge()).stripTrailingZeros().toPlainString();

            Pb6Bargeinfo pb6Bargeinfo = pb6BargeinfoService.getById(pb6Waterwaycargo.getBargeidid());

            String description = waterwaycargono + "，" + "驳船：" + pb6Bargeinfo.getBargename() + "向" + pubCompanyService.getComsnameById(pb6Waterwaycargo.getComid()) + "进行驳船支付";

            String companyId = "";
            if(1L == pb6Waterwaycargo.getComid()){
                companyId = PayUtil.COMPANY_ID_XS;
            } else if(16L == pb6Waterwaycargo.getComid()){
                companyId = PayUtil.COMPANY_ID_LS;
            } else if(3L == pb6Waterwaycargo.getComid() || 4L == pb6Waterwaycargo.getComid()){
                companyId = PayUtil.COMPANY_ID_XG;
            } else if(2L == pb6Waterwaycargo.getComid() ){
                companyId = PayUtil.COMPANY_ID_HP;
            }else {
                throw new CustomException("不支持的港口！");
            }
            PayApplyDTO payApplyDTO = PayApplyDTO
                    .builder()
                    .reqmsg(PayApplyDTO
                            .Reqmsg
                            .builder()
                            .reqhead(Reqhead
                                    .builder()
                                    .trancode(PayUtil.APPLY_TRAN_CODE)
                                    .sendtime(nowDate)
                                    .chanid(chainId)
                                    .chanflow(chanflow)
                                    .build())
                            .cdtrid(companyId)
                            .amt(amount)
                            .billid(waterwaycargono)
                            .billdesc(description)
                            .operuser("root")
                            .remark(null)
                            .reqtype("app")
                            .costcode(null)
                            .resulturl(redirectUrl)
                            .paymentnm(null)
                            .paymentacct(null)
                            .signature(Sha256Util.getSha256(chainId + chanflow + companyId + amount + securityKey))
                            .build())
                    .build();

            PayApplyVO payApplyVO = restTemplate.postForObject(PayUtil.BASE_URL, payApplyDTO, PayApplyVO.class);

            if (!"0000".equals(payApplyVO.getResmsg().getReshead().getProcd())) {
                throw new CustomException(payApplyVO.getResmsg().getReshead().getProinfo());
            }

            // 记录到支付表和支付明细表
            Pb30OlEpayorder pb30OlEpayorder = new Pb30OlEpayorder();
            pb30OlEpayorder.setOrderid(count);// 流水号
            pb30OlEpayorder.setStarttime(nowDate);
            pb30OlEpayorder.setEndtime(null);
            pb30OlEpayorder.setOrderdescrip(description);
            pb30OlEpayorder.setAmount(amount);
            pb30OlEpayorder.setStatus("0");// 0未支付
            pb30OlEpayorder.setCustomerid("" + pb6Bargeinfo.getId());
            pb30OlEpayorder.setCustomername(pb6Bargeinfo.getBargename());
            pb30OlEpayorder.setComid("" + pb6Waterwaycargo.getComid());
            pb30OlEpayorder.setRemark(null);
            pb30OlEpayorder.setPaytype("0");// 0支付
            pb30OlEpayorder.setChanflow(chanflow);
            pb30OlEpayorder.setChanid(chainId);
            pb30OlEpayorder.setEpayorderid(payApplyVO.getResmsg().getOrderid());
            pb30OlEpayorder.setPaytime(null);
            pb30OlEpayorder.setRefundorderid(null);
            pb30OlEpayorder.setRefundchanflow(null);
            pb30OlEpayorder.setFinancecheck("N");
            pb30OlEpayorder.setPayperson(pb6Bargeinfo.getBargename());
            pb30OlEpayorder.setContactcellphone(pb6Bargeinfo.getContactphone());
            pb30OlEpayorder.setBusinesstype("4");// 1小程序支付
            pb30OlEpayorderService.save(pb30OlEpayorder);

            Pb30OlEpaybargedetail pb30OlEpaybargedetail = new Pb30OlEpaybargedetail();
            pb30OlEpaybargedetail.setOrderid(pb30OlEpayorder.getId());
            pb30OlEpaybargedetail.setConsignflag(pb6Waterwaycargo.getConsignflag());
            pb30OlEpaybargedetail.setOutorinformid(pb6Waterwaycargo.getOutorinformid());
            pb30OlEpaybargedetail.setWaterwaycargoid(waterwaycargono);
            pb30OlEpaybargedetail.setPaytime(null);
            pb30OlEpaybargedetail.setPayman(pb6Bargeinfo.getBargename());
            pb30OlEpaybargedetail.setPaytype("现结");
            pb30OlEpaybargedetail.setCount(amount);
            pb30OlEpaybargedetail.setWeightvalue(pb6Waterwaycargo.getRationweight());
            pb30OlEpaybargedetail.setTransportcharge(pb6Waterwaycargo.getTransportcharge());
            pb30OlEpaybargedetail.setCargoportcharge(pb6Waterwaycargo.getCargoportcharge());
            pb30OlEpaybargedetail.setServiceagentcharge(pb6Waterwaycargo.getServiceagentcharge());
            pb30OlEpaybargedetail.setBusinessagentcharge(pb6Waterwaycargo.getBusinessagentcharge());
            pb30OlEpaybargedetail.setBerthcharge(pb6Waterwaycargo.getBerthcharge());
            pb30OlEpaybargedetail.setRemark(null);
            pb30OlEpaybargedetail.setComid("" + pb6Waterwaycargo.getComid());
            pb30OlEpaybargedetail.setStatus("0");// 0未支付
            pb30OlEpaybargedetail.setActualmount(amount);
            pb30OlEpaybargedetail.setContactphone(pb6Bargeinfo.getContactphone());
            pb30OlEpaybargedetail.setResourse("4");// 1小程序支付
            pb30OlEpaybargedetailService.save(pb30OlEpaybargedetail);

            /** 用于判断支付成功是否回调
             * 如果20秒没有回调，就自己获取订单信息更改状态
             */
            this.longTimeNoReply(pb30OlEpayorder.getId(), chainId, securityKey);

            return payApplyVO;
        }
    }

    /**
     * 生成16位随机码
     *
     * @return 随机码
     */
    private String getOrderIdByUUId() {
        int first = new Random(10).nextInt(8) + 1;
        int hashCodeV = UUID.randomUUID().toString().hashCode();
        if (hashCodeV < 0) { //有可能是负数
            hashCodeV = -hashCodeV;
        }
        return first + String.format("%015d", hashCodeV);
    }

    /**
     * 支付后长时间没有回调接口的调用方法
     */
    private void longTimeNoReply(Long pb30OlEpayorderId, String chainId, String securityKey) {
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                System.out.println("--------支付后长时间没有回调接口的调用方法-------");
                Pb30OlEpayorder pb30OlEpayorder = pb30OlEpayorderService.getById(pb30OlEpayorderId);
                Assert.notNull(pb30OlEpayorder, "支付不存在!");

                if (pb30OlEpayorder.getStatus().equals("1")) {
                    System.out.println("-------已回调支付成功方法--------");
                } else {
                    Pb30OlEpaybargedetail pb30OlEpaybargedetail = pb30OlEpaybargedetailService.getOne(Wrappers.<Pb30OlEpaybargedetail>query().eq("ORDERID", pb30OlEpayorder.getId()));
                    Assert.notNull(pb30OlEpaybargedetail, "支付明细不存在!");

                    PayQueryOrderDTO payQueryOrderDTO = PayQueryOrderDTO.builder()
                            .reqmsg(PayQueryOrderDTO
                                    .Reqmsg
                                    .builder()
                                    .reqhead(Reqhead
                                            .builder()
                                            .trancode(PayUtil.GET_ORDER_TRAN_CODE)
                                            .sendtime(DateUtils.getTime())
                                            .chanid(chainId)
                                            .chanflow(pb30OlEpayorder.getChanflow())
                                            .build())
                                    .orgorderid(pb30OlEpayorder.getEpayorderid())
                                    .signature(Sha256Util.getSha256(chainId + pb30OlEpayorder.getChanflow() + pb30OlEpayorder.getEpayorderid() + securityKey))
                                    .build())
                            .build();
                    PayQueryOrderVO payQueryOrderVO = restTemplate.postForObject(PayUtil.BASE_URL, payQueryOrderDTO, PayQueryOrderVO.class);

                    if (!"0000".equals(payQueryOrderVO.getResmsg().getReshead().getProcd())) {
                        throw new CustomException(payQueryOrderVO.getResmsg().getReshead().getProinfo());
                    }

                    String nowDate = DateUtils.getTime();
                    if (payQueryOrderVO.getResmsg().getSts().equals("01")) {
                        System.out.println("-------支付成功未回调，自己更改状态--------");
                        pb30OlEpayorderService.update(Wrappers.<Pb30OlEpayorder>update()
                                .set("STATUS", "1")// 1支付成功
                                .set("REMARK", "处理成功")
                                .set("PAYTIME", nowDate)
                                .eq("ID", pb30OlEpayorder.getId()));
                        pb30OlEpaybargedetailService.update(Wrappers.<Pb30OlEpaybargedetail>update()
                                .set("PAYTIME", nowDate)
                                .set("REMARK", "支付成功")
                                .set("STATUS", "1")// 1支付成功
                                .eq("ID", pb30OlEpaybargedetail.getId()));
                        pb6CargoconsignmentdetailService.update(Wrappers.<Pb6Cargoconsignmentdetail>lambdaUpdate()
                                .set(Pb6Cargoconsignmentdetail::getWxoperatestate, 4) // 4 待驳船主预约
                                .eq(Pb6Cargoconsignmentdetail::getWaterwaycargoid, pb30OlEpaybargedetail.getWaterwaycargoid()));

                        Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.searchWaterwaycargoByWaterwaycargoid(pb30OlEpaybargedetail.getWaterwaycargoid());
                        if(pb6Waterwaycargo.getChargebalancetype() != null && "月结".equals(pb6Waterwaycargo.getChargebalancetype())){
                            pb6WaterwaycargoService.update(new LambdaUpdateWrapper<Pb6Waterwaycargo>().set(Pb6Waterwaycargo::getChargebalancetype, "现结")
                                    .set(Pb6Waterwaycargo::getCargosize, null)
                                    .eq(Pb6Waterwaycargo::getWaterwaycargoid, pb30OlEpaybargedetail.getWaterwaycargoid()));
                        }
                    } else if (payQueryOrderVO.getResmsg().getSts().equals("03")) {
                        longTimeNoReply(pb30OlEpayorderId, chainId, securityKey);
                    } else if(payQueryOrderVO.getResmsg().getSts().equals("02")){
                        System.out.println("-------支付失败未回调，自己更改状态--------");
                        pb30OlEpayorderService.update(Wrappers.<Pb30OlEpayorder>update()
                                .set("STATUS", "2")// 1支付失败
                                .set("REMARK", "处理失败")
                                .set("PAYTIME", nowDate)
                                .eq("ID", pb30OlEpayorder.getId()));
                        pb30OlEpaybargedetailService.update(Wrappers.<Pb30OlEpaybargedetail>update()
                                .set("PAYTIME", nowDate)
                                .set("REMARK", "支付失败")
                                .set("STATUS", "2")// 支付失败
                                .eq("ID", pb30OlEpaybargedetail.getId()));
                    }
                }
            }
        }, 20000);// 设定指定的时间time,此处为20000毫秒
    }

    @PostMapping("/searchAmount")
    public AjaxResult searchAmount(@RequestBody String waterwaycargoid){
       List<Pb30OlEpaybargedetail> pb30OlEpaybargedetailList=pb30OlEpaybargedetailService.list(Wrappers.<Pb30OlEpaybargedetail>lambdaQuery().
                eq(Pb30OlEpaybargedetail::getWaterwaycargoid,waterwaycargoid).eq(Pb30OlEpaybargedetail::getStatus,1));
       if (pb30OlEpaybargedetailList.size()>0){
           Pb30OlEpaybargedetail pb30OlEpaybargedetail=pb30OlEpaybargedetailList.get(0);
           Pb30OlEpayorder pb30OlEpayorder=pb30OlEpayorderService.getById(pb30OlEpaybargedetail.getOrderid());
           Pb6Waterwaycargo pb6Waterwaycargo=pb6WaterwaycargoService.getOne(Wrappers.<Pb6Waterwaycargo>lambdaQuery().
                   eq(Pb6Waterwaycargo::getWaterwaycargoid,waterwaycargoid));
           float a=Float.parseFloat(pb30OlEpayorder.getAmount());
           float b=Float.parseFloat(pb6Waterwaycargo.getTotalcharge());
           if (a==b){
               return AjaxResult.success("匹配");
           }else{
               return AjaxResult.success("支付金额为"+pb30OlEpayorder.getAmount()+",与水路运单不匹配");
           }
       }else{
           return AjaxResult.error("该水路运单没有找到已支付订单");
       }
    }
}

package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.databarge.domain.InvoicePersonnel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 发票经手人员表mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
public interface InvoicePersonnelMapper extends BaseMapper<InvoicePersonnel> {
    List<Map<String,Object>> search();
    InvoicePersonnel searchBywaterwaycargoid(@Param("waterwaycargoid") String waterwaycargoid);
}

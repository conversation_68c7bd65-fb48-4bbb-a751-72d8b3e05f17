package com.ruoyi.common.domain.dto;

import com.ruoyi.common.domain.Cargoconsignment;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.UploadAddress;
import lombok.*;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class CargocmentDTO {


    /**
     *  托运单
     */
    private Cargoconsignment cargoconsignment;


    /**
     *  托运单明细
     */
    private Cargoconsignmentdetail cargoconsignmentdetail;

    /**
     * 1-退单，2-改单
     */
    private Integer state;//前端传递的条件


    /**
     * 文件信息
     */
    private List<UploadAddress> uploadAddress;


}

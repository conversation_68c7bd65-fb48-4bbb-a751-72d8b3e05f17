/* pages/consignment/consigndetail/consigndetail.wxss */
.consign-message{
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 70rpx;
  border-bottom: 1px solid #ddd;
  padding: 0 30rpx;
  width: 100%;
}
.consign-top{
  height: auto;
  width: 100%;
  margin-bottom: 20rpx;
  background: #fff;
}
.consign-detail{
  padding-top: 20rpx;
  background:#f8f8f8;
}
.message-title{
  color: #999;
  font-size: 28rpx;
}
.message-value{
  color: #333;
  font-size: 28rpx;
  flex:1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.consign-middle{
  height: 20rpx;
  background: #f5f5f5;
  width: 100%;
}
.consign-bottom-title{
  height: 76rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  width: 100%;
  border-bottom: 1px solid #ddd;
  background:#fff;
}
.annex-text{
  color: #333;
  font-size: 32rpx;
}
.consign-bottom-title .download-btn{
  height: 60rpx;
  border:1px solid #00426B;
  border-radius: 33rpx;
  line-height: 56rpx;
  color: #00426B;
  background: #ffffff;
  width: 220rpx;
  padding: 0rpx;
  margin: 0rpx;
  font-size: 28rpx;
  font-weight: normal;
}
.order-item{
  border-bottom: 1px solid #ddd;
  padding: 0 20rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.order-item-title{
  font-size: 28rpx;
  color: #999;
  line-height: 60rpx;
  width: auto;
}
.order-item-content{
  font-size: 28rpx;
  color: #0099ff;
  padding-left: 40rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.order-item-url{
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.arrow-icon{
  flex-shrink: 0;
  width: auto;
}
.detail-oper{
  height: 200rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.detail-oper .oper-btn{
  position: fixed;
  left: 50rpx;
  right: 50rpx;
  bottom: 50rpx;
  width: 80%;
  height: 90rpx;
  padding: 0rpx;
  border-radius: 10rpx;
  border: none;
  line-height: 90rpx;
  color: #fff;
  background: rgb(0, 66, 107);
}
.consign-title{
  width:100%;
  height: 60rpx;
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  padding: 0 30rpx;
  border-bottom: 1px solid #ddd;
}
.order-list{
  background-color: #fff;
  margin-bottom: 20rpx;
}
.radio-list{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 40rpx;
  height: 70rpx;
}
.radio-item{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-right: 40rpx;
  font-size: 28rpx;
}
.deliver-item{
  width:100%;
  height: 60rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 32rpx;
  color: #00639d;
  padding: 0 30rpx;
  border-bottom: 1px solid #ddd;
  background-color: #fff;
}
.add-message-emailipt{
  width: 100%;
  height:55rpx;
  color: #333;
  padding: 0 20rpx;
  font-size: 30rpx;
  border: 1rpx solid #ddd;
  text-align: left ;
}
.consign-half-item {
  width: 50%;
  flex-shrink: 0;
  color: #999;
  font-size: 28rpx;
}
.consign-col3-item {
  width: 45%;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  flex-shrink: 0;
}
.consign-col2-item {
  width: 27.5%;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  flex-shrink: 0;
}
.value-fontcolor{
  color: #333;
}
.checktxt {
  font-size: 28rpx;
  height: 50rpx;
  border: 1px solid #00426B;
  border-radius: 33rpx;
  line-height: 44rpx;
  color: #00426B;
  background: #ffffff;
  width: 110rpx;
  padding: 0rpx;
  margin: 0rpx;
  font-size: 28rpx;
  font-weight: normal;
  text-align: center;
}
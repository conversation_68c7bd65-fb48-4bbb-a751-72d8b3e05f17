// pages/minepage/message/message.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 查询条件参数
    form: {
      searchValue:"",//搜索关键字
      beginTime: "",//开始时间
      endTime: "",//结束时间
    },
    //消息列表
    messageList: []
  },

  /**
   * 
   * 方法
   */
    /**
   * 判断是否存在，不存在返回空字符串或其他
   * @param {传进来的变量} empty 
   * @param {为空时返回的值} replace 
   */
  emptyReturn(empty,replace){
    if(!replace || replace==null){
      replace = ""
    }
    if(empty == null || !empty){
      return replace
    }else{
      return empty
    }
  },

  //清空截止时间
  deleteEndTime(){
    this.setData({
      "form.endTime":""
    })
  },


  //清空开始时间
  deleteBeginTime(){
    this.setData({
      "form.beginTime":""
    })
  },

  //清空关键字
  deleteSearchValue(){
    this.setData({
      "form.searchValue":""
    })
  },

  //获取信息通知列表
  getNoticeListDatas(){
    let that = this 
    let param = {
      ...that.data.form
    }
    app.$post(app.$url.mine.getNoticeList,param).then(res=>{
      if(res.code == 200){
        var reg = new RegExp('\\\\n', 'g')
        res.data.map(item=>{
          item.title = that.emptyReturn(item.title)
          item.createTime = that.emptyReturn(item.createTime)
          item.isRead = that.emptyReturn(item.isRead)
          item.content =  item.content.replace(reg,' ')
        })
        that.setData({
          messageList:res.data
        })
      }else {
      app.$message(res.msg)
      }
    })
  },

  // 开始时间回调
  handleBeginDateChange(e) {
    //时间撮比较日期大小
    let beginTime = new Date(e.detail.value).getTime() //开始时间
    let endTime =  new Date(this.data.form.endTime).getTime()//结束时间
    if(beginTime && endTime && beginTime > endTime){
      app.$message("开始时间需小于或等于截止时间")
      this.setData({
        'form.beginTime':""
      })
      return
    }
    this.setData({
      'form.beginTime': e.detail.value
    })
  },

  // 结束时间回调
  handleEndDateChange(e) {
    //时间撮比较日期大小
    let endTime = new Date(e.detail.value).getTime()//结束时间
    let beginTime =  new Date(this.data.form.beginTime).getTime()//开始时间
    if(beginTime && endTime && endTime < beginTime){
      app.$message("截止时间需大于或等于开始时间")
      this.setData({
        'form.endTime':""
      })
      return
    }
    this.setData({
      'form.endTime': e.detail.value
    })
  },

  //关键字input
  searchValueInput(e){
    let searchValue = e.detail.value 
    this.setData({
      'form.searchValue':searchValue,
    })
    console.log(this.data)
    
  },
  
  //搜索
  searchHandle(){
    this.getNoticeListDatas()
  },
  
  //跳转详情页面
  jumpDetail(e){
    let currentObj = JSON.stringify(e.currentTarget.dataset.item)//当前对象
    wx.navigateTo({
      url: "/pages/minepage/messagedetail/messagedetail?currentObj="+currentObj
    })
  },


  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
  
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getNoticeListDatas()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.getNoticeListDatas()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
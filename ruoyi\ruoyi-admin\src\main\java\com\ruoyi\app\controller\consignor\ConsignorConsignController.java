package com.ruoyi.app.controller.consignor;

import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.app.controller.support.print.PrintUtil;
import com.ruoyi.barge.domain.bo.BargeUserBO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.Customer;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.bo.CargocmentBO;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.bo.CoutformBO;
import com.ruoyi.common.domain.dto.CargocmentDTO;
import com.ruoyi.common.domain.vo.WaterwayCargoVO;
import com.ruoyi.common.service.CustomerService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.consignor.service.ConsignorConsignService;
import com.ruoyi.databarge.domain.Pb6Bargework;
import com.ruoyi.databarge.service.Pb6BargeworkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ruoyi.common.core.domain.AjaxResult.getErrors;


/**
 * @Description 货主的托运控制层
 * <AUTHOR>
 * @Date 2020/7/30  10:45
 */
@Api(value = "货主-托运单-api", tags = "货主-托运单-api")
@Slf4j
@RestController
@RequestMapping("/consignor/consign")
public class ConsignorConsignController {

    @Autowired
    private ConsignorConsignService consignorConsignService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private Pb6BargeworkService pb6BargeworkService;
    @Autowired
    private PrintUtil printUtil;

    /**
     * 查询托运单列表
     *
     * @param status
     * @return
     */
    @ApiOperation("不同状态栏数据展示")
    @GetMapping("/status")
    public AjaxResult selectConsignByStatus(@RequestParam(value = "status", required = false) Long status,
                                            @RequestParam(value = "searchValue", required = false) String searchValue,
                                            @RequestParam(value = "beginTime", required = false) String beginTime,
                                            @RequestParam(value = "endTime", required = false) String endTime) {

        return AjaxResult.success(consignorConsignService.selectConsignByStatus(status, searchValue, beginTime, endTime));
    }

    /**
     * 托运单详情
     *
     * @param consignId 托运单主键id
     * @return
     */
    @ApiOperation(value = "托运单回显数据")
    @GetMapping("/bookingNoteDetail")
    public AjaxResult selectConsignById(Long consignId) {

        CargocmentBO cargocmentBO = consignorConsignService.selectConsignById(consignId);

        return AjaxResult.success(cargocmentBO);
    }


    /**
     * 修改托运单信息
     *
     * @param cargocmentBO
     * @return
     */
    @ApiOperation("修改托运单信息")
    @PutMapping("/update/BookingNote")
    public AjaxResult updateBookingNote(@RequestBody CargocmentBO cargocmentBO) {

        consignorConsignService.updateBookingNote(cargocmentBO);

        return AjaxResult.success();
    }


    /**
     * 办理托运单
     *
     * @param cargocmentBO
     * @return
     */
    @ApiOperation("办理托运单")
    @PostMapping("/transact")
    public AjaxResult transactBookingNote(@RequestBody CargocmentBO cargocmentBO) {

        consignorConsignService.transactBookingNote(cargocmentBO);

        return AjaxResult.success();
    }


    /**
     * 查询当前用户派过的驳船
     *
     * @return
     */
    @ApiOperation("查询用户派过的驳船")
    @GetMapping("/record")
    public AjaxResult recordBarge() {

        return AjaxResult.success(consignorConsignService.recordBarge());
    }


    /**
     * 托运单-改单/退单
     *
     * @param cargocmentDTO
     * @return
     */
    @ApiOperation("托运单-(改单/退单)")
    @PutMapping("/modify")
    public AjaxResult modificationBookingNote(@RequestBody CargocmentDTO cargocmentDTO) {
        consignorConsignService.modificationBookingNote(cargocmentDTO);
        return AjaxResult.success();

    }


    /**
     * 匹配出库单
     *
     * @param coutformBO
     * @return
     */
    @ApiOperation("匹配出库单")
    @PostMapping("/coutform")
    public AjaxResult selectCoutformByCoutformId(@RequestBody CoutformBO coutformBO) {

        return AjaxResult.success(consignorConsignService.selectCoutformByCoutformId(coutformBO));
    }

    /**
     * 查询历史收货人
     *
     * @return
     */
    @ApiOperation("历史收货人")
    @GetMapping("/consignee")
    public AjaxResult consignee() {

        return AjaxResult.success(consignorConsignService.consignee());
    }



    /**
     * 匹配船公司显示
     *
     * @param shipCompanyName
     * @return
     */
    @ApiOperation("匹配船公司显示")
    @GetMapping("/shipCompany")
    public AjaxResult selectShipCompanyByShipCompanyName(String shipCompanyName) {

        return AjaxResult.success(consignorConsignService.selectShipCompanyByShipCompanyName(shipCompanyName));
    }


    /**
     * 获取月结公司列表
     * @return
     */
    @PostMapping("/getMonthCompany")
    public AjaxResult getMonthCompany(@RequestBody Customer customer) {
        List<Customer> customerList = customerService.getMothCompanyList(customer);
        return AjaxResult.success(customerList);
    }


    /**
     * 搜索驳船
     *
     * @param searchValue
     * @return
     */
    @ApiOperation("搜索驳船")
    @GetMapping("/searchBarge")
    public AjaxResult searchBarge(String searchValue) {
        return AjaxResult.success(consignorConsignService.searchBarge(searchValue));
    }


    /**
     * 搜索目的港
     *
     * @param searchValue
     * @return
     */
    @ApiOperation("搜索目的港")
    @GetMapping("/searchEndPort")
    public AjaxResult searchEndPort(String searchValue) {
        return AjaxResult.success(consignorConsignService.searchEndPort(searchValue));
    }

    /**
     * 水路运单详情
     *
     * @param id 托运单明细id
     * @param status 1 改单需要数据 2水路详情
     * @return
     */
    @ApiOperation("水路运单详情")
    @GetMapping("/waybillDetail")
    public AjaxResult waybillDetail(@RequestParam("id") Long id , @RequestParam("status") Integer status) throws Exception {
        WaterwayCargoVO waterwayCargoVO = consignorConsignService.waybillDetail(id, status);

/*
        String waterwayCargoId = waterwayCargoVO.getWaterwayCargoId();
        AjaxResult pdfOrWord = getPdfOrWord(waterwayCargoId);
        HashMap data = (HashMap) pdfOrWord.get("data");
        waterwayCargoVO.setBase64Map(data);
*/

        return AjaxResult.success(waterwayCargoVO);

    }


    /**
     * 删除托运单
     *
     * @return
     */
    @ApiOperation("删除托运单")
    @DeleteMapping("/delete/bookingNote")
    public AjaxResult deleteBookingNote(@RequestBody CargocmentBO cargocmentBO) {
        consignorConsignService.deleteBookingNote(cargocmentBO);
        return AjaxResult.success();

    }


    /**
     * 取消改单/退单操作
     *
     * @return
     */
    @ApiOperation("取消改单/退单操作")
    @PostMapping("/cancelOperation")
    public AjaxResult cancelOperation(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        try {
            consignorConsignService.cancelOperation(cargocmentdetailBO);
            return AjaxResult.success();
        }
        catch (Exception e){
            return AjaxResult.success(e.getMessage());
        }
    }



    /**
     * 个人信息保存
     * @param
     * @return
     */
    @ApiOperation("个人信息保存")
    @PostMapping("/personRecord")
    public AjaxResult personRecord(@RequestBody @Valid BargeUserBO bargeUserBO, BindingResult result) {

        if (bargeUserBO.getUserId() == null) {
            log.error("用户id不能为空");
            return AjaxResult.error("用户id不能为空");
        }

        // 判断BindingResult是否保存错误的验证信息，如果有，则直接return
        if (result.hasErrors()) {
            return getErrors(result);
        }

        return consignorConsignService.personRecord(bargeUserBO);
    }

    /**
     * 个人详情
     * @return
     */
    @PostMapping("/recordDetail")
    public AjaxResult recordDetail() {
        return consignorConsignService.recordDetail();
    }


    /**
     * 获取 水路货物运单.pdf 货物交接清单.docx 广州港新沙港务有限公司驳船装货交接凭证.docx 文件
     * @param waterwayCargoId 水路运单编号
     * @return base64
     */
    @GetMapping("/getPdfOrWord")
    public AjaxResult getPdfOrWord(@RequestParam("waterwayCargoId") String waterwayCargoId) throws Exception {

        if (StringUtils.isBlank(waterwayCargoId)) {
            return AjaxResult.error("水路运单编号不能为空");
        }
        Pb6Bargework pb6Bargework = pb6BargeworkService.getBaseMapper().selectOne(new QueryWrapper<Pb6Bargework>().eq("WATERWAYCARGOID", waterwayCargoId));
        HashMap<String, String> base64Map = new HashMap<>();
        for (int type = 1; type <= 3 ; type++) {
            ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
            String fileName="";
            switch (type){
                case 1:
                    if (pb6Bargework != null) {
                        printUtil.printBCJJPZ(pb6Bargework.getId(),byteArrayOutputStream);
                        fileName="广州港新沙港务有限公司驳船装货交接凭证.docx";
                    }
                    break;
                case 2:
                    printUtil.printHWJJQD(waterwayCargoId,byteArrayOutputStream);
                    fileName="货物交接清单.docx";
                    break;
                case 3:
                    printUtil.printSLHWYD(waterwayCargoId,byteArrayOutputStream);
                    fileName="水路货物运单.pdf";
                    break;
                default:
                    break;
            }
            base64Map.put(fileName, Base64.encode(byteArrayOutputStream.toByteArray()));
        }
        return AjaxResult.success(base64Map);
    }
    /**
     * 托运单改单时判断出库单件数，为0时则前端配载件数回写0
     * 不为0，则前端配载件数必填
     */
    @GetMapping("/checkAmt")
    public AjaxResult checkAmt(@RequestParam("outOrInFormId")String outOrInFormId){
        String amt = consignorConsignService.checkAmt(outOrInFormId);
        if(amt.equals("0")){
            return new AjaxResult(500,"出库单件数为0");
        }
        else {
            return AjaxResult.success();
        }
    }
}

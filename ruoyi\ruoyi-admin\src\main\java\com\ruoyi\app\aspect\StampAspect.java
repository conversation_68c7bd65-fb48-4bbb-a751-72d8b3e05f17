package com.ruoyi.app.aspect;

import cn.hutool.core.convert.Convert;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.web.utils.BargeUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-11 10:34
 */
@Slf4j
@Aspect
@Component
public class StampAspect {

    @Autowired
    private CargocmentdetailService cargocmentdetailService;

    /**
     * 配置织入点
     */
    @Pointcut("execution(* com.ruoyi.app.controller.barge.BargeConsignController.defray(..))")
    public void stampCut() {

    }

    /**
     * 支付完毕之后
     *
     * @param rvt 返回参数
     */
    @AfterReturning(pointcut = "stampCut()", returning = "rvt")
    public void doDefrayAfterReturning(Object rvt) {
        try {
            AjaxResult ajaxResult = Convert.convert(AjaxResult.class, rvt);
            log.info("doDefrayAfterReturning - 返回对象：{}", ajaxResult);
            if (HttpStatus.SUCCESS == Convert.toInt(ajaxResult.get("code"))) {
                CargocmentdetailBO cargocmentdetailBO = Convert.convert(CargocmentdetailBO.class,ajaxResult.get(AjaxResult.DATA_TAG));
                log.info("doDefrayAfterReturning - 返回参数：{}", cargocmentdetailBO);
                if (StringUtils.isNotNull(cargocmentdetailBO)) {
                    log.warn("水路运单编号：{}", cargocmentdetailBO.getWaterwayCargoId());
                    if (StringUtils.isNotBlank(cargocmentdetailBO.getWaterwayCargoId())) {
                        Thread.sleep(2000);
                        BargeUtils.defrayStamp(cargocmentdetailBO.getWaterwayCargoId(), BargeUtils.WATERWAYCARGO_FILENAME);
                    }
                }
            }
        } catch (Exception e) {
            log.error("支付盖章出错", e);
        }
    }

    /**
     * 月结审批同意之后
     *
     * @param rvt 返回参数
     */
    @AfterReturning(pointcut = "execution(* com.ruoyi.app.controller.carrier.CarrierController.startAudit(..))", returning = "rvt")
    public void doStartAuditAfterReturning(Object rvt) {
        try {
            AjaxResult ajaxResult = Convert.convert(AjaxResult.class, rvt);
            log.info("doStartAuditAfterReturning - 返回对象：{}", ajaxResult);
            if (HttpStatus.SUCCESS == Convert.toInt(ajaxResult.get("code"))) {
                CargocmentdetailBO result = Convert.convert(CargocmentdetailBO.class, ajaxResult.get(AjaxResult.DATA_TAG));
                log.info("doStartAuditAfterReturning - 返回参数：{}", result);
                if (StringUtils.isNotNull(result) && result.getAuditFlag() == 3) {
                    List<Long> detailIds = result.getDetailIds();
                    Thread.sleep(2000);
                    detailIds.forEach(id -> {
                        Cargoconsignmentdetail detail = cargocmentdetailService.getById(id);
                        if (StringUtils.isNotNull(detail)) {
                            log.warn("水路运单编号：{}", detail.getWaterwayCargoId());
                            BargeUtils.defrayStamp(detail.getWaterwayCargoId(), BargeUtils.WATERWAYCARGO_FILENAME);
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("月结审批同意之后盖章出错", e);
        }
    }
}

// pages/consignment/consigndetail/bargeOutboundInquiry.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //驳船出库列表数组
    listData:[1,2,3,4,5,6,7,8,9,10],
    // 查询条件参数
    form: {
      search:"",//搜索关键字-驳船名称
      startTime: '',//开始时间
      endTime: '',//结束时间
      outboundNum:"",//出库单号或到验号
    },
  },

  /**
   * 
   * 方法
   */

   /**
   * 判断是否存在，不存在返回空字符串或其他
   * @param {传进来的变量} empty 
   * @param {为空时返回的值} replace 
   */
  emptyReturn(empty,replace){
    if(!replace || replace==null){
      replace = ""
    }
    if(empty == null || !empty){
      return replace
    }else{
      return empty
    }
  },

  //搜索
  handleSearch(){
    
  },

  //导出
  handleExport(){
    
  },
  
  //关键字input
  searchInput(e){
    this.setData({
      "form.search":e.detail.value
    })
  },

  //出库单号或到验号input
  outboundNumInput(e){
    this.setData({
      "form.outboundNum":e.detail.value
    })
  },


   // 开始时间回调
   handleBeginDateChange(e) {
    //时间撮比较日期大小
    let beginTime = new Date(e.detail.value).getTime() //开始时间
    let endTime =  new Date(this.data.form.endTime).getTime()//结束时间
    if(beginTime && endTime && beginTime > endTime){
      app.$message("开始时间需小于或等于截止时间")
      this.setData({
        'form.startTime':""
      })
      return
    }
    this.setData({
      'form.startTime': e.detail.value
    })
  },

  // 结束时间回调
  handleEndDateChange(e) {
    //时间撮比较日期大小
    let endTime = new Date(e.detail.value).getTime()//结束时间
    let beginTime =  new Date(this.data.form.startTime).getTime()//开始时间
    if(beginTime && endTime && endTime < beginTime){
      app.$message("截止时间需大于或等于开始时间")
      this.setData({
        'form.endTime':""
      })
      return
    }
    this.setData({
      'form.endTime': e.detail.value
    })
  },





  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
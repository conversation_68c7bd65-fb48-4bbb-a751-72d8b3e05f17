package com.ruoyi.databarge.domain.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * Description: 驳船预报管理查询VO
 *
 * @Author: ChenJin on 2020/9/24.
 * @Date: 2020/9/24 10:16
 */
@Getter
@Setter
public class BargeCheckInVO {

    private String waterwaycargoid;
    private String outorinformid;
    private String loadometerid;
    private String consignflag;
    private String bargename;
    private String flagbargestate;
    private String mmsi;
    private String rationpiece;
    private String rationweight;
    private String cargename;
    private String wxappointmenttime;
    private String registertime;
    private String wxrationcontactnumber;
    private String phonenumber;
    private String comid;
    private String wxoperatetime;
    private String fiveDayReporting;
    private String isreporting;
}

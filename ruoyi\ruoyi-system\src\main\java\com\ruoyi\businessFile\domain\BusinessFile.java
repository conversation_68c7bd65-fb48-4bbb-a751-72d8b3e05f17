package com.ruoyi.businessFile.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 14:57
 */
@TableName("business_file")
@Data
@KeySequence(value = "SEQ_BUSINESS_FILE")
public class BusinessFile extends BaseEntity {

    @TableId(value = "file_id",type = IdType.INPUT)
    private String fileId;

    //文件归属类型
    @TableField("file_business_type")
    private String fileBusinessType;

    //放到数据库中
    @TableField("file_business_id")
    private String fileBusinessID;

    @TableField("file_type_name")
    private String fileTypeName;

    @TableField("file_sort")
    private Integer fileSort;

    @TableField("file_name")
    private String fileName;

    @TableField("file_sysid")
    private Long fileSysid;

    private String fileRemark;

}

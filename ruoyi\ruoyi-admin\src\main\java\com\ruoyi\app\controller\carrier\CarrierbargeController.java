package com.ruoyi.app.controller.carrier;

import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.barge.domain.bo.BargeUserBO;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.BargeInfoAudit;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.UploadAddress;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.vo.BargeInfoVO;
import com.ruoyi.common.enums.UploadDataType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.UploadAddressMapper;
import com.ruoyi.common.service.BargeInfoAuditService;
import com.ruoyi.common.service.BargeInfoService;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ftp.FtpUtils;
import com.ruoyi.system.service.AuthorizeService;
import com.ruoyi.web.controller.common.CommonController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Arrays;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/26 9:20
 */
@RestController
@RequestMapping("/carrierapp")
public class CarrierbargeController {
    @Autowired
    private CargocmentdetailService cargocmentdetailService;
    @Autowired
    private BargeInfoService bargeInfoService;

    @Autowired
    private BargeInfoAuditService bargeInfoAuditService;

    @Autowired
    private AuthorizeService authorizeService;

    @Autowired
    private UploadAddressMapper uploadAddressMapper;

    @Autowired
    private BargeCenterService bargeCenterService;

    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    @Autowired
    private CommonController commonController;

    @Autowired
    private FtpUtils ftpUtils;
    // ---------------------驳船管理----------------------//

    /**
     * 驳船管理-驳船查询
     */
    @RequestMapping("/barge/list")
    public AjaxResult listByCompanyId(@RequestBody BargeInfoBO bargeInfoBO){
        if(bargeInfoBO.getBargeCompanyId()==null){
            throw new CustomException("船公司不存在");
        }
        if(bargeInfoBO.getSearchBargeType().equals("1")){//1.查询船公司已有的驳船(只是已审核过的驳船)
            List<BargeInfoVO> bargeList = bargeInfoService.getBargeListByCompanyId(bargeInfoBO);
            return AjaxResult.success(bargeList);
        }else if(bargeInfoBO.getSearchBargeType().equals("2")){//2 查询驳船列表携带当前用户-查询未挂靠的但是有当前用户修改或创建的驳船列表
            SysUser loginUser = SecurityUtils.getLoginUser().getUser();
            bargeInfoBO.setUserId(loginUser.getUserId());
            bargeInfoBO.setUserName(loginUser.getNickName());
            List<BargeInfoVO> bargeList = bargeInfoService.getBargeListByCompanyUser(bargeInfoBO);
            return AjaxResult.success(bargeList);
        }else{
            throw new CustomException("参数异常!");
        }

    }

    /**
     * 查询驳船详情
     * @param bargeInfoAudit
     * @return
     */
    @RequestMapping("barge/details")
    public AjaxResult bargeDetails(@RequestBody BargeInfoAudit bargeInfoAudit){
        //这里只查询资料，详情已经在列表中查出，前端只做显示不用再查询一次
        BargeInfo bargeInfo = bargeInfoService.getById(bargeInfoAudit.getPb6bargeInfoId());

        // 获取资料
        QueryWrapper<UploadAddress> wrapper = new QueryWrapper<>();
        wrapper.eq("LINK_ID", bargeInfoAudit.getPb6bargeInfoId())
                .eq("LINK_TYPE", UploadDataType.BARGE_RECORD_DATA.getType())
                .eq("STATUS", 1)
                .eq("BAK_FLAG", 1)
                .le("rownum", 4)
                .orderByDesc("UPLOAD_TIME")
                .orderByDesc("DATA_TYPE");
        List<UploadAddress> list = uploadAddressMapper.selectList(wrapper);

        list.forEach(item -> {
            String url = item.getUrl();
            //String[] u = url.split("/gzgtest");
            String path;
            String base64 = null;
            if (url.contains("/gzgtest")) {
                //path = dataLocal + u[1];
                path = "http://************" + url.substring(url.indexOf("/bargeRecord"));
                try {
                    URL urls =new URL(path); // 创建URL
                    URLConnection urlConnection = urls.openConnection(); // 试图连接并取得返回状态码
                    urlConnection.connect();
                    HttpURLConnection httpconn =(HttpURLConnection)urlConnection;
                    if (httpconn.getResponseCode() == HttpURLConnection.HTTP_OK) {
                        InputStream inputStream = urlConnection.getInputStream();
                        ByteArrayOutputStream outStream =new ByteArrayOutputStream();
                        byte[] b = new byte[2048];
                        int len;
                        while((len=inputStream.read(b))!=-1){
                            outStream.write(b,0,len);
                        }
                        outStream.close();
                        inputStream.close();
                        base64 = Base64.encode(outStream.toByteArray());
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else {
                //path = dataLocal + url.substring(url.indexOf("/ConsignPicture"));
                base64 = ftpUtils.downloadBase64(url);
            }
            item.setFileBase64(base64);

            //item.setUrl(path);
        });
        return AjaxResult.success(list);
    }
    /**
     * 驳船管理-查询已有未挂靠本公司的驳船
     */
    @RequestMapping("/barge/listByNoCompany")
    public AjaxResult listByNoCurrentCompany(@RequestBody BargeInfoBO bargeInfoBO){
        if(bargeInfoBO.getBargeCompanyId()==null){
            throw new CustomException("船公司不存在");
        }
        List<BargeInfoVO> bargeList = bargeInfoService.getBargeListByNoCompany(bargeInfoBO);
        return AjaxResult.success(bargeList);
    }

    /**
     * 驳船管理-新增驳船或修改驳船
     */
    @PreAuthorize("@ss.hasAnyPermi({'ship:barge:add','ship:barge:update'})")
    @RequestMapping("/barge/addorupdate")
    public AjaxResult addBarge(@RequestBody BargeInfoAudit bargeInfoAudit){
        String bargeName = bargeInfoAudit.getBargeName();
        char[] arr = bargeName.toCharArray();
        String string = new String();
        for(int i = 0 ; i < arr.length ; i++){
            if(arr[i] == '粵'){
                arr[i] = '粤';
            }
            string += arr[i];
        }
        bargeInfoAudit.setBargeName(string);

        String seal = bargeInfoAudit.getSealImg();
        if (StringUtils.isNotBlank(seal)) {
            String path = commonController.saveSeal(seal,SecurityUtils.getLoginUser().getUser().getUserId());
            bargeInfoAudit.setSealPath(path);
        }

        return bargeInfoAuditService.addBarge(bargeInfoAudit);
    }

//    /**
//     * 获取驳船挂靠资料连接列表
//     * @param bargeInfoAudit
//     * @return
//     */
//    @RequestMapping("/barge/bargeResources")
//    public AjaxResult getBargeResource(@RequestBody BargeInfoAudit bargeInfoAudit){
//        bargeInfoAuditService.getBargeResource(bargeInfoAudit);
//    }

    /**
     * 驳船管理-驳船挂靠，修改挂靠
     */
    @PreAuthorize("@ss.hasAnyPermi({'ship:barge:binding','ship:barge:delete'})")
    @RequestMapping("/barge/bindingBarge")
    public AjaxResult bindingBarge(@RequestBody BargeInfoAudit bargeInfoAudit){


        String seal = bargeInfoAudit.getSealImg();
        if (StringUtils.isNotBlank(seal)) {
            String path = commonController.saveSeal(seal,SecurityUtils.getLoginUser().getUser().getUserId());
            bargeInfoAudit.setSealPath(path);
        }
        // 判断驳船审核状态 审核中不发送消息
        //bargeCompanyLink.setCheckFlag(true);
        //bargeInfoAudit.getBargeCompanyLink().setBindingType(bargeInfoAudit.getBargeCompanyLink().getUpdateBindingType());
        //System.out.println(bargeInfoAudit.getBargeCompanyLink().getBindingType());
        return bargeInfoAuditService.updateBarge(bargeInfoAudit);
        //int i = bargeCompanyLinkService.bingdingBarge(bargeInfoAudit.getBargeCompanyLink());

    }

    /**
     * 查询驳船出库记录
     * @param bargeInfoBO
     * @return
     */
    @RequestMapping("barge/record")
    public AjaxResult bargeRecord(@RequestBody BargeInfoBO bargeInfoBO){
        List<Cargoconsignmentdetail> list = cargocmentdetailService.list(new LambdaQueryWrapper<Cargoconsignmentdetail>()
                .eq(Cargoconsignmentdetail::getBargeName, bargeInfoBO.getBargeName())
                .eq(Cargoconsignmentdetail::getOnineresourse, 3)
        );
        return AjaxResult.success(list);
    }

    /**
     * 查询驳船轨迹
     * @return
     */
    @RequestMapping("barge/route")
    public AjaxResult bargeRoute(@RequestBody BargeInfoBO bargeInfoBO){
        return AjaxResult.success();
    }



    /////////////////////------------------------我的--驳船成员管理--------/////////////////

    /**
     * 获取船公司自有船列表
     * @return
     */
    @PostMapping("/barge/getBargeListByMyself")
    public AjaxResult getBargeListByMyself(@RequestBody BargeUserBO bargeUser) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        BargeInfoBO bargeInfoBO = new BargeInfoBO();
        bargeInfoBO.setBargeCompanyId(user.getCompanyId());
        bargeInfoBO.setBindingType(1);
        bargeInfoBO.setSearchValue(bargeUser.getSearchValue());
        List<BargeInfoVO> bargeList = bargeInfoService.getBargeListByCompanyId(bargeInfoBO);
        bargeList.forEach(item -> {
            // 设置驳船主id
            SysUser su = sysUserBargeMapper.queryBargeUserByBargeId(null, item.getPb6bargeInfoId());
            if (su != null) {
                item.setUserId(su.getUserId());
            }
        });
        return AjaxResult.success(bargeList);
    }

    /**
     * 获取船员列表
     * @return
     */
    @PostMapping("/barge/getShipCrewList")
    public AjaxResult getShipCrewList(@RequestBody BargeUserBO bargeUserBO) {
        return bargeCenterService.getCrewList(bargeUserBO);
    }

    /**
     * 添加船员
     * @return
     */
    @ApiOperation("添加船员")
    @PostMapping("/barge/addShipCrew")
    public AjaxResult addShipCrew(@RequestBody @Valid BargeUserBO bargeUserBO, BindingResult result) {

        // 判断BindingResult是否保存错误的验证信息，如果有，则直接return
        if (result.hasErrors()) {
            return AjaxResult.getErrors(result);
        }

        if (bargeUserBO.getUserId() == null) {
            return AjaxResult.error("驳船主id不能为空");
        }

        return bargeCenterService.addCrew(bargeUserBO);
    }

    /**
     * 删除船员
     * @param bargeUserBO
     * @return
     */
    @ApiOperation("删除船员")
    @PostMapping("/barge/delShipCrew")
    public AjaxResult delShipCrew(@RequestBody BargeUserBO bargeUserBO) {
        List<SysUserBarge> list = bargeUserBO.getUserBargeList();
        if (list == null || list.size() <= 0) {
            return AjaxResult.error("请选择船员");
        }

        return bargeCenterService.delCrew(bargeUserBO);
    }

    /**
     * 授权
     * @param sysUser
     * @return
     */
    @PostMapping("/barge/shipAuthorize")
    public AjaxResult shipAuthorize(@RequestBody SysUser sysUser) {

        if (sysUser.getUserId() == null) {
            return AjaxResult.error("被授权用户id不能为空");
        }
        // 查询该驳船是否有驳船主
        SysUser su = sysUserBargeMapper.queryBargeUserByBargeId(null, sysUser.getBargeId());
        if (null == su) {
            return AjaxResult.error("该驳船尚未绑定驳船主，请联系驳船主前往驳船主小程序绑定该驳船");
        }
        return AjaxResult.success(authorizeService.authorize(sysUser.getUserId(), sysUser.getBargeId(), sysUser.getMenus()));
    }
}

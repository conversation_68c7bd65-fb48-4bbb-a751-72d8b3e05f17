package com.ruoyi.basic.controller;


import com.ruoyi.basic.domain.BasicRate;
import com.ruoyi.basic.service.BasicRateService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023/11/20 9:53
 */
@RequestMapping("/basic/rate")
@RestController
public class BasicRateController extends BaseController {

    @Autowired
    BasicRateService basicRateService;

    /**
     * @param
     * @return
     * @description lsit
     * <AUTHOR>
     * @date 2023/11/16 14:43
     */
    @RequestMapping("/list")
    public TableDataInfo list(BasicRate basicRate){
        startPage();
        return getDataTable(basicRateService.selectBasicRateList(basicRate));
    }

    /**
     * @param
     * @return
     * @description add
     * @date 2023/8/21 15:45
     */
    @PostMapping("add")
    public AjaxResult add(@RequestBody BasicRate basicRate) {
        return basicRateService.addBasicRate(basicRate);
    }

    /**
     * @param
     * @return
     * @description edit
     * @date 2023/8/21 15:45
     */
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody BasicRate basicRate) {
        return basicRateService.updateBasicRate(basicRate);
    }

    /**
     * @param
     * @return
     * @description delete
     * @date 2023/8/21 15:45
     */
    @DeleteMapping("delete/{rateId}")
    public AjaxResult delete(@PathVariable Long rateId){
        return AjaxResult.success(basicRateService.removeById(rateId));
    }

    /**
     * @param
     * @return
     * @description get
     * <AUTHOR>
     * @date 2023/11/16 16:31
     */
    @GetMapping("get/{rateId}")
    public AjaxResult get(@PathVariable Long rateId){
        return AjaxResult.success(basicRateService.getById(rateId));
    }

    /**
     * @param
     * @return
     * @description 客户列表
     * @date 2023/11/20 10:00
     */
    @PostMapping("customerList")
    public AjaxResult customerList(){
        return AjaxResult.success(basicRateService.selectBasicCustomerList());
    }

    /**
     * @param
     * @return
     * @description 船舶列表
     * @date 2023/11/20 10:00
     */
    @PostMapping("shipList")
    public AjaxResult shipList(){
        return AjaxResult.success(basicRateService.selectBasicShipList());
    }

    /**
     * @param
     * @return
     * @description 码头列表
     * @date 2023/11/20 10:00
     */
    @PostMapping("terminalList")
    public AjaxResult terminalList(){
        return AjaxResult.success(basicRateService.selectBasicTerminalList());
    }

}

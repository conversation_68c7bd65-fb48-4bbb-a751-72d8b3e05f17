package com.ruoyi.app.controller.support.print;

import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.databarge.shipxy.ShipxyUtil;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;

/**
 * <AUTHOR>
 * @Date 2020/11/25 16:57
 * @Description:
 */
@RestController
@RequestMapping("/test1")
@AllArgsConstructor
public class TestController111 {
    private final PrintUtil printUtil;
   @Autowired
    FddCommonService fddCommonService;

    @GetMapping("/print")//http://localhost:8080/gzgapp/test1/print?type=3&&id=BBZ042105171012
    //https://bulkbarge.gzport.com/gzgapp/test1/print?type=3&&id=BBZ042105171012
//    http://localhost:8080/gzgapp/test1/print?type=1&&id=1118345
    //http://localhost:8080/gzgapp/test1/print?type=2&&id=BBZ022201160014
    public void print(@RequestParam("type") Integer type, @RequestParam("id") String id, HttpServletResponse response) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
        String fileName="";

        switch (type){
            case 1:
                printUtil.printBCJJPZ(Long.parseLong(id),byteArrayOutputStream);
                fileName="驳船装货交接凭证.pdf";
                break;
            case 2:
                printUtil.printHWJJQD(id,byteArrayOutputStream);
                fileName="货物交接清单.pdf";
                break;
            case 3:
                printUtil.printSLHWYD(id,byteArrayOutputStream);
                fileName="水路货物运单.pdf";
                break;
            default:
                break;
        }

        response.setContentLength(byteArrayOutputStream.size());
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Content-Disposition","attachment;filename="+new String(fileName.getBytes(), "ISO-8859-1"));
        response.getOutputStream().write(byteArrayOutputStream.toByteArray());
    }

    @GetMapping("/testBCJJPZZS")
  public void testBCJJPZZS(){

        String customerId="0a1a18f6bbee4ba486afde5fbe53bc1d";
        File file=new File("C:\\Users\\<USER>\\Desktop\\cs.pdf");
        String docNo="test111111111111308";

        // 1.上传并创建合同
        fddCommonService.uploadAndSaveContract(null,customerId,docNo,file);

        String sealName="新港驳船装货专用章";

        String customerIdBCZ="0a1a18f6bbee4ba486afde5fbe53bc1d";
        String sealName2="新港沿海交接专用章";
//        String sealName4=null;

        // 2.免验证签署
        fddCommonService.autoSign(docNo,null,customerId,sealName, IdUtils.fastSimpleUUID(),338.0f,452.0f);
        fddCommonService.autoSign(docNo,null,customerIdBCZ,sealName2,IdUtils.fastSimpleUUID(),750.0f,452.0f);
//        AutoSignVO autoSignVO3=fddCommonService.autoSign(docNo,null,customerId,sealName4,IdUtils.fastSimpleUUID(),1000.0f,452.0f);

        // 3.查看合同
        System.out.println(fddCommonService.viewContract(docNo));
    }
}

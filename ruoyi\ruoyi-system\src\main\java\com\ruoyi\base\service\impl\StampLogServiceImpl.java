package com.ruoyi.base.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.domain.StampLog;
import com.ruoyi.base.service.StampLogService;
import com.ruoyi.base.mapper.StampLogMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.service.impl.Pb6WaterwaycargoServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【STAMP_LOG(盖章日志)】的数据库操作Service实现
* @createDate 2024-12-24 16:51:31
*/
@Service
public class StampLogServiceImpl extends ServiceImpl<StampLogMapper, StampLog>
    implements StampLogService{

    @Autowired
    private Pb6WaterwaycargoServiceImpl pb6WaterwaycargoService;

    /**
     * 新增盖章日志
     *
     * @param fileName 文件名 detailId 托运单下驳船详情id
     * @return 结果
     */
    @Override
    public AjaxResult insertStampLog(String fileName, Long waterwayId,String status,String result,String stampBy) {

        StampLog stampLog = new StampLog();

        // 文件名
        stampLog.setFileName(fileName);
        // 托运单下驳船详情id
        stampLog.setDetailId(waterwayId.toString());
        // 驳船名 大船名 大船航次查询水路运单得出
        Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getById(waterwayId);
        if(pb6Waterwaycargo != null){
            stampLog.setBargeName(pb6Waterwaycargo.getBargeName());
            stampLog.setShipName(pb6Waterwaycargo.getShipName());
            stampLog.setShipNumber(pb6Waterwaycargo.getShipNumber());
            stampLog.setBargeNumber(pb6Waterwaycargo.getBargeNumber());
        }
        stampLog.setStatus(status);
        stampLog.setResult(result);
        stampLog.setStampBy(stampBy);

        if(save(stampLog)){
            return AjaxResult.success();
        }

        return AjaxResult.error();
    }



}





package com.ruoyi.businessFile.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 17:13
 */
@TableName(value ="sys_oss")
@Data
@KeySequence(value = "SEQ_SYS_OSS")
public class SysOss extends BaseEntity {

    /**
     * 文件ID
     */
    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * 原始名称
     */
    private String fileOriginName;

    /**
     * MINIO 上传文件路径
     */
    private String fileMinioPath;

    /**
     * 删除标记
     */
    private String delFlag;

}

package com.ruoyi.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.BargeInfoAudit;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.vo.BargeInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BargeInfoAuditMapper extends BaseMapper<BargeInfoAudit> {
    int addCarrierBarge(BargeInfoAudit bargeInfoAudit);

    Long generateBargeId();
}

package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.databarge.domain.Pb3Coutform;
import com.ruoyi.databarge.mapper.Pb3CoutformMapper;
import com.ruoyi.databarge.service.Pb3CoutformService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 出库单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-13
 */
@Service
public class Pb3CoutformServiceImpl extends ServiceImpl<Pb3CoutformMapper, Pb3Coutform> implements Pb3CoutformService {

    @Override
    public List<Pb3Coutform> findByCoutformId(String coutformId) {
        QueryWrapper<Pb3Coutform> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb3Coutform::getCoutformid, coutformId);
        return super.list(queryWrapper);
    }

    @Override
    public List<String> listCoutFormId(String coutformid) {
        return baseMapper.listCoutFormId(coutformid);
    }

    @Override
    public Pb3Coutform findOneByCoutformId(String coutformId) {
        List<Pb3Coutform> pb3CoutformList = this.findByCoutformId(coutformId);
        if(pb3CoutformList.size() == 1){
            return pb3CoutformList.get(0);
        }
        throw new CustomException("出库单 " + coutformId + " 不正确");
    }
}
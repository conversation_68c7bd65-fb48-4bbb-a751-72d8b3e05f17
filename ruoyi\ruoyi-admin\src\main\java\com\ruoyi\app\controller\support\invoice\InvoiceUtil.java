package com.ruoyi.app.controller.support.invoice;

import cn.hutool.core.date.DateUtil;
import com.alibaba.druid.sql.visitor.functions.If;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.Invoice.domain.bo.InvoiceBO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.common.mapper.WaterwayCargoMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.databarge.domain.ShipInvoiceHistory;
import com.ruoyi.databarge.domain.vo.PortLoadingMsgVO;
import com.ruoyi.databarge.service.Pb6BargeCheckMessageService;
import com.ruoyi.databarge.service.ShipInvoiceHistoryService;
import com.ruoyi.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.ruoyi.app.controller.support.invoice.InvoiceTestController.getRandomOrderNo;

/**
 * <AUTHOR>
 * @Date 2020/12/7 11:38
 * @Description:
 */
@Component
public class InvoiceUtil {
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private ShipInvoiceHistoryService shipInvoiceHistoryService;
    @Autowired
    private WaterwayCargoMapper waterwayCargoMapper;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private Pb6BargeCheckMessageService pb6BargeCheckMessageService;
    //测试环境  BBZ042104231029
//    public static final String IDENTITY="2329CC5F90EDAA8208F1F3C72A0CE72A713A9D425CD50CDE";// 7AF8F3FA35248FE67A946FAC4A0F5277ED04AB36AFB7E56B
//    public static final String SALETAXNUM="339901999999142";//
//    public static final String KP_URL="http://nnfpbox.nuonuocs.cn/shop/buyer/allow/cxfKp/cxfServerKpOrderSync.action?order=%s";// https://nnfp.jss.com.cn/shop/buyer/allow/cxfKp/cxfServerKpOrderSync.action?order=%s
//    public static final String KPCX_BYFPQQLSH_URL="http://nnfpbox.nuonuocs.cn/shop/buyer/allow/ecOd/queryElectricKp.action?order=%s";// https://nnfp.jss.com.cn/shop/buyer/allow/ecOd/queryElectricKp.action
//    public static final String KPCX_BYORDERNO_URL="http://nnfpbox.nuonuocs.cn/shop/buyer/allow/ecOd/queryElectricKp.action?order=%s";// https://nnfp.jss.com.cn/shop/buyer/allow/ecOd/queryElectricKp.action


    //正式环境
    public static final String IDENTITY="7AF8F3FA35248FE67A946FAC4A0F5277ED04AB36AFB7E56B";
    public static final String SALETAXNUM="914401011904317405";
    public static final String KP_URL="https://nnfp.jss.com.cn/shop/buyer/allow/cxfKp/cxfServerKpOrderSync.action?order=%s";
    public static final String KPCX_BYFPQQLSH_URL="https://nnfp.jss.com.cn/shop/buyer/allow/ecOd/queryElectricKp.action?order=%s";
    public static final String KPCX_BYORDERNO_URL="https://nnfp.jss.com.cn/shop/buyer/allow/ecOd/queryElectricKp.action?order=%s";

    public InvoiceKpVO kp(InvoiceDTO invoiceDTO){
        String json=JSON.toJSONString(invoiceDTO);
        String order=DESDZFP.encrypt(json);
        InvoiceKpVO invoiceKpVO=restTemplate.postForObject(String.format(KP_URL,order),null, InvoiceKpVO.class);
        return invoiceKpVO;
    }

    public InvoiceKpcxVO kpcxByFpqqlsh(String fpqqlsh){
        String order=DESDZFP.encrypt("{\n" +
                "\"identity\":\""+IDENTITY+"\",\n" +
                "\"fpqqlsh\":\""+fpqqlsh+"\",\n" +
                "\"isOfferInvoiceDetail\":true\n" +
                "}");
        InvoiceKpcxVO invoiceKpcxVO=restTemplate.postForObject(String.format(KPCX_BYFPQQLSH_URL,order),null,InvoiceKpcxVO.class);
        return invoiceKpcxVO;
    }

    public InvoiceKpcxVO kpcxByOrderno(String orderno){
        String order=DESDZFP.encrypt("{\n" +
                "\"identity\":\""+IDENTITY+"\",\n" +
                "\"orderno\":\""+orderno+"\",\n" +
                "\"isOfferInvoiceDetail\":true\n" +
                "}");
        InvoiceKpcxVO invoiceKpcxVO=restTemplate.postForObject(String.format(KPCX_BYORDERNO_URL,order),null,InvoiceKpcxVO.class);
        return invoiceKpcxVO;
    }

    public AjaxResult invoicekp(InvoiceBO invoiceBO) {
        WaterwayCargo waterwayCargo = waterwayCargoMapper.selectOne(new QueryWrapper<WaterwayCargo>()
                .eq("WATERWAYCARGOID", invoiceBO.getWaterwayCargoId()));

        //判断是否有实装吨数
        PortLoadingMsgVO portLoadingMsgVO=pb6BargeCheckMessageService.searchPortLoadingInfo(invoiceBO.getWaterwayCargoId());
        if (portLoadingMsgVO== null) {
            return AjaxResult.error("没有实装吨数");
        }
        //判断该水路是否开过票
        ShipInvoiceHistory shipInvoiceHistoryBefore=shipInvoiceHistoryService.getOne(Wrappers.<ShipInvoiceHistory>lambdaQuery()
                .eq(ShipInvoiceHistory::getPb6WaterwaycargoId,waterwayCargo.getID())
                .eq(ShipInvoiceHistory::getKptype,1)
                .eq(ShipInvoiceHistory::getState,1));
        if (shipInvoiceHistoryBefore!=null){
            return AjaxResult.error("该水路运单已开过票");
        }

        if (invoiceBO.getInvoicetype().equals("0")){
            //判断是否在停开发票时间内
            SysDictData sysDictData;
            List<SysDictData> dictDataList = dictTypeService.selectDictDataByType("invoice_stop_kpdate");
            if(dictDataList.size() == 0){
                return AjaxResult.error("没有找到发票停止开票日期数据");
            } else if(dictDataList.size() >= 2){
                return AjaxResult.error("找到多条发票停止开票日期数据，请核对！");
            } else {
                 sysDictData = dictDataList.get(0);
            }
            Calendar cal= Calendar.getInstance();
            int d =cal.get(Calendar.DATE);
            invoiceBO.setCreate_time(DateUtils.getTime());//新增创建时间
            if (Integer.parseInt(sysDictData.getDictValue())>=d){
                //发票销售方信息固定不变
                invoiceBO.setSaleaccount("建行黄埔支行44001470901053000321");
                invoiceBO.setSalephone("020-********");
                invoiceBO.setSaleaddress("广州市黄埔区黄埔大道东983号49-50层");
                invoiceBO.setSaletaxnum(InvoiceUtil.SALETAXNUM);
                InvoiceDTO invoiceDTO = new InvoiceDTO();
                invoiceDTO.setIdentity(InvoiceUtil.IDENTITY);
                invoiceBO.setInvoicedate(DateUtils.getTime());
                invoiceBO.setOrderno(getRandomOrderNo());

                //根据填入的电话和邮件 判断发票发送方式
                if (StringUtils.isBlank(invoiceBO.getPhone())){
                    invoiceBO.setPhone(null);
                }
                if (StringUtils.isBlank(invoiceBO.getEmail())){
                    invoiceBO.setEmail(null);
                }
                if (invoiceBO.getPhone()!=null&&invoiceBO.getEmail()!=null){
                    invoiceBO.setTsfs("2");
                }else if (invoiceBO.getPhone()==null&&invoiceBO.getEmail()!=null){
                    invoiceBO.setTsfs("0");
                }else if (invoiceBO.getPhone()!=null&&invoiceBO.getEmail()==null){
                    invoiceBO.setTsfs("1");
                }else{
                    invoiceBO.setTsfs("-1");
                }



                List<InvoiceDTO.Detail> details = new ArrayList<>();
                List<ShipInvoiceHistory.Detail> list =  invoiceBO.getDetail();
                if (StringUtils.isNotNull(list)) {
                    list.forEach(detail -> {
                        InvoiceDTO.Detail detailDTO = new InvoiceDTO.Detail();
                        DecimalFormat df = new DecimalFormat("#.00");//保留两位小数方法

//                        waterwayCargo.setTotalCharge("100");//测试用
                        detail.setTaxamt(waterwayCargo.getTotalCharge());// 含税金额
                        double taxfreeamt=Double.valueOf(waterwayCargo.getTotalCharge())/1.06;//不含税额=含税额/1.06
                        detail.setTaxfreeamt(df.format(taxfreeamt));//不含税额
                        detail.setPrice(df.format(taxfreeamt));//单价和不含税额一样
                        String tax=df.format(Double.valueOf(waterwayCargo.getTotalCharge())-Double.valueOf(df.format(taxfreeamt)));
                        detail.setTax(tax);//税额=含税额-不含税额
                        detail.setGoodsname("代理费");
                        detail.setSpbm("*********");//*物流辅助服务*商品编码
                        detail.setUnit("吨");
                        detail.setFphxz("0");//发 票 行 性 质:0, 正 常 行;
                        detail.setHsbz("0");//单价不含税
                        detail.setTaxrate("0.06");
                        detail.setNum("1");
                        BeanUtils.copyBeanProp(detailDTO, detail);
                        details.add(detailDTO);
                    });
                }

                InvoiceDTO.Order order = new InvoiceDTO.Order();
                BeanUtils.copyBeanProp(order, invoiceBO);
                order.setDetail(details);
                invoiceDTO.setOrder(order);
                InvoiceKpVO invoiceKpVO = kp(invoiceDTO);
                System.out.println(invoiceKpVO);
                if (invoiceKpVO.getStatus().equals("0000")&&invoiceKpVO.getMessage().equals("同步成功")&&invoiceKpVO.getFpqqlsh()!=null){
                    ShipInvoiceHistory shipInvoiceHistoryDK=shipInvoiceHistoryService.getOne(Wrappers.<ShipInvoiceHistory>lambdaQuery()
                            .eq(ShipInvoiceHistory::getPb6WaterwaycargoId,waterwayCargo.getID())
                            .eq(ShipInvoiceHistory::getState,3));

                    if (shipInvoiceHistoryDK==null){
                        ShipInvoiceHistory shipInvoiceHistory=new ShipInvoiceHistory();
                        BeanUtils.copyBeanProp(shipInvoiceHistory, invoiceBO);
//                    shipInvoiceHistory.setShipUserId(loginUser.getUserId());
//                    shipInvoiceHistory.setShipUserName(loginUser.getUserName());
                        if (StringUtils.isNotNull(waterwayCargo)) {
                            shipInvoiceHistory.setPb6WaterwaycargoId(waterwayCargo.getID());
                        }
                        shipInvoiceHistory.setFpqqlsh(invoiceKpVO.getFpqqlsh());
                        shipInvoiceHistory.setState("1");
                        shipInvoiceHistoryService.save(shipInvoiceHistory);
                    }else{
                        Long shipInvoiceHistoryDKID=shipInvoiceHistoryDK.getId();
                        BeanUtils.copyBeanProp(shipInvoiceHistoryDK, invoiceBO);
                        shipInvoiceHistoryDK.setPb6WaterwaycargoId(waterwayCargo.getID());
                        shipInvoiceHistoryDK.setFpqqlsh(invoiceKpVO.getFpqqlsh());
                        shipInvoiceHistoryDK.setState("1");
                        shipInvoiceHistoryDK.setId(shipInvoiceHistoryDKID);
                        shipInvoiceHistoryService.updateById(shipInvoiceHistoryDK);
                    }
                    return  AjaxResult.success("开票成功");
                }else{
                    return  AjaxResult.error(invoiceKpVO.getMessage());
                }

            }else{
                ShipInvoiceHistory shipInvoiceHistory=invoiceBO;
                shipInvoiceHistory.setState("0");
                shipInvoiceHistoryService.save(shipInvoiceHistory);
                return AjaxResult.success("已过发票停止开票日期，不开票");
            }
        }else if (invoiceBO.getInvoicetype().equals("1")){
            ShipInvoiceHistory shipInvoiceHistory=invoiceBO;
            shipInvoiceHistory.setState("1");
            shipInvoiceHistoryService.save(shipInvoiceHistory);
            return AjaxResult.success("专票不开票");
        }else{
            return AjaxResult.error("发票类型有误");
        }
    }
}

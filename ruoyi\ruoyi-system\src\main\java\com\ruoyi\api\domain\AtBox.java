package com.ruoyi.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/11/1 9:01
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AtBox {

    private String orderNum;          // 订单编码
    private String blNo;              // 提单号
    private String boxNum;             // 箱号
    private String boxType;            // 箱型
    private String boxSize;            // 箱尺寸
    private String eof;                // 空重
    private String sealNo;             // 铅封号
    private String goods;              // 货名
    private String weight;             // 货重
    private String port;               // 驳运起运港
    private String nextPort;           // 驳运目的港
    private String boxMainOut;         // 对外箱主
    private String connectShipName;    // 衔接大船船名
    private String connectVoyageNum;   // 衔接大船航次
    private String arrivalDate;        // 海船预计抵港时间
    private String packingType;        // 包装类型
    private String ports;              // 海船航线
    private String pressureYearsFlag;   // 是否压年
    private String pressureStartTime;  // 压年开始
    private String pressureYearsTime;  // 压年截止

}

package com.ruoyi.common.utils.message.request;


import com.ruoyi.common.utils.message.request.mpTem.MpTemplateMsg;
import com.ruoyi.common.utils.message.request.weappTem.WeappTemplateMsg;

/**
 * 下发小程序和公众号统一的服务消息 请求PO
 * https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/uniform-message/uniformMessage.send.html
 * <AUTHOR>
 *
 */
public class ReqPo {

	//接口调用凭证
	private String access_token;
	//用户openid，可以是小程序的openid，也可以是mp_template_msg.appid对应的公众号的openid
	private String touser;
	//小程序模板消息相关的信息，可以参考小程序模板消息接口; 有此节点则优先发送小程序模板消息
	private WeappTemplateMsg weapp_template_msg;
	//公众号模板消息相关的信息，可以参考公众号模板消息接口；有此节点并且没有weapp_template_msg节点时，发送公众号模板消息
	private MpTemplateMsg mp_template_msg;
	
	
	public String getAccess_token() {
		return access_token;
	}
	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}
	public String getTouser() {
		return touser;
	}
	public void setTouser(String touser) {
		this.touser = touser;
	}
	public WeappTemplateMsg getWeapp_template_msg() {
		return weapp_template_msg;
	}
	public void setWeapp_template_msg(WeappTemplateMsg weapp_template_msg) {
		this.weapp_template_msg = weapp_template_msg;
	}
	public MpTemplateMsg getMp_template_msg() {
		return mp_template_msg;
	}
	public void setMp_template_msg(MpTemplateMsg mp_template_msg) {
		this.mp_template_msg = mp_template_msg;
	}

	@Override
	public String toString() {
		return "ReqPo{" +
				"access_token='" + access_token + '\'' +
				", touser='" + touser + '\'' +
				", weapp_template_msg=" + weapp_template_msg +
				", mp_template_msg=" + mp_template_msg +
				'}';
	}
}

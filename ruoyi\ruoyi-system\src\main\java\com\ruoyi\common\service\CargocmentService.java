package com.ruoyi.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.domain.Cargoconsignment;
import com.ruoyi.common.domain.bo.CargocmentBO;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.vo.CargocmentVO;
import com.ruoyi.consignor.domain.vo.ConsignorConsignVO;

import java.util.List;

/**
 * @Description 运单接口
 * <AUTHOR>
 * @Date 2020/8/5 9:53
 */
public interface CargocmentService extends IService<Cargoconsignment> {

    public void waybillsSelectShips(CargocmentBO cargocmentBO);

    public void addWaybills(CargocmentBO cargocmentBO);

    public List<ConsignorConsignVO> billslist(CargocmentdetailBO cargocmentdetailBO);


    public String getNewSerialNo(String businessType,Long comId,String date,int length);
}

/* pages/mainpage/main/main.wxss */
.main-page{

}
.main-search{
  height: 140rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px solid #ddd;
  position: fixed;
  top: 0rpx;
  left: 0rpx;
  right: 0rpx;
  background: #ffffff;
  z-index: 100;
}
.main-tab{
  flex: 1;
  color: #999999;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.main-tab.tab-active {
  color: #00426B;
}
.main-first{
  border-right: 1px solid #ddd;
}
.main-content{
  padding-top: 140rpx;
}
.main-icon{
  font-size: 60rpx;
}

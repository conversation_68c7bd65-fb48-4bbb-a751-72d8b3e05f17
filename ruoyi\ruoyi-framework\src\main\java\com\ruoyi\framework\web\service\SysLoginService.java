package com.ruoyi.framework.web.service;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.FirstLoginException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.security.provider.MyUsernamePasswordAuthenticationToken;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.exception.user.UserPasswordNotMatchException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;

import java.util.Calendar;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 登录验证
     * 
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String logintype,String usertype,String wxCode,String phoneNumber, String username, String password, String code, String uuid,Integer index)
    {
//        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
//        String captcha = redisCache.getCacheObject(verifyKey);
//        redisCache.deleteObject(verifyKey);
        /*if (captcha == null)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }*/
        // 用户验证
        Authentication authentication = null;
        try
        {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            //走自定义用户验证
            authentication = authenticationManager
                    .authenticate(new MyUsernamePasswordAuthenticationToken(logintype,usertype,wxCode,phoneNumber,username, password,index));
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                SysUser sysUser = userService.selectUserByUserName(username);
                Long lockNumber = sysUser.getLockNumber();
                lockNumber++;
                sysUser.setLockNumber(lockNumber);
                if(lockNumber % 5 == 0){
                    int i = Integer.parseInt(String.valueOf(lockNumber/5));
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.MINUTE,10*i);
                    sysUser.setLockTime(calendar.getTime());
                }

                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                userMapper.updateById(sysUser);
                throw new CustomException("密码错误超过五次账户将被锁定，您还有"+(5-(lockNumber%5))+"次机会");
            }
            else
            {
                if(e instanceof FirstLoginException){
                    throw new FirstLoginException(e.getMessage(),((FirstLoginException) e).getCode());
                }else{
                    AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                    throw new CustomException(e.getMessage());
                }

            }
        }
        SysUser sysUser = userService.selectUserByUserName(username);
        if (ObjectUtil.isNotNull(sysUser) && StringUtils.isNotNull(sysUser)){
            sysUser.setLockNumber(0L);
            sysUser.setLockTime(null);
            userMapper.updateById(sysUser);
        }

        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        if (ObjectUtil.isNotNull(index) && index == 1 && !"gzonlineuser".equals(usertype)){
            return loginUser.getUser().getPhonenumber();
        }
        // 生成token
        return tokenService.createToken(loginUser);
    }
}

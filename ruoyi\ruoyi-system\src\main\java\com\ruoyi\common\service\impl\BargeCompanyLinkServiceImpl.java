package com.ruoyi.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.BargeCheckMessage;
import com.ruoyi.common.domain.BargeCompanyLink;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.Customer;
import com.ruoyi.common.enums.BargeCheckMessageType;
import com.ruoyi.common.enums.CheckFlagEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.BargeCheckMessageMapper;
import com.ruoyi.common.mapper.BargeCompanyLinkMapper;
import com.ruoyi.common.mapper.BargeInfoMapper;
import com.ruoyi.common.mapper.CustomerMapper;
import com.ruoyi.common.service.BargeCompanyLinkService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Description 驳船-公司对应实现类
 * <AUTHOR>
 * @Date 2020/8/29 17:54
 */
@Service
public class BargeCompanyLinkServiceImpl extends ServiceImpl<BargeCompanyLinkMapper, BargeCompanyLink> implements BargeCompanyLinkService {

    @Autowired
    private BargeCheckMessageMapper bargeCheckMessageMapper;
    @Autowired
    private BargeInfoMapper bargeInfoMapper;
    @Autowired
    private CustomerMapper customerMapper;

    /**
     * 驳船绑定操作
     * @param bargeCompanyLink
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public int bingdingBarge(BargeCompanyLink bargeCompanyLink) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String time = DateUtils.getTime();
        Integer isAudit = bargeCompanyLink.getIsAudit();
        if(isAudit.intValue()== CheckFlagEnum.BINDING_AUDIT.getCode()){//绑定审批
            bargeCompanyLink.setStatus(0);
            bargeCompanyLink.setCreateTime(time);
            bargeCompanyLink.setCreateById(user.getUserId());
            bargeCompanyLink.setCreateByName(user.getNickName());
            bargeCompanyLink.setAuditStatus(0);
            this.saveBargeCompany(bargeCompanyLink);
            int result = baseMapper.insert(bargeCompanyLink);
            if (bargeCompanyLink.isCheckFlag()) {
                // 查询驳船
                BargeInfo bargeInfo = bargeInfoMapper.selectById(bargeCompanyLink.getBargeId());
                // 插入消息
                this.bargeCheckMsg(BargeCheckMessageType.ADD_BARGE_ADDITIONAL.getCode().shortValue(), SecurityUtils.getLoginUser().getUser(), StringUtils.isNull(bargeInfo) ? bargeCompanyLink.getBargeName() : bargeInfo.getBargeName(), bargeCompanyLink.getId(), (short) 0);
            }
            return result;
        }else if(isAudit.intValue() == CheckFlagEnum.UPDATE_BINDING_AUDIT.getCode()){//修改挂靠
            if(bargeCompanyLink.getId()==null){
                log.error("数据有误，id不存在");
                throw new CustomException("数据有误，id不存在");
            }
            bargeCompanyLink.setUpdatebyId(user.getUserId());
            bargeCompanyLink.setUpdatebyName(user.getNickName());
            bargeCompanyLink.setUpdateTime(time);
            bargeCompanyLink.setAuditStatus(0);
            this.saveBargeCompany(bargeCompanyLink);
            int result = baseMapper.updateById(bargeCompanyLink);
            if (bargeCompanyLink.isCheckFlag()) {
                // 查询驳船
                BargeInfo bargeInfo = bargeInfoMapper.selectById(bargeCompanyLink.getBargeId());
                // 插入消息
                this.bargeCheckMsg(BargeCheckMessageType.BARGE_ATTACHMENT_MODIFICATION_REVIEW.getCode().shortValue(), SecurityUtils.getLoginUser().getUser(), bargeInfo.getBargeName(),bargeCompanyLink.getId(), (short) 0);
            }
            return result;
        }else if(isAudit.intValue() == CheckFlagEnum.CANCEL_BINDING_AUDIT.getCode()){//取消挂靠
            if(bargeCompanyLink.getId()==null){
                log.error("数据有误，id不存在");
                throw new CustomException("数据有误，id不存在");
            }
            bargeCompanyLink.setUpdatebyId(user.getUserId());
            bargeCompanyLink.setUpdatebyName(user.getNickName());
            bargeCompanyLink.setUpdateTime(time);
            //bargeCompanyLink.setAuditStatus(0);
            bargeCompanyLink.setStatus(0);
            bargeCompanyLink.setIsDelete(0);
            int result = baseMapper.updateById(bargeCompanyLink);
            if (bargeCompanyLink.isCheckFlag()) {
                // 查询驳船
                BargeInfo bargeInfo = bargeInfoMapper.selectById(bargeCompanyLink.getBargeId());
                // 插入消息
                this.bargeCheckMsg(BargeCheckMessageType.THE_BARGE_WAS_CANCELLED.getCode().shortValue(), SecurityUtils.getLoginUser().getUser(), bargeInfo.getBargeName(),bargeCompanyLink.getId(), (short) 1);
            }
            return result;
        }else{
            return 0;
        }
    }


    /**
     * 插入表PB6_BARGE_CHECK_MESSAGE
     * @param mType BargeCheckMessageType 0、驳船备案审核；1、驳船备案加挂靠；
     *              2、驳船信息修改审核；3、驳船添加挂靠； 4、驳船挂靠修改审核； 5、驳船取消挂靠；
     * @param loginUser 登录用户
     * @param bargeName 驳船名称
     * @param bargeCompanyId 驳船挂靠公司id
     */
    private void bargeCheckMsg(short mType, SysUser loginUser, String bargeName, Long bargeCompanyId, short auditFlag) {
        BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();
        bargeCheckMessage.setMType(mType);
        bargeCheckMessage.setApplyManId(loginUser.getUserId());
        bargeCheckMessage.setApplyMan(loginUser.getNickName());
        bargeCheckMessage.setApplyTime(new Date());
        bargeCheckMessage.setAuditFlag(auditFlag);
        //bargeCheckMessage.setPb6BargeInfoAuditId(bargeInfoAudit.getId());
        bargeCheckMessage.setPb6BargeCompanyId(bargeCompanyId);
        bargeCheckMessage.setBargeName(bargeName);
        bargeCheckMessageMapper.insert(bargeCheckMessage);
    }

    /**
     * 保存信息
     * @param bargeCompany
     */
    private void saveBargeCompany(BargeCompanyLink bargeCompany) {
        Long companyId = bargeCompany.getCompanyId();
        if (StringUtils.isNotNull(companyId)) {
            Customer customer = customerMapper.selectById(companyId);
            bargeCompany.setCompanyName(customer.getCFullName());
        }
    }
}

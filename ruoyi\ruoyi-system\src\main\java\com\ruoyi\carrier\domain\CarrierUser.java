package com.ruoyi.carrier.domain;

import com.ruoyi.common.domain.OLPicture;
import lombok.*;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/7/24 15:29
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CarrierUser {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 业务系统用户id
     */
    private Long gmUserId;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 账号名称
     */
    private String account;

    /**
     * 是否审核通过标识
     */
    private String passport;

    private String comId;



    /**
     * 注册资料
     */
    private List<OLPicture> pictureList;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 身份证id
     */
    private String cId;

}

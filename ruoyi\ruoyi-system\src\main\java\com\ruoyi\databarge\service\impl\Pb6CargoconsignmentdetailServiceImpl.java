package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.databarge.domain.dto.BargeCheckInSearchDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeCheckInVO;
import com.ruoyi.databarge.domain.vo.Pb6CargoconsignmentVO;
import com.ruoyi.databarge.mapper.Pb6CargoconsignmentdetailMapper;
import com.ruoyi.databarge.service.Pb6CargoconsignmentdetailService;
import com.ruoyi.databarge.shipxy.ShipxyUtil;
import com.ruoyi.databarge.shipxy.SingleBargeInfoSearchResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;

/**
 * <p>
 * 驳船托运单明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Service
public class Pb6CargoconsignmentdetailServiceImpl extends ServiceImpl<Pb6CargoconsignmentdetailMapper, Pb6Cargoconsignmentdetail> implements Pb6CargoconsignmentdetailService {

    @Autowired
    private ShipxyUtil shipxyUtil;

    @Override
    public List<Pb6CargoconsignmentVO> searchPb6Cargoconsignment(Pb6CargoconsignmentSearchDTO pb6CargoconsignmentSearchDTO) {
        if(StringUtils.isNotBlank(pb6CargoconsignmentSearchDTO.getEndDate())){
            pb6CargoconsignmentSearchDTO.setEndDate(pb6CargoconsignmentSearchDTO.getEndDate() + " 23:59:59");
        }
        // 前端传过来的startDate有时候是2024-9-8，有时候是2024-09-08,所以这里做处理，如果月份是一位数，就在前面加0
        if(StringUtils.isNotBlank(pb6CargoconsignmentSearchDTO.getStartDate())){
            String[] split = pb6CargoconsignmentSearchDTO.getStartDate().split("-");
            if(split.length == 3 && split[1].length() == 1){
                pb6CargoconsignmentSearchDTO.setStartDate(split[0] + "-0" + split[1] + "-" + split[2]);
            }
        }

        return super.baseMapper.searchPb6Cargoconsignment(pb6CargoconsignmentSearchDTO);
    }

    @Override
    public Pb6CargoconsignmentDTO searchApplyDateByConsignFlag(String consignflag) {
        List<Pb6CargoconsignmentDTO> applyDateList = super.baseMapper.searchApplyDateByConsignFlag(consignflag);
        if(applyDateList.size() != 1){
            throw new CustomException("通过托运单号查找托运单主表信息错误，请核对！");
        }
        return applyDateList.get(0);
    }

    @Override
    public void searchPortByEndPortName(String endPortName) {
        List<String> strings = super.baseMapper.searchPortByEndPortName(endPortName);
        if(strings == null || strings.size() == 0){
            throw new CustomException("目的港口不存在，请到基础数据里添加！");
        }
    }

    @Override
    public List<Pb6Cargoconsignmentdetail> searchBargeByWxappointmenttime(){
        Date now = new Date();
        SimpleDateFormat dateFormat= new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        String wxappointmenttime= dateFormat.format(now);
        SimpleDateFormat dateFormat2= new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        String wxappointmenttime1= dateFormat2.format(now);
        List<Pb6Cargoconsignmentdetail> bargeList=super.baseMapper.searchBargeByWxappointmenttime(wxappointmenttime,wxappointmenttime1);
        return  bargeList;
    }

//    @Override
//    public List<BargeCheckInVO> searchBargeCheckINVO(BargeCheckInSearchDTO bargeCheckInSearchDTO) {
//        return super.baseMapper.searchBargeCheckINVO(bargeCheckInSearchDTO);
//    }

    @Override
    public IPage<BargeCheckInVO> searchBargeCheckINVO(BargeCheckInSearchDTO bargeCheckInSearchDTO) {
        Long pageNum= Optional.ofNullable(bargeCheckInSearchDTO.getPageNum()).orElse(0L);
        Long pageSize= Optional.ofNullable(bargeCheckInSearchDTO.getPageSize()).orElse(-1L);
        return super.baseMapper.searchBargeCheckINVO(new Page<>(pageNum, pageSize), bargeCheckInSearchDTO);
    }

    @Override
    public boolean updateInfoByID(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) {
        return super.updateById(pb6Cargoconsignmentdetail);
    }

    /**
     * 异步操作任务调度线程池
     */
    private ScheduledExecutorService executor = SpringUtils.getBean("scheduledExecutorService");

    public Future<SingleBargeInfoSearchResult> queryShip(String bargeName) {
        return executor.submit(() -> shipxyUtil.queryShip(bargeName));
    }
    /**
     * 查询新沙当前预约报到的船的信息
     * @return mmsi集合
     */
    @Override
    public List<String> searchBargeOrder(Long comid) throws ExecutionException, InterruptedException {
        List<Pb6Cargoconsignmentdetail> list = baseMapper.searchBargeOrder(comid);
        List<Long> longs = new ArrayList<>();

        List<Future<SingleBargeInfoSearchResult>> futureList = new ArrayList<>();
        for(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail: list){
            Future<SingleBargeInfoSearchResult> future = queryShip(pb6Cargoconsignmentdetail.getBargename());
            futureList.add(future);
//            SingleBargeInfoSearchResult singleBargeInfoSearchResult = shipxyUtil.queryShip(pb6Cargoconsignmentdetail.getBargename());
//            if(singleBargeInfoSearchResult != null && singleBargeInfoSearchResult.getData().size() > 0){
//                longs.add(singleBargeInfoSearchResult.getData().get(0).getMmsi());
//            }
        }
        List<Future<SingleBargeInfoSearchResult>> futureListDelete = new ArrayList<>();
        while (futureListDelete.size() != futureList.size()){
            for(Future<SingleBargeInfoSearchResult> future: futureList){
                if(!futureListDelete.contains(future) && future.isDone()){
                    SingleBargeInfoSearchResult singleBargeInfoSearchResult = future.get();
                    if(singleBargeInfoSearchResult != null && singleBargeInfoSearchResult.getData().size() > 0){
                        longs.add(singleBargeInfoSearchResult.getData().get(0).getMmsi());
                    }
                    futureListDelete.add(future);
                }
            }
            Thread.sleep(300);
        }
        List<String> result = new ArrayList<>();

        //船讯网一次只能查100条数据，这里一次只给90条查
        int length = (longs.size() / 90) + 1;
        for(int i = 0; i < length; i++){
            String midResult = "";

            //最后的数据
            if(i == length - 1){
                midResult = StringUtils.join(longs.subList(i * 90, longs.size()), ",");
            } else {
                midResult = StringUtils.join(longs.subList(i * 90, (i + 1) * 90), ",");
            }
            result.add(midResult);
        }
        return result;
    }

    @Override
    public IPage<BargeCheckInVO> xgXjSearch(BargeCheckInSearchDTO bargeCheckInSearchDTO) {
        Long pageNum= Optional.ofNullable(bargeCheckInSearchDTO.getPageNum()).orElse(0L);
        Long pageSize= Optional.ofNullable(bargeCheckInSearchDTO.getPageSize()).orElse(-1L);
        return super.baseMapper.xgXjSearch(new Page<>(pageNum, pageSize), bargeCheckInSearchDTO);
    }

    @Override
    public List<Pb6Cargoconsignmentdetail> searchByConsignId(Long consignid) {
        return baseMapper.searchByConsignId(consignid);
    }

    @Override
    public List<Pb6Cargoconsignmentdetail> searchRepeatByBargeInfo(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) {
        return baseMapper.searchRepeatByBargeInfo(pb6Cargoconsignmentdetail);
    }

    @Override
    public List<Pb6Cargoconsignmentdetail> searchRepeatBarge(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) {
        return baseMapper.searchRepeatBarge(pb6Cargoconsignmentdetail);
    }

    @Override
    public List<Pb6Cargoconsignmentdetail> searchRepeatBargeExceptNow(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) {
        return baseMapper.searchRepeatBargeExceptNow(pb6Cargoconsignmentdetail);
    }

    @Override
    public List<Pb6Cargoconsignmentdetail> searchByShipInfoId(Long shipInfoId) {
        return baseMapper.searchByShipInfoId(shipInfoId);
    }
}

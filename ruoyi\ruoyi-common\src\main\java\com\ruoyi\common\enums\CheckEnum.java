package com.ruoyi.common.enums;

/**
 * 审核状态枚举
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/6 15:04
 */
public enum CheckEnum {

    /**
     * 0-待审核，1-审核通过，2-审核不通过
     */
    WAIT_CHECK(0,"待审核"),
    PASS_CHECK(1, "审核通过"),
    FAIL_CHECK(2, "审核不通过");

    private final Integer code;

    private final String codeName;

    CheckEnum(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }

    public static String getCodeName(int code){
        CheckEnum[] checkEnums = values();
        for(CheckEnum checkEnum: checkEnums){
            if(checkEnum.getCode() == code){
                return checkEnum.getCodeName();
            }
        }
        return null;
    }
}

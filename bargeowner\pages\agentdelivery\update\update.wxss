/* pages/consignment/consignmodify/consignmodify.wxss */
.consign-message{
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 72rpx;
  border-bottom: 1px solid #ddd;
  padding: 0 30rpx;
  width: 100%;
}
.consign-top{
  height: auto;
  width: 100%;
  margin-bottom: 20rpx;
  background: #fff;
}
.consign-detail{
  padding-top: 20rpx;
  background:#f8f8f8;
}
.message-title{
  color: #999;
  font-size: 28rpx;
  width: 270rpx;
}
.message-value{
  color: #333;
  font-size: 28rpx;
  flex:1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.message-value1{
  color: #999;
}

.message-value .btn {
  background: #00426B;
}
.consign-middle{
  height: 20rpx;
  background: #f5f5f5;
  width: 100%;
}
.consign-bottom-title{
  height: 76rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  width: 100%;
  border-bottom: 1px solid #ddd;
  background:#fff;
}
.annex-text{
  color: #333;
  font-size: 32rpx;
}
.consign-bottom-title .download-btn{
  height: 60rpx;
  border:1px solid #00426B;
  border-radius: 33rpx;
  line-height: 56rpx;
  color: #00426B;
  background: #ffffff;
  width: 220rpx;
  padding: 0rpx;
  margin: 0rpx;
  font-size: 28rpx;
  font-weight: normal;
}
.order-item{
  border-bottom: 1px solid #ddd;
  padding: 0 20rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.order-item-title{
  font-size: 28rpx;
  color: #999;
  line-height: 60rpx;
  width: auto;
}
.order-item-content{
  font-size: 28rpx;
  color: #0099ff;
  padding-left: 40rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.order-item-url{
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.arrow-icon{
  flex-shrink: 0;
  width: auto;
}
.detail-oper{
  height: 0rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.detail-oper .oper-btn{
  position: fixed;
  left: 50rpx;
  right: 50rpx;
  bottom: 50rpx;
  width: 80%;
  height: 90rpx;
  padding: 0rpx;
  border-radius: 10rpx;
  border: none;
  line-height: 90rpx;
  color: #fff;
  background: rgb(0, 66, 107);
}
.consign-title{
  width:100%;
  height: 60rpx;
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  padding: 0 30rpx;
  border-bottom: 1px solid #ddd;
}
.order-list{
  background-color: #fff;
  margin-bottom: 20rpx;
}
.add-message-ipt{
  flex: 1;
  width: 100%;
  height: 100%;
  color: #333;
  padding: 0 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
}
.star{
  width: 8rpx;
  flex-shrink: 0;
  margin:0 5rpx;
  display: inline-block;
  color:red;
}
.search-date-selected{
  height: 100%;
  width: 100%;
  color: #333;
}
.search-date-text{
  height: 100%;
  width: 100%;
  color: #808080;
}
.radio-list{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 40rpx;
  height: 70rpx;
}
.radio-item{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-right: 40rpx;
  font-size: 28rpx;
}
.deliver-item{
  width:100%;
  height: 60rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 32rpx;
  color: #00639d;
  padding: 0 30rpx;
  border-bottom: 1px solid #ddd;
  background-color: #fff;
}
.mask1{
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  background:rgba(103, 103, 103,.5);
}
.search-dialog{
  position: fixed;
  left: 0;
  top: 150rpx;
  right: 0;
  bottom: 0;
  z-index: 5000;
  width: 100%;
  height: auto;
  background:#FFF;
}
.add-message-emailipt{
  width: 100%;
  height:55rpx;
  color: #333;
  padding: 0 20rpx;
  font-size: 30rpx;
  border: 1rpx solid #ddd;
  text-align: left ;
}
.image {
  padding-left: 50rpx;
}
.orderNum-dialog .weui-half-screen-dialog__ft{
  padding: 0rpx;
}
.orderNum-dialog .weui-half-screen-dialog__bd{
  padding-bottom: 0rpx;
}
.search-wrap{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.search-wrap .add-btn{
  height: 60rpx;
  width: 120rpx;
  margin-left: 20rpx;
  line-height: 60rpx;
  padding: 0rpx;
  background: #00426B;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: normal;
}
.dialog-searchkey{
  height: 70rpx;
  width: 100%;
  border-radius: 10rpx;
  border: 1px solid #ddd;
  padding: 0 10px;
}
.dialog-content{
  height: 400rpx;
  overflow-y: auto;
  padding-top: 20rpx;
}
.orderNum-list{
  width: 100%;
}
.orderNum-item{
  width: 100%;
  display: flex;
  flex-direction: row;
  min-height: 70rpx;
  align-items: center;
  border-bottom: 1px solid #ddd;
}
.orderNum-formid{
  margin-left: 20rpx;
}
.dialog-wrap{
  height: 100rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.dialog-wrap .dialog-btn{
  height: 80rpx;
  width: 80%;
  line-height: 80rpx;
  padding: 0rpx;
  background: #00426B;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: normal;
  border: none;
}
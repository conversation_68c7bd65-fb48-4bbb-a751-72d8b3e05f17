package com.ruoyi.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.OlUser;
import com.ruoyi.common.mapper.OlUserMapper;
import com.ruoyi.common.service.OlUserService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description 业务系统用户
 * <AUTHOR>
 * @Date 2020/8/26 15:27
 */
@Service
public class OlUserServiceImpl extends ServiceImpl<OlUserMapper, OlUser> implements OlUserService {
    @Autowired
    ISysUserService iSysUserService;
    @Override
    public int updateOlUser(OlUser olUser) {
        SysUser sysUser = iSysUserService.selectUserByGmUserId(olUser.getId());
        SysUser temp = new SysUser();
        temp.setUserId(sysUser.getUserId());
        temp.setNickName(olUser.getContactPerson());
        iSysUserService.updateUserProfile(temp);
        return baseMapper.updateOlUser(olUser);
    }
}

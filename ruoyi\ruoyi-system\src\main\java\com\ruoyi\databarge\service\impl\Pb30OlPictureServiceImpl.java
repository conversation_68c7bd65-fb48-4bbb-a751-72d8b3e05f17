package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.databarge.domain.Pb30OlPicture;
import com.ruoyi.databarge.mapper.Pb30OlPictureMapper;
import com.ruoyi.databarge.service.Pb30OlPictureService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/11/16.
 * @Date: 2020/11/16 8:43
 */
@Service
public class Pb30OlPictureServiceImpl extends ServiceImpl<Pb30OlPictureMapper, Pb30OlPicture> implements Pb30OlPictureService {

    @Override
    public List<Pb30OlPicture> searchByUserId(String userId) {
        QueryWrapper<Pb30OlPicture> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb30OlPicture::getUserId, userId);
        return super.list(queryWrapper);
    }
}

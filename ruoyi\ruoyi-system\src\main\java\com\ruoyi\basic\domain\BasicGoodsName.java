package com.ruoyi.basic.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/1/11 16:58
 */
@KeySequence(value = "SEQ_BASIC_GOODS_NAME")
@TableName(value ="BASIC_GOODS_NAME")
@Data
public class BasicGoodsName extends BaseEntity implements Serializable {
    /**
     * 货名ID
     */
    @TableId(value = "GOODS_NAME_ID",type = IdType.INPUT)
    private Long goodsNameId;

    /**
     * 货类ID
     */
    private Long goodsCategoryId;

    /**
     * 货名
     */
    private String goodsName;

    @TableLogic
    private String delFlag;

    /**
     * 是否密度限制
     */
    private String limitFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

package com.ruoyi.databarge.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2020/11/17 9:55
 * @Description:
 */
@Getter
@Setter
public class Pb30OlEpayorderDetailVO {

    @TableField("ORDERID")
    private String orderid;// 订单号

    @TableField("CONSIGNFLAG")
    private String consignflag;// 托运单和

    @TableField("OUTORINFORMID")
    private String outorinformid;// 出入库单号

    @TableField("WATERWAYCARGOID")
    private String waterwaycargoid;// 水路运单号

    @TableField("CUSTOMERNAME")
    private String customername;// 驳船名称

    @TableField("PAYTIME")
    private String paytime;// 付款日期

    @TableField("PAYMAN")
    private String payman;// 付款人

    @TableField("PAYTYPE")
    private String paytype;// 结算方式

    @TableField("COUNT")
    private String count;// 总金额

    @TableField("STATUS")
    private String status;// 订单状态

    @TableField("RESOURSE")
    private String resourse;// 支付方式

    @TableField("WEIGHTVALUE")
    private String weightvalue;// 计费重量

    @TableField("TRANSPORTCHARGE")
    private String transportcharge;// 运费

    @TableField("CARGOPORTCHARGE")
    private String cargoportcharge;// 货港费

    @TableField("SERVICEAGENTCHARGE")
    private String serviceagentcharge;// 围栏油费

    @TableField("BUSINESSAGENTCHARGE")
    private String businessagentcharge;// 代理费

    @TableField("BERTHCHARGE")
    private String berthcharge;// 停泊费

    @TableField("REMARK")
    private String remark;// 备注

    @TableField("ACTUALMOUNT")
    private String actualmount;// 实际应付金额

    @TableField("ALREADYMOUNT")
    private String alreadymount;// 已付金额

    @TableField("CONTACTPHONE")
    private String contactphone;// 付款人联系方式

    private String flagbargestate; //驳船状态

    private String endworktime; //完工时间

    private Integer state; // 发票状态

    private String fphm; // 发票号码

}

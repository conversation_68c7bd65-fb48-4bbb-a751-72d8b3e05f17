package com.ruoyi.businessFile.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.businessFile.domain.SysOss;
import com.ruoyi.businessFile.mapper.SysOssMapper;
import com.ruoyi.businessFile.service.SysOssService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletContext;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 17:16
 */
@Service
public class SysOssServiceImpl extends ServiceImpl<SysOssMapper, SysOss> implements SysOssService {

    @Override
    public List<SysOss> selectSysOssList(SysOss sysOss) {
        QueryWrapper<SysOss> queryWrapper = new QueryWrapper<>();

        queryWrapper.lambda()
                .like(StringUtils.isNotEmpty(sysOss.getFileOriginName()), SysOss::getFileOriginName, sysOss.getFileOriginName())
                .orderByDesc(SysOss::getId);

        return this.list(queryWrapper);

    }

    @Override
    public AjaxResult uploadFile(MultipartFile file)  {
        String fileName = null;
        try {
            fileName = saveToLocal(file);
        } catch (IOException e) {
            e.printStackTrace();
        }
        String url = "url" + fileName;
        AjaxResult ajax = AjaxResult.success();
        ajax.put("url", url);
        ajax.put("fileName", fileName);
        ajax.put("newFileName", FileUtils.getName(fileName));
        ajax.put("originalFilename", file.getOriginalFilename());

        String originName = file.getOriginalFilename();

        SysOss oss = new SysOss();

        oss.setFileOriginName(originName);

        oss.setFileMinioPath(fileName);

        this.save(oss);
        ajax.put("id", oss.getId());
        return ajax;
    }


    /**
     * 将文件保存到本地
     *
     * @param file 上传的文件
     * @return 本地文件路径
     * @throws IOException 如果保存过程中发生I/O异常
     */
    private String saveToLocal(MultipartFile file) throws IOException {

        // 开发环境下的相对路径根目录
        // String relativeRootDir = "D:/gzcw/businessFiles";

        // 相对路径根目录
        String relativeRootDir = "/gzcw/businessFiles";

        // 获取当前日期，用于构建年月日的文件夹结构
        LocalDate currentDate = LocalDate.now();

        // 构建业务文件夹相对路径，格式：/gzcw/businessFiles/yyyy/MM/dd
        String relativeBusinessFolderPath = relativeRootDir + File.separator + currentDate.getYear() +
                File.separator + currentDate.getMonthValue() +
                File.separator + currentDate.getDayOfMonth();

        // 获取文件名
        String originalFilename = file.getOriginalFilename();

        // 在文件名后追加时间戳
        String newFileName = appendTimestampToFileName(originalFilename);

        // 构建相对路径
        String relativeFilePath = relativeBusinessFolderPath + File.separator + newFileName;

        // 创建业务文件夹
        File businessFolder = new File(relativeBusinessFolderPath);
        businessFolder.mkdirs();

        // 将上传文件保存到相对路径
        file.transferTo(Paths.get(relativeFilePath));

        // 返回相对路径，用于构建访问URL
        return relativeFilePath;

    }

    /**
     * 在文件名后追加时间戳
     *
     * @param originalFilename 原始文件名
     * @return 带时间戳的新文件名
     */
    private String appendTimestampToFileName(String originalFilename) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        int lastDotIndex = originalFilename.lastIndexOf(".");
        if (lastDotIndex != -1) {
            // 如果文件名包含扩展名，则在扩展名前插入时间戳
            return originalFilename.substring(0, lastDotIndex) + "_" + timestamp + originalFilename.substring(lastDotIndex);
        } else {
            // 如果文件名没有扩展名，则直接在文件名后追加时间戳
            return originalFilename + "_" + timestamp;
        }
    }

}

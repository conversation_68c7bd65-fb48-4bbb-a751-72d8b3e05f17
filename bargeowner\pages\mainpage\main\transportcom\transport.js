// pages/mainpage/main/transportcom/transport.js
const app = getApp();

Component({
  options: {
    addGlobalClass: true, // 使全局css生效
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    //用户类型userType：00系统用户， 01-注册用户，10-货主，11-托运单联系人，12-船公司管理员，13-船公司业务员，14-驳船管理员，15-驳船业务员
    userInfos:"",//用户信息
    //运力列表
    listData:[],
    // 查询条件参数
    form: {
      searchValue:"",//搜索关键字
      beginTime: '',//开始时间
      endTime: '',//结束时间
      maxWeight:"",//最大载重量
      minWeight:"",//最小载重量
    },
    //是否显示删除确认弹窗
    deleteDialog:false,
    //删除弹窗按钮
    deleteButtons:[{text:"确定"},{text:"取消"}],
    //当前行数据
    clickCurrentObj:{},
  },
  
  lifetimes: {
    attached: function() {
      // 在组件实例进入页面节点树时执行
      let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息
      this.setData({
        userInfos,
      })
      this.getCapacityData()
    },
    detached: function() {
      // 在组件实例被从页面节点树移除时执行
    },
  },

  pageLifetimes: {
    show: function() {
      // 页面被展示
      let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息
      this.setData({
        userInfos,
      })
      this.getCapacityData()
    },
    hide: function() {
      // 页面被隐藏
    },
    resize: function(size) {
      // 页面尺寸变化
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
     /**
     * 判断是否存在，不存在返回空字符串或其他
     * @param {传进来的变量} empty 
     * @param {为空时返回的值} replace 
     */
    emptyReturn(empty,replace){
      if(!replace || replace==null){
        replace = ""
      }
      if(empty == null || !empty){
        return replace
      }else{
        return empty
      }
    },

    //清空截止时间
    deleteEndTime(){
      this.setData({
        "form.endTime":""
      })
    },


    //清空开始时间
    deleteBeginTime(){
      this.setData({
        "form.beginTime":""
      })
    },

    //清空最大载重量
    deleteMaxWeight(){
      this.setData({
        "form.maxWeight":""
      })
    },

    
    //清空最小载重量
    deleteMinWeight(){
      this.setData({
        "form.minWeight":""
      })
    },

    //清空驳船名称关键字
    deleteSearchValue(){
      this.setData({
        "form.searchValue":""
      })
    },

    //删除弹窗出现
    deleteHandle(e){
      this.setData({
        clickCurrentObj:e.currentTarget.dataset.item,
        deleteDialog:true
      })
    },

    //删除弹窗按钮
    tapDeleteButton(e){
      //确定按钮
      if(e.detail.index === 0){
        app.$post(app.$url.main.deleteCapacity,{id:this.data.clickCurrentObj.id}).then(res=>{
          if(res.code == 200){
            this.getCapacityData()
            app.$message(res.msg)
          }else{
            app.$message(res.msg)
          }
        })
        
      }else if(e.detail.index === 1){
      //取消按钮
      }

    
      this.setData({
        deleteDialog:false
      })
    },

    //修改
    modifyHandle(e){
      let transportObj = JSON.stringify(e.currentTarget.dataset.item) //当前运力对象
      wx.navigateTo({
        url:"/pages/mainpage/transportmodify/transportmodify?transportObj="+transportObj
      })
    },

 
    //关键字input
    searchValueInput(e){
      this.setData({
        "form.searchValue":e.detail.value
      })
    },

    //最小载重量input
    minWeightInput(e){
      this.setData({
        "form.minWeight":e.detail.value
      })
    },
    
    //最大载重量input
    maxWeightInput(e){
      this.setData({
        "form.maxWeight":e.detail.value
      })
    },

    //获取运力信息列表
    getCapacityData() {
      let that = this 
      let param = {
        ...that.data.form
      }
      let minWeight = param.minWeight //最小载重量
      let maxWeight = param.maxWeight //最大载重量
      if(minWeight && maxWeight && minWeight > maxWeight){
        app.$message("最小载重量需小于或等于最大载重量",5000)
        that.setData({
          "form.minWeight":""
        })
        return
        
      }
      app.$post(app.$url.main.getCapacityList,param).then(res=>{
        if(res.code == 200){
          res.data.map(item=>{
            item.bargeName = that.emptyReturn(item.bargeName)
            item.capacity = that.emptyReturn(item.capacity)
            item.phone = that.emptyReturn(item.phone)
            item.startTime = that.emptyReturn(item.startTime)
            item.endTime = that.emptyReturn(item.endTime)
            
            item.bargeIdentifier = that.emptyReturn(item.bargeIdentifier)
            item.remark = that.emptyReturn(item.remark)
          })
          that.setData({
            listData:res.data
          })
        }else {
        app.$message(res.msg)
        }
      })
    },

    //搜索
    handleSearch(){
      this.getCapacityData()
    },

    // 跳转到详情页面
    handleTransportDetail(e) {
      let transportObj = JSON.stringify(e.currentTarget.dataset.item) //当前运力对象
      wx.navigateTo({
        url: '/pages/mainpage/transportdetail/transportdetail?transportObj='+transportObj,
        success: (result)=>{
          
        },
        fail: ()=>{},
        complete: ()=>{}
      });
    },

    // 跳转到运力发布界面
    handleTransportPublish() {
      wx.navigateTo({
        url: '/pages/mainpage/transportpublish/transportpublish',
        success: (result)=>{
          
        },
        fail: ()=>{},
        complete: ()=>{}
      });
    },

    // 开始时间回调
    handleBeginDateChange(e) {
      //时间撮比较日期大小
      let beginTime = new Date(e.detail.value).getTime() //开始时间
      let endTime =  new Date(this.data.form.endTime).getTime()//结束时间
      if(beginTime && endTime && beginTime > endTime){
        app.$message("开始时间需小于或等于截止时间")
        this.setData({
          'form.beginTime':""
        })
        return
      }
      this.setData({
        'form.beginTime': e.detail.value
      })
    },

    // 结束时间回调
    handleEndDateChange(e) {
      //时间撮比较日期大小
      let endTime = new Date(e.detail.value).getTime()//结束时间
      let beginTime =  new Date(this.data.form.beginTime).getTime()//开始时间
      if(beginTime && endTime && endTime < beginTime){
        app.$message("截止时间需大于或等于开始时间")
        this.setData({
          'form.endTime':""
        })
        return
      }
      this.setData({
        'form.endTime': e.detail.value
      })
    },
  
  }
})

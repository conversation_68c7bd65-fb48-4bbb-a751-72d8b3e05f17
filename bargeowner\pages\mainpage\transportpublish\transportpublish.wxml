<!--pages/mainpage/transportpublish/transportpublish.wxml-->
<view class="transport-page" hover-class="none" hover-stop-propagation="false">
  <view class="transport-top" hover-class="none" hover-stop-propagation="false">
    <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>驳船名称：</view>
      <input type="text" class="add-message-ipt" placeholder="请输入驳船名称" value="{{form.bargeName}}" bindinput="bargeNameInput"/>
    </view>
    <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>船舶识别号：</view>
      <input type="text" class="add-message-ipt" placeholder="请输入船舶识别号" value="{{form.bargeIdentifier}}" bindinput="bargeIdentifierInput"/>
    </view>
    <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>联系电话：</view>
      <input type="number" class="add-message-ipt" placeholder="请输入联系电话" value="{{form.phone}}" bindinput="phoneInput" maxlength="11"/>
    </view>
     <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>承载吨数：</view>
      <input type="digit" class="add-message-ipt" placeholder="请输入承载吨数" value="{{form.capacity}}" bindinput="capacityInput"/>
    </view>
<!-- 
    <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>开始时间：</view>
      <picker class="add-message-ipt" mode="date" value="{{form.startTime}}" bindchange="handleBeginDateChange">
        <view class="{{form.startTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{form.startTime ? form.startTime :'请选择开始时间'}}</view>
      </picker>
    </view>
    <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>结束时间：</view>
      <picker class="add-message-ipt" mode="date" value="{{form.endTime}}" bindchange="handleEndDateChange">
        <view class="{{form.endTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{form.endTime ? form.endTime :'请选择结束时间'}}</view>
      </picker>
    </view> -->

    <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>开始日期：</view>
      <picker class="add-message-ipt" class="add-message-ipt" mode="date" value="{{startDate}}"  bindchange="startDateChange">
        <view class="{{startDate? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{startDate ? startDate :'请选择开始日期'}}</view>
      </picker>
    </view>
    <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>开始时间：</view>
      <picker class="add-message-ipt" class="add-message-ipt" mode="time" value="{{startTime}}"  bindchange="startTimeChange">
        <view class="{{startTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{startTime ? startTime :'请选择开始时间'}}</view>
      </picker>
    </view>
    
    <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>结束日期：</view>
      <picker class="add-message-ipt" class="add-message-ipt" mode="date" value="{{endDate}}"  bindchange="endDateChange">
        <view class="{{endDate? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{endDate ? endDate :'请选择结束日期'}}</view>
      </picker>
    </view>
    <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>结束时间：</view>
      <picker class="add-message-ipt" class="add-message-ipt" mode="time" value="{{endTime}}"  bindchange="endTimeChange">
        <view class="{{endTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{endTime ? endTime :'请选择结束时间'}}</view>
      </picker>
    </view>
    <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-title" hover-class="none" hover-stop-propagation="false">备注：</view>
      <input type="text" class="add-message-ipt" placeholder="请输入备注" value="{{form.remark}}" bindinput="remarkInput"/>
    </view>
  </view>
  <button class="submit-btn" bindtap="publishHanlde">发布</button>

</view>

package com.ruoyi.common.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.domain.UploadAddress;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/30 11:22
 */
@Data
public class BargeInfoVO {

    /**
     * 驳船id主键
     */
    private Long id;

    private Long pb6bargeInfoId;

    /**
     * 船舶识别号
     */
    private String bargeId;

    /**
     * 驳船名称
     */
    private String bargeName;

    /**
     * 船舶载货量（A级）
     */
    private String bargeLoadA;

    /**
     * 船舶载货量（B级）
     */
    private String bargeLoadB;

    /**
     * 证书有效期
     */
    private String validSailDate;

    /**
     * 船籍港
     */
    private String belongArea;

    /**
     * 驳船所有人
     */
    private String bargeOwner;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 录入人
     */
    private String recorder;

    /**
     * 驳船经营人
     */
    private String bargeOperator;

    /**
     * MMSI标识
     */
    private String MMSI;

    /**
     * 驳船净吨
     */
    private String bargeWeight;

    private Integer isCheck;

    private Long userId;

    private String userName;


    /**
     * 驳船审核标识(1.备案审核，2.修改审核)
     */
    private Integer checkFlag;

    /**
     * 备案审核（0-待审核，1-审核通过，2-审核不通过）
     */
    private Integer recordCheck;

    /**
     * 修改审核（0-待审核，1-审核通过，2-审核不通过）
     */
    private Integer updateCheck;

    /**
     * 挂靠审核类型（0-修改挂靠，1-挂靠）
     */
    private Integer isAudit;
    
    /**
     * 挂靠审核（0-待审核，1-审核通过，2-审核不通过）
     */
    private Integer auditStatus;

    /**
     * 绑定类型（1-自有、2-临时挂靠、3-长期挂靠，4-其他）
     */
    private Integer bindingType;

    /**
     * 绑定类型（1-自有、2-临时挂靠、3-长期挂靠，4-其他）
     */
    private Integer updateBindingType;

    private List<UploadAddress> dataList;

    /**
     * 驳船类型 1-自由船 2-挂靠船
     */
    private String bargeType;

    /**
     * 驳船挂靠公司id
     */
    private Long companyId;

    /**
     * 驳船挂靠公司名称
     */
    private String companyName;
    /**
     * 驳船与公司关联表的id
     */
    private Long bargeCompanLinkId;

    /**
     * fdd电子签章url
     */
    private ShipFddUserRel fdd;

    /**
     * 船员列表
     */
    private List crewList;

    // 装载重量最小
    @TableField("LOADING_WEIGHT_MIN")
    private String loadingWeightMin;

    // 装载重量最大
    @TableField("LOADING_WEIGHT_MAX")
    private String loadingWeightMax;

    // 不允许装载货物
    @TableField("CARGO_NOT_ALLOWED")
    private String cargoNotAllowed;
}

package com.ruoyi.app.controller.databarge;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.databarge.domain.Pb3Coutform;
import com.ruoyi.databarge.service.Pb3CoutformService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 出库单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-13
 */
@RestController
@RequestMapping("/barge/pb3coutform")
public class Pb3CoutformController {

    @Autowired
    private Pb3CoutformService pb3CoutformService;

    @PostMapping("findByCoutformId")
    public AjaxResult findByCoutformId(@RequestBody String coutformid){
        List<Pb3Coutform> pb3CoutformList = pb3CoutformService.findByCoutformId(coutformid);
        if(pb3CoutformList.size() != 1){
            throw new CustomException("出库单信息错误，请核对");
        }
        return AjaxResult.success(pb3CoutformList.get(0));
    }

    @PostMapping("/listCOutFormId")
    public AjaxResult listCOutFormId(@RequestBody String coutFormId){
        return AjaxResult.success(pb3CoutformService.listCoutFormId(coutFormId));
    }
}

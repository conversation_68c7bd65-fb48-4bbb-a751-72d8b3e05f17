package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.Pb6WaterCargoMobile;
import com.ruoyi.databarge.domain.dto.Pb6WaterCargoForMobileSearchDTO;
import com.ruoyi.databarge.mapper.Pb6WaterCargoMobileMapper;
import com.ruoyi.databarge.service.Pb6WaterCargoMobileService;
import com.ruoyi.databarge.service.Pb6WaterwaycargoService;
import com.ruoyi.databarge.shipxy.ShipxyUtil;
import com.ruoyi.databarge.shipxy.SingleBargeAisSearchResult;
import com.ruoyi.databarge.shipxy.SingleBargeInfoSearchResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2021/6/11.
 * @Date: 2021/6/11 15:55
 */
@Service
public class Pb6WaterCargoMobileServiceImpl extends ServiceImpl<Pb6WaterCargoMobileMapper, Pb6WaterCargoMobile> implements Pb6WaterCargoMobileService {

    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;

    @Autowired
    private ShipxyUtil shipxyUtil;

    @Override
    public void updatePb6WaterCargoMobile() {
        remove(new QueryWrapper<>());
        List<Pb6WaterCargoMobile> pb6WaterCargoMobileOrderList = pb6WaterwaycargoService.searchPb6WaterCargoForMobile(0);
        pb6WaterCargoMobileOrderList.forEach( i -> {
            queryShipxy(i);
            i.setStatus(0);
        });
        saveBatch(pb6WaterCargoMobileOrderList);

        List<Pb6WaterCargoMobile> pb6WaterCargoMobileLoadList = pb6WaterwaycargoService.searchPb6WaterCargoForMobile(1);
        pb6WaterCargoMobileLoadList.forEach( i -> {
            queryShipxy(i);
            i.setStatus(1);
        });
        saveBatch(pb6WaterCargoMobileLoadList);
    }

    @Override
    public List<Pb6WaterCargoMobile> searchPb6WaterCargoForMobile(Pb6WaterCargoForMobileSearchDTO pb6WaterCargoForMobileSearchDTO) {
        LambdaQueryWrapper<Pb6WaterCargoMobile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Pb6WaterCargoMobile::getStatus, pb6WaterCargoForMobileSearchDTO.getStatus())
                .and(StringUtils.isNotBlank(pb6WaterCargoForMobileSearchDTO.getKeyWord()),
                        i -> i.like(Pb6WaterCargoMobile::getBargename, pb6WaterCargoForMobileSearchDTO.getKeyWord())
                                .or().likeLeft(Pb6WaterCargoMobile::getWaterwaycargoid, pb6WaterCargoForMobileSearchDTO.getKeyWord())
                                .or().eq(Pb6WaterCargoMobile::getMmsi, pb6WaterCargoForMobileSearchDTO.getKeyWord()));
        return list(queryWrapper);
    }

    private void queryShipxy(Pb6WaterCargoMobile pb6WaterCargoMobile){
        //查询船的MMSI
        SingleBargeInfoSearchResult singleBargeInfoSearchResult = shipxyUtil.queryShip(pb6WaterCargoMobile.getBargename());
        if(singleBargeInfoSearchResult != null && singleBargeInfoSearchResult.getData().size() > 0){
            pb6WaterCargoMobile.setMmsi(singleBargeInfoSearchResult.getData().get(0).getMmsi());

            //查询船的位置
            SingleBargeAisSearchResult singleBargeAisSearchResult = shipxyUtil.singleShipQuery(pb6WaterCargoMobile.getMmsi());
            if(singleBargeAisSearchResult != null && singleBargeAisSearchResult.getData().size() > 0){
                SingleBargeAisSearchResult.SingleBargeAis singleBargeAis = singleBargeAisSearchResult.getData().get(0);
                double distance = shipxyUtil.getDistance(ShipxyUtil.XS_LAT, ShipxyUtil.XS_LON,
                        Double.parseDouble(singleBargeAis.getLat()) / 1000000, Double.parseDouble(singleBargeAis.getLon()) / 1000000);
                pb6WaterCargoMobile.setDistance(new BigDecimal(distance / 1000).setScale(0, BigDecimal.ROUND_DOWN));
            } else {
                pb6WaterCargoMobile.setDistance(BigDecimal.ZERO);
            }
        }
    }
}

package com.ruoyi.databarge.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.base.service.impl.SSerialServiceImpl;
import com.ruoyi.basic.domain.BasicGoodsName;
import com.ruoyi.basic.domain.BasicRate;
import com.ruoyi.basic.service.BasicGoodsNameService;
import com.ruoyi.basic.service.impl.BasicGoodsNameServiceImpl;
import com.ruoyi.basic.service.impl.BasicRateServiceImpl;
import com.ruoyi.businessFile.service.impl.BusinessFileServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.UploadAddress;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.impl.UploadAddressServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.mapper.Pb6CargoconsignmentMapper;
import com.ruoyi.databarge.service.Pb6BargeinfoService;
import com.ruoyi.databarge.service.Pb6CargoconsignmentService;
import com.ruoyi.databarge.service.Pb6CargoconsignmentdetailService;
import com.ruoyi.databarge.service.WechatMpUserService;
import com.ruoyi.databarge.wechat.MpMessageDTO;
import com.ruoyi.databarge.wechat.MpMessageResult;
import com.ruoyi.databarge.wechat.MpUtil;
import com.ruoyi.ship.domain.BargeBlacklist;
import com.ruoyi.ship.domain.ShipInfo;
import com.ruoyi.ship.service.ShipInfoService;
import com.ruoyi.ship.service.impl.BargeBlacklistServiceImpl;
import com.ruoyi.ship.tool.domain.UpdateWeightExcel;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.impl.SysDictDataServiceImpl;
import com.ruoyi.wechat.service.WechatMpAccessTokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 托运单主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-23
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Pb6CargoconsignmentServiceImpl extends ServiceImpl<Pb6CargoconsignmentMapper, Pb6Cargoconsignment> implements Pb6CargoconsignmentService {

    @Autowired
    private Pb6CargoconsignmentdetailService pb6CargoconsignmentdetailService;

    @Autowired
    private Pb6BargeinfoService pb6BargeinfoService;

    @Autowired
    private ShipInfoService shipInfoService;

    @Autowired
    private Pb6WaterwaycargoServiceImpl pb6WaterwaycargoService;

    @Autowired
    private SysDictDataServiceImpl sysDictDataService;

    @Autowired
    private BargeBlacklistServiceImpl bargeBlacklistService;

    @Autowired
    private BasicGoodsNameServiceImpl basicGoodsNameService;

    @Autowired
    SSerialServiceImpl sSerialService;

    @Autowired
    BusinessFileServiceImpl businessFileService;

    @Autowired
    private BasicRateServiceImpl basicRateService;

    @Autowired
    private UploadAddressServiceImpl uploadAddressService;


    @Override
    public AjaxResult addConsign(Pb6Cargoconsignment pb6Cargoconsignment) {

        // 如果shipInfoId为空，报错
        if(pb6Cargoconsignment.getShipInfoId() == null){
            return AjaxResult.error("大船信息为空，请重试");
        }

        // 如果gzpId不为空，并且已经在表中存在，则报错
        if(pb6Cargoconsignment.getGzpId() != null){
            QueryWrapper<Pb6Cargoconsignment> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(Pb6Cargoconsignment::getGzpId, pb6Cargoconsignment.getGzpId());
            // del_flag = 0
            queryWrapper.lambda().eq(Pb6Cargoconsignment::getDelFlag, "0");
            List<Pb6Cargoconsignment> pb6Cargoconsignments = super.list(queryWrapper);
            if(pb6Cargoconsignments.size() > 0){
                return AjaxResult.error("该生产系统托运单已经存在，请勿重复导入");
            }
        }

        // 大船信息
        ShipInfo shipInfo = shipInfoService.getById(pb6Cargoconsignment.getShipInfoId());



        // 申请时间为当前时间 类型为String 2024-02-01 16:38:45
        pb6Cargoconsignment.setApplydate(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));

        // 代理费率
        // 费率
        BasicRate basicRate = new BasicRate();
        basicRate.setCusName(pb6Cargoconsignment.getConsigner());
        basicRate.setSettlementMethodCode(shipInfo.getSettlementMethod());

        BasicGoodsName basicGoodsName = basicGoodsNameService.selectBasicGoodsNameByName(pb6Cargoconsignment.getCargename());
        basicRate.setGoodsCategoryId(basicGoodsName.getGoodsCategoryId());
        basicRate.setPackingMethodCode(shipInfo.getPackingMethod());
        List<BasicRate> basicRates = basicRateService.selectBasicRateList(basicRate);
        if(basicRates.size() == 0){
            return AjaxResult.error("未找到对应的费率信息");
        }
        basicRate = basicRates.get(0);
        pb6Cargoconsignment.setRate(basicRate.getRate());

        // 金额等于总吨数乘以费率
        pb6Cargoconsignment.setAmount(pb6Cargoconsignment.getTotalTonnage().multiply(pb6Cargoconsignment.getRate()));

        if(super.save(pb6Cargoconsignment)){
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.error("新增失败");
    }

    @Override
    public AjaxResult editConsign(Pb6Cargoconsignment pb6Cargoconsignment) {

        // 当前数据库的托运单
        Pb6Cargoconsignment pb6CargoconsignmentDb = baseMapper.selectById(pb6Cargoconsignment.getId());

        // 如果当前托运单已经生成结算单，不允许修改

        if(!"0".equals(pb6CargoconsignmentDb.getSettlementStatus())){
            return AjaxResult.error("已经生成结算单，不允许修改");
        }

        // 如果当前托运单下的托运单明细已经确认，不允许修改
        List<Pb6Cargoconsignmentdetail> pb6Cargoconsignmentdetails = pb6CargoconsignmentdetailService.searchByConsignId(pb6Cargoconsignment.getId());
        //
        // for(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail : pb6Cargoconsignmentdetails){
        //     if("5".equals(pb6Cargoconsignmentdetail.getWxoperatestate())){
        //         return AjaxResult.error("存在驳船已经确认，不允许修改");
        //     }
        // }

        // 设置托运单信息 货名 提单号 积载因素 托运人名称 收货人名称 结算单位
        pb6CargoconsignmentDb.setCargename(pb6Cargoconsignment.getCargename());
        pb6CargoconsignmentDb.setBillNo(pb6Cargoconsignment.getBillNo());
        pb6CargoconsignmentDb.setStowageFactor(pb6Cargoconsignment.getStowageFactor());
        pb6CargoconsignmentDb.setConsignee(pb6Cargoconsignment.getConsignee());
        pb6CargoconsignmentDb.setConsigner(pb6Cargoconsignment.getConsigner());
        pb6CargoconsignmentDb.setSettlementCompany(pb6Cargoconsignment.getSettlementCompany());
        pb6CargoconsignmentDb.setTotalTonnage(pb6Cargoconsignment.getTotalTonnage());
        pb6CargoconsignmentDb.setCargoType(pb6Cargoconsignment.getCargoType());

        // 金额重新计算
        BigDecimal multiply = pb6Cargoconsignment.getTotalTonnage().multiply(pb6Cargoconsignment.getRate());
        pb6CargoconsignmentDb.setAmount(multiply);

        // 更新托运单
        if(super.updateById(pb6CargoconsignmentDb)){

            for(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail : pb6Cargoconsignmentdetails){
                // 更新水路运单字段
                if(pb6Cargoconsignmentdetail.getWaterwaycargoid()!=null){
                    Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getById(pb6Cargoconsignmentdetail.getWaterwaycargoid());
                    pb6Waterwaycargo.setBeginPort(pb6Cargoconsignment.getBeginport());
                    pb6Waterwaycargo.setEndPort(pb6Cargoconsignment.getEndport());
                    pb6Waterwaycargo.setCargosize(pb6Cargoconsignment.getSettlementCompany());
                    pb6Waterwaycargo.setConsigner(pb6Cargoconsignment.getConsigner());
                    pb6Waterwaycargo.setConsignee(pb6Cargoconsignment.getConsignee());
                    pb6Waterwaycargo.setCargename(pb6Cargoconsignment.getCargename());
                    pb6WaterwaycargoService.updateById(pb6Waterwaycargo);
                }
            }

            return AjaxResult.success("修改成功");
        }


        return AjaxResult.error("修改失败，请联系管理员");
    }

    @Override
    public AjaxResult deleteCargoconsignment(List<Long> consignIds) {
        // 托运单列表
        List<Pb6Cargoconsignment> pb6Cargoconsignments = super.listByIds(consignIds);

        // 遍历托运单，如果已经开票，不允许删除
        for(Pb6Cargoconsignment pb6Cargoconsignment : pb6Cargoconsignments){
            if(!"0".equals(pb6Cargoconsignment.getSettlementStatus())){
                return AjaxResult.error("已经生成结算单，不允许删除");
            }
        }

        // 删除托运单
        for(Pb6Cargoconsignment pb6Cargoconsignment : pb6Cargoconsignments){
            if(super.removeById(pb6Cargoconsignment.getId())){
                // 删除托运单明细,托运单明细列表，调用托运单明细的删除方法
                List<Pb6Cargoconsignmentdetail> pb6Cargoconsignmentdetails = pb6CargoconsignmentdetailService.searchByConsignId(pb6Cargoconsignment.getId());
                for(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail : pb6Cargoconsignmentdetails){
                    this.deleteCargoconsignmentDeatil(pb6Cargoconsignmentdetail);
                }
            }
        }
        return AjaxResult.success("删除成功");
    }

    @Override
    public List<Pb6Cargoconsignment> searchByConsignFlagForDealRepeat(String consignFlag) {
        QueryWrapper<Pb6Cargoconsignment> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb6Cargoconsignment::getConsignflag, consignFlag);
        return super.list(queryWrapper);
    }

    @Override
    public AjaxResult generateWaterWayCargo(List<Long> consignmentIds) {

        // 托运单列表
        List<Pb6Cargoconsignment> pb6Cargoconsignments = super.listByIds(consignmentIds);

        for(Pb6Cargoconsignment pb6Cargoconsignment : pb6Cargoconsignments){

            // 托运单下的明细列表
            List<Pb6Cargoconsignmentdetail> pb6Cargoconsignmentdetails = pb6CargoconsignmentdetailService.searchByConsignId(pb6Cargoconsignment.getId());

            // 遍历明细列表
            for(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail : pb6Cargoconsignmentdetails){

                // 1、已经配载的跳过
                if(!("0".equals(pb6Cargoconsignmentdetail.getFlagbargestate()) || "1".equals(pb6Cargoconsignmentdetail.getFlagbargestate())
                    || "8".equals(pb6Cargoconsignmentdetail.getFlagbargestate()))){
                    continue;
                }

                // 2、未配载的，先判断驳船是否备案，如果未备案，运单备注为“驳船未备案，无法生成运单” ，更新
                List<Pb6Bargeinfo> pb6Bargeinfos = pb6BargeinfoService.searchByBargeName(pb6Cargoconsignmentdetail.getBargename());
                if(pb6Bargeinfos.size() == 0){
                    pb6Cargoconsignmentdetail.setWaterWayRemark("驳船未备案，无法生成运单");
                    pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
                }else{
                    pb6Cargoconsignmentdetail.setWaterWayRemark("");

                    // 查询是否有重复的驳船名和航次并且提单号不一样
                    List<Pb6Cargoconsignmentdetail> repeatCargoconsignmentdetails = pb6CargoconsignmentdetailService.searchRepeatBarge(pb6Cargoconsignmentdetail);
                    // 0 特殊情况，存在驳船+航次相同的驳船，需要判断加起来的配载量是否超过驳船的载重量
                    BigDecimal sumLoadingWeight = new BigDecimal(pb6Cargoconsignmentdetail.getRationweight());
                    if(repeatCargoconsignmentdetails.size() > 0){
                        for(Pb6Cargoconsignmentdetail repeatCargoconsignmentdetail : repeatCargoconsignmentdetails){
                            // id不一样，说明是不同的驳船
                            if(!repeatCargoconsignmentdetail.getId().equals(pb6Cargoconsignmentdetail.getId())){
                                sumLoadingWeight = sumLoadingWeight.add(new BigDecimal(repeatCargoconsignmentdetail.getRationweight()));
                            }
                        }
                    }

                    // 如果备案了，判断是否符合备案条件，如果不符合，运单备注为“不符合某某条件，无法生成运单”，更新
                    // TODO
                    // 1、载重是否在航区范围内
                    Pb6Bargeinfo pb6Bargeinfo = pb6Bargeinfos.get(0);
                    BigDecimal rationWeight = new BigDecimal(pb6Cargoconsignmentdetail.getRationweight());
                    BigDecimal loadAWeight = new BigDecimal(pb6Bargeinfo.getBargeloada());
                    BigDecimal loadBWeight = new BigDecimal(pb6Bargeinfo.getBargeloadb());
                    if("A".equals(pb6Cargoconsignmentdetail.getWorkArea())){
                        if(sumLoadingWeight.compareTo(loadAWeight) > 0){
                            pb6Cargoconsignmentdetail.setWaterWayRemark("载重超过驳船载货量（A级），无法生成运单");
                            pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
                            continue;
                        }
                    }
                    if("B".equals(pb6Cargoconsignmentdetail.getWorkArea())){
                        if(sumLoadingWeight.compareTo(loadBWeight) > 0){
                            pb6Cargoconsignmentdetail.setWaterWayRemark("载重超过驳船载货量（B级），无法生成运单");
                            pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
                            continue;
                        }
                    }
                    // 2、货类是否是不允许装载货类
                    if(pb6Bargeinfo.getCargoNotAllowed() != null && !"".equals(pb6Bargeinfo.getCargoNotAllowed())){

                        // 如果禁装货物是 无 ，返回
                        // 如果禁装货物是 铁矿类，得到铁矿类的所有货名，遍历，如果有相同的货名，则提示
                        if("无".equals(pb6Bargeinfo.getCargoNotAllowed())){

                        }else{
                            // 禁装货物可能多选，用逗号分隔，所以先分割，得到禁装货物列表
                            String[] cargoNotAllowed = pb6Bargeinfo.getCargoNotAllowed().split(",");
                            // 遍历禁装货物列表，每个禁装货物都要查找对应的货名
                            for(String cargoNotAllowed1 : cargoNotAllowed){
                                List<BasicGoodsName> basicGoodsNames = basicGoodsNameService.selectBasicGoodsNameByGoodsCategory(cargoNotAllowed1);
                                // 遍历货名，如果有相同的货名，则提示
                                for(BasicGoodsName basicGoodsName1 : basicGoodsNames){
                                    if(basicGoodsName1.getGoodsName().equals(pb6Cargoconsignmentdetail.getCargoName())){
                                        return AjaxResult.error("装载了禁装货物，无法生成运单");
                                    }
                                }
                            }
                        }
                    }
                    // 3、货类是铁矿的情况下，是否需要按照载重量的80%来配重
                    ShipInfo shipInfo = shipInfoService.getById(pb6Cargoconsignment.getShipInfoId());
                    if("1".equals(shipInfo.getIsFixedLoading())){
                        BigDecimal loadingWeight80;
                        // 需要按照配载重量的80%来装载
                        // 配载重量的80%
                        if("A".equals(pb6Cargoconsignmentdetail.getWorkArea())){
                            loadingWeight80 = new BigDecimal(pb6Bargeinfo.getBargeloada()).multiply(new BigDecimal(0.8));
                        }else{
                            loadingWeight80 = new BigDecimal(pb6Bargeinfo.getBargeloadb()).multiply(new BigDecimal(0.8));
                        }
                        // loadingWeight80四舍五入
                        loadingWeight80 = loadingWeight80.setScale(0, BigDecimal.ROUND_HALF_UP);
                        if(rationWeight.compareTo(loadingWeight80) >  0){
                            pb6Cargoconsignmentdetail.setWaterWayRemark("配载重量大于载重量的80%，请检查！");
                            pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
                            continue;
                        }
                    }

                    // 驳船证书是否还在有效期
                    if (DateUtils.getNowDate().after(DateUtils.parseDate(pb6Bargeinfo.getValidsaildate()))) {
                        pb6Cargoconsignmentdetail.setWaterWayRemark("驳船证书已过期，无法生成运单");
                        pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
                        continue;
                    }

                    if("".equals(pb6Cargoconsignmentdetail.getWaterWayRemark())){
                        pb6Cargoconsignmentdetail.setBargeid(pb6Bargeinfo.getId());
                        Pb6Waterwaycargo pb6Waterwaycargo = getPb6Waterwaycargo(pb6Cargoconsignment, pb6Cargoconsignmentdetail, pb6Bargeinfo, shipInfo,null);

                        // 2、更新托运单明细的配载状态为已配载,驳船ID为备案了的驳船ID
                        pb6Cargoconsignmentdetail.setFlagbargestate("3");
                        pb6Cargoconsignmentdetail.setWaterwaycargoid(pb6Waterwaycargo.getId().toString());
                        pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
                    }

                }


            }
        }

        return AjaxResult.success("生成成功");
    }

    @Override
    public AjaxResult addCargoconsignmentDeatil(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) {

        // 驳船名和航次重合的驳船名
        String bargeNameAndVoyageRepeat = "";

        // 判断新增的驳船是否在黑名单中，如果在，不允许新增
        BargeBlacklist bargeBlacklist = new BargeBlacklist();
        bargeBlacklist.setBargeName(pb6Cargoconsignmentdetail.getBargename());
        List<BargeBlacklist> bargeBlacklists = bargeBlacklistService.selectBargeBlacklistList(bargeBlacklist);
        if(bargeBlacklists.size() > 0){
            return AjaxResult.error("驳船已被拉黑，不允许新增");
        }

        // 托运单
        Pb6Cargoconsignment pb6Cargoconsignment = super.getById(pb6Cargoconsignmentdetail.getConsignid());

        // 赋值提单号
        pb6Cargoconsignmentdetail.setBillNo(pb6Cargoconsignment.getBillNo());

        String bargeVoyage = pb6Cargoconsignmentdetail.getBargename() + "_" + pb6Cargoconsignmentdetail.getVoyage() + "_" + pb6Cargoconsignmentdetail.getBillNo();

        // 托运单下的驳船信息
        List<Pb6Cargoconsignmentdetail> pb6CargoconsignmentdetailListSelect = shipInfoService.selectByConsignid(pb6Cargoconsignmentdetail.getConsignid());

        // 遍历托运单，把驳船名和航次和提单号 拼接成字符串，存到bargeVoyage中，用于判断是否已经存在
        for(Pb6Cargoconsignmentdetail pb6CargoconsignmentdetailSave : pb6CargoconsignmentdetailListSelect){
            String saveBargeVoyage = pb6CargoconsignmentdetailSave.getBargename() + "_" + pb6CargoconsignmentdetailSave.getVoyage() + "_" + pb6CargoconsignmentdetailSave.getBillNo();
            if(bargeVoyage.equals(saveBargeVoyage)){
                return AjaxResult.error("该托运单下存在相同船名航次提单号的驳船，请检查！");
            }
        }

        // 判断当前大船所需文件是否都已上传，如果没有，则返回错误信息
        if(!businessFileService.isAllFileUpload(pb6Cargoconsignment.getShipInfoId())){
            return AjaxResult.error("导入失败，大船所需文件未全部上传，请检查！");
        }

        // 查询不同大船下，七天内同船名的船舶信息
        pb6Cargoconsignmentdetail.setShipInfoId(pb6Cargoconsignment.getShipInfoId());
        List<Pb6Cargoconsignmentdetail> repeatBarges = pb6CargoconsignmentdetailService.searchRepeatByBargeInfo(pb6Cargoconsignmentdetail);
        if(repeatBarges.size() > 0){
            for(Pb6Cargoconsignmentdetail repeatCargoconsignmentdetail : repeatBarges){
                ShipInfo shipInfo1 = shipInfoService.selectByBargeInfoId(repeatCargoconsignmentdetail.getId());
                // 提示 七天内，xx驳船在xx大船下存在，请检查，并换行
                bargeNameAndVoyageRepeat += "七天内，发现驳船：" + repeatCargoconsignmentdetail.getBargename() + "在大船：" + shipInfo1.getShipName() + "下工作，请检查！！" + "\n";
            }
        }

        // 查询大船信息下的所有驳船信息
        List<Pb6Cargoconsignmentdetail> pb6CargoconsignmentdetailsExist = pb6CargoconsignmentdetailService.searchByShipInfoId(pb6Cargoconsignment.getShipInfoId());
        for(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail1 : pb6CargoconsignmentdetailsExist){
            if(pb6Cargoconsignmentdetail1.getBargename().equals(pb6Cargoconsignmentdetail.getBargename())){
                // 提示 XXX大船内，存在XXX驳船重复，请核实！
                bargeNameAndVoyageRepeat +=  "大船内，存在" + pb6Cargoconsignmentdetail.getBargename() + "驳船重复，请核实！" + "\n";
                break;
            }
        }

        // 先根据驳船名查询驳船信息，看是否备案，如果没有备案，则直接新增，状态设置为未备案
        List<Pb6Bargeinfo> pb6Bargeinfos = pb6BargeinfoService.searchByBargeName(pb6Cargoconsignmentdetail.getBargename());
        if (pb6Bargeinfos.size() == 0) {
            pb6Cargoconsignmentdetail.setWaterWayRemark("驳船未备案，无法生成运单");
            if(pb6CargoconsignmentdetailService.save(pb6Cargoconsignmentdetail)){
                bargeNameAndVoyageRepeat +=  "新增成功，" + pb6Cargoconsignmentdetail.getBargename() + "未备案，请跟进！" + "\n";
                return AjaxResult.success(bargeNameAndVoyageRepeat);
            }
            return AjaxResult.error("新增失败");
        } else {
            pb6Cargoconsignmentdetail.setWaterWayRemark("");

            // 查询是否有重复的驳船名和航次并且提单号不一样
            List<Pb6Cargoconsignmentdetail> repeatCargoconsignmentdetails = pb6CargoconsignmentdetailService.searchRepeatBarge(pb6Cargoconsignmentdetail);
            // 0 特殊情况，存在驳船+航次相同的驳船，需要判断加起来的配载量是否超过驳船的载重量
            BigDecimal sumLoadingWeight = new BigDecimal(pb6Cargoconsignmentdetail.getRationweight());
            if(repeatCargoconsignmentdetails.size() > 0){
                for(Pb6Cargoconsignmentdetail repeatCargoconsignmentdetail : repeatCargoconsignmentdetails){
                    sumLoadingWeight = sumLoadingWeight.add(new BigDecimal(repeatCargoconsignmentdetail.getRationweight()));
                }
            }


            // 如果备案了，判断是否符合备案条件，如果不符合，运单备注为“不符合某某条件，无法生成运单”，更新
            // TODO
            // 1、载重是否在航区范围内

            Pb6Bargeinfo pb6Bargeinfo = pb6Bargeinfos.get(0);
            BigDecimal rationWeight = new BigDecimal(pb6Cargoconsignmentdetail.getRationweight());
            BigDecimal loadAWeight = new BigDecimal(pb6Bargeinfo.getBargeloada());
            BigDecimal loadBWeight = new BigDecimal(pb6Bargeinfo.getBargeloadb());
            if ("A".equals(pb6Cargoconsignmentdetail.getWorkArea())) {
                if (sumLoadingWeight.compareTo(loadAWeight) > 0) {
                    return AjaxResult.error("导入失败，驳船" + pb6Cargoconsignmentdetail.getBargename() + "的配载重量大于航区A的载重量，请检查！");
                    // pb6Cargoconsignmentdetail.setWaterWayRemark("载重超过驳船载货量（A级），无法生成运单");
                    // if(pb6CargoconsignmentdetailService.save(pb6Cargoconsignmentdetail)){
                    //     return AjaxResult.success(pb6Cargoconsignmentdetail);
                    // }
                    // return AjaxResult.error("新增失败");
                }
            }
            if ("B".equals(pb6Cargoconsignmentdetail.getWorkArea())) {
                if (sumLoadingWeight.compareTo(loadBWeight) > 0) {
                    return AjaxResult.error("导入失败，驳船" + pb6Cargoconsignmentdetail.getBargename() + "的配载重量大于航区A的载重量，请检查！");
                    // pb6Cargoconsignmentdetail.setWaterWayRemark("载重超过驳船载货量（B级），无法生成运单");
                    // if(pb6CargoconsignmentdetailService.save(pb6Cargoconsignmentdetail)){
                    //     return AjaxResult.success(pb6Cargoconsignmentdetail);
                    // }
                    // return AjaxResult.error("新增失败");
                }
            }
            // 2、货类是否是不允许装载货类
            if (pb6Bargeinfo.getCargoNotAllowed() != null && !"".equals(pb6Bargeinfo.getCargoNotAllowed())) {
                // 如果禁装货物是 无 ，返回
                // 如果禁装货物是 铁矿类，得到铁矿类的所有货名，遍历，如果有相同的货名，则提示
                if("无".equals(pb6Bargeinfo.getCargoNotAllowed())){
                }else{
                    // 禁装货物可能多选，用逗号分隔，所以先分割，得到禁装货物列表
                    String[] cargoNotAllowed = pb6Bargeinfo.getCargoNotAllowed().split(",");
                    // 遍历禁装货物列表，每个禁装货物都要查找对应的货名
                    for(String cargoNotAllowed1 : cargoNotAllowed){
                        List<BasicGoodsName> basicGoodsNames = basicGoodsNameService.selectBasicGoodsNameByGoodsCategory(cargoNotAllowed1);
                        // 遍历货名，如果有相同的货名，则提示
                        for(BasicGoodsName basicGoodsName1 : basicGoodsNames){
                            if(basicGoodsName1.getGoodsName().equals(pb6Cargoconsignmentdetail.getCargoName())){
                                return AjaxResult.error("装载了禁装货物，无法生成运单");
                            }
                        }
                    }
                }

            }
            // 3、是否需要按照载重量的80%来配重
            ShipInfo shipInfo = shipInfoService.getById(pb6Cargoconsignment.getShipInfoId());
            if ("1".equals(shipInfo.getIsFixedLoading())) {
                BigDecimal loadingWeight80;
                // 需要按照配载重量的80%来装载
                // 配载重量的80%
                if ("A".equals(pb6Cargoconsignmentdetail.getWorkArea())) {
                    loadingWeight80 = new BigDecimal(pb6Bargeinfo.getBargeloada()).multiply(new BigDecimal(0.8));
                } else {
                    loadingWeight80 = new BigDecimal(pb6Bargeinfo.getBargeloadb()).multiply(new BigDecimal(0.8));
                }
                // loadingWeight80四舍五入
                loadingWeight80 = loadingWeight80.setScale(0, BigDecimal.ROUND_HALF_UP);
                if (rationWeight.compareTo(loadingWeight80) >  0) {
                    return AjaxResult.error("配载重量大于载重量的80%，请检查！");
                    // pb6Cargoconsignmentdetail.setWaterWayRemark("未按照配载重量的80%来配重");
                    // if(pb6CargoconsignmentdetailService.save(pb6Cargoconsignmentdetail)){
                    //     return AjaxResult.success(pb6Cargoconsignmentdetail);
                    // }
                    // return AjaxResult.error("新增失败");
                }
            }

            BasicGoodsName basicGoodsName = basicGoodsNameService.selectBasicGoodsNameByName(pb6Cargoconsignment.getCargename());

            // 当货名需要判断积载因素时，才判断
            if(basicGoodsName.getLimitFlag().equals("1")){
                // 并且此时船舶的装载容量范围不为空
                if(pb6Bargeinfo.getLoadingWeightMin() != null && !"".equals(pb6Bargeinfo.getLoadingWeightMin()) &&
                        pb6Bargeinfo.getLoadingWeightMax() != null && !"".equals(pb6Bargeinfo.getLoadingWeightMax())){
                    // 提单积载因素为空报错
                    if(pb6Cargoconsignment.getStowageFactor() == null || "".equals(pb6Cargoconsignment.getStowageFactor())){
                        return AjaxResult.error("导入失败，提单" + pb6Cargoconsignment.getBillNo() + "的积载因素为空，请检查！");
                    }
                    BigDecimal stowageFactor = new BigDecimal(pb6Cargoconsignment.getStowageFactor());
                    // 1/积载因素 单位是吨
                    BigDecimal stowageFactorReciprocal = new BigDecimal(1).divide(stowageFactor, 2, BigDecimal.ROUND_HALF_UP);
                    if(stowageFactorReciprocal.compareTo(new BigDecimal(pb6Bargeinfo.getLoadingWeightMax())) > 0){
                        return AjaxResult.error("导入失败，驳船" + pb6Cargoconsignmentdetail.getBargename() + "的积载因素大于驳船的装载容量范围，请检查！");
                    }else if(stowageFactorReciprocal.compareTo(new BigDecimal(pb6Bargeinfo.getLoadingWeightMin())) < 0){
                        return AjaxResult.error("导入失败，驳船" + pb6Cargoconsignmentdetail.getBargename() + "的积载因素小于驳船的装载容量范围，请检查！");
                    }
                }
            }

            // 驳船证书是否还在有效期
            if (DateUtils.getNowDate().after(DateUtils.parseDate(pb6Bargeinfo.getValidsaildate()))) {
                return AjaxResult.error("导入失败，驳船" + pb6Cargoconsignmentdetail.getBargename() + "的证书已过期，请检查！");
            }

            // 如果已经备案，则判断是否符合新增条件，如果符合，同意新增，状态设置为已配载，并生成水路运单
            pb6Cargoconsignmentdetail.setBargeid(pb6Bargeinfo.getId());
            Pb6Waterwaycargo pb6Waterwaycargo = getPb6Waterwaycargo(pb6Cargoconsignment, pb6Cargoconsignmentdetail, pb6Bargeinfo, shipInfo,null);

            // 2、更新托运单明细的配载状态为已配载,驳船ID为备案了的驳船ID
            pb6Cargoconsignmentdetail.setFlagbargestate("3");
            pb6Cargoconsignmentdetail.setWaterwaycargoid(pb6Waterwaycargo.getId().toString());
            if(pb6CargoconsignmentdetailService.save(pb6Cargoconsignmentdetail)){
                return AjaxResult.success(bargeNameAndVoyageRepeat);
            }
            return AjaxResult.error("新增失败");
        }
    }

    @Override
    public AjaxResult updateCargoconsignmentDeatil(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) {

        // 已完结的无法修改
        if("5".equals(pb6Cargoconsignmentdetail.getFlagbargestate())){
            return AjaxResult.error("已完结的无法修改");
        }

        // 原托运单明细
        Pb6Cargoconsignmentdetail pb6CargoconsignmentdetailDb = pb6CargoconsignmentdetailService.getById(pb6Cargoconsignmentdetail.getId());

        // 托运单
        Pb6Cargoconsignment pb6Cargoconsignment = super.getById(pb6Cargoconsignmentdetail.getConsignid());

        // 驳船信息
        List<Pb6Bargeinfo> pb6Bargeinfos = pb6BargeinfoService.searchByBargeName(pb6Cargoconsignmentdetail.getBargename());

        // 判断修改后是否符合生成水路运单条件
        pb6Cargoconsignmentdetail.setWaterWayRemark("");

        if(pb6Bargeinfos.size() > 0){

            // 查询是否有重复的驳船名和航次并且提单号不一样
            List<Pb6Cargoconsignmentdetail> repeatCargoconsignmentdetails = pb6CargoconsignmentdetailService.searchRepeatBargeExceptNow(pb6Cargoconsignmentdetail);
            // 0 特殊情况，存在驳船+航次相同的驳船，需要判断加起来的配载量是否超过驳船的载重量
            BigDecimal sumLoadingWeight = new BigDecimal(pb6Cargoconsignmentdetail.getRationweight());
            if(repeatCargoconsignmentdetails.size() > 0){
                for(Pb6Cargoconsignmentdetail repeatCargoconsignmentdetail : repeatCargoconsignmentdetails){
                    sumLoadingWeight = sumLoadingWeight.add(new BigDecimal(repeatCargoconsignmentdetail.getRationweight()));
                }
            }

            // 如果备案了，判断是否符合备案条件，如果不符合，运单备注为“不符合某某条件，无法生成运单”，更新
            // TODO
            // 1、载重是否在航区范围内
            Pb6Bargeinfo pb6Bargeinfo = pb6Bargeinfos.get(0);
            BigDecimal rationWeight = new BigDecimal(pb6Cargoconsignmentdetail.getRationweight());
            BigDecimal loadAWeight = new BigDecimal(pb6Bargeinfo.getBargeloada());
            BigDecimal loadBWeight = new BigDecimal(pb6Bargeinfo.getBargeloadb());
            if ("A".equals(pb6Cargoconsignmentdetail.getWorkArea())) {
                if (rationWeight.compareTo(loadAWeight) > 0) {
                    return AjaxResult.error("载重超过驳船载货量（A级），无法修改");
                }
                if(sumLoadingWeight.compareTo(loadAWeight) > 0){
                    return AjaxResult.error("修改失败，存在重复驳船" + pb6Cargoconsignmentdetail.getBargename() + "的配载重量大于航区A的载重量，请检查！");
                }
            }
            if ("B".equals(pb6Cargoconsignmentdetail.getWorkArea())) {
                if (rationWeight.compareTo(loadBWeight) > 0) {
                    return AjaxResult.error("载重超过驳船载货量（B级），无法修改");
                }
                if(sumLoadingWeight.compareTo(loadBWeight) > 0){
                    return AjaxResult.error("修改失败，存在重复驳船" + pb6Cargoconsignmentdetail.getBargename() + "的配载重量大于航区B的载重量，请检查！");
                }
            }
            // 3、是否需要按照载重量的80%来配重
            ShipInfo shipInfo = shipInfoService.getById(pb6Cargoconsignment.getShipInfoId());
            if ("1".equals(shipInfo.getIsFixedLoading())) {
                BigDecimal loadingWeight80;
                // 需要按照配载重量的80%来装载
                // 配载重量的80%
                if ("A".equals(pb6Cargoconsignmentdetail.getWorkArea())) {
                    loadingWeight80 = new BigDecimal(pb6Bargeinfo.getBargeloada()).multiply(new BigDecimal(0.8));
                } else {
                    loadingWeight80 = new BigDecimal(pb6Bargeinfo.getBargeloadb()).multiply(new BigDecimal(0.8));
                }
                // loadingWeight80四舍五入
                loadingWeight80 = loadingWeight80.setScale(0, BigDecimal.ROUND_HALF_UP);
                if (rationWeight.compareTo(loadingWeight80) > 0) {
                    return AjaxResult.error("配载重量大于载重量的80%，请检查！");
                }
            }

            BasicGoodsName basicGoodsName = basicGoodsNameService.selectBasicGoodsNameByName(pb6Cargoconsignment.getCargename());

            // 当货名需要判断积载因素时，才判断
            if(basicGoodsName.getLimitFlag().equals("1")){
                // 并且此时船舶的装载容量范围不为空
                if(pb6Bargeinfo.getLoadingWeightMin() != null && !"".equals(pb6Bargeinfo.getLoadingWeightMin()) &&
                        pb6Bargeinfo.getLoadingWeightMax() != null && !"".equals(pb6Bargeinfo.getLoadingWeightMax())){
                    BigDecimal stowageFactor = new BigDecimal(pb6Cargoconsignment.getStowageFactor());
                    // 1/积载因素 单位是吨
                    BigDecimal stowageFactorReciprocal = new BigDecimal(1).divide(stowageFactor, 2, BigDecimal.ROUND_HALF_UP);
                    if(stowageFactorReciprocal.compareTo(new BigDecimal(pb6Bargeinfo.getLoadingWeightMax())) > 0){
                        return AjaxResult.error("导入失败，驳船" + pb6Cargoconsignmentdetail.getBargename() + "的积载因素大于驳船的装载容量范围，请检查！");
                    }else if(stowageFactorReciprocal.compareTo(new BigDecimal(pb6Bargeinfo.getLoadingWeightMin())) < 0){
                        return AjaxResult.error("导入失败，驳船" + pb6Cargoconsignmentdetail.getBargename() + "的积载因素小于驳船的装载容量范围，请检查！");
                    }
                }
            }
        }

        // 如果都符合，可以修改
        if(pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail)){
            if(pb6Bargeinfos.size() > 0){
                // 更新成功，更新水路运单
                Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getById(pb6Cargoconsignmentdetail.getWaterwaycargoid());
                // 航次 配载吨数
                pb6Waterwaycargo.setBargeNumber(pb6Cargoconsignmentdetail.getVoyage());
                pb6Waterwaycargo.setRationweight(pb6Cargoconsignmentdetail.getRationweight());
                // 金额可能需要重新计算
                // 金额 业务代理费 等于配载重量乘以业务代理费率
                BigDecimal cost = pb6Cargoconsignment.getRate().multiply(new BigDecimal(pb6Cargoconsignmentdetail.getRationweight()));
                pb6Waterwaycargo.setBusinessagentcharge(cost.toString());
                // 特约事项
                pb6Waterwaycargo.setSpecialNotice(pb6Cargoconsignmentdetail.getSpecialNotice());

                if(pb6WaterwaycargoService.updateById(pb6Waterwaycargo)){
                }
            }

            // 判断是否修改了配载吨数
            if(!pb6CargoconsignmentdetailDb.getRationweight().equals(pb6Cargoconsignmentdetail.getRationweight())){
                Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getById(pb6Cargoconsignmentdetail.getWaterwaycargoid());
                // 改了配载吨数，发送微信公众号消息
                Map<String, Object> params = new HashMap<>();
                // 船名航次 穗港901/240411  大船船名 凯航发展 货物名称 铁矿粉 原吨数 现吨数
                params.put("thing1", pb6Cargoconsignmentdetail.getBargename() + "/" + pb6Cargoconsignmentdetail.getVoyage());
                params.put("thing2", pb6Waterwaycargo.getShipName());
                params.put("thing3", pb6Waterwaycargo.getCargename());
                params.put("character_string4", pb6CargoconsignmentdetailDb.getRationweight());
                params.put("character_string5", pb6Cargoconsignmentdetail.getRationweight());

                // 发送消息
                List<SysUser> sysUserList = sysUserBargeMapper.queryAllUserByBargeId(pb6Cargoconsignmentdetail.getBargeid());

                if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWxrationcontactnumber())){
                    List<SysUser> ration = sysUserMapper.selectCargoConsignPersonByPhone(pb6Cargoconsignmentdetail.getWxrationcontactnumber());
                    sysUserList.addAll(ration);
                }

                for(SysUser sysUser : sysUserList){
                    // 发送消息
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(sysUser.getUnionId())) {

                        // 查询前，先根据openId去查询用户信息
                        wechatMpUserService.getUserInfo(sysUser.getUnionId());

                        // 2、发公众号消息
                        List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                        if (wechatMpUserList.size() == 0) {
                            log.warn("该用户未关注广州港船务有限公司!");
//                    throw new CustomException("5");
                        } else if (wechatMpUserList.size() == 1) {
                            String accessToken = wechatMpAccessTokenService.getAccessToken();
                            MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                                    .template_id("2osIjsRxtlptlRf8FfQXGCbXIExn9KRSw1NAfDvL52k")
                                    .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing1").toString()).color("#000000").build())
                                    .data("thing2", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing2").toString()).color("#000000").build())
                                    .data("thing3", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing3").toString()).color("#000000").build())
                                    .data("character_string4", MpMessageDTO.MpMessageDataField.builder().value(params.get("character_string4").toString()).color("#000000").build())
                                    .data("character_string5", MpMessageDTO.MpMessageDataField.builder().value(params.get("character_string5").toString()).color("#000000").build())
                                    .build();
                            System.out.println("W H Y");
                            System.out.println(mpMessageDTO);
                            MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                            System.out.println("水路运单修改吨数通知");
                            System.out.println(mpMessageResult);
                        } else {
                            throw new CustomException("数据库数据错误!");
                        }
                    } else {
                        log.warn("该用户没有unionid，发送水路运单修改吨数通知失败！");
//                throw new CustomException("4");
                    }
                }
            }

            return AjaxResult.success(pb6Cargoconsignmentdetail);
        }
        return AjaxResult.error("更新失败");
    }

    @Override
    public AjaxResult deleteCargoconsignmentDeatil(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) {

        // 根据ID查询最新的驳船信息
        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail1 = pb6CargoconsignmentdetailService.getById(pb6Cargoconsignmentdetail.getId());

        // 删除条件，已完结的不允许删除
        if("5".equals(pb6Cargoconsignmentdetail1.getFlagbargestate())){
            return AjaxResult.error("已经完结，不允许删除");
        }

        if(pb6CargoconsignmentdetailService.removeById(pb6Cargoconsignmentdetail1)){
            // 删除成功，删除水路运单
            if(pb6Cargoconsignmentdetail1.getWaterwaycargoid() != null && !"".equals(pb6Cargoconsignmentdetail1.getWaterwaycargoid())){
                if(pb6WaterwaycargoService.removeById(pb6Cargoconsignmentdetail1.getWaterwaycargoid())){
                    return AjaxResult.success(pb6Cargoconsignmentdetail1);
                }
            }
            return AjaxResult.success(pb6Cargoconsignmentdetail1);
        }

        return AjaxResult.error("删除失败,请联系管理员");
    }

    @Override
    public AjaxResult cancelCargoconsignment(List<Long> detailIds) {

        // 作废的驳船列表
        List<Pb6Cargoconsignmentdetail> pb6Cargoconsignmentdetails = pb6CargoconsignmentdetailService.listByIds(detailIds);

        // 遍历托运单，如果状态不是已完结，不允许作废操作
        for(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail : pb6Cargoconsignmentdetails){
            if(!"5".equals(pb6Cargoconsignmentdetail.getFlagbargestate())){
                return AjaxResult.error("只有已完结的驳船才能作废");
            }
        }

        // 作废水路运单
        for(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail : pb6Cargoconsignmentdetails){

            // 关联的水路运单
            Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getById(pb6Cargoconsignmentdetail.getWaterwaycargoid());

            // 1、更新托运单明细的状态为已报道 flagbargestate = 6 wxoperatestate = 5
            pb6Cargoconsignmentdetail.setFlagbargestate("6");
            pb6Cargoconsignmentdetail.setWxoperatestate(5);
            pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);

            // 2、删除水路运单文件
            // 查询uploadAddress表中linkid为水路运单id，linkType为40的记录
            QueryWrapper<UploadAddress> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(UploadAddress::getLinkId, pb6Cargoconsignmentdetail.getWaterwaycargoid());
            queryWrapper.lambda().eq(UploadAddress::getLinkType, 40);
            List<UploadAddress> uploadAddresses = uploadAddressService.list(queryWrapper);
            for(UploadAddress uploadAddress : uploadAddresses){
                // 删除文件,即把linkID置为0，关联公司ID置为原linkID
                uploadAddress.setLinkCompanyId(uploadAddress.getLinkId());
                uploadAddress.setLinkId(0L);
                uploadAddressService.updateById(uploadAddress);
            }

            // 3、发送微信公众号消息
            Map<String, Object> params = new HashMap<>();
            // 船名航次 穗港901/240411  大船船名 凯航发展 运单编号 GS2404110002 作废时间 2024/04/19 16:29
            params.put("thing1", pb6Cargoconsignmentdetail.getBargename() + "/" + pb6Cargoconsignmentdetail.getVoyage());
            params.put("thing2", pb6Waterwaycargo.getShipName());
            params.put("character_string3", pb6Waterwaycargo.getWaterwaycargoid());
            // 作废时间
            Date date = new Date();
            String time = DateUtils.parseDateToStr("yyyy/MM/dd HH:mm", date);
            params.put("time4", time);

            // 发送消息
            List<SysUser> sysUserList = sysUserBargeMapper.queryAllUserByBargeId(pb6Cargoconsignmentdetail.getBargeid());

            if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWxrationcontactnumber())){
                List<SysUser> ration = sysUserMapper.selectCargoConsignPersonByPhone(pb6Cargoconsignmentdetail.getWxrationcontactnumber());
                sysUserList.addAll(ration);
            }

            for(SysUser sysUser : sysUserList){
                // 发送消息
                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(sysUser.getUnionId())) {

                    // 查询前，先根据openId去查询用户信息
                    wechatMpUserService.getUserInfo(sysUser.getUnionId());

                    // 2、发公众号消息
                    List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                    if (wechatMpUserList.size() == 0) {
                        log.warn("该用户未关注广州港船务有限公司!");
//                    throw new CustomException("5");
                    } else if (wechatMpUserList.size() == 1) {
                        String accessToken = wechatMpAccessTokenService.getAccessToken();
                        MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                                .template_id("08plt-fr3DprrSw9xI5eNEwueQ-baFAr0gMYbnth3KM")
                                .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing1").toString()).color("#000000").build())
                                .data("thing2", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing2").toString()).color("#000000").build())
                                .data("character_string3", MpMessageDTO.MpMessageDataField.builder().value(params.get("character_string3").toString()).color("#000000").build())
                                .data("time4", MpMessageDTO.MpMessageDataField.builder().value(params.get("time4").toString()).color("#000000").build())
                                .build();
                        System.out.println("W H Y");
                        System.out.println(mpMessageDTO);
                        MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                        System.out.println("水路运单生成通知");
                        System.out.println(mpMessageResult);
                    } else {
                        throw new CustomException("数据库数据错误!");
                    }
                } else {
                    log.warn("该用户没有unionid，发送作废通知失败！");
//                throw new CustomException("4");
                }
            }


        }

        return AjaxResult.success("作废成功");
    }

    @Override
    public AjaxResult importUpdateWeightExcel(MultipartFile file, Long consignId) throws IOException {

        // 托运单下的驳船信息
        List<Pb6Cargoconsignmentdetail> pb6CargoconsignmentdetailListSelect = shipInfoService.selectByConsignid(consignId);

        // easyExcel导入
        List<UpdateWeightExcel> updateWeightExcelList = EasyExcel.read(file.getInputStream()).
                head(UpdateWeightExcel.class).sheet(0).doReadSync();

        // 遍历导入的数据，如果在驳船信息中，存在相同的船名和航次，则更新配载吨数
        for(UpdateWeightExcel updateWeightExcel : updateWeightExcelList){
            for(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail : pb6CargoconsignmentdetailListSelect){
                if(updateWeightExcel.getBargeName().equals(pb6Cargoconsignmentdetail.getBargename()) &&
                        updateWeightExcel.getVoyage().equals(pb6Cargoconsignmentdetail.getVoyage())){
                    // 如果配载吨数不一样，则更新
                    // 原配载吨数
                    String rationWeightBefore = pb6Cargoconsignmentdetail.getRationweight();
                    // 现配载吨数
                    if(updateWeightExcel.getRationWeight() == null){
                        return AjaxResult.error("配载吨数不能为空");
                    }
                    String rationWeightAfter = updateWeightExcel.getRationWeight().stripTrailingZeros().toString();

                    if(!rationWeightBefore.equals(rationWeightAfter)){
                        pb6Cargoconsignmentdetail.setRationweight(rationWeightAfter);
                        pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
                        // 水路运单对象
                        Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getById(pb6Cargoconsignmentdetail.getWaterwaycargoid());
                        // 改了配载吨数，发送微信公众号消息
                        Map<String, Object> params = new HashMap<>();
                        // 船名航次 穗港901/240411  大船船名 凯航发展 货物名称 铁矿粉 原吨数 现吨数
                        params.put("thing1", pb6Cargoconsignmentdetail.getBargename() + "/" + pb6Cargoconsignmentdetail.getVoyage());
                        params.put("thing2", pb6Waterwaycargo.getShipName());
                        params.put("thing3", pb6Waterwaycargo.getCargename());
                        params.put("character_string4", rationWeightBefore);
                        params.put("character_string5", rationWeightAfter);

                        // 发送消息
                        List<SysUser> sysUserList = sysUserBargeMapper.queryAllUserByBargeId(pb6Cargoconsignmentdetail.getBargeid());

                        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWxrationcontactnumber())){
                            List<SysUser> ration = sysUserMapper.selectCargoConsignPersonByPhone(pb6Cargoconsignmentdetail.getWxrationcontactnumber());
                            sysUserList.addAll(ration);
                        }

                        for(SysUser sysUser : sysUserList){
                            // 发送消息
                            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(sysUser.getUnionId())) {

                                // 查询前，先根据openId去查询用户信息
                                wechatMpUserService.getUserInfo(sysUser.getUnionId());

                                // 2、发公众号消息
                                List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                                if (wechatMpUserList.size() == 0) {
                                    log.warn("该用户未关注广州港船务有限公司!");
//                    throw new CustomException("5");
                                } else if (wechatMpUserList.size() == 1) {
                                    String accessToken = wechatMpAccessTokenService.getAccessToken();
                                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                                            .template_id("2osIjsRxtlptlRf8FfQXGCbXIExn9KRSw1NAfDvL52k")
                                            .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing1").toString()).color("#000000").build())
                                            .data("thing2", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing2").toString()).color("#000000").build())
                                            .data("thing3", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing3").toString()).color("#000000").build())
                                            .data("character_string4", MpMessageDTO.MpMessageDataField.builder().value(params.get("character_string4").toString()).color("#000000").build())
                                            .data("character_string5", MpMessageDTO.MpMessageDataField.builder().value(params.get("character_string5").toString()).color("#000000").build())
                                            .build();
                                    System.out.println("W H Y");
                                    System.out.println(mpMessageDTO);
                                    MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                                    System.out.println("水路运单修改吨数通知");
                                    System.out.println(mpMessageResult);
                                } else {
                                    throw new CustomException("数据库数据错误!");
                                }
                            } else {
                                log.warn("该用户没有unionid，发送水路运单修改吨数通知失败！");
//                throw new CustomException("4");
                            }
                        }
                    }
                }
            }
        }

        return AjaxResult.success("导入成功");
    }

    public Pb6Waterwaycargo getPb6Waterwaycargo(Pb6Cargoconsignment pb6Cargoconsignment, Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail, Pb6Bargeinfo pb6Bargeinfo, ShipInfo shipInfo,String nickName) {
        // 4、符合备案条件，生成水路运单
        // TODO
        // 1、生成水路运单
        Pb6Waterwaycargo pb6Waterwaycargo = new Pb6Waterwaycargo();
        // 船名 航次 起运港 目的港 费用结算方式 托运人 收货人 货名 包装方式 重量 金额 结算人
        pb6Waterwaycargo.setBargeName(pb6Cargoconsignmentdetail.getBargename());
        pb6Waterwaycargo.setBargeNumber(pb6Cargoconsignmentdetail.getVoyage());
        pb6Waterwaycargo.setBeginPort(pb6Cargoconsignment.getBeginport());
        pb6Waterwaycargo.setEndPort(pb6Cargoconsignment.getEndport());
        pb6Waterwaycargo.setCargosize(pb6Cargoconsignment.getSettlementCompany());
        // 大船名，大船航次
        pb6Waterwaycargo.setShipName(shipInfo.getShipName());
        pb6Waterwaycargo.setShipNumber(shipInfo.getVoyageNumber());
        // 费用结算方式 ，存的是字典的value，需要转换成字典的label
        String settlementMethod = sysDictDataService.selectDictLabel("settlement_method", shipInfo.getSettlementMethod());
        pb6Waterwaycargo.setChargebalancetype(settlementMethod);
        pb6Waterwaycargo.setConsigner(pb6Cargoconsignment.getConsigner());
        pb6Waterwaycargo.setConsignee(pb6Cargoconsignment.getConsignee());
        pb6Waterwaycargo.setCargename(pb6Cargoconsignment.getCargename());
        // 包装方式，存的是字典的value，需要转换成字典的label
        String packingMethod = sysDictDataService.selectDictLabel("packing_method", shipInfo.getPackingMethod());
        pb6Waterwaycargo.setPackagetype(packingMethod);
        pb6Waterwaycargo.setRationweight(pb6Cargoconsignmentdetail.getRationweight());
        // 特约事项
        pb6Waterwaycargo.setSpecialNotice(pb6Cargoconsignmentdetail.getSpecialNotice());
        // 水路货物运单编号
        pb6Waterwaycargo.setWaterwaycargoid(sSerialService.getSerialNumberByCode("waterwayCode"));

        if(nickName != null && !"".equals(nickName)){
            pb6Waterwaycargo.setRationprincipal(nickName);
        }else {
            SysUser loginUser = SecurityUtils.getLoginUser().getUser();
            // 配载人 配载时间
            pb6Waterwaycargo.setRationprincipal(loginUser.getNickName());
        }
        // 配载日期 当前时间格式化为yyyy-MM-dd HH:mm:ss
        // 获取当前时间
        Date date = new Date();
        // 格式化时间
        String time = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", date);
        pb6Waterwaycargo.setRationdate(time);

        // 业务代理费率为托运单
        pb6Waterwaycargo.setBusinessagentchargerate(pb6Cargoconsignment.getRate().toString());

        // 金额 业务代理费 等于配载重量乘以业务代理费率
        BigDecimal cost = pb6Cargoconsignment.getRate().multiply(new BigDecimal(pb6Cargoconsignmentdetail.getRationweight()));

        pb6Waterwaycargo.setBusinessagentcharge(cost.toString());

        // 总计 = 业务代理费
        pb6Waterwaycargo.setTotalcharge(cost.toString());



        // 不为空的  驳船基本信息主键
        pb6Waterwaycargo.setBargeidid(pb6Bargeinfo.getId());

        // 生成水路运单（确认实装时再生成）,先保存
        if(pb6WaterwaycargoService.save(pb6Waterwaycargo)){
            // 发送运单生成消息
            this.sendNewWaterWayMessage(pb6Waterwaycargo,pb6Cargoconsignmentdetail);
        }
        return pb6Waterwaycargo;
    }

    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    @Autowired
    private WechatMpUserService wechatMpUserService;

    @Autowired
    SysUserMapper sysUserMapper;

    @Autowired
    private WechatMpAccessTokenService wechatMpAccessTokenService;

    @Autowired
    private MpUtil mpUtil;


    // 新运单生成通知，发送公众号消息
    public void sendNewWaterWayMessage(Pb6Waterwaycargo pb6Waterwaycargo,Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) {
        Map<String, Object> params = new HashMap<>();
        // 船名航次
        String bargeVoyage = pb6Waterwaycargo.getBargeName() +"/" + pb6Waterwaycargo.getBargeNumber();
        params.put("thing1",bargeVoyage);
        // 大船船名
        params.put("thing2",pb6Waterwaycargo.getShipName());
        // 货名
        params.put("thing3",pb6Waterwaycargo.getCargename());
        // 配载吨数
        params.put("character_string4",pb6Waterwaycargo.getRationweight());

        List<SysUser> sysUserList = sysUserBargeMapper.queryAllUserByBargeId(pb6Cargoconsignmentdetail.getBargeid());

        if(StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWxrationcontactnumber())){
            List<SysUser> ration = sysUserMapper.selectCargoConsignPersonByPhone(pb6Cargoconsignmentdetail.getWxrationcontactnumber());
            sysUserList.addAll(ration);
        }

        for(SysUser sysUser: sysUserList){

            if (StringUtils.isNotBlank(sysUser.getUnionId())) {

                // 查询前，先根据openId去查询用户信息
                wechatMpUserService.getUserInfo(sysUser.getUnionId());

                // 2、发公众号消息
                List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                if (wechatMpUserList.size() == 0) {
                    log.warn("该用户未关注广州港船务有限公司!");
//                    throw new CustomException("5");
                } else if (wechatMpUserList.size() == 1) {
                    String accessToken = wechatMpAccessTokenService.getAccessToken();
                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                            .template_id("7--a-EKbPEpZxPWO3V_6NXlTU3wKX5qJ0yUHszR-x2U")
                            .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing1").toString()).color("#000000").build())
                            .data("thing2", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing2").toString()).color("#000000").build())
                            .data("thing3", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing3").toString()).color("#000000").build())
                            .data("character_string4", MpMessageDTO.MpMessageDataField.builder().value(params.get("character_string4").toString()).color("#000000").build())
                            .build();
                    System.out.println("W H Y");
                    System.out.println(mpMessageDTO);
                    MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                    System.out.println("");
                    System.out.println(mpMessageResult);
                } else {
                    throw new CustomException("数据库数据错误!");
                }
            } else {
                log.warn("该用户没有unionid，发送实装数失败！");
//                throw new CustomException("4");
            }
        }
    }

}

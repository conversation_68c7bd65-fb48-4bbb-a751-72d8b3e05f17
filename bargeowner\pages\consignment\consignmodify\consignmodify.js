// pages/consignment/consignmodify/consignmodify.js
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //预约时间
    appointmentTime:"",
    //预约日期
    appointmentDate:"",
    //当前托运对象
    currentConsignObj:{},
    //表单
    form:{},
    //比较前后修改使用
    compareOldForm:{},
    showSearchDialog:false,//是否显示查找月结单位的弹窗
    //选中的月结单位数组
    selectedProjectsArray:[],
    //是否显示批量下载弹窗
    batchDialog:false,
    //用户邮箱 
    userEmail:"",
    //批量下载按钮
    batchButtons:[{text: '取消'}, {text: '确定'}],

  },

   /**
 * 
 * 方法
 */
  /**
 * 判断是否存在，不存在返回空字符串或其他
 * @param {传进来的变量} empty 
 * @param {为空时返回的值} replace 
 */
emptyReturn(empty,replace){
  if(!replace || replace==null){
    replace = ""
  }
  if(empty == null || !empty){
    return replace
  }else{
    return empty
  }
},

  /**
   *  获取水路运单资料
   * 参数：
   * waterwayCargoId 水路运单编号
     type 文件类型 1-广州港新沙港务有限公司驳船装货交接凭证.docx 2-货物交接清单.docx 3-水路货物运单.pdf
   */
  getSeaWayBill(e) {
    let item = e.currentTarget.dataset.item 
    let param = {
      type: item.type,
      waterwayCargoId: this.data.form.waterWayCargoId,
    }
    if(!param.waterwayCargoId) {
      app.$message('水路运单未生成，暂无可预览文件');
    }
    app.$downLoad(app.$url.consign.getPdfOrWord, param).then((res) => {
      if(res.byteLength <= 100) {
        let uint8_msg = new Uint8Array(res);
        let decodedString = JSON.parse(String.fromCharCode.apply(null, uint8_msg));
        app.$message(`预览文件失败，原因${decodedString.msg}`);
        return
      }
      const fs = wx.getFileSystemManager(); // 获取全局唯一的文件管理器
      // 写文件
      fs.writeFile({
        filePath: wx.env.USER_DATA_PATH + `/${item.dataName}`, //写入的文件路径 (本地路径)
        data: res, // 写入的文本或二进制数据
        encoding: "binary", // 文件的字符编码
        success(res) {
          wx.openDocument({
            filePath: wx.env.USER_DATA_PATH + `/${item.dataName}`,  //拿上面存入的文件路径
            success: function (res) {
              console.log(res);
            }
          })
        },
      })
    })

  
  
  },

 //出库记录查询
 outBoundJump(){
  wx.navigateTo({
    url: "/pages/consignment/bargeOutboundInquiry/bargeOutboundInquiry",
  })
},

//邮箱input
emailInput(e){
  this.setData({
    userEmail:e.detail.value
  })
},

//点击批量下载弹窗按钮
tapBatchDialogButton(e){
  // 点击取消按钮
  if(e.detail.index == 0) {
  }else {
    // 点击确定按钮-发送邮件 //参数
    let param = {
      toEmail:this.data.userEmail,
      waterwayCargoId: this.data.form.waterWayCargoId,
    }
    //邮箱校验格式
    let emailReg = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    let email = param.toEmail //邮箱
    if(!email){
      app.$message("请输入邮箱")
      return
    }
    if(email && !emailReg.test(email)) {
      app.$message("你输入的邮箱格式不正确!")
      return;
    } 
    app.$post(app.$url.consign.sendEmail,param).then(res=>{
      if(res.code == 200){
        app.$message(res.msg)
      }else {
        app.$message(res.msg)
      }
    })
  }
  this.setData({
    batchDialog:false
  })
},

//批量下载
batchDown(){
  this.setData({
    batchDialog:true
  })
},

//托运单联系人
wxRationContactNumberInput(e){
  this.setData({
    "form.wxRationContactNumber":e.detail.value
  })
},

//关闭搜索月结单位弹窗,获取子组件选中的值
closeSearchDialog(e){
  let that = this
  let selectedArr = e.detail.selectedArr //获取子组件选中月结单位的数组
  if(selectedArr && selectedArr.length>0){
    that.setData({
      "form.companyId":selectedArr[0].id, // 公司id
      "form.companyName":selectedArr[0].cfullName, // 公司名称
      "form.wxMonthChargeByName":selectedArr[0].cfullName,//月结单位
    })
  }else{
    that.setData({
      "form.companyId":"", // 公司id
      "form.companyName":"", // 公司名称
      "form.wxMonthChargeByName":"",//月结单位
    })
  }
  that.setData({
    showSearchDialog : !that.data.showSearchDialog,
    selectedProjectsArray:selectedArr,
  })
 
 
},


// 点击确定
handleConfirm() { 
  let wxAppOintmentTime = this.data.appointmentDate + " " + this.data.appointmentTime//预约时间
  let {consignDetailId,wxRationContactNumber,rationWeight,bargeTel,wxOperateState,chargeBalanceType,companyId,companyName} = this.data.form
  // rationWeight 后端返回的托运单重量单位为kg,前端显示为吨，需转换
  rationWeight = rationWeight* 1000
  //参数
  let param = {
    id:this.data.currentConsignObj.consignDetailId, //运单详情id 必填
    modifyReason:"", // 退单原因 必填
    wxRationContactNumber,//托运单联系人 (托运单联系人就是一个电话号码) 改单必填
    rationWeight,// 托运单重量 改单必填
    state:"2", // 1-退单，2-改单
    bargeTel, // 驳船主联系电话
    wxOperateState,// 状态
    chargeBalanceType,// 支付方式 0-月结，1-现结 改单必填
    companyId, // 船公司id
    companyName, // 船公司名称
    wxAppOintmentTime,//预约时间
    updateRationContactNumber:""// 是否只修改托运单联系人 true-是 false-不是
  }


  if(!param.id){
    app.$message("运单详情id参数不能为空")
    return
  }
  if(!param.rationWeight){
    app.$message("请输入托运单配载重量")
    return
  }
 
  //托运单联系人
  if(!param.wxRationContactNumber){
    app.$message("请输入托运单联系人")
    return
  }
  //手机校验
  let phoneReg = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/
  if(param.wxRationContactNumber && !phoneReg.test(param.wxRationContactNumber)){
    app.$message("托运单联系人输入格式不正确")
    return;
  }
  if(!param.bargeTel){
    app.$message("请输入驳船主联系电话")
    return
  }
  if(param.bargeTel && !phoneReg.test(param.bargeTel)){
    app.$message("驳船主联系电话输入格式不正确")
    return;
  }
  if(param.chargeBalanceType !=='0' && param.chargeBalanceType != 1){
    app.$message("请选择费用结算方式")
    return
  }

  //结算方式切换-选中公司月结，月结单位输入框才会显示，点击现结会消失
  //现结
  if(param.chargeBalanceType == "1"){
    param.companyId = ""
    param.companyName = ""
  }else if(param.chargeBalanceType === "0"){
  //月结-必须选择月结单位
    if(this.data.selectedProjectsArray && this.data.selectedProjectsArray.length == 0){
      app.$message("请选择月结单位")
      return
    }
    
  }

  if(!this.data.appointmentDate){
    app.$message("请选择预约日期")
    return
  }
  if(!this.data.appointmentTime){
    app.$message("请选择预约时间")
    return
  }


  //修改前后数据对比-已修改的数组
  let modifyArr = []
  //需要判断是否只修改wxRationContactNumber：只修改wxRationContactNumber，updateRationContactNumber传true,否则updateRationContactNumber传false
  for(let key in this.data.compareOldForm){
    let obj = {}
    for(let key1 in param){
      if(key === key1 && (key == "rationWeight" || key == "wxRationContactNumber" || key == "bargeTel" || key == "companyId" || key ==  "wxAppOintmentTime" || key=="chargeBalanceType")&& this.data.compareOldForm[key] != param[key1]){
        obj = {[key]:this.data.compareOldForm[key]+"-"+param[key1]}
        modifyArr.push(obj)
      }
    }
  }
  if(modifyArr.length > 1){
    param.updateRationContactNumber = false
   
  }else if(modifyArr.length == 1){
  //已修改字段数组为1时，判断是否是该wxRationContactNumber
    modifyArr.map(each=>{
      for(let key3 in each){
        if(key3 == "wxRationContactNumber"){
          param.updateRationContactNumber = true
        }
      }
    })
  
  }
    

  //结算方式切换-选中公司月结，月结单位输入框才会显示，点击现结会消失
  //现结
  if(param.chargeBalanceType == "1"){
    param.companyId = ""
    param.companyName = ""
  }else if(param.chargeBalanceType === "0"){
  //月结-必须选择月结单位
    if(!param.companyId){
      app.$message("请选择月结单位")
      return
    }
    
  }


  app.$post(app.$url.consign.chargeback,param).then(res=>{
    if(res.code == 200){
      app.$message(res.msg)
      wx.navigateBack({
        delta: 1
      });
    }else{
      app.$message(res.msg)
    }
  })
  
},

//获取详情
getDetailData(){
  if(!this.data.currentConsignObj.consignDetailId){
    app.$message("详情参数id为空")
    return
  }
  //运单详情id
  app.$post(app.$url.consign.getConsignDetail,{id:this.data.currentConsignObj.consignDetailId}).then(res=>{
    if(res.code == 200){
      if(res.data){
        let appointmentDate = ""
        let appointmentTime = ""
        //校验null值
        for(let key in res.data){
          if(key !=  "wxOperateState"){
            res.data[key] = this.emptyReturn(res.data[key])
          }
          if(key == "wxAppOintmentTime" && res.data[key].length>0){
            appointmentDate = res.data[key].slice(0,10)
            appointmentTime = res.data[key].slice(12)
          }
        }
        this.setData({
          appointmentDate,
          appointmentTime
        })
        

        /**
         *  1、已支付才给展示水路运单文件，“待确认、待支付”状态下不展示文件。
            2、“待预约、待报道、已报到”这三个状态就展示一个文件：“水路运单”
            3、“已完成”状态下，展示3个文件：“水路运单、驳船交接凭证、驳船交接清单”
          */
        
        // 固定写死文件列表
        let dataList = [{
          type: 1,
          dataName: "广州港新沙港务有限公司驳船装货交接凭证.pdf",
        },{
          type: 2,
          dataName: "货物交接清单.pdf",
        },{
          type: 3,
          dataName: "水路货物运单.pdf",
        },]
        let resultDataList = [] //过滤后的文件列表

        let wxOperateStateText = this.data.currentConsignObj.wxOperateStateText //订单状态
        dataList.map(item=>{
          if(item.type == 3 && (wxOperateStateText == "待预约" || wxOperateStateText == "待报到" || wxOperateStateText == "已报到")) {
            resultDataList.push(item)
          }else if(wxOperateStateText == "已完成") {
            resultDataList.push(item)
          }
        })
        res.data.dataList = resultDataList

        
        /**
         * 支付、改单参数companyId和companyName：
         *  如果没用重新选择月结单位，则获取的是托运单
            详情接口的wxMonthChargeById和wxMonthChargeByName的值，
            如果重新选择月结单位，则获取选中的id和cfullName的值
         */

        //初始化判断wxMonthChargeById是否为空,有值放进月结单位选中数组selectedProjectsArray； wxMonthChargeById-月结公司id ,wxMonthChargeByName-月结公司名称  companyId,companyName
        this.setData({
          form:{
            ...res.data,
            companyId:res.data.wxMonthChargeById,
            companyName:res.data.wxMonthChargeByName,
          }
        })
        this.setData({
          compareOldForm:{...this.data.form} //比较前后修改使用
        })
        if(res.data.wxMonthChargeById){
          let selectedProjectsArray = [{id:res.data.wxMonthChargeById,cfullName:res.data.wxMonthChargeByName}]
          this.setData({
            selectedProjectsArray,
          })
        }
  

      }
    }else {
    app.$message(res.msg)
    }
  })
},

//托运单配载重量input
rationWeightInput(e){
  this.setData({
    "form.rationWeight":e.detail.value
  })
},
rationPieceInput(e){
    this.setData({
        "form.rationPiece":e.detail.value
      })
},

//驳船主联系电话input
bargeTelInput(e){
  this.setData({
    "form.bargeTel":e.detail.value
  })
},

// 结算方式切换-选中公司月结，月结单位输入框才会显示，点击现结会消失
radioChange(e){
  this.setData({
    "form.chargeBalanceType":e.detail.value
  })
},

//月结单位input
wxMonthChargeByNameInput(e){
  this.setData({
    showSearchDialog : !this.data.showSearchDialog,
  })
},

//预约时间input
// wxAppointmentTimeInput(e){
//   this.setData({
//     "form.wxAppointmentTime":e.detail.value
//   })
// },

// 点击日期改变
bindDateChange(e) {
  this.setData({
    appointmentDate: e.detail.value
  })
  
},

// 点击时间改变
bindTimeChange(e) {
  this.setData({
    appointmentTime: e.detail.value + ":00"
  })
  
},



  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.currentConsignObj != "{}"){
      let currentConsignObj = JSON.parse(options.currentConsignObj)
      this.setData({
        currentConsignObj,
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getDetailData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
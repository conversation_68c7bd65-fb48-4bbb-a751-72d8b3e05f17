package com.ruoyi.common.domain;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 运单实体
 * <AUTHOR>
 * @Date 2020/8/4 14:51
 */
@Setter
@Getter
@ToString
@TableName(value = "PB6_CARGOCONSIGNMENT_BAK")
public class CargoconsignmentBak {

    @TableId(value = "ID")
    private Long id;
    @TableField("FLAGVERIFICATION")
    private String flagVerification;
    @TableField("APPLYDATE")
    private String applyDate;
    @TableField("BARGENAME")
    private String bargeName;
    @TableField("BEGINPORT")
    private String beginPort;
    @TableField("CARGENAME")
    private String cargeName;
    @TableField("COMID")
    private Long comId;
    @TableField("CONSIGNFLAG")
    private String consignFlag;
    @TableField("CONSIGNEE")
    private String consignee;
    @TableField("CONSIGNER")
    private String consigner;
    @TableField("CONSIGNMENTFLAG")
    private String consignmentFlag;
    @TableField("ENDPORT")
    private String endPort;
    @TableField("FORMTIME")
    private String formTime;
    @TableField("LOADOMETERID")
    private String loadometerId;
    @TableField("MIDPORT")
    private String midPort;
    @TableField("MODIFYDATE")
    private String modifyDate;
    @TableField("MODIFYMAN")
    private String modifyMan;
    @TableField("MODIFYMANID")
    private Long modifyManId;
    @TableField("OUTORINFORMID")
    private String outOrInformId;
    @TableField("PACKAGETYPE")
    private String packageType;
    @TableField("RATIONPIECE")
    private String rationPiece;
    @TableField("RATIONWEIGHT")
    private String rationWeight;
    @TableField("SHIPMENTPLACE")
    private String shipmentPlace;
    @TableField("SHIPMENTUNPLACE")
    private String shipmentunPlace;
    @TableField("SHIPPINGCONAME")
    private String shippingCoName;
    @TableField("SPECIALPROCEEDING")
    private String specialProceeding;
    @TableField("SSTYLETYPE")
    private String sstyleType;
    @TableField("SHIPPERDEPT")
    private String shipperDept;
    @TableField("FLAGCOUTFORM")
    private String flagCoutform;
    @TableField("CHECKMAN")
    private String checkMan;
    @TableField("CHECKTIME")
    private String checkTime;
    @TableField("WORKTYPE")
    private String workType;
    @TableField("ONINERESOURSE")
    private String onineresourse;
    @TableField("WATERWAYCARGOID")
    private String waterwayCargoId;
    @TableField("CHARGEBALANCETYPE")
    private String chargeBalanceType;

    /**
     * 派船公司id
     */
    @TableField("WXSELECTSHIPBYID")
    private Long wxSelectShipById;
    /**
     * 派船公司名称
     */
    @TableField("WXSELECTSHIPBYNAME")
    private String wxSelectShipByName;
    /**
     * 派船状态(0：待派船，1：以派船)
     */
    @TableField("WXSELECTSHIPSTATE")
    private Integer wxSelectShipState;
    /**
     * 派船类型(货主办理托运单时)(0:货主派船 1：船公司派船)
     */
    @TableField("WXSELECTSHIPTYPE")
    private Integer wxSelectShipType;
    /**
     * 出库单类型1.本司2.它司
     */
    @TableField("WXOUTORINFORMTYPE")
    private Integer wxOutorInformType;
    /**
     * 办单类型1-货主；   2-船公司
     */
    @TableField("WXAPPLYUSERTYPE")
    private Integer wxApplyUserType;
    /**
     * 创建人id
     */
    @TableField("WXCREATEUSERBYID")
    private Integer wxCreateUserById;

    /**
     * 创建人所属公司id
     */
    @TableField("WXCREATECOMPANYID")
    private Long wxCreateCompanyId;

    /**
     * 创建时间
     */
    @TableField("WXCREATETIME")
    private String wxCreateTime;

    /**
     *  其他 沿海/内河 港口（2021-02-07添加）
     */
    @TableField("OTHERCOASTALINLANDPORT")
    private String otherCoastalInlandPort;

}

package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 港口表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PUB_PORT")
public class PubPort implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId("ID")
    private Long id;

    /**
     * 港口数字编码
     */
    @TableField("PORTID")
    private String portid;

    /**
     * 港口英文名
     */
    @TableField("PORTENAME")
    private String portename;

    /**
     * 港口中文名
     */
    @TableField("PORTCNAME")
    private String portcname;

    /**
     * 港口运输类型代码
     */
    @TableField("PORTTYPE")
    private Integer porttype;

    /**
     * 所属省份
     */
    @TableField("PROVINCE")
    private Long province;

    /**
     * 所属国籍英文简称
     */
    @TableField("COUNTRYID")
    private Long countryid;

    /**
     * 港口字符代码
     */
    @TableField("PORTNUM")
    private String portnum;

    /**
     * 港口名称拼音首字母
     */
    @TableField("PYCAP")
    private String pycap;

    /**
     * 是否可用
     */
    @TableField("ISACTIVE")
    private String isactive;

    /**
     * 港口所属国家
     */
    @TableField("COUNTRYNAME")
    private String countryname;

    /**
     * 统计锁死
     */
    @TableField("TJLOCK")
    private String tjlock;

    /**
     * 航区： A级、B级、null
     */
    @TableField("NAVIGATINGZONE")
    private String navigatingzone;

    /**
     * 是否属于物流公司： 0否，1是
     */
    @TableField("BELONGWLCOMPANY")
    private Integer belongwlcompany;

    /**
     * 海事编码 (物流公司)
     */
    @TableField("MARITIMECODE")
    private String maritimecode;

    /**
     * 所属海事一级编码 (物流公司)
     */
    @TableField("FIRSTMARITIMECODE")
    private String firstmaritimecode;
}

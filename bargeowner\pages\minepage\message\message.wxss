/* pages/minepage/message/message.wxss */
.message-page{
  padding-top: 200rpx;
}
.message-list{
  padding:0 20rpx;
}
.message-search{
  height: 200rpx;
  position: fixed;
  top: 0rpx;
  right: 0rpx;
  left: 0rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  background: #fff;
  z-index: 100;
  padding: 0 20rpx;
}
.search-ipt{
  height: 70rpx;
  line-height: 66rpx;
  flex:1;
  font-size: 28rpx;
}
.date-select{
  height: 70rpx;
  display: flex;
  flex-direction: row;
  width: 690rpx;
  align-items: center;
  justify-content: space-between;
}
.date-view{
  position: relative;
  height: 70rpx;
  border: 1px solid #ddd;
  width: 280rpx;
  border-radius: 10rpx;
}
.date-ipt{
  height: 66rpx;
  line-height: 66rpx;
  width: 240rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
}
.date-range{
  color: #ddd;
  padding: 0 5rpx;
}
.date-select .search-btn{
  height: 60rpx;
  margin: 0rpx;
  width: 60rpx;
  padding: 0rpx;
  color: #fff;
  font-size: 32rpx;
  line-height: 60rpx;
  background: #00426B;
}
.message-item{
  max-height: 240rpx;
  margin: 30rpx 0rpx;
  border: 1px solid #ddd;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 20rpx;
  padding: 0 20rpx;
  position: relative;
}
.notice-icon{
  font-size: 40rpx;
  color: #00426B;
}
.message-content{
  max-width: 90%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10rpx;
 
}
.detail-icon{
  flex-shrink: 0;
}
.message-content-title{
  flex-shrink: 0;
  height: 50rpx;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding: 5rpx 20rpx;
}
.message-content-text{
  flex-shrink: 0;
  max-height:130rpx;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding: 0rpx 20rpx;
  line-height: 35rpx;

}
.message-content-date{
  flex-shrink: 0;
  height: 50rpx;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding: 5rpx 20rpx;
}
.read-status{
  flex-shrink: 0;
  width:15rpx;
  height: 15rpx;
  background: #ff0000;
  border-radius: 50%;
  margin:0 3rpx;
}
.search-ipt-wrap{
  flex-shrink: 0;
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.close-icon{
  flex-shrink: 0;
  width: 40rpx;
  text-align: center;
  height: 70rpx;
  line-height: 70rpx;
  color: #ddd;
}
.close-iconp{
  flex-shrink: 0;
  position: absolute;
  right: 6rpx;
  top: 15rpx;
  width: 40rpx;
  height:auto;
  color: #ddd;
}
.search-data-box{
  position: relative;
  height: 70rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  flex-shrink: 0;
  flex: 1;
  display: flex;
  align-items: center;
}
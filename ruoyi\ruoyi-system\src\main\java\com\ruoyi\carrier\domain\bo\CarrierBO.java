package com.ruoyi.carrier.domain.bo;

import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2020/8/18 10:13
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CarrierBO {

    /**
     * 账号名
     */
    private String account;
    /**
     * 密码
     */
    //@NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 角色类型（0-未知，3-管理员，4-业务人员）
     */
    @NotNull(message = "角色类型不能为空")
    private Integer roleType;

    /**
     * 真实姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String realName;

    /**
     * 身份证
     */
    @NotBlank(message = "身份证不能为空")
    @Pattern(regexp = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$|^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|X)$", message = "身份证格式不正确")
    private String cardId;

    /**
     * 电话
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 船公司id
     */
    @NotNull(message = "船公司id不能为空")
    private Long companyId;

    @NotBlank(message = "船公司名称不能为空")
    private String companyName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 微信号
     */
    private String weChatNumber;

    private String comId;
    /**
     * 印章文件上传
     */
    private String sealfileBase64;


}

package com.ruoyi.common.enums;

/**
 * 小程序托运单操作状态
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/7 15:25
 */
public enum WxOperateStatus {

    /**
     * 0:待驳船主确认 1:待支付  2:待船公司审批(月结) 3船公司审批不通过(月结) 4:待驳船主预约
     * 5:驳船主已预约 6:取消预约 7:待审核(退单改单) 8:审核通过(退单改单) 9:审核不通过(退单改单)
     */

    WAIT_BARGE_CONFIRM(0, "待驳船主确认"),

    WAIT_PAY(1, "待支付"),

    WAIT_COMPANY_CHECK(2, "待船公司审批(月结)"),

    COMPANY_FAIL_CHECK(3, "船公司审批不通过(月结)"),

    WAIT_BARGE_RESERVE(4, "待驳船主预约"),

    PASS_BARGE_RESERVE(5, "驳船主已预约"),

    CANCEL_RESERVE(6, "取消预约"),

    CHARGEBACK_WAIT_CHECK(7, "待审核(退单改单)"),

    CHARGEBACK_PASS_CHECK(8, "审核通过(退单改单)"),

    CHARGEBACK_FAIL_CHECK(9, "审核不通过(退单改单)");

    private final Integer code;

    private final String codeName;

    WxOperateStatus(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.databarge.domain.Pb1Customer;
import com.ruoyi.databarge.domain.dto.Pb1CustomerSearchDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户基本资料 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-10
 */
public interface Pb1CustomerMapper extends BaseMapper<Pb1Customer> {

    IPage<Pb1Customer> searchForPage(Page<Pb1Customer> page, @Param("pb1CustomerSearchDTO") Pb1CustomerSearchDTO pb1CustomerSearchDTO);

    String searchMaxCustomerId(@Param("preString") String preString);

    List<Pb1Customer> searchPb1CustomerByName(@Param("cfullname") String cfullname);
}

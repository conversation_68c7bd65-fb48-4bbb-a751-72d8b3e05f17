// pages/agentdelivery/query/query.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    searchValue: "",
    listData: []
  },

  //关键字input 实时搜索
  searchValueInput(e){
    this.setData({
      searchValue: e.detail.value
    })
    if(e.detail.value.length > 3){
      setTimeout(()=>{
        this.getSupplyData()
      },1500)
    }
  },

  //清空关键字
  deleteSearchValue(){
    this.setData({
      searchValue: ""
    })
    console.log("点击了")
  },

  //查询代理发货
  getSupplyData(){
    app.$post(app.$url.agentDelivery.list, {"searchValue": this.data.searchValue}).then(res=>{
      console.log(res)
      this.setData({
        listData: res.data
      })
    })
  },

  //上传代理
  uploadAgent(){
    wx.navigateTo({
      url: '../update/update',
    })
  },

  //点击修改
  updateAgentDelivery(e){
    wx.navigateTo({
      url: '../update/update',
      success: function (res) {
          res.eventChannel.emit('acceptDataFromOpenerPage', e.currentTarget.dataset.item)
      },
      fail: function (res) {
          console.log(res)
      }
  })
  },
  //点击删除
  deleteAgentDelivery(e){
    let _this = this
    wx.showModal({
      title: '警告',
      content: '确定删除代理发货信息吗',
      success (res) {
        if (res.confirm) {
          app.$post(app.$url.agentDelivery.delete, e.currentTarget.dataset.item.id).then(res=>{
            app.$message("删除成功")
            _this.getSupplyData()
          })
        } else if (res.cancel) {
          app.$message("取消删除")
          console.log('用户点击取消')
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getSupplyData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.getSupplyData()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
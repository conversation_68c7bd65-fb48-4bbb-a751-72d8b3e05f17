package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.databarge.domain.AgentDelivery;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Description:
 *
 * @Author: <PERSON><PERSON>in on 2021/7/13.
 * @Date: 2021/7/13 9:48
 */
public interface AgentDeliveryMapper extends BaseMapper<AgentDelivery> {

    @Select("select b.bargename from sys_user_barge t \n" +
            "left join pb6_bargeinfo b\n" +
            "on t.barge_id = b.id\n" +
            "where t.user_id=#{userId}\n" +
            "and rownum = 1")
    String selectUserBargeName(@Param("userId") Long userId);
}

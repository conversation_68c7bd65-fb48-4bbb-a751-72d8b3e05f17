package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.databarge.domain.Pb30OlUser;
import com.ruoyi.databarge.domain.dto.Pb30OlUserSearchDTO;
import com.ruoyi.databarge.mapper.Pb30OlUserMapper;
import com.ruoyi.databarge.service.Pb30OlUserService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/11/16.
 * @Date: 2020/11/16 8:36
 */
@Service
public class Pb30OlUserServiceImpl extends ServiceImpl<Pb30OlUserMapper, Pb30OlUser> implements Pb30OlUserService {

    @Override
    public IPage<Pb30OlUser> searchCheckedBargeCompany(Pb30OlUserSearchDTO pb30OlUserSearchDTO) {
        Long pageNum= Optional.ofNullable(pb30OlUserSearchDTO.getPageNum()).orElse(0L);
        Long pageSize= Optional.ofNullable(pb30OlUserSearchDTO.getPageSize()).orElse(-1L);
        return super.baseMapper.searchCheckedBargeCompany(new Page<>(pageNum, pageSize), pb30OlUserSearchDTO.getCompany());
    }

    @Override
    public IPage<Pb30OlUser> searchNotCheckBargeCompany(Pb30OlUserSearchDTO pb30OlUserSearchDTO) {
        Long pageNum= Optional.ofNullable(pb30OlUserSearchDTO.getPageNum()).orElse(0L);
        Long pageSize= Optional.ofNullable(pb30OlUserSearchDTO.getPageSize()).orElse(-1L);
        return super.baseMapper.searchNotCheckBargeCompany(new Page<>(pageNum, pageSize), pb30OlUserSearchDTO.getCompany());
    }

    @Override
    public List<Pb30OlUser> searchAllClerksByCompanyId(Long companyId) {
        return super.baseMapper.searchAllClerksByCompanyId(companyId);
    }
}

package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.databarge.domain.Pb6Bargeinfo;
import com.ruoyi.databarge.domain.PubPort;
import com.ruoyi.databarge.domain.dto.CountryDTO;
import com.ruoyi.databarge.domain.dto.PubPortSearchDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * ??? Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
public interface PubPortMapper extends BaseMapper<PubPort> {
    IPage<PubPort> searchPageByNameAndCountry(Page<Pb6Bargeinfo> page, @Param("pubPortSearchDTO") PubPortSearchDTO pubPortSearchDTO);
    List<CountryDTO> searchCountryByCName(@Param("countrycname") String countrycname);

    List<PubPort> searchPortByName(@Param("searchName")String searchName);

    @Select("select PORTCNAME from PUB_PORT where ID=#{id}")
    public String getPortcnameById(Long id);
}

package com.ruoyi.common.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.bo.UploadBO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/29 16:47
 */
public interface FtpService {

    /**
     * 文件上传
     * @param file
     * @param uploadBO
     * @return
     */
    AjaxResult ftpUpload(MultipartFile file, UploadBO uploadBO);

    /**
     * 下载
     * @param url
     * @param response
     * @param request
     */
    void download(String url, HttpServletResponse response, HttpServletRequest request);

    /**
     * web端上传
     * @param file
     * @param userName
     * @return
     */
    AjaxResult webUpload(MultipartFile file, String userName);

    /**
     * 印章上传
     * @param sealImg base64图片
     * @return
     */
    String selaUpload(String sealImg);

    /**
     * 保存告知书等文件
     * @param fileList
     * @param waterwayCargoId 水路运单编号
     * @return
     */
    AjaxResult saveNoticeFile(List<HashMap<String, Object>> fileList, String waterwayCargoId, Long userId, Long companyId, String contractNo);

    /**
     * 保存水路运单等文件
     * @param fileList
     * @param waterwayCargoId 水路运单编号
     * @return
     */
    AjaxResult saveCargoFile(List<HashMap<String, Object>> fileList, String waterwayCargoId, Long userId, Long companyId, String contractNo);

    /**
     * 下载水路运单等文件
     * @param url
     * @param response
     * @param request
     */
    void downloadCargoFile(String url, HttpServletResponse response, HttpServletRequest request);

    /**
     * 下载水路运单等文件
     * @param url
     */
    ByteArrayOutputStream downloadCargoFile(String url);
}

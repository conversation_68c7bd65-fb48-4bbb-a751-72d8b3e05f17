package com.ruoyi.app.controller.databarge;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.dadiyang.equator.Equator;
import com.github.dadiyang.equator.FieldInfo;
import com.github.dadiyang.equator.GetterBaseEquator;
import com.ruoyi.app.controller.support.print.SlydService;
import com.ruoyi.common.annotation.DuplicateSubmitToken;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.CargocmentService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.DataScope;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.dto.BargeCheckInSearchDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentMainAndDetailAndBakDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeCheckInVO;
import com.ruoyi.databarge.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <p>
 * 驳船托运单明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@RestController
@Slf4j
@RequestMapping("/barge/cargoconsignmentdetail")
public class Pb6CargoconsignmentdetailController {

    @Autowired
    private Pb6CargoconsignmentdetailService pb6CargoconsignmentdetailService;

    @Autowired
    private Pb6BargeinfoService pb6BargeinfoService;

    @Autowired
    private Pb6CargoconsignmentService pb6CargoconsignmentService;

    @Autowired
    private Pb6BargeCheckNodeService pb6BargeCheckNodeService;

    @Autowired
    private Pb6BargeCheckMessageService pb6BargeCheckMessageService;

    @Autowired
    private Pb6CargoconsignmentBakService pb6CargoconsignmentBakService;

    @Autowired
    private Pb6CargoconsignmentdetailBakService pb6CargoconsignmentdetailBakService;

    @Autowired
    private CargocmentService cargocmentService; //运单接口，新生成托运单号使用

    @Autowired
    private UploadAddressDomainService uploadAddressDomainService;

    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;

    @Autowired
    private Pb3CoutformService pb3CoutformService;

    @Autowired
    private PubPortService pubPortService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private SlydService slydService;

    @Autowired
    private Pb6BargeworkController pb6BargeworkController;


    private static final String GENERATE_WATERCARGO_URL = "https://bulkbarge.gzport.com/gzgapp/barge/consignment/againStamp?waterwayCargoId=%s&&defrayStamp=水路货物运单.pdf";

    @PostMapping()
    public AjaxResult searchPb6Cargoconsignment(@RequestBody Pb6CargoconsignmentSearchDTO pb6CargoconsignmentSearchDTO) {
        DataScope.getDataScope("t", pb6CargoconsignmentSearchDTO, Boolean.FALSE);
        return AjaxResult.success(pb6CargoconsignmentdetailService.searchPb6Cargoconsignment(pb6CargoconsignmentSearchDTO));
    }

    // 托运单明细详情
    @GetMapping("/detail/{id}")
    public AjaxResult getPb6Cargoconsignmentdetail(@PathVariable("id") Long id) {
        return AjaxResult.success(pb6CargoconsignmentdetailService.getById(id));
    }

    //用于查询新托运单号
    @PostMapping("/searchNewConsignFlag")
    public AjaxResult searchNewConsignFlag(@RequestBody Long id) {
        return AjaxResult.success(pb6CargoconsignmentdetailService.getById(id).getConsignflag());
    }

    @Log(title = "货物托运单管理", businessType = BusinessType.CHECK)
    @PostMapping("/update/{bargeid}")
    public AjaxResult updatePb6CargoconsignmentDetail(@RequestBody Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail, @PathVariable("bargeid") Long bargeid) throws URISyntaxException {
        if (pb6Cargoconsignmentdetail.getIsverificate() != null && pb6Cargoconsignmentdetail.getIsverificate().equals("已退单")) {
            return AjaxResult.error("该驳船已退单，请重新选择");
        }
        if (pb6Cargoconsignmentdetail.getApplymodify() != null) {
            return AjaxResult.error("该驳船已申请改单/退单，请重新选择");
        }
        if (pb6Cargoconsignmentdetail.getFlagbargestate() != null) {
            switch (pb6Cargoconsignmentdetail.getFlagbargestate()) {
                case "3":
                    return AjaxResult.error("该驳船已配载，请重新选择");
                case "4":
                    return AjaxResult.error("该驳船已报到，请重新选择");
                case "5":
                    return AjaxResult.error("该驳船已离港，请重新选择");
                case "6":
                    return AjaxResult.error("该驳船已退单，请重新选择");
                default:
            }
        }
        Pb6Bargeinfo pb6Bargeinfo = pb6BargeinfoService.getById(bargeid);
        Pb6CargoconsignmentDTO pb6CargoconsignmentDTO = pb6CargoconsignmentdetailService.searchApplyDateByConsignFlag(pb6Cargoconsignmentdetail.getConsignflag());
        if (pb6CargoconsignmentDTO.getApplydate().compareTo(pb6Bargeinfo.getValidsaildate()) > 0 && !pb6Cargoconsignmentdetail.getFlagbargestate().equals("2")) {
            return AjaxResult.error("驳船： " + pb6Cargoconsignmentdetail.getBargename() + " 的行驶证有效期为： " + pb6Bargeinfo.getValidsaildate() + "; 行驶证已过期！");
        }
        BigDecimal ba = new BigDecimal(pb6Bargeinfo.getBargeloada());
        BigDecimal bb = new BigDecimal(pb6Bargeinfo.getBargeloadb());
        BigDecimal b = ba.max(bb).divide(new BigDecimal("1000"), 2);
        if (new BigDecimal(pb6Cargoconsignmentdetail.getRationweight()).compareTo(b) > 0 && pb6Cargoconsignmentdetail.getFlagbargestate().equals("1")) {
            return AjaxResult.error("注意： 申请重量大于驳船最大载货量" + b.toString() + "吨！审核无法通过！");
        }
        if ("1".equals(pb6Cargoconsignmentdetail.getFlagbargestate()) && StringUtils.isNotBlank(pb6CargoconsignmentDTO.getEndport())) {
            pb6CargoconsignmentdetailService.searchPortByEndPortName(pb6CargoconsignmentDTO.getEndport());
        }
        pb6Cargoconsignmentdetail.setBargeid(bargeid);
        pb6Cargoconsignmentdetail.setWxoperatestate(0);
        pb6Cargoconsignmentdetail.setOnineresourse("3");

        pb6Cargoconsignmentdetail.setCheckman(SecurityUtils.getUsername());
        pb6Cargoconsignmentdetail.setChecktime(DateUtils.getTime());
        boolean result = pb6CargoconsignmentdetailService.updateInfoByID(pb6Cargoconsignmentdetail);
        if (result) {
            pb6BargeCheckMessageService.sendMessageForCargoConsignmentChecked(pb6Cargoconsignmentdetail, bargeid);
        }
        return AjaxResult.success(result);
    }

    @PostMapping("/searchBargeCheckINVO")
    public AjaxResult searchBargeCheckINVO(@RequestBody BargeCheckInSearchDTO bargeCheckInSearchDTO) {
        DataScope.getDataScope("w", bargeCheckInSearchDTO, Boolean.FALSE);
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        //新港西基的水路运单和出入库单完全模糊查询
        if(sysUser.getDeptId() == 21L || sysUser.getDeptId() == 82L || sysUser.getDeptId() == 3L ||sysUser.getDeptId() == 4L){
            IPage<BargeCheckInVO> page = pb6CargoconsignmentdetailService.xgXjSearch(bargeCheckInSearchDTO);
            List<BargeCheckInVO> list = page.getRecords();
            list.forEach(item ->{
                if(item.getIsreporting() != null && item.getIsreporting().equals("N")){
                    item.setIsreporting("否");
                }
                else {
                    item.setIsreporting("是");
                }
                try {
                    if(pb6BargeworkController.isGreenBarge(Long.parseLong(item.getComid()),item.getBargename())){
                        item.setFiveDayReporting("是");
                    }
                    else {
                        item.setFiveDayReporting("否");
                    }
                }
                catch (Exception e){
                    item.setFiveDayReporting("否");
                }
            });
            return AjaxResult.success(page);
        }
        else {
            IPage<BargeCheckInVO> page = pb6CargoconsignmentdetailService.searchBargeCheckINVO(bargeCheckInSearchDTO);
            List<BargeCheckInVO> list = page.getRecords();
            list.forEach(item ->{
                System.out.println(item.getIsreporting());
                if(item.getIsreporting() != null && item.getIsreporting().equals("N")){
                    item.setIsreporting("否");
                }
                else {
                    item.setIsreporting("是");
                }
                try {
                    if(pb6BargeworkController.isGreenBarge(Long.parseLong(item.getComid()),item.getBargename())){
                        item.setFiveDayReporting("是");
                    }
                    else {
                        item.setFiveDayReporting("否");
                    }
                }
                catch (Exception e){
                    item.setFiveDayReporting("否");
                }
            });
            return AjaxResult.success(page);
        }
    }

    /**
     * 通过消息里面保存的货物托运单明细表ID查询托运单主表及明细表信息 或者备份表一块查。
     * 退单 sign = 0 只查原表， 改单 sign = 1 还需查备份表
     *
     * @param pb6CargoConsignmentDeatilId 货物托运单明细表ID
     * @param sign                        退改单标识， 0退单，1改单
     * @return 主明细表，或者再加备份表
     */
    @PostMapping("/searchPb6CargoconsignMainAndDetailAndBak/{sign}")
    public AjaxResult searchPb6CargoconsignMainAndDetailAndBak(@RequestBody Long pb6CargoConsignmentDeatilId, @PathVariable("sign") Integer sign) {
        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailService.getById(pb6CargoConsignmentDeatilId);
        if (pb6Cargoconsignmentdetail == null) {
            throw new CustomException("托运单明细表为空，请核对！");
        }
        System.out.println(pb6Cargoconsignmentdetail.toString());
        Pb6Cargoconsignment pb6Cargoconsignment = pb6CargoconsignmentService.getById(pb6Cargoconsignmentdetail.getConsignid());
        Pb6CargoconsignmentMainAndDetailAndBakDTO pb6CargoconsignmentMainAndDetailAndBakDTO = new Pb6CargoconsignmentMainAndDetailAndBakDTO();
        pb6CargoconsignmentMainAndDetailAndBakDTO.setPb6Cargoconsignment(pb6Cargoconsignment);
        pb6CargoconsignmentMainAndDetailAndBakDTO.setPb6Cargoconsignmentdetail(pb6Cargoconsignmentdetail);
        if (sign == 1) {
            Pb6CargoconsignmentBak pb6CargoconsignmentBak = pb6CargoconsignmentBakService.getById(pb6Cargoconsignment.getId());
            Pb6CargoconsignmentdetailBak pb6CargoconsignmentdetailBak = pb6CargoconsignmentdetailBakService.getById(pb6Cargoconsignmentdetail.getId());
            pb6CargoconsignmentMainAndDetailAndBakDTO.setPb6CargoconsignmentBak(pb6CargoconsignmentBak);
            pb6CargoconsignmentMainAndDetailAndBakDTO.setPb6CargoconsignmentdetailBak(pb6CargoconsignmentdetailBak);
        }
        return AjaxResult.success(pb6CargoconsignmentMainAndDetailAndBakDTO);
    }

    @PostMapping("/compareDiffField")
    public AjaxResult compareDiffField(@RequestBody Long pb6CargoConsignmentDeatilId) {
        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailService.getById(pb6CargoConsignmentDeatilId);
        if (pb6Cargoconsignmentdetail == null) {
            throw new CustomException("托运单明细表为空，请核对！");
        }
        Pb6CargoconsignmentdetailBak pb6CargoconsignmentdetailBak = pb6CargoconsignmentdetailBakService.getById(pb6Cargoconsignmentdetail.getId());
        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail1 = new Pb6Cargoconsignmentdetail();
        BeanUtils.copyProperties(pb6CargoconsignmentdetailBak, pb6Cargoconsignmentdetail1);

        //获取不同的属性
        Equator equator = new GetterBaseEquator();
        List<FieldInfo> fieldInfoList1 = equator.getDiffFields(pb6Cargoconsignmentdetail, pb6Cargoconsignmentdetail1);

        Pb6Cargoconsignment pb6Cargoconsignment = pb6CargoconsignmentService.getById(pb6Cargoconsignmentdetail.getConsignid());
        Pb6CargoconsignmentBak pb6CargoconsignmentBak = pb6CargoconsignmentBakService.getById(pb6Cargoconsignment.getId());
        Pb6Cargoconsignment pb6Cargoconsignment1 = new Pb6Cargoconsignment();
        BeanUtils.copyProperties(pb6CargoconsignmentBak, pb6Cargoconsignment1);

        List<FieldInfo> fieldInfoList2 = equator.getDiffFields(pb6Cargoconsignment, pb6Cargoconsignment1);

        fieldInfoList1.addAll(fieldInfoList2);
        return AjaxResult.success(fieldInfoList1);
    }

    //物流公司审核退改单
    @Log(title = "物流公司审核退改单", businessType = BusinessType.CHECK)
    @PostMapping("/orderBackOrUpdateOfWL")
    @Transactional
    public AjaxResult orderBackOrUpdateOfWL(@RequestBody Pb6CargoconsignmentMainAndDetailAndBakDTO mainAndDetailAndBakDTO) throws URISyntaxException {
        if (!(mainAndDetailAndBakDTO.getSign() != null && (mainAndDetailAndBakDTO.getSign() == 0 || mainAndDetailAndBakDTO.getSign() == 1))) {
            return AjaxResult.success("当前提交的不是退改单数据，请核对！");
        }

        Pb6CargoconsignmentdetailBak pb6CargoconsignmentdetailBak = mainAndDetailAndBakDTO.getPb6CargoconsignmentdetailBak();

        String result = "操作成功！";

        AjaxResult ajaxResult = AjaxResult.success();

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();

        // 审核改单： 1.审核节点表添加数据
        if (StringUtils.isNotBlank(pb6CargoconsignmentdetailBak.getWxnode())) {
            Pb6BargeCheckNode pb6BargeCheckNode = new Pb6BargeCheckNode();
            pb6BargeCheckNode.setWxnode(Integer.parseInt(pb6CargoconsignmentdetailBak.getWxnode()));

            //查找最近的一条消息，获取其申请人、时间等相关信息
            Pb6BargeCheckNode pb6BargeCheckNodeRecent = pb6BargeCheckNodeService.searchRecentByLinkId(pb6CargoconsignmentdetailBak.getId());
            if (pb6BargeCheckNodeRecent != null) {
                if (StringUtils.isNotBlank(pb6BargeCheckNodeRecent.getWxapplyman())) {
                    pb6BargeCheckNode.setWxapplyman(pb6BargeCheckNodeRecent.getWxapplyman());
                }
                if (StringUtils.isNotBlank(pb6BargeCheckNodeRecent.getWxapplytime())) {
                    pb6BargeCheckNode.setWxapplyaudittime(pb6BargeCheckNodeRecent.getWxapplytime());
                }
            }
            pb6BargeCheckNode.setWxapplyauditman(sysUser.getUserName());
            pb6BargeCheckNode.setWxapplyaudittime(DateUtils.getTime());
            pb6BargeCheckNode.setAuditstates(mainAndDetailAndBakDTO.getCheckStatus());
            pb6BargeCheckNode.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
            pb6BargeCheckNode.setLinkid(pb6CargoconsignmentdetailBak.getId());
            pb6BargeCheckNode.setWxnodeflag(mainAndDetailAndBakDTO.getSign());
            pb6BargeCheckNodeService.save(pb6BargeCheckNode);
        } else {
            result = result + "(当前托运单明细表没有找到节点表ID)";
        }

        if (mainAndDetailAndBakDTO.getSign() == 0) { //退单
            Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailService.getById(mainAndDetailAndBakDTO.getPb6CargoconsignmentdetailBak().getId());
            log.info("***********************退改审核原明细信息*************************");
            log.info(pb6Cargoconsignmentdetail.toString());
            // 审核退单： 2.托运单明细表信息修改
            if (mainAndDetailAndBakDTO.getCheckStatus() == 1) {
                //审核通过
                pb6Cargoconsignmentdetail.setApplymodify("1");
                pb6Cargoconsignmentdetail.setFlagbargestate("6"); // 6.已退单
                pb6Cargoconsignmentdetail.setWxoperatestate(8); // 8 审核通过(退单改单)
                pb6Cargoconsignmentdetail.setNewWaybill("0");
            } else {
                pb6Cargoconsignmentdetail.setApplymodify("2");
                pb6Cargoconsignmentdetail.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
                pb6Cargoconsignmentdetail.setAuditstates(1);
            }
            pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
            log.info("***********************退改审核修改后明细信息*************************");
            log.info(pb6Cargoconsignmentdetail.toString());
            // 2021-07-06 添加，对水路运单的更改
            Pb6Waterwaycargo pb6Waterwaycargo = null;
            if (ObjectUtil.isNotNull(pb6Cargoconsignmentdetail.getWaterwaycargoid())){
                pb6Waterwaycargo = pb6WaterwaycargoService.searchWaterwaycargoByWaterwaycargoid(pb6Cargoconsignmentdetail.getWaterwaycargoid());
            }
            if (pb6Waterwaycargo != null) {
                pb6Waterwaycargo.setFlagbargestate("4"); // 4：已退单
                pb6WaterwaycargoService.updateById(pb6Waterwaycargo);
            }
        } else if (mainAndDetailAndBakDTO.getSign() == 1) { //改单
            // 审核改单： 2.托运单明细表 及其备份表 信息修改 生成新纪录
            if (mainAndDetailAndBakDTO.getCheckStatus() == 1) {
                //审核通过
                // (1)把托运单主表备份表的数据拿出来，生成一个新的托运单号，并保存为新数据
                Pb6CargoconsignmentBak pb6CargoconsignmentBak = mainAndDetailAndBakDTO.getPb6CargoconsignmentBak();
                Pb6Cargoconsignment pb6Cargoconsignment = new Pb6Cargoconsignment();
                BeanUtils.copyProperties(pb6CargoconsignmentBak, pb6Cargoconsignment);
                pb6Cargoconsignment.setId(null);

                String consignflag = cargocmentService.getNewSerialNo("TD", pb6Cargoconsignment.getComid(), DateUtils.dateTimeNow("yyMM"), 4);
                pb6Cargoconsignment.setConsignflag(consignflag);

                //改单结果写入主表 特约事项 字段
                pb6Cargoconsignment.setSpecialproceeding(StringUtils.isNotBlank(pb6CargoconsignmentBak.getSpecialproceeding()) ?
                        (pb6CargoconsignmentBak.getSpecialproceeding() + "；由" + pb6CargoconsignmentBak.getConsignflag() + "改为" + consignflag) : "由" + pb6CargoconsignmentBak.getConsignflag() + "改为" + consignflag);
                pb6CargoconsignmentService.save(pb6Cargoconsignment);

                // 通过托运单号查询，数据条数为1，执行成功，大于1，托号重复，等于0则是插入失败
                List<Pb6Cargoconsignment> pb6CargoconsignmentList = pb6CargoconsignmentService.searchByConsignFlagForDealRepeat(consignflag);
                if (pb6CargoconsignmentList.size() == 0) {
                    //插入失败，再试一次
                    String consignflagNew = cargocmentService.getNewSerialNo("TD", pb6Cargoconsignment.getComid(), DateUtils.dateTimeNow("yyMM"), 4);
                    pb6Cargoconsignment.setConsignflag(consignflagNew);

                    //改单结果写入主表 特约事项 字段
                    pb6Cargoconsignment.setSpecialproceeding(StringUtils.isNotBlank(pb6CargoconsignmentBak.getSpecialproceeding()) ?
                            (pb6CargoconsignmentBak.getSpecialproceeding() + "；由" + pb6CargoconsignmentBak.getConsignflag() + "改为" + consignflagNew) : "由" + pb6CargoconsignmentBak.getConsignflag() + "改为" + consignflagNew);

                    pb6CargoconsignmentService.save(pb6Cargoconsignment);
                    List<Pb6Cargoconsignment> pb6CargoconsignmentList1 = pb6CargoconsignmentService.searchByConsignFlagForDealRepeat(consignflag);
                    if (pb6CargoconsignmentList1.size() != 1) {
                        throw new CustomException("改单审核出错，请重试！");
                    }
                } else if (pb6CargoconsignmentList.size() > 1) {
                    //并发数据重复，删掉新插入数据，重试一次
                    pb6CargoconsignmentService.removeById(pb6Cargoconsignment.getId());
                    String consignflagNew = cargocmentService.getNewSerialNo("TD", pb6Cargoconsignment.getComid(), DateUtils.dateTimeNow("yyMM"), 4);
                    pb6Cargoconsignment.setConsignflag(consignflagNew);
                    pb6Cargoconsignment.setId(null);
                    //改单结果写入主表 特约事项 字段
                    pb6Cargoconsignment.setSpecialproceeding(StringUtils.isNotBlank(pb6CargoconsignmentBak.getSpecialproceeding()) ?
                            (pb6CargoconsignmentBak.getSpecialproceeding() + "；由" + pb6CargoconsignmentBak.getConsignflag() + "改为" + consignflagNew) : "由" + pb6CargoconsignmentBak.getConsignflag() + "改为" + consignflagNew);
                    pb6CargoconsignmentService.save(pb6Cargoconsignment);
                    List<Pb6Cargoconsignment> pb6CargoconsignmentList1 = pb6CargoconsignmentService.searchByConsignFlagForDealRepeat(consignflag);
                    if (pb6CargoconsignmentList1.size() != 1) {
                        throw new CustomException("改单审核出错，请重试！");
                    }
                }

                //(2)把托运单明细表备份数据拿出来
                Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = new Pb6Cargoconsignmentdetail();
                BeanUtils.copyProperties(pb6CargoconsignmentdetailBak, pb6Cargoconsignmentdetail);
                pb6Cargoconsignmentdetail.setId(null); //ID
                pb6Cargoconsignmentdetail.setConsignflag(pb6Cargoconsignment.getConsignflag()); //托号
                pb6Cargoconsignmentdetail.setConsignid(pb6Cargoconsignment.getId()); //主表ID

                //2022.02.24改为原有的状态
                //2021.09.29 添加
               /* if(StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWaterwaycargoid())){
                    pb6Cargoconsignmentdetail.setFlagbargestate("3"); // 3：已配载
                    pb6Cargoconsignmentdetail.setWxoperatestate(4); //4:待驳船主预约
                } else {
                    pb6Cargoconsignmentdetail.setFlagbargestate("1");
                    pb6Cargoconsignmentdetail.setWxoperatestate(0);
                }*/
                pb6Cargoconsignmentdetail.setApplymodify(null); //不再申请退改单
                pb6Cargoconsignmentdetail.setModifyreason(null); //退改单原因为空
                pb6Cargoconsignmentdetail.setFailurereasons(null); //不通过原因为空
                pb6Cargoconsignmentdetail.setWxnode(null); //当前没有流程节点
                pb6Cargoconsignmentdetail.setAuditstates(null); //审核状态
                pb6Cargoconsignmentdetail.setNewdetailid(null); //新托号ID
                pb6Cargoconsignmentdetail.setNewWaybill("1");
                pb6CargoconsignmentdetailService.save(pb6Cargoconsignmentdetail);

                // 2021-06-30 添加，对水路运单的更改
                if (StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWaterwaycargoid())) {
                    Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.searchWaterwaycargoByWaterwaycargoid(pb6Cargoconsignmentdetail.getWaterwaycargoid());

                    List<Pb3Coutform> pb3CoutformList = pb3CoutformService.findByCoutformId(pb6Cargoconsignment.getOutorinformid());
                    if (pb3CoutformList.size() == 0) {
                        throw new CustomException("出库单信息为空");
                    } else if (pb3CoutformList.size() > 1) {
                        throw new CustomException("发现多条出库单信息");
                    }
                    pb6Waterwaycargo.setOutorinformid(pb3CoutformList.get(0).getCoutformid());
                    pb6Waterwaycargo.setOutorinformidid(pb3CoutformList.get(0).getId());

                    pb6Waterwaycargo.setBargeName(pb3CoutformList.get(0).getShipname());
                    pb6Waterwaycargo.setConsignee(pb6Cargoconsignment.getConsignee());
                    pb6Waterwaycargo.setPackagetype(pb3CoutformList.get(0).getPackagetype());
                    pb6Waterwaycargo.setPackagetypeid(pb3CoutformList.get(0).getPackagetypeid());
                    List<PubPort> endPortList = pubPortService.searchWlPortByName(pb6Cargoconsignment.getEndport());
                    if (endPortList.size() > 0) {
                        pb6Waterwaycargo.setEndportid(endPortList.get(0).getId());
                        pb6Waterwaycargo.setValiddate(endPortList.get(0).getPortcname());
                    }
                    List<PubPort> midPortList = pubPortService.searchWlPortByName(pb6Cargoconsignment.getMidport());
                    if (midPortList.size() > 0) {
                        pb6Waterwaycargo.setMidportid(midPortList.get(0).getId());
                    }
                    pb6Waterwaycargo.setCargenameid(pb3CoutformList.get(0).getCargoserialnumber());
                    pb6Waterwaycargo.setCargename(pb3CoutformList.get(0).getCargoname());
                    pb6Waterwaycargo.setLoadometeridid(pb6WaterwaycargoService.searchLoadMeterId(pb6Cargoconsignment.getLoadometerid()));
                    pb6Waterwaycargo.setLoadometerid(pb6Cargoconsignment.getLoadometerid());
                    pb6Waterwaycargo.setSpecialproceeding(pb6CargoconsignmentBak.getSpecialproceeding());
                    pb6Waterwaycargo.setFlagbargestate("3"); //3：已取消报到，处于配载状态，并且已有报到信息

                    //2021-08-16 对其他XX港需要另外处理
                    if (StringUtils.isNotBlank(pb6Cargoconsignment.getEndport()) && pb6Cargoconsignment.getEndport().contains("其他")) {
                        if (StringUtils.isNotBlank(pb6Cargoconsignment.getOtherCoastalInlandPort())) {
                            pb6Waterwaycargo.setValiddate(pb6Cargoconsignment.getOtherCoastalInlandPort());
                        }
                    }
                    pb6WaterwaycargoService.updateById(pb6Waterwaycargo);

                    ajaxResult.put("watercargoId", pb6Waterwaycargo.getWaterwaycargoid());
                }

                //(3)更改托运单明细表及其备份表的信息
                Pb6Cargoconsignmentdetail cargoconsignmentdetail = pb6CargoconsignmentdetailService.getById(pb6CargoconsignmentdetailBak.getId());
                cargoconsignmentdetail.setFlagbargestate("7"); //状态设为 7、已改单
                cargoconsignmentdetail.setApplymodify("4"); //4、申请改单成功
                cargoconsignmentdetail.setWxoperatestate(8); //8审核通过(退单改单)
                cargoconsignmentdetail.setNewdetailid(pb6Cargoconsignmentdetail.getId());
                cargoconsignmentdetail.setNewWaybill("0");

                //(4)有文件的情况下更新文件存储信息
                List<UploadAddressDomain> uploadAddressList = uploadAddressDomainService.searchLinkFileForUpdateByLinkId(pb6CargoconsignmentBak.getId());
                if (uploadAddressList.size() > 0) {
                    //审核通过把linkId改为新的主表ID，把BakFlag改为 1 即时正式文件
                    uploadAddressList.forEach(i -> {
                        i.setLinkId(pb6Cargoconsignment.getId());
                        i.setBakFlag(1);
                    });
                    uploadAddressDomainService.updateUploadAddressList(uploadAddressList);
                }

                //审核通过后，去掉原托运单明细中的水路运单号
                cargoconsignmentdetail.setWaterwaycargoid("");
                pb6CargoconsignmentdetailService.updateById(cargoconsignmentdetail);

                pb6CargoconsignmentdetailBak.setFlagbargestate("7");
                pb6CargoconsignmentdetailBak.setApplymodify("4");
                pb6CargoconsignmentdetailBak.setNewdetailid(pb6Cargoconsignmentdetail.getId());
                pb6CargoconsignmentdetailBak.setNewWaybill("0");
                pb6CargoconsignmentdetailBak.setWaterwaycargoid("");
                pb6CargoconsignmentdetailBakService.updateById(pb6CargoconsignmentdetailBak);

                //(5)改单前的托运单主表 特约事项字段填入 改单信息
                Pb6Cargoconsignment cargoconsignment = pb6CargoconsignmentService.getById(pb6CargoconsignmentBak.getId());
                cargoconsignment.setSpecialproceeding(StringUtils.isNotBlank(cargoconsignment.getSpecialproceeding()) ?
                        (pb6CargoconsignmentBak.getSpecialproceeding() + "；由" + pb6CargoconsignmentBak.getConsignflag() + "改为" + pb6Cargoconsignment.getConsignflag()) : "由" + pb6CargoconsignmentBak.getConsignflag() + "改为" + pb6Cargoconsignment.getConsignflag());
                pb6CargoconsignmentService.updateById(cargoconsignment);

            } else {
                //审核不通过
                Pb6Cargoconsignmentdetail cargoconsignmentdetail = pb6CargoconsignmentdetailService.getById(pb6CargoconsignmentdetailBak.getId());
                cargoconsignmentdetail.setApplymodify("5"); //4、申请改单失败
                cargoconsignmentdetail.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
                cargoconsignmentdetail.setAuditstates(1);
                pb6CargoconsignmentdetailService.updateById(cargoconsignmentdetail);

                pb6CargoconsignmentdetailBak.setApplymodify("4");
                pb6CargoconsignmentdetailBak.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
                pb6CargoconsignmentdetailBak.setAuditstates(1);
                pb6CargoconsignmentdetailBakService.updateById(pb6CargoconsignmentdetailBak);

                //(4)有文件的情况下更新文件存储信息
                List<UploadAddressDomain> uploadAddressList = uploadAddressDomainService.searchLinkFileForUpdateByLinkId(mainAndDetailAndBakDTO.getPb6CargoconsignmentBak().getId());
                if (uploadAddressList.size() > 0) {
                    uploadAddressList.forEach(i -> i.setStatus(0));
                    uploadAddressDomainService.updateUploadAddressList(uploadAddressList);
                }
            }
        }

        // 审核改单： 3.消息表更改
        Pb6BargeCheckMessage pb6BargeCheckMessage = pb6BargeCheckMessageService.getById(mainAndDetailAndBakDTO.getPb6BargeCheckMessageId());
        pb6BargeCheckMessage.setAuditmanid(sysUser.getUserId());
        pb6BargeCheckMessage.setAuditman(sysUser.getUserName());
        pb6BargeCheckMessage.setAudittime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        pb6BargeCheckMessage.setAuditflag(mainAndDetailAndBakDTO.getCheckStatus());
        pb6BargeCheckMessage.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
//        pb6BargeCheckMessage.setWaterwaycargoid("");
        pb6BargeCheckMessageService.updateById(pb6BargeCheckMessage);

        //退改单不管物流公司审核通不通过都得发送信息
        result = result + pb6BargeCheckMessageService.sendMessageForRefundOrUpdateCargoConsignment(pb6BargeCheckMessage);

        ajaxResult.replace("msg", result);

        //退单审核通过重新生成水路运单
        if (mainAndDetailAndBakDTO.getSign() == 0) {
            slydService.chargebackStamp(pb6CargoconsignmentdetailBak.getWaterwaycargoid());
        }

        return ajaxResult;
    }


    //码头审核退单
    @PostMapping("/orderBackOfPort")
    @Transactional
    public AjaxResult orderBackOfPort(@RequestBody Pb6CargoconsignmentMainAndDetailAndBakDTO mainAndDetailAndBakDTO) throws URISyntaxException {

        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailService.getById(mainAndDetailAndBakDTO.getPb6Cargoconsignmentdetail().getId());

        String result = "操作成功！";

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();

        // 审核退单： 1.审核节点表添加数据
        if (mainAndDetailAndBakDTO.getCheckStatus() == 1) {
            if (StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWxnode())) {
                Pb6BargeCheckNode pb6BargeCheckNode = new Pb6BargeCheckNode();
                Pb6BargeCheckNode pb6BargeCheckNode1 = new Pb6BargeCheckNode();
                pb6BargeCheckNode.setWxnode(Integer.parseInt(pb6Cargoconsignmentdetail.getWxnode()));
                pb6BargeCheckNode1.setWxnode(3);

                //查找最近的一条消息，获取其申请人、时间等相关信息
                Pb6BargeCheckNode pb6BargeCheckNodeRecent = pb6BargeCheckNodeService.searchRecentByLinkId(pb6Cargoconsignmentdetail.getId());
                if (pb6BargeCheckNodeRecent != null) {
                    if (StringUtils.isNotBlank(pb6BargeCheckNodeRecent.getWxapplyman())) {
                        pb6BargeCheckNode.setWxapplyman(pb6BargeCheckNodeRecent.getWxapplyman());
                        pb6BargeCheckNode1.setWxapplyman(pb6BargeCheckNodeRecent.getWxapplyman());
                    }
                    if (StringUtils.isNotBlank(pb6BargeCheckNodeRecent.getWxapplytime())) {
                        pb6BargeCheckNode.setWxapplytime(pb6BargeCheckNodeRecent.getWxapplytime());
                        pb6BargeCheckNode1.setWxapplytime(pb6BargeCheckNodeRecent.getWxapplytime());
                    }
                }
                pb6BargeCheckNode.setWxapplyauditman(sysUser.getUserName());
                pb6BargeCheckNode.setWxapplyaudittime(DateUtils.getTime());
                pb6BargeCheckNode.setAuditstates(mainAndDetailAndBakDTO.getCheckStatus());
                pb6BargeCheckNode1.setAuditstates(0);
                pb6BargeCheckNode.setLinkid(pb6Cargoconsignmentdetail.getId());
                pb6BargeCheckNode1.setLinkid(pb6Cargoconsignmentdetail.getId());
                pb6BargeCheckNode.setWxnodeflag(0);
                pb6BargeCheckNode1.setWxnodeflag(0);
                System.out.println(pb6BargeCheckNode);
                System.out.println(pb6BargeCheckNode1);
                pb6BargeCheckNodeService.save(pb6BargeCheckNode);
                pb6BargeCheckNodeService.save(pb6BargeCheckNode1);
            } else {
                result = result + "(当前托运单明细表没有找到节点表ID)";
            }
        } else {
            if (StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWxnode())) {
                Pb6BargeCheckNode pb6BargeCheckNode = new Pb6BargeCheckNode();
                pb6BargeCheckNode.setWxnode(Integer.parseInt(pb6Cargoconsignmentdetail.getWxnode()));

                //查找最近的一条消息，获取其申请人、时间等相关信息
                Pb6BargeCheckNode pb6BargeCheckNodeRecent = pb6BargeCheckNodeService.searchRecentByLinkId(pb6Cargoconsignmentdetail.getId());
                if (pb6BargeCheckNodeRecent != null) {
                    if (StringUtils.isNotBlank(pb6BargeCheckNodeRecent.getWxapplyman())) {
                        pb6BargeCheckNode.setWxapplyman(pb6BargeCheckNodeRecent.getWxapplyman());
                    }
                    if (StringUtils.isNotBlank(pb6BargeCheckNodeRecent.getWxapplytime())) {
                        pb6BargeCheckNode.setWxapplytime(pb6BargeCheckNodeRecent.getWxapplytime());
                    }
                }
                pb6BargeCheckNode.setWxapplyauditman(sysUser.getUserName());
                pb6BargeCheckNode.setWxapplyaudittime(DateUtils.getTime());
                pb6BargeCheckNode.setAuditstates(mainAndDetailAndBakDTO.getCheckStatus());
                pb6BargeCheckNode.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
                pb6BargeCheckNode.setLinkid(pb6Cargoconsignmentdetail.getId());
                pb6BargeCheckNode.setWxnodeflag(0);
                pb6BargeCheckNodeService.save(pb6BargeCheckNode);
            } else {
                result = result + "(当前托运单明细表没有找到节点表ID)";
            }
        }


        // 审核退单： 2.托运单明细表信息修改
        if (mainAndDetailAndBakDTO.getCheckStatus() == 1) {
            //审核通过
            pb6Cargoconsignmentdetail.setWxnode("2");
            pb6Cargoconsignmentdetail.setAuditstates(0);
        } else {
            pb6Cargoconsignmentdetail.setApplymodify("2");
            pb6Cargoconsignmentdetail.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
            pb6Cargoconsignmentdetail.setAuditstates(1);
        }
        pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);

        // 审核退单： 3.消息表更改
        if (mainAndDetailAndBakDTO.getCheckStatus() == 1) {
            Pb6BargeCheckMessage pb6BargeCheckMessage = pb6BargeCheckMessageService.getById(mainAndDetailAndBakDTO.getPb6BargeCheckMessageId());
            pb6BargeCheckMessage.setAuditmanid(sysUser.getUserId());
            pb6BargeCheckMessage.setAuditman(sysUser.getUserName());
            pb6BargeCheckMessage.setAudittime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            pb6BargeCheckMessage.setAuditflag(mainAndDetailAndBakDTO.getCheckStatus());
            pb6BargeCheckMessageService.updateById(pb6BargeCheckMessage);

            //审核退单：4.增加消息数据
            Pb6BargeCheckMessage pb6BargeCheckMessage1 = new Pb6BargeCheckMessage();
            pb6BargeCheckMessage1.setMtype(6);
            pb6BargeCheckMessage1.setApplymanid(pb6BargeCheckMessage.getApplymanid());
            pb6BargeCheckMessage1.setApplyman(pb6BargeCheckMessage.getApplyman());
            pb6BargeCheckMessage1.setApplytime(pb6BargeCheckMessage.getApplytime());
            pb6BargeCheckMessage1.setAuditflag(0);
            pb6BargeCheckMessage1.setBargename(pb6BargeCheckMessage.getBargename());
            pb6BargeCheckMessage1.setWaterwaycargoid(pb6BargeCheckMessage.getWaterwaycargoid());
            pb6BargeCheckMessage1.setPb6bargeinfoauditid(pb6BargeCheckMessage.getPb6bargeinfoauditid());
            pb6BargeCheckMessage1.setPb6bargecompanyid(pb6BargeCheckMessage.getPb6bargecompanyid());
            pb6BargeCheckMessage1.setPb6cargoconsignmentdetailid(pb6BargeCheckMessage.getPb6cargoconsignmentdetailid());
            pb6BargeCheckMessage1.setConsignflag(pb6BargeCheckMessage.getConsignflag());
            pb6BargeCheckMessage1.setRemark(pb6BargeCheckMessage.getRemark());
            pb6BargeCheckMessageService.save(pb6BargeCheckMessage1);
        } else {
            Pb6BargeCheckMessage pb6BargeCheckMessage = pb6BargeCheckMessageService.getById(mainAndDetailAndBakDTO.getPb6BargeCheckMessageId());
            pb6BargeCheckMessage.setAuditmanid(sysUser.getUserId());
            pb6BargeCheckMessage.setAuditman(sysUser.getUserName());
            pb6BargeCheckMessage.setAudittime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            pb6BargeCheckMessage.setAuditflag(mainAndDetailAndBakDTO.getCheckStatus());
            pb6BargeCheckMessage.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
            pb6BargeCheckMessageService.updateById(pb6BargeCheckMessage);

            //码头审核不通过需要发送消息
            pb6BargeCheckMessageService.sendMessageForRefundOrUpdateCargoConsignment(pb6BargeCheckMessage);
        }

        return AjaxResult.success(result);
    }

    //码头审核改单
    @PostMapping("/orderUpdateOfPort")
    @Transactional
    public AjaxResult orderUpdateOfPort(@RequestBody Pb6CargoconsignmentMainAndDetailAndBakDTO mainAndDetailAndBakDTO) throws URISyntaxException {
        Pb6CargoconsignmentdetailBak pb6CargoconsignmentdetailBak = mainAndDetailAndBakDTO.getPb6CargoconsignmentdetailBak();
        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailService.getById(pb6CargoconsignmentdetailBak.getId());
        Pb6BargeCheckMessage pb6BargeCheckMessageOld = pb6BargeCheckMessageService.getById(mainAndDetailAndBakDTO.getPb6BargeCheckMessageId());
        if (pb6BargeCheckMessageOld.getAuditflag().equals(1)) {
            return AjaxResult.error("该信息已审核");
        }
        String result = "操作成功！";

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();

        // 审核改单： 1.审核节点表添加数据
        if (mainAndDetailAndBakDTO.getCheckStatus() == 1) {
            if (StringUtils.isNotBlank(pb6CargoconsignmentdetailBak.getWxnode())) {
                Pb6BargeCheckNode pb6BargeCheckNode = new Pb6BargeCheckNode();
                Pb6BargeCheckNode pb6BargeCheckNode1 = new Pb6BargeCheckNode();
                pb6BargeCheckNode.setWxnode(Integer.parseInt(pb6CargoconsignmentdetailBak.getWxnode()));
                pb6BargeCheckNode1.setWxnode(2);
                //查找最近的一条消息，获取其申请人、时间等相关信息
                Pb6BargeCheckNode pb6BargeCheckNodeRecent = pb6BargeCheckNodeService.searchRecentByLinkId(pb6CargoconsignmentdetailBak.getId());
                if (pb6BargeCheckNodeRecent != null) {
                    if (StringUtils.isNotBlank(pb6BargeCheckNodeRecent.getWxapplyman())) {
                        pb6BargeCheckNode.setWxapplyman(pb6BargeCheckNodeRecent.getWxapplyman());
                        pb6BargeCheckNode1.setWxapplyman(pb6BargeCheckNodeRecent.getWxapplyman());
                    }
                    if (StringUtils.isNotBlank(pb6BargeCheckNodeRecent.getWxapplytime())) {
                        pb6BargeCheckNode.setWxapplytime(pb6BargeCheckNodeRecent.getWxapplytime());
                        pb6BargeCheckNode1.setWxapplytime(pb6BargeCheckNodeRecent.getWxapplytime());
                    }
                }
                pb6BargeCheckNode.setWxapplyauditman(sysUser.getUserName());
                pb6BargeCheckNode.setWxapplyaudittime(DateUtils.getTime());
                pb6BargeCheckNode.setAuditstates(mainAndDetailAndBakDTO.getCheckStatus());
                pb6BargeCheckNode1.setAuditstates(0);
                pb6BargeCheckNode.setLinkid(pb6CargoconsignmentdetailBak.getId());
                pb6BargeCheckNode1.setLinkid(pb6CargoconsignmentdetailBak.getId());
                pb6BargeCheckNode.setWxnodeflag(1);
                pb6BargeCheckNode1.setWxnodeflag(1);
                pb6BargeCheckNodeService.save(pb6BargeCheckNode);
                pb6BargeCheckNodeService.save(pb6BargeCheckNode1);
            } else {
                result = result + "(当前托运单明细表没有找到节点表ID)";
            }
        } else {
            if (StringUtils.isNotBlank(pb6CargoconsignmentdetailBak.getWxnode())) {
                Pb6BargeCheckNode pb6BargeCheckNode = new Pb6BargeCheckNode();
                pb6BargeCheckNode.setWxnode(Integer.parseInt(pb6CargoconsignmentdetailBak.getWxnode()));

                //查找最近的一条消息，获取其申请人、时间等相关信息
                Pb6BargeCheckNode pb6BargeCheckNodeRecent = pb6BargeCheckNodeService.searchRecentByLinkId(pb6CargoconsignmentdetailBak.getId());
                if (pb6BargeCheckNodeRecent != null) {
                    if (StringUtils.isNotBlank(pb6BargeCheckNodeRecent.getWxapplyman())) {
                        pb6BargeCheckNode.setWxapplyman(pb6BargeCheckNodeRecent.getWxapplyman());
                    }
                    if (StringUtils.isNotBlank(pb6BargeCheckNodeRecent.getWxapplytime())) {
                        pb6BargeCheckNode.setWxapplytime(pb6BargeCheckNodeRecent.getWxapplytime());
                    }
                }
                pb6BargeCheckNode.setWxapplyauditman(sysUser.getUserName());
                pb6BargeCheckNode.setWxapplyaudittime(DateUtils.getTime());
                pb6BargeCheckNode.setAuditstates(mainAndDetailAndBakDTO.getCheckStatus());
                pb6BargeCheckNode.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
                pb6BargeCheckNode.setLinkid(pb6CargoconsignmentdetailBak.getId());
                pb6BargeCheckNode.setWxnodeflag(1);
                pb6BargeCheckNodeService.save(pb6BargeCheckNode);
            } else {
                result = result + "(当前托运单明细表没有找到节点表ID)";
            }
        }


        // 审核改单： 2.托运单明细表 及其备份表 信息修改
        if (mainAndDetailAndBakDTO.getCheckStatus() == 1) {
            pb6Cargoconsignmentdetail.setWxnode("2");
            pb6Cargoconsignmentdetail.setAuditstates(0);
            pb6CargoconsignmentdetailBak.setWxnode("2");
            pb6CargoconsignmentdetailBak.setAuditstates(0);

        } else {
            pb6Cargoconsignmentdetail.setApplymodify("5");
            pb6Cargoconsignmentdetail.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
            pb6Cargoconsignmentdetail.setAuditstates(1);
            pb6CargoconsignmentdetailBak.setApplymodify("5");
            pb6CargoconsignmentdetailBak.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
            pb6CargoconsignmentdetailBak.setAuditstates(1);

            //(4)码头只是审核不通过才修改 有文件的情况下更新文件存储信息
            List<UploadAddressDomain> uploadAddressList = uploadAddressDomainService.searchLinkFileForUpdateByLinkId(mainAndDetailAndBakDTO.getPb6CargoconsignmentBak().getId());
            if (uploadAddressList.size() > 0) {
                //改单失败，把所有文件都设为失效
                uploadAddressList.forEach(i -> i.setStatus(0));
                uploadAddressDomainService.updateUploadAddressList(uploadAddressList);
            }
        }
        pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
        pb6CargoconsignmentdetailBakService.updateById(pb6CargoconsignmentdetailBak);


        // 审核改单： 3.消息表更改
        if (mainAndDetailAndBakDTO.getCheckStatus() == 1) {
            Pb6BargeCheckMessage pb6BargeCheckMessage = pb6BargeCheckMessageService.getById(mainAndDetailAndBakDTO.getPb6BargeCheckMessageId());
            pb6BargeCheckMessage.setAuditmanid(sysUser.getUserId());
            pb6BargeCheckMessage.setAuditman(sysUser.getUserName());
            pb6BargeCheckMessage.setAudittime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            pb6BargeCheckMessage.setAuditflag(mainAndDetailAndBakDTO.getCheckStatus());
            pb6BargeCheckMessageService.updateById(pb6BargeCheckMessage);

            //审核改单：4.增加消息数据
            Pb6BargeCheckMessage pb6BargeCheckMessage1 = new Pb6BargeCheckMessage();
            pb6BargeCheckMessage1.setMtype(8);
            pb6BargeCheckMessage1.setApplymanid(pb6BargeCheckMessage.getApplymanid());
            pb6BargeCheckMessage1.setApplyman(pb6BargeCheckMessage.getApplyman());
            pb6BargeCheckMessage1.setApplytime(pb6BargeCheckMessage.getApplytime());
            pb6BargeCheckMessage1.setAuditflag(0);
            pb6BargeCheckMessage1.setBargename(pb6BargeCheckMessage.getBargename());
            pb6BargeCheckMessage1.setWaterwaycargoid(pb6BargeCheckMessage.getWaterwaycargoid());
            pb6BargeCheckMessage1.setPb6bargeinfoauditid(pb6BargeCheckMessage.getPb6bargeinfoauditid());
            pb6BargeCheckMessage1.setPb6bargecompanyid(pb6BargeCheckMessage.getPb6bargecompanyid());
            pb6BargeCheckMessage1.setPb6cargoconsignmentdetailid(pb6BargeCheckMessage.getPb6cargoconsignmentdetailid());
            pb6BargeCheckMessage1.setConsignflag(pb6BargeCheckMessage.getConsignflag());
            pb6BargeCheckMessage1.setRemark(pb6BargeCheckMessage.getRemark());
            pb6BargeCheckMessageService.save(pb6BargeCheckMessage1);
        } else {
            Pb6BargeCheckMessage pb6BargeCheckMessage = pb6BargeCheckMessageService.getById(mainAndDetailAndBakDTO.getPb6BargeCheckMessageId());
            pb6BargeCheckMessage.setAuditmanid(sysUser.getUserId());
            pb6BargeCheckMessage.setAuditman(sysUser.getUserName());
            pb6BargeCheckMessage.setAudittime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            pb6BargeCheckMessage.setAuditflag(mainAndDetailAndBakDTO.getCheckStatus());
            pb6BargeCheckMessage.setFailurereasons(mainAndDetailAndBakDTO.getFailureReasons());
            pb6BargeCheckMessageService.updateById(pb6BargeCheckMessage);

            //码头审核不通过需要发送消息
            pb6BargeCheckMessageService.sendMessageForRefundOrUpdateCargoConsignment(pb6BargeCheckMessage);
        }

        return AjaxResult.success(result);
    }

    // 修改托运单联系人电话
    @PostMapping("/updateBargeOwnerTel")
    public AjaxResult updateBargeOwnerTel(@RequestBody Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) {
        Pb6Cargoconsignmentdetail cargoconsignmentdetail = pb6CargoconsignmentdetailService.getById(pb6Cargoconsignmentdetail.getId());
        cargoconsignmentdetail.setWxrationcontactnumber(pb6Cargoconsignmentdetail.getWxrationcontactnumber().trim());
        return AjaxResult.success(pb6CargoconsignmentdetailService.updateById(cargoconsignmentdetail));
    }

    /**
     * 查询新沙当前预约报到的船的信息
     *
     * @return mmsi集合的字符串
     */
    @DuplicateSubmitToken
    @PostMapping("/searchBargeOrder")
    public AjaxResult searchBargeOrder(@RequestBody Long comid) throws ExecutionException, InterruptedException {

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        if (!sysUser.isAdmin()) {

            Integer isAdmin = null;
            for (SysRole sysRole : sysUser.getRoles()) {
                if ("wlbcAdmin".equals(sysRole.getRoleKey())) {
                    isAdmin = 0;
                    break;
                } else if ("lsDataScope".equals(sysRole.getRoleKey())) {
                    isAdmin = 1;
                } else if ("xsDataScope".equals(sysRole.getRoleKey())) {
                    isAdmin = 2;
                } else if("xgDataScope".equals(sysRole.getRoleKey())){
                    isAdmin = 3;
                } else if("xjDataScope".equals(sysRole.getRoleKey())){
                    isAdmin = 4;
                }
            }

            if (isAdmin == null) {
                return AjaxResult.error("没有查询预约船权限");
            } else if (isAdmin == 1) {
                comid = 16L;
            } else if (isAdmin == 2) {
                comid = 1L;
            } else if (isAdmin == 3) {
                comid = 3L;
            } else if (isAdmin == 4) {
                comid = 4L;
            }
        }
        return AjaxResult.success(pb6CargoconsignmentdetailService.searchBargeOrder(comid));
    }
}


package com.ruoyi.businessFile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.businessFile.domain.SysOss;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 17:16
 */
public interface SysOssService extends IService<SysOss> {

    List<SysOss> selectSysOssList(SysOss sysOss);

    public AjaxResult uploadFile(MultipartFile file);

}

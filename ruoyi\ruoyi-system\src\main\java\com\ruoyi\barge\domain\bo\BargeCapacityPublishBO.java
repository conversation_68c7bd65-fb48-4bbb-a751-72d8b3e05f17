package com.ruoyi.barge.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/11 11:17
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class BargeCapacityPublishBO extends BaseEntity {

    /**
     * 运力主键id
     */
    private Long id;

    /**
     * 驳船id
     */
    private Long bargeId;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^(((13[0-9]{1})|(15[0-9]{1})|(18[0-9]{1}))+\\d{8})$", message = "手机号格式不正确")
    private String phone;

    /**
     * 运力数据
     */
    @NotBlank(message = "运力数据不能为空")
    private String capacity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 驳船名称
     */
    private String bargeName;

    /**
     * 驳船识别号
     */
    private String bargeIdentifier;

    private Long maxWeight;

    private Long minWeight;
}

package com.ruoyi.common.utils.message;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.JacksonUtil;
import com.ruoyi.common.utils.message.request.ReqPo;
import com.ruoyi.common.utils.message.request.mpTem.MpTemplateMsg;
import com.ruoyi.common.utils.message.request.weappTem.WechatTemplate;
import com.ruoyi.common.utils.message.responce.RespPo;
import com.ruoyi.common.utils.message.tmpl.TmplBase;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * 发送微信统一服务消息工具类
 * 
 * 下发小程序和公众号统一的服务消息
 * 发送主体 可以是小程序,也可以是公众号
 * 
 * <AUTHOR>
 *
 */
@SuppressWarnings("deprecation")
@Component
public class WechatMessageUtil {
	private Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	// 发送模版消息接口
	final String sendMessageUrl = "https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?access_token=ACCESS_TOKEN";

	// 发送模版消息接口
	final String templateMessageUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=ACCESS_TOKEN";

//	@Value("${wx.public.appid}")
//    String publicAppid;
//	@Value("${wx.public.secret}")
//    String publicSecret;
	@Value("${wx-public.access-token-url}")
	String accessTokenUrl;

	@Value("${wx-public.update-access-token-url}")
	String updateAccessTokenUrl;

	@Autowired
    RestTemplate restTemplate;

	/**
	 * 调用发送公众号消息接口
	 * @param wechatTemplate
	 * @param accessToken
	 * @return
	 */
	public RespPo templateMsgSend(WechatTemplate wechatTemplate, String accessToken) {
		String url = templateMessageUrl.replace("ACCESS_TOKEN", accessToken);
		String string = JSONObject.toJSON(wechatTemplate).toString();
		restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
		ResponseEntity<RespPo> postForEntity = restTemplate.postForEntity(url,string,RespPo.class);
		return postForEntity.getBody();
	}

	/**
	 * 发送模版信息
	 * 
	 * @param reqPo
	 * @return
	 * @throws JsonProcessingException
	 * @throws UnsupportedEncodingException
	 */
	@SuppressWarnings({ "resource" })
	public RespPo uniformSend(ReqPo reqPo) throws JsonProcessingException, UnsupportedEncodingException {
		RespPo respPo = null;
		LOGGER.info("发送公众号模版信息 start");
		
		String accessToken = reqPo.getAccess_token();
		String reqPoJson = JacksonUtil.defaultInstance().pojo2json(reqPo);
		LOGGER.info("发送公众号模版信息 - 请求参数: " + reqPoJson);
		// 获取小程序全局唯一后台接口调用凭据 接口
		String url = sendMessageUrl.replace("ACCESS_TOKEN", accessToken);

		HttpPost request = new HttpPost(url);
		StringEntity stringEntity = new StringEntity(reqPoJson, ContentType.DEFAULT_TEXT.withCharset(Charset.defaultCharset()));
		request.setEntity(stringEntity);
		request.setHeader(new BasicHeader("Content-Type", "application/json"));
		HttpResponse response = null;

		try {
			HttpClient client = new DefaultHttpClient();
			response = client.execute(request);
			if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				String strResult = EntityUtils.toString(response.getEntity());
				if (!StringUtils.isEmpty(strResult)) {
					LOGGER.info("发送公众号模版信息 - 返回: " + strResult);
					respPo = JacksonUtil.defaultInstance().json2pojo(strResult, RespPo.class);
				}
			}
		} catch (IOException e) {
			e.printStackTrace();
			LOGGER.error("发送公众号模版信息失败!!!");
		}

		return respPo;
	}

	/**
	 * 组装发送模版信息所需的请求参数 - Deliveryman
	 * @param base 公众号模版类
	 * @param openId 小程序的openid
	 * @return
	 * @throws Exception
	 */
	public ReqPo packagePublicReqPo(TmplBase base, String openId) throws Exception {
		ReqPo reqPo = new ReqPo();
		MpTemplateMsg mpTemplateMsg = new MpTemplateMsg();

		String accessToken = getPublicAccessToken();
		if (StringUtils.isEmpty(accessToken)) {
			LOGGER.info("公众号主体无法获得有效的accessToken");
			return null;
		}

		reqPo.setTouser(openId);//用户openid，可以是小程序的openid，也可以是mp_template_msg.appid对应的公众号的openid
		reqPo.setAccess_token(accessToken);
		reqPo.setMp_template_msg(mpTemplateMsg);
		reqPo.setWeapp_template_msg(null);

		mpTemplateMsg.setAppid(null);// 公众号appid
		mpTemplateMsg.setTemplate_id(base.tmplID);// template_id
		mpTemplateMsg.setData(base.getData());// 公众号模板消息的数据
		mpTemplateMsg.setMiniprogram(base.miniprogram);// 公众号模板消息所要跳转的小程序
		mpTemplateMsg.setUrl(null);// 公众号模板消息所要跳转的url

		return reqPo;
	}
	public String getPublicAccessToken() throws URISyntaxException {



		System.out.println(accessTokenUrl);
		return restTemplate.getForObject(new URI(accessTokenUrl), String.class);
	}

	public AjaxResult updateAccessToken() throws URISyntaxException {
		return restTemplate.getForObject(new URI(updateAccessTokenUrl),AjaxResult.class);
	}

}

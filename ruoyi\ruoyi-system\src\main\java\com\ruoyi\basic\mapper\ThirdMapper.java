package com.ruoyi.basic.mapper;

import com.ruoyi.basic.domain.vo.ShipPersonVo;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.ship.domain.vo.BargeThirdVo;
import com.ruoyi.ship.domain.vo.CargoconsignmentThirdVo;
import com.ruoyi.ship.domain.vo.ShipInfoThirdVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 第三库 生产系统操作
 * @date 2024/12/20 9:19
 */
@DataSource(value = DataSourceType.THIRD)
public interface ThirdMapper {

    // 根据指导员名称查询大船信息
    List<ShipPersonVo> selectShipPersonList(ShipPersonVo shipPersonVo);

    // 还未离港、意向部门为港盛的大船信息
    List<ShipInfoThirdVo> selectShipInfoThirdList(ShipInfoThirdVo shipInfoThirdVo);

    // 根据到验号查询提单信息
    List<CargoconsignmentThirdVo> selectCargoconsignmentThirdList(String voyageNumber);

    // 根据提单号查询驳船信息
    List<BargeThirdVo> selectBargeThirdList(String billNo);

}

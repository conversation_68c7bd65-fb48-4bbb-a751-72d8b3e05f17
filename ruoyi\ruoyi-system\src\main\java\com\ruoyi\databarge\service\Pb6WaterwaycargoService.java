package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.databarge.domain.Pb6WaterCargoMobile;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.domain.dto.Pb6WaterCargoSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeFileEmailVo;
import com.ruoyi.databarge.domain.vo.ConfrimPb6WaterwaycargoVo;
import com.ruoyi.databarge.domain.vo.Pb6WaterCargoVO;
import com.ruoyi.databarge.domain.vo.WaterWayCargoH5Vo;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface Pb6WaterwaycargoService extends IService<Pb6Waterwaycargo> {

    Pb6Waterwaycargo searchWaterwaycargoByWaterwaycargoid(String Waterwaycargoid);

    IPage<Pb6WaterCargoVO> searchPagePb6WaterCargo(Pb6WaterCargoSearchDTO pb6WaterCargoSearchDTO);

    //查询全部不分页，用于下载
    List<Pb6WaterCargoVO> searchListPb6WaterCargo(Pb6WaterCargoSearchDTO pb6WaterCargoSearchDTO);

    List<Pb6Waterwaycargo> findByPb6WaterwaycargoFuzzy(String waterwaycargoid);

    List<Pb6WaterCargoMobile> searchPb6WaterCargoForMobile(int status);

    List<Pb6WaterCargoMobile> searchPb6WaterCargoForMobileWorking(String keyWord);

    Long searchLoadMeterId(String loadometerid);

    /**
     * 判断是否能支付、预约及报到
     * @param pb6Waterwaycargo 水路运单
     * @param sign 标识，用于返回错误提示 0支付、1预约、2报到
     * @return true 代表可以向后执行
     */
    void canOrderOrForcast(Pb6Waterwaycargo pb6Waterwaycargo, int sign);

    List<String> waterwayCargoIdStamp(String applytime);

    //查询南粮未确认实装的水路运单
    List<Pb6WaterCargoVO> selectNLConfirmAuto(String startTime,String endTime);

    //自动确认
    boolean updateAuto(WaterwayCargo waterwayCargo);

    // 水路运单List
    List<Pb6Waterwaycargo> selectPb6WaterwaycargoList(Pb6Waterwaycargo pb6Waterwaycargo);

    // 指导员端水路运单List
    List<WaterWayCargoH5Vo> selectPb6WaterwaycargoListH5(Pb6Waterwaycargo pb6Waterwaycargo);

    // 更新水路运单
    AjaxResult updatePb6Waterwaycargo(Pb6Waterwaycargo pb6Waterwaycargo);

    // 指导员确认实装数
    AjaxResult confirmPb6Waterwaycargo(Pb6Waterwaycargo pb6Waterwaycargo1);

    // 指导员确认是否放行
    AjaxResult confirmRelease(Pb6Waterwaycargo pb6Waterwaycargo);



    /**
     * @param
     * @return
     * @description 发送驳船文件邮件
     * <AUTHOR>
     * @date 2025/1/16 21:29
     */
    void sendBargeFilesViaEmail(BargeFileEmailVo bargeFileEmailVo) throws IOException;



}

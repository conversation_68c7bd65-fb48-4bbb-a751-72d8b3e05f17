package com.ruoyi.basic.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/1/11 16:58
 */
@KeySequence(value = "SEQ_BASIC_RATE")
@TableName(value ="BASIC_RATE")
@Data
public class BasicRate extends BaseEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long rateId;

    /**
     * 客户
     */
    private String cusName;

    /**
     * 货类
     */
    private String goodsCategory;

    private Long goodsCategoryId;

    /**
     * 结算方式
     */
    private String settlementMethod;

    /**
     * 包装方式
     */
    private String packingMethod;

    /**
     *
     */
    private BigDecimal rate;

    /**
     *
     */
    private Long cusId;

    /**
     *
     */
    private String settlementMethodCode;

    /**
     *
     */
    private String packingMethodCode;

    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public BasicRate(String cusName, String goodsCategory, String settlementMethod, String packingMethod) {
        this.cusName = cusName;
        this.goodsCategory = goodsCategory;
        this.settlementMethod = settlementMethod;
        this.packingMethod = packingMethod;
    }

    public BasicRate() {
    }
}

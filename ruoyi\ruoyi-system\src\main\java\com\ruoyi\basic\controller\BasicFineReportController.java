package com.ruoyi.basic.controller;

import com.ruoyi.basic.domain.BasicFineReport;
import com.ruoyi.basic.service.BasicFineReportService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1
 * @Description
 */
@RestController
@RequestMapping("/basic/fineReport")
public class BasicFineReportController extends BaseController {

    @Autowired
    BasicFineReportService basicFineReportService;

    //查询
    @GetMapping("/list")
    public TableDataInfo tableDataInfo(BasicFineReport basicFineReport){
        startPage();
        List<BasicFineReport> list =basicFineReportService.basicFineReportList(basicFineReport);
        System.out.println(list);
        return getDataTable(list);

    }
    //新增
    @PostMapping("/add")
    public AjaxResult add(@RequestBody BasicFineReport basicFineReport) {
        basicFineReportService.save(basicFineReport);
        return AjaxResult.success(basicFineReport);
    }

}

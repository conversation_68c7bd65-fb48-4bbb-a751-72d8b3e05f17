package com.ruoyi.app.controller.databarge;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.databarge.DataScope;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.dto.ComprehensiveQueryDTO;
import com.ruoyi.databarge.domain.vo.StatisticalQueryVO;
import com.ruoyi.databarge.mapper.ComprehensiveQueryMapper;
import com.ruoyi.databarge.mapper.UploadAddressDomainMapper;
import com.ruoyi.databarge.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/barge/comprehensiveQuery")
@Slf4j
public class ComprehensiveQueryController {
    @Autowired
    Pb6BargeCheckMessageService pb6BargeCheckMessageService;
    @Autowired
    Pb30OlUserService pb30OlUserService;
    @Autowired
    Pb6CargoconsignmentService pb6CargoconsignmentService;
    @Autowired
    Pb6BargeworkService pb6BargeworkService;
    @Autowired
    Pb6WaterwaycargoService pb6WaterwaycargoService;
    @Autowired
    UploadAddressDomainMapper uploadAddressDomainMapper;
    @Autowired
    ComprehensiveQueryMapper comprehensiveQueryMapper;

    @PostMapping("/homeQuery")
    public AjaxResult homeQuery(@RequestBody ComprehensiveQueryDTO comprehensiveQueryDTO){
        System.out.println(comprehensiveQueryDTO);
        DataScope.getDataScope("t", comprehensiveQueryDTO, Boolean.FALSE);
        if (StringUtils.isNotBlank(comprehensiveQueryDTO.getStartTime())){
            comprehensiveQueryDTO.setStartTime(comprehensiveQueryDTO.getStartTime()+" 00:00:00");
        }
        if (StringUtils.isNotBlank(comprehensiveQueryDTO.getEndTime())){
            comprehensiveQueryDTO.setEndTime(comprehensiveQueryDTO.getEndTime()+" 24:59:59");
        }
        StatisticalQueryVO statisticalQueryVO=new  StatisticalQueryVO();
        //驳船主管理员注册：
        List<Pb6BargeCheckMessage> pb6BargeCheckMessageList=pb6BargeCheckMessageService.list(Wrappers.<Pb6BargeCheckMessage>lambdaQuery().
                eq(Pb6BargeCheckMessage::getDelflag,0).in(Pb6BargeCheckMessage::getMtype,"0","1","2").eq(Pb6BargeCheckMessage::getAuditflag,1).
                notLike(Pb6BargeCheckMessage::getBargename,"测试").
                gt(Pb6BargeCheckMessage::getAudittime,Timestamp.valueOf(comprehensiveQueryDTO.getStartTime())).
                lt(Pb6BargeCheckMessage::getAudittime,Timestamp.valueOf(comprehensiveQueryDTO.getEndTime())));
        statisticalQueryVO.setPb6BargeCheckMessageListSize(pb6BargeCheckMessageList.size());
        //船公司注册：
        List<Pb30OlUser> pb30OlUserList=pb30OlUserService.list(Wrappers.<Pb30OlUser>lambdaQuery().like(Pb30OlUser::getPassport,"2").eq(Pb30OlUser::getRoleId,3).
                gt(Pb30OlUser::getAuditDate,comprehensiveQueryDTO.getStartTime()).lt(Pb30OlUser::getAuditDate,comprehensiveQueryDTO.getEndTime()));
        statisticalQueryVO.setPb30OlUserListSize(pb30OlUserList.size());
        //线上办理托运单：
        List<Pb6Cargoconsignment> pb6CargoconsignmentList=comprehensiveQueryMapper.searchPb6Cargoconsignment(comprehensiveQueryDTO);
        statisticalQueryVO.setPb6CargoconsignmentListSize(pb6CargoconsignmentList.size());
        //港务分公司';

        //人工报到的数量：
        comprehensiveQueryDTO.setRegisterprincipal("人工报到");
        List<Pb6Bargework> pb6BargeworkListSD=comprehensiveQueryMapper.searchPb6Bargework(comprehensiveQueryDTO);
        statisticalQueryVO.setPb6BargeworkListSDSize(pb6BargeworkListSD.size());
        //船方自动报到的数量：
        comprehensiveQueryDTO.setRegisterprincipal("自动报到");
        List<Pb6Bargework> pb6BargeworkList=comprehensiveQueryMapper.searchPb6Bargework(comprehensiveQueryDTO);
        statisticalQueryVO.setPb6BargeworkListZDSize(pb6BargeworkList.size());
        //确认实装数的驳船数量：
        List<Pb6Waterwaycargo> pb6WaterwaycargoList=comprehensiveQueryMapper.searchPb6Waterwaycargo(comprehensiveQueryDTO);
        statisticalQueryVO.setPb6WaterwaycargoListSize(pb6WaterwaycargoList.size());
        //港务分公司';
        //生成电子水路运单：
        List<UploadAddressDomain> uploadAddressDomainList=comprehensiveQueryMapper.searchUploadAddress(comprehensiveQueryDTO);
        statisticalQueryVO.setUploadAddressDomainListSize(uploadAddressDomainList.size());

        List<StatisticalQueryVO> statisticalQueryVOList=new ArrayList<>();
        statisticalQueryVOList.add(statisticalQueryVO);
        return AjaxResult.success(statisticalQueryVOList);
    }
}

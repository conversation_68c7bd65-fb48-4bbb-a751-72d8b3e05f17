package com.ruoyi.framework.security.provider;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.GMUser;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.UserType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.exception.FirstLoginException;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.wechart.WxAppletOpenidUtil;
import com.ruoyi.common.utils.wechart.WxOpenIdPo;
import com.ruoyi.databarge.domain.Pb30OlUser;
import com.ruoyi.databarge.service.Pb30OlUserService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.pubuser.domain.PubUser;
import com.ruoyi.pubuser.service.PubUserService;
import com.ruoyi.ssouser.domain.SSOUser;
import com.ruoyi.ssouser.service.SSOUserSerivce;
import com.ruoyi.system.service.AuthorizeService;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import org.apache.commons.codec.digest.Md5Crypt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @Description 自定义账号密码认证
 * <AUTHOR>
 * @Date 2020/7/27 10:56
 */
@Component
public class MyAuthenticationProvider implements AuthenticationProvider {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ISysUserService iSysUserService;
    @Autowired
    private SSOUserSerivce ssoUserSerivce;//统一认证服务类
    @Autowired
    private BCryptPasswordEncoder passwordEncorder;
    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private WxAppletOpenidUtil wxAppletOpenidUtil;
    @Autowired
    private ISysMenuService iSysMenuService;
    @Autowired
    private AuthorizeService authorizeService;
    @Autowired
    private Pb30OlUserService pb30OlUserService;
    @Autowired
    private PubUserService pubUserService;
    @Autowired
    private ISysRoleService sysRoleService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String presentedPassword = (String) authentication.getCredentials();
        MyUsernamePasswordAuthenticationToken authenticationToken = (MyUsernamePasswordAuthenticationToken) authentication;
        String userType = authenticationToken.getUserType();
        String loginType = authenticationToken.getLoginType();
        String wxCode = authenticationToken.getWxCode();
        String phoneNumber = authenticationToken.getPhoneNumber();
        Integer index = authenticationToken.getIndex();

        UserDetails userDeatils = null;
        //对用户进行分类,不同的用户走不同的认证流程
//        CONSIGNOR("10","货主"),
//        WAYBILLCONTACT("11","托运单联系人"),
//        CARRIERADMIN("12","船公司管理员"),
//        CARRIEUSER("13","船公司业务员"),
//        BARGEADMIN("14","驳船管理员"),
//        BARGEUSER("15","驳船业务员");
        if ("pc".equalsIgnoreCase(loginType)) {//pc端登陆
            if ("bargeCompany".equalsIgnoreCase(userType)) {//船公司管理员||船公司业务员

                GMUser gmUser = iSysUserService.selectAppUserByGmis(username);
                SysUser sysUser = iSysUserService.selectUserByGmUserId(gmUser.getId());
                sysUser.setCompanyId(gmUser.getCompanyId());
                sysUser.setCompanyName(gmUser.getCompanyName());
                sysUser.setPassport(gmUser.getPassport());
                sysUser.setGmUser(gmUser);
                sysUser.setNickName(gmUser.getContactPerson());

                userDeatils = this.createLoginUser(sysUser);
                UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDeatils, authentication.getCredentials(), userDeatils.getAuthorities());
                result.setDetails(authentication.getDetails());
                return result;
            } else {//默认物流公司
                // 根据用户名获取用户信息
                SysUser sysUser = iSysUserService.selectUserByUserName(username);
                if (sysUser != null) {
                    userDeatils = this.createLoginUser(sysUser);
                    //userDeatils = new User(username, sysUser.getPassword(), AuthorityUtils.commaSeparatedStringToAuthorityList("USER"));
                    if (authentication.getCredentials() == null) {
                        throw new BadCredentialsException("登录名或密码错误");
                    } else if (!this.passwordEncorder.matches(presentedPassword, userDeatils.getPassword())) {
                        throw new BadCredentialsException("登录名或密码错误");
                    } else {
                        UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDeatils, authentication.getCredentials(), userDeatils.getAuthorities());
                        result.setDetails(authentication.getDetails());
                        return result;
                    }
                } else {
                    throw new BadCredentialsException("用户名不存在");
//                    sysUser = new SysUser();
//                    PubUser pubUser = pubUserService.selectByUserid(username.toUpperCase());
//                    if(pubUser == null){
//                        throw new BadCredentialsException("用户名不存在");
//                    } else {
//                        sysUser.setUserType(UserType.PUBUSER.getCode());
//                        sysUser.setUserName(username);
//                        sysUser.setNickName(pubUser.getUsername());
//                        sysUser.setDeptId(pubUser.getComid());
//
//                        SysRole sysRole = sysRoleService.selectRoleById(201L);
//                        List<SysRole> sysRoleList = new ArrayList<>();
//                        sysRoleList.add(sysRole);
//
//                        SysRole sysRoleDataScope = null;
//                        if(pubUser.getComid() == 1){
//                            // 82 新沙数据权限
//                            sysRoleDataScope = sysRoleService.selectRoleById(82L);
//                        } else if(pubUser.getComid() == 3){
//                            // 161 新港数据权限
//                            sysRoleDataScope = sysRoleService.selectRoleById(161L);
//                        } else if(pubUser.getComid() == 4){
//                            // 181 西基数据权限
//                            sysRoleDataScope = sysRoleService.selectRoleById(181L);
//                        } else if(pubUser.getComid() == 16){
//                            // 81 南粮数据权限
//                            sysRoleDataScope = sysRoleService.selectRoleById(81L);
//                        }
//                        sysRoleList.add(sysRoleDataScope);
//
//                        sysUser.setRoles(sysRoleList);
//                        userDeatils = this.createLoginUser(sysUser);
//
//                        if (authentication.getCredentials() == null) {
//                            throw new BadCredentialsException("登录名或密码错误");
//                        } else if (!Md5Utils.encodeByMd5(presentedPassword).toUpperCase().equals(pubUser.getUserpwd())) {
//                            throw new BadCredentialsException("登录名或密码错误");
//                        } else {
//                            UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDeatils, authentication.getCredentials(), userDeatils.getAuthorities());
//                            result.setDetails(authentication.getDetails());
//                            return result;
//                        }
//                    }
                }
            }
        } else {//app小程序登陆
            /**
             * first:app登陆流程（网厅注册用户：货主、船公司）
             * 1.根据wxcode获取openid,并根据openid查询用户是否存在
             * 2.如果不存在，校验用户密码是否已经填写，
             *     1.如果没有填写(首次登陆)抛出异常，跳转到账号密码填写界面
             *     2.如果已经填写第三方账号密码验证;并同步用户信息
             * 2.如果用户存在，第三方校验用户(手机号)的有效性
             *
             * second:app登陆流程（非网厅注册用户：驳船用户、托用单联系人）
             * 1.根据wxcode获取openid,并根据openid查询用户是否存在
             * 2.如果用户不存在，默认注册;
             * 2.如果用户已存在，获取权限
             */
            if (StringUtils.isEmpty(wxCode)) {
                logger.info("微信code参数异常{}->" + wxCode);
                throw new CustomException("微信code参数异常");
            }

            //根据微信wxCode换取opneId，区分两个小程序
            if ("gzonlineuser".equalsIgnoreCase(userType)) {//(网厅用户)(货主|船公司用户)
                //获取微信openid
                /*WxOpenIdPo wxOpenIdPo = wxAppletOpenidUtil.GetWxOpenIdConsignor(wxCode);
                if (wxOpenIdPo == null || StringUtils.isEmpty(wxOpenIdPo.getOpenid())) {
                    logger.info("登录认证失败,请重新登陆!" + wxOpenIdPo == null ? "" : wxOpenIdPo.getErrmsg());
                    throw new CustomException("获取微信openid异常");
                }*/
                WxOpenIdPo wxOpenIdPo = this.getWxOpenIdPo(wxCode);
                String openId = wxOpenIdPo.getOpenid();
                String unionId = wxOpenIdPo.getUnionid();
                //SysUser sysUser = iSysUserService.selectUserByOpenId(openId);

                List<SysUser> userList = iSysUserService.selectUserListByOpenId(openId);
                AtomicReference<SysUser> sysUser = new AtomicReference<>();
                for (SysUser user : userList) {
                    if (UserType.CARRIERADMIN.getCode().equals(user.getUserType())
                            || UserType.CARRIEUSER.getCode().equals(user.getUserType())
                            || UserType.CONSIGNOR.getCode().equals(user.getUserType())) {
                        sysUser.set(user);
                        break;
                    }
                }

                // 查询是否有两个账号 货主账号和船公司账号(船公司 包含管理员和业务员)
                List<SysUser> cargoList = iSysUserService.getCargoUserByOpenId(openId,"20");
                if (cargoList.size()>1) {
                    cargoList.forEach(item->{
                        if (UserType.CARRIERADMIN.getCode().equals(item.getUserType())) {
                            sysUser.set(item);
                        } else if (UserType.CARRIEUSER.getCode().equals(item.getUserType()) && !UserType.CARRIERADMIN.getCode().equals(sysUser.get().getUserType())) {
                            sysUser.set(item);
                        }
                    });
                }

                if (sysUser.get() == null) {//无用户
                    if (!StringUtils.isEmpty(username)) {
                        SSOUser tempUser = new SSOUser();
                        tempUser.setAccount(username);
                        SSOUser ssoUser = ssoUserSerivce.getSSOUser(tempUser);
                        if (ssoUser == null) {
                            throw new BadCredentialsException("用户名不存在");
                        } else {
                            if (Md5Utils.MD5EncodeUtf8(presentedPassword).equals(ssoUser.getPassword())) {//统一认证密码校验
                                //校验是否用户在第三方已经认证
                                GMUser gmUser = iSysUserService.selectAppUserByGmisOrigin(username);
                                if (gmUser != null) {//已授权认证
                                    //判断此账号是否已经存在
                                    sysUser.set(iSysUserService.selectUserByGmUserId(gmUser.getId()));
                                    if (sysUser.get() == null) {
                                        //更新到业务数据库
                                        sysUser.set(new SysUser());
                                        /*if ("1".equalsIgnoreCase(gmUser.getCustomerTypeId())) {//货主
                                            sysUser.get().setUserType(UserType.CONSIGNOR.getCode());
                                        } else if ("5".equalsIgnoreCase(gmUser.getCustomerTypeId())) {//船公司用户
                                            sysUser.get().setUserType(gmUser.getRoleId() == 4 ? UserType.CARRIEUSER.getCode() : UserType.CARRIERADMIN.getCode());
                                        } else {
                                            throw new BadCredentialsException("非货主、船公司用户");
                                        }*/
                                        if ("3".equalsIgnoreCase(gmUser.getCustomerTypeId()) || "1".equalsIgnoreCase(gmUser.getCustomerTypeId())|| "2".equalsIgnoreCase(gmUser.getCustomerTypeId())) {//货主
                                            sysUser.get().setUserType(UserType.CONSIGNOR.getCode());
                                        } else if ("5".equalsIgnoreCase(gmUser.getCustomerTypeId())) {//船公司用户
                                            throw new CustomException("船公司用户请前往网上营业厅散杂货船公司管理业务注册");
                                        } else {
                                            throw new CustomException("非货主、船公司用户");
                                        }
                                        sysUser.get().setOpenId(openId);
                                        sysUser.get().setUnionId(unionId);
                                        sysUser.get().setUserName(gmUser.getUserId());
                                        sysUser.get().setPhonenumber(gmUser.getContactCellPhone());
                                        sysUser.get().setCompanyId(gmUser.getCompanyId());
                                        sysUser.get().setCompanyName(gmUser.getCompanyName());
                                        sysUser.get().setComId(gmUser.getComId());
                                        sysUser.get().setEmail(ssoUser.getEmail());
                                        sysUser.get().setGmUserId(gmUser.getId());
                                        //sysUser.get().setNickName(gmUser.getContactPerson());
                                        //用户添加，授权
                                        int i = iSysUserService.insertUser(sysUser.get());
                                    } else {
                                        /*if(sysUser.get().getOpenId()!=null){//如果此账号绑定用户，则不能登陆
                                            throw new FirstLoginException("此账号已绑定用户", HttpStatus.FIRST_LOGIN);
                                        }*/
                                        sysUser.get().setUserName(gmUser.getUserId());
                                        sysUser.get().setPhonenumber(gmUser.getContactCellPhone());
                                        sysUser.get().setCompanyId(gmUser.getCompanyId());
                                        sysUser.get().setCompanyName(gmUser.getCompanyName());
                                        sysUser.get().setNickName(gmUser.getContactPerson());
                                    }
                                    sysUser.get().setGmUser(gmUser);

                                    // 校验船公司是否审核通过
                                    if (!UserType.CONSIGNOR.getCode().equals(sysUser.get().getUserType())) {
                                        this.checkGmUser(gmUser);
                                        // 校验是否有权限
                                        this.checkUserAuth(sysUser.get());
                                    } else {
                                        // 货主账号查询pb30_ol_user表里面的passport字段是否含有1，若有则登陆船公司
                                        Pb30OlUser olUser = pb30OlUserService.getById(sysUser.get().getGmUserId());
                                        if (olUser != null) {
                                            String passport = olUser.getPassport();
                                            String[] array = passport.split(",");
                                            for (String s : array) {
                                                if ("2".equals(s)) {
                                                    SysUser carrieUser = new SysUser();
                                                    BeanUtils.copyBeanProp(carrieUser, sysUser.get());
                                                    carrieUser.setUserType(UserType.CARRIEUSER.getCode());
                                                    sysUser.set(carrieUser);

                                                    this.checkGmUser(gmUser);
                                                    // 校验是否有权限
                                                    this.checkUserAuth(sysUser.get());
                                                }
                                            }
                                        }
                                    }

                                    // 重新绑定openId和unionId
                                    this.updateUserUnionIdAndOpenId(sysUser.get().getUserId(),unionId, openId);
                                    sysUser.get().setOpenId(openId);
                                    sysUser.get().setUnionId(unionId);

                                    /*userDeatils = this.createLoginUser(sysUser.get());
                                    UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDeatils, authentication.getCredentials(), userDeatils.getAuthorities());
                                    result.setDetails(authentication.getDetails());
                                    return result;*/
                                    return this.getUserAuthToken(sysUser.get(), authentication);

                                } else {
                                    throw new CustomException("用户未认证授权，请联系管理员授权");
                                }

                            } else {
                                throw new CustomException("登录名或密码错误");
                            }
                        }
                    } else {
                        throw new FirstLoginException("首次登陆请输入账号密码", HttpStatus.FIRST_LOGIN);
                    }

                } else {//登陆

                    // 校验船公司是否审核通过
                    GMUser gmUser = iSysUserService.selectAppUserByGmis(username);
                    if (!UserType.CONSIGNOR.getCode().equals(sysUser.get().getUserType())) {
                        this.checkGmUser(gmUser);
                        // 校验是否有权限
                        this.checkUserAuth(sysUser.get());
                    } else {
                        // 货主账号查询pb30_ol_user表里面的passport字段是否含有1，若有则登陆船公司
                        Pb30OlUser olUser = pb30OlUserService.getById(sysUser.get().getGmUserId());
                        if (olUser != null) {
                            String passport = olUser.getPassport();
                            String[] array = passport.split(",");
                            for (String s : array) {
                                if ("2".equals(s)) {
                                    SysUser carrieUser = new SysUser();
                                    BeanUtils.copyBeanProp(carrieUser, sysUser.get());
                                    carrieUser.setUserType(UserType.CARRIEUSER.getCode());
                                    sysUser.set(carrieUser);

                                    this.checkGmUser(gmUser);
                                    // 校验是否有权限
                                    this.checkUserAuth(sysUser.get());
                                }
                            }
                        }
                    }

                    // 重新绑定openId和unionId
                    this.updateUserUnionIdAndOpenId(sysUser.get().getUserId(),unionId, openId);
                    sysUser.get().setUnionId(unionId);
                    sysUser.get().setOpenId(openId);

                    /*userDeatils = this.createLoginUser(sysUser.get());
                    UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDeatils, authentication.getCredentials(), userDeatils.getAuthorities());
                    result.setDetails(authentication.getDetails());
                    return result;*/
                    return this.getUserAuthToken(sysUser.get(), authentication);
                }

            } else if ("waybillscontactuser".equalsIgnoreCase(userType)) {//托运单联系人

                //获取微信openid
                /*WxOpenIdPo wxOpenIdPo = wxAppletOpenidUtil.GetWxOpenIdConsignor(wxCode);
                if (wxOpenIdPo == null || StringUtils.isEmpty(wxOpenIdPo.getOpenid())) {
                    logger.info("登录认证失败,请重新登陆!" + wxOpenIdPo == null ? "" : wxOpenIdPo.getErrmsg());
                    throw new CustomException("获取微信openid异常");
                }*/
                WxOpenIdPo wxOpenIdPo = this.getWxOpenIdPo(wxCode);
                String openId = wxOpenIdPo.getOpenid();
                String unionId = wxOpenIdPo.getUnionid();

                // SysUser sysUser = iSysUserService.selectUserByUserName(phoneNumber);
                //SysUser sysUser = iSysUserService.selectUserByOpenId(openId);
                List<SysUser> userList = iSysUserService.selectUserListByOpenId(openId);
                AtomicReference<SysUser> sysUser = new AtomicReference<>();
                userList.forEach(item -> {
                    if (UserType.WAYBILLCONTACT.getCode().equals(item.getUserType())) {
                        sysUser.set(item);
                    }
                });
                if (sysUser.get() == null) {
                    if (StringUtils.isEmpty(phoneNumber) || ObjectUtil.isNull(phoneNumber) || "".equals(phoneNumber)){
                        throw new FirstLoginException("请重新点击获取手机号！", 602);
                    }
                    int i = iSysUserService.selectRationContactNumberByDetail(phoneNumber);
                    if (i > 0) {
                        sysUser.set(new SysUser());
                        sysUser.get().setUserType(UserType.WAYBILLCONTACT.getCode());
                        sysUser.get().setOpenId(openId);
                        sysUser.get().setUnionId(unionId);
                        sysUser.get().setUserName(phoneNumber);
                        sysUser.get().setPhonenumber(phoneNumber);
                        //sysUser.get().setNickName(phoneNumber);
                        int i1 = iSysUserService.insertUser(sysUser.get());
                        if (i1 > 0) {
                            //sysUser = iSysUserService.selectUserByOpenId(openId);
                            /*userDeatils = this.createLoginUser(sysUser.get());
                            UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDeatils, authentication.getCredentials(), userDeatils.getAuthorities());
                            result.setDetails(authentication.getDetails());
                            return result;*/
                            return this.getUserAuthToken(sysUser.get(), authentication);
                        }
                    } else {
                        throw new CustomException("您不是托运单联系人");
                    }
                } else {

                    // 重新绑定openId和unionId
                    this.updateUserUnionIdAndOpenId(sysUser.get().getUserId(),unionId, openId);
                    sysUser.get().setUnionId(unionId);
                    sysUser.get().setOpenId(openId);

                    /*userDeatils = this.createLoginUser(sysUser.get());
                    UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDeatils, authentication.getCredentials(), userDeatils.getAuthorities());
                    result.setDetails(authentication.getDetails());
                    return result;*/
                    return this.getUserAuthToken(sysUser.get(), authentication);
                }
            } else if ("bargeuser".equalsIgnoreCase(userType)) {//驳船管理员||驳船业务员
                //获取微信openid
                WxOpenIdPo wxOpenIdPo = wxAppletOpenidUtil.GetWxOpenIdBarge(wxCode);
                if (wxOpenIdPo == null || org.apache.commons.lang3.StringUtils.isEmpty(wxOpenIdPo.getOpenid())) {
                    logger.info("登录认证失败,请重新登陆!" + wxOpenIdPo == null ? "" : wxOpenIdPo.getErrmsg());
                    throw new CustomException("获取微信openid异常");
                }
                String openId = wxOpenIdPo.getOpenid();
                String unionId = wxOpenIdPo.getUnionid();

                List<SysUser> userList = iSysUserService.getUserByOpenId(openId);
                        boolean flag = false;
                        if ((StringUtils.isEmpty(phoneNumber) || "".equals(phoneNumber))){
                            for (SysUser user : userList) {
                                if ((ObjectUtil.isNull(user) || StringUtils.isEmpty(user.getPhonenumber()) || ObjectUtil.isNull(user.getPhonenumber()) || "".equals(user.getPhonenumber()))) {
                                    flag = true;
                                }
                                else {
                                    phoneNumber = user.getPhonenumber();
                                    flag  = false;
                                }
                            }
                            if (userList.size() == 0){
                                flag = true;
                            }
                        }
                        if (flag){
                            throw new FirstLoginException("请点击重新登录！",603);
                        }
                SysUser su = new SysUser();
                su.setUserType(UserType.BARGEADMIN.getCode());
                su.setPhonenumber(phoneNumber);
                SysUser sysUser = iSysUserService.getUserByPhone(su);

                if (sysUser == null) {
                    su.setUserType(UserType.BARGEUSER.getCode());
                    SysUser crew = iSysUserService.getUserByPhone(su);
                    if (crew != null) {
                        crew.setUnionId(unionId);
                        crew.setOpenId(openId);
                        iSysUserService.updateUserProfile(crew);
                        sysUser = crew;
                    }
                }

                if (sysUser == null) {//无用户
                    //更新到业务数据库
                    sysUser = new SysUser();
                    sysUser.setUserType(null);//首次登陆注册，设置用户类型为空，在界面选择绑定。
                    sysUser.setOpenId(openId);
                    sysUser.setUnionId(unionId);
                    sysUser.setUserName(phoneNumber);
                    sysUser.setPhonenumber(phoneNumber);
                    //sysUser.setNickName(phoneNumber);
                    //用户添加，授权
                    int i = iSysUserService.insertUser(sysUser);
                    if (i > 0) {
                        userDeatils = this.createLoginUser(sysUser);
                        UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDeatils, authentication.getCredentials(), userDeatils.getAuthorities());
                        result.setDetails(authentication.getDetails());
                        return result;
                    }

                } else {//登陆


                    // 重新绑定openId和unionId
                    this.updateUserUnionIdAndOpenId(sysUser.getUserId(), unionId, openId);
                    sysUser.setUnionId(unionId);
                    sysUser.setOpenId(openId);


                    return this.getUserAuthToken(sysUser, authentication);
                }
            }
        }
        return null;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return true;
    }

    /**
     * 重新绑定用户openId和UnionId
     * @param userId 用户id
     * @param unionId
     * @param openId
     */
    public void updateUserUnionIdAndOpenId(Long userId, String unionId, String openId) {
        SysUser su = new SysUser();
        su.setUserId(userId);
        su.setUnionId(unionId);
        su.setOpenId(openId);
        iSysUserService.updateUserProfile(su);
    }

    /**
     * 获取微信openId
     * @param wxCode 微信code
     * @return
     */
    public WxOpenIdPo getWxOpenIdPo(String wxCode) {
        WxOpenIdPo wxOpenIdPo = wxAppletOpenidUtil.GetWxOpenIdConsignor(wxCode);
        if (wxOpenIdPo == null || StringUtils.isEmpty(wxOpenIdPo.getOpenid())) {
            logger.info("登录认证失败,请重新登陆!" + wxOpenIdPo == null ? "" : wxOpenIdPo.getErrmsg());
            throw new CustomException("获取微信openid异常");
        }
        return wxOpenIdPo;
    }

    /**
     * 创建登录用户
     * @param user 当前用户对象
     * @return 返回登录用户和权限
     */
    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user, permissionService.getMenuPermission(user));
    }

    /**
     *
     * @param sysUser 用户对象
     * @param authentication 权限
     * @return UsernamePasswordAuthenticationToken
     */
    public UsernamePasswordAuthenticationToken getUserAuthToken(SysUser sysUser, Authentication authentication) {
        UserDetails userDetails = this.createLoginUser(sysUser);
        UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDetails, authentication.getCredentials(), userDetails.getAuthorities());
        result.setDetails(authentication.getDetails());
        return result;
    }

    /**
     * 校验船公司是否审核通过
     * @param gmUser
     */
    public void checkGmUser(GMUser gmUser) {
        if (!StringUtils.isEmpty(gmUser)) {
            String passport = gmUser.getPassport();
            if (StringUtils.isEmpty(passport)) {
                throw new CustomException("审核未通过");
            }
            String[] split = passport.split(",");
            for (int i = 0; i < split.length; i++) {
                if ("2".equals(split[i])) {
                    break;
                }
                if (i == split.length-1) {
                    throw new CustomException("审核未通过");
                }
            }
        }
    }

    /**
     * 校验船公司管理员和业务员是否有权限，若没有则提示
     * @param user
     */
    public void checkUserAuth(SysUser user) {
        AjaxResult result = authorizeService.getAuthList(user.getUserId());
        List<SysMenu> userMenus = ((HashMap<String, List<SysMenu>>) result.get("data")).get("userMenus");
        if (userMenus.size() <= 0) {
            throw new CustomException("没有权限，请联系管理员授权");
        }
    }
}

package com.ruoyi.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.api.controller.ReportApi;
import com.ruoyi.api.domain.BargeParam;
import com.ruoyi.api.mapping.ReportApiMapper;
import com.ruoyi.api.service.ReportApiService;
import com.ruoyi.databarge.domain.Pb6Cargoconsignment;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.ship.domain.ShipInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/6/13 17:25
 */
@Service
public class ReportApiServiceImpl extends ServiceImpl<ReportApiMapper, ReportApi> implements ReportApiService {


    @Override
    public List<ShipInfo> getShipCount(BargeParam bargeParam) {
        return baseMapper.getShipCount(bargeParam);
    }

    @Override
    public List<Pb6Cargoconsignmentdetail> getBargeCount(BargeParam bargeParam) {
        return baseMapper.getBargeCount(bargeParam);
    }

    @Override
    public List<Pb6Cargoconsignment> selectByShipInfoId(Long shipInfoId) {
        return baseMapper.selectByShipInfoId(shipInfoId);
    }
}

package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 公司表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PUB_COMPANY")
public class PubCompany implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 公司ID
     */
    @TableField("COMID")
    private String comid;

    /**
     * 公司名称
     */
    @TableField("COMNAME")
    private String comname;

    /**
     * 备注
     */
    @TableField("PERSON")
    private String person;

    /**
     * 电话
     */
    @TableField("TEL")
    private String tel;

    /**
     * 联系方式
     */
    @TableField("ACCOUNTNUM")
    private String accountnum;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 公司简称
     */
    @TableField("COMSNAME")
    private String comsname;

    /**
     * 公司类型（0：集团，1：装卸作业公司，2：辅助作业公司，其他类型公司再定）
     */
    @TableField("COMTYPE")
    private String comtype;

    /**
     * 港口ID
     */
    @TableField("PORTID")
    private Long portid;

    /**
     * 调度中心ID
     */
    @TableField("DD_ID")
    private Long ddId;

    /**
     * 集团打印顺序
     */
    @TableField("PRINTORDER")
    private Long printorder;

    /**
     * 调度公司名简称
     */
    @TableField("DBCOMSNAME")
    private String dbcomsname;


}

package com.ruoyi.app.controller.support.invoice;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/7 11:13
 * @Description:
 */
@Getter
@Setter
public class InvoiceDTO {
    private String identity;
    private Order order;

    @Getter
    @Setter
    public static class Order{
        private String buyername;
        private String taxnum;
        private String phone;
        private String address;
        private String account;
        private String telephone;
        private String orderno;
        private String invoicedate;
        private String clerk;
        private String saleaccount;
        private String salephone;
        private String saleaddress;
        private String saletaxnum;
        private String kptype;
        private String message;
        private String payee;
        private String checker;
        private String fpdm;
        private String fphm;
        private String tsfs;
        private String email;
        private String qdbz;
        private String qdxmmc;
        private String dkbz;
        private String deptid;
        private String clerkid;
        private String invoiceLine;
        private String cpybz;
        private String billinfono;
        private List<Detail> detail;
    }

    @Getter
    @Setter
    public static class Detail{
        private String goodsname;
        private String num;
        private String price;
        private String hsbz;
        private String taxrate;
        private String spec;
        private String unit;
        private String spbm;
        private String zsbm;
        private String fphxz;
        private String yhzcbs;
        private String zzstsgl;
        private String lslbs;
        private String kce;
        private String taxfreeamt;
        private String tax;
        private String taxamt;
    }
}


package com.ruoyi.databarge.domain.dto;

import com.ruoyi.common.core.domain.BaseParamEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/12/15.
 * @Date: 2020/12/15 11:13
 */
@Getter
@Setter
public class Pb6WaterCargoSearchDTO extends BaseParamEntity {

    private String waterwaycargoid;

    private String consignflag;

    private Integer flagbargestate;

    private String shipnumber;

    private String bargename;

    private String outorinformid;

    private String rationbegindate;

    private String rationenddate;

    private String shipname;

    private String beginport;

    private String chargebalancetype;

    /**
     * 发票状态
     * 空查全部，0开票中  1已开票  2已冲红  3待开票
     */
    private String invoicestate;

    private String cargename;

    private Integer sendpractime; //0为未发送实装数，1为已发送

    //沿海或内陆   0查沿海，1查内陆
    private Integer coastalOrInland;

    private Long pageNum;

    private Long pageSize;
}

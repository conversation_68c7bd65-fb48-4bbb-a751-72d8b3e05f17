// pages/login/login.js
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    CustomBar: app.globalData.CustomBar,
    showLoginBtn: false,//显示选择登录弹窗
    openid: '',
    session_key: '',
    phoneNumber: '', // 手机号 
    showIdentityDialog: false, // 是否显示选择身份的对话框
    idenButtons: [{text: '取消'}, {text: '确定'}], // 对话框按钮
    checkedUserType:null,//选中的角色
    showNormalDialog:false,//普通用户弹窗
    normalIdenButtons: [{text: '重新选身份',extClass:'normalbtn'}], // 普通用户弹窗重选按钮
    normalIdenButtons1: [{text: '取消'}, {text: '确定'}], // 普通用户弹窗按钮
    showUnpassedDialog:false,//管理备案未通过弹窗
    unpassedButtons: [{text: '取消'}, {text: '确定'}], // 管理备案未通过弹窗按钮
    showReviewDialog:false,//管理备案审核中弹窗
    ReviewButtons:[{text: '关闭'}], // 管理备案审核中弹窗按钮
    unpassTitle:"当前备案审核未通过，请确定是否重新提交审核",//驳船备案不通过或驳船已绑定的弹窗提示语
    flag:false,
    indexNumber:1,
    phoneOld:"",
    checked:false,

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    wx.login({
      timeout: 20000,
      success: (result) => { 
        this.loginInit(result.code);
      },
      fail: () => {

      },
      complete: () => {

      }
    });
  },

  /**
   * 方法 
   */

  //点击管理备案审核中按钮
  tapReviewDialog(e){
    this.setData({
      showReviewDialog:false
    })
    let isRecorded = JSON.parse(wx.getStorageSync('isRecorded'))
    if(e.detail.index === 0){
    //关闭按钮-需要判断管理员之前是否成功备案，true成功，false失败;失败停留登录页；成功，挑转主页
      // if(isRecorded == false){
      //   return  
      // }else{
      //   wx.switchTab({url: "/pages/mainpage/main/main"});
      // }
      return  
    }
  },
  //点击管理备案未通过弹窗按钮
  tapUnpassedDialog(e){
    let index = e.currentTarget.dataset.index
    this.setData({
      showUnpassedDialog:false,
    })
    let isRecorded = wx.getStorageSync('isRecorded') ? JSON.parse(wx.getStorageSync('isRecorded')) : "" // 首次备案是否成功
    //判断备案状态recordStatus备案状态： 1-审核中、2-审核通过、3-审核不通过、4-没有进行驳船备案
    let recordStatus = wx.getStorageSync('recordStatus') //当前备案状态recordStatus
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):"";//获取用户信息    
    console.log("showUnpassedDialog",this.data.showUnpassedDialog)
    //点击取消就还是在登录页面，点击确定就跳转到备案页面，可重新提交
    //确定按钮,跳转备案页面
    if(index === 1){
      // 首次备案成功并且再次备案，当前审核状态为审核通过才能进入页面
      if(userInfos.userType == "14") {
        setTimeout(()=>{
          wx.navigateTo({url: '/pages/minepage/record/record'});
          return
        },1000)
      }

      
     
    }else if(index === 0){
    //取消按钮
      
    }
  },

  //查询是否备案成功
  checkRecordIsStatus(){
    app.$post(app.$url.mine.checkRecordIsSuccess,{}).then(res=>{
      if(res.code == 200){
        console.log("res3",res)
        // res.data.recordStatus = 3//暂时
        let isRecorded = wx.getStorageSync('isRecorded') ? JSON.parse(wx.getStorageSync('isRecorded')) : "" //管理员之前是否成功备案，true成功，false失败,首次登录没有isRecorded属性代表没有备案
        /**
         * 判断备案状态recordStatus备案状态： 1-审核中、2-审核通过、3-审核不通过、4-没有进行驳船备案
         * 选角色为管理员提交备案成功，但审核未通过状态需重新提交审核弹窗,备案审核中状态，直接提示备案正在审核中
         */
        wx.setStorageSync('recordStatus', JSON.stringify(res.data.recordStatus)) //当前备案状态recordStatus
        if(res.data.recordStatus){
          //1 备案审核中
          if(res.data.recordStatus == 1){
            this.setData({
              showReviewDialog:true
            })
            return
          }}
          if(res.data.recordStatus == 2){
            //2 管理员驳船备案成功跳主页
            wx.switchTab({url: "/pages/consignment/consignmanager/consignmanager"});
            return
          }
          if(res.data.recordStatus == 3){
          //3 备案审核不通过
            this.setData({
              showUnpassedDialog:true,
              unpassTitle:"当前备案审核未通过，请确定是否重新提交审核",//弹窗提示语
            })
            // 首次备案成功不显示这个“重新选身份”按钮
            if(isRecorded) {
              this.setData({
                normalIdenButtons: null
              })
            }
            return
          }
          if(res.data.recordStatus == 4){
          //4 没有进行驳船备案
            this.setData({
              showUnpassedDialog:true,
              unpassTitle:"您还没有进行驳船备案，请前往备案",//弹窗提示语
            })
            // 首次备案成功不显示这个“重新选身份”按钮
            if(isRecorded) {
              this.setData({
                normalIdenButtons: null
              })
            }
            return
          }
      }else {
        app.$message(res.msg)
      }
    })
  },

  //获取用户信息,保存本地
  getLoginUserInfo(){
    let that = this    
    app.$get(app.$url.getInfo, {}).then(res=>{
      if(res.code == 200){  
        // res.isRecorded = false // 暂时
        wx.setStorageSync("userRole",JSON.stringify(res.roles))//角色信息
        wx.setStorageSync("userInfo",JSON.stringify(res.user))//用户信息
        // wx.setStorageSync("permissions",JSON.stringify(res.permissions))//权限数组
        wx.setStorageSync('isRecorded', JSON.stringify(res.isRecorded)) //管理员之前是否成功备案，true成功，false失败,首次登录没有isRecorded属性代表没有备案
        //用户类型赋值
        that.setData({
          checkedUserType:res.user.userType ? res.user.userType :""
        })
        // 将权限数组改成权限表单
        let permissions = {}
        res.permissions.map(item=>{
          let index = item.lastIndexOf(":")
          let keyWord = item.substring(index+1)
          permissions[keyWord] = item
        })
        wx.setStorageSync("permissions",JSON.stringify(permissions))//权限表单
  
        /**
         * 1、管理员-首次备案成功，后面再次备案审核中或者审核不通过状态，都不能进入小程序。；
         *    首次备案是否成功isRecorded：true成功，false失败;失败停留登录页；成功，跳转主页。
         * 2、管理员-首次备案失败，跳转页面之前先判断管理员当前备案是否成功；
         */

        if(res.user.userType == 14){
          this.checkRecordIsStatus() 
          return
        }
      
        //普通用户:bindBarge:true绑船成功，false绑船失败; 绑船失败出现用户绑船弹窗; 普通用户成功绑船后才能进入小程序页面
        if(res.user.userType == 15 && res.bindBarge == false){
          this.setData({
            showNormalDialog:true
          })
          return
        }
        
        //用户类型userType：00系统用户， 01-注册用户，10-货主，11-托运单联系人，12-船公司管理员，13-船公司业务员，14-驳船管理员，15-驳船业务员
        //用户类型不为"00"和不为空时
        if(res.user.userType !== "00" && res.user.userType != null){ 
          wx.switchTab({url: "/pages/consignment/consignmanager/consignmanager"});
        }else{
        //用户类型为"00"",显示选择身份弹窗
          that.setData({
            showIdentityDialog: true,
          })
        }
      }else{
        app.$message(res.msg)
      }
    });
  },
  loginInit(code) {
    let param = {
      userType: 'bargeuser',
      wxCode: code,
    };
    app.$post(app.$url.getWxOpenId,param).then(res => {
      if(res.code == 200) {
        this.setData({
          'showLoginBtn': true,
          'openid': res.data.openid,
          'session_key': res.data.session_key
        })
      }else {
        app.$message(res.msg);
      }
    })
    wx.setStorageSync("activeTab",JSON.stringify(1)) //设置托运单tab
  },

  // 获取手机号之后登录成功后-判断身份：1、普通用户和管理员直接登录后进小程序；2、未添加身份的选择管理员，跳转备案页面；
  handleLogin: function () { 
    wx.login({
      timeout: 20000,
      success: (result) => {
        let param = {
          wxCode: result.code,
          userType: 'bargeuser',
          phoneNumber: this.data.phoneNumber,
          indexNumber:1,
        }
        if(this.data.flag){
          param.indexNumber = 2;
        }
        app.$post(app.$url.login, param).then(res => {
          if(res.code == 603){
            this.setData({
                flag: true
              })
          }
          if(res.code == 700){
            const that = this;
            wx.showModal({
              title: '警告',
              content: '确定以' + res.msg +'登录吗',
              success (inside) {
                if (inside.confirm) {
                  wx.login({
                    timeout: 20000,
                    success: (again) => {
                      let param = {
                        wxCode: again.code,
                        userType: 'bargeuser',
                        phoneNumber: res.msg,
                        indexNumber:2,
                      }
                      app.$post(app.$url.login, param).then(other=>{
                         //保存token值
                          wx.setStorageSync('token', other.token);
                        //获取用户的信息，登录成功之后的操作
                        that.getLoginUserInfo()
                      })
                    }})
                } else if (inside.cancel) {
                  console.log("你好");
                  that.setData({
                    flag: true
                  })
                  app.$message("请重新登录");
                }
              }
            })
          }
          if(res.code == 200) {
            //保存token值
            wx.setStorageSync('token', res.token);
            //获取用户的信息，登录成功之后的操作
            this.getLoginUserInfo()
          }else {
            app.$message(res.msg);
          }
        }).catch(res => {
          console.log(res)
        })
      },
      fail: () => {

      },
      complete: () => {

      }
    });
  },

  // 微信获取手机号
  loginGetPhoneNumber(e) {
    if(!this.data.checked){
      app.$message('请阅读并勾选同意系统服务协议！');
    }
    else{
      // 显示确认弹窗
      wx.showModal({
        title: '提示',
        content: '本系统为广州港船务有限公司驳船主及业务员使用，需要获取手机号，确认是否登录？',
        success: (res) => {
          if (res.confirm) {
            // 用户点击确定，继续登录流程
            this.handleLogin();
          }
        }
      });
    }
  },
  radioChange(e){
    this.setData({
      checked:true
    })
  },

  loginGetPhoneNumberFlag(e){
    if(e.detail.errMsg === "getPhoneNumber:ok") {
        // 获取用户手机号
        let param = {
          sessionKey: this.data.session_key,
          encryptedData: e.detail.encryptedData,
          iv: e.detail.iv,
          indexNumber:null
        }
        app.$post(app.$url.getWxUserInfo, param).then(res => {
          if(res.code == 200) {
            this.setData({
              phoneNumber: res.data.phoneNumber
            })
            //调登录接口
            this.handleLogin()
          }else {
            app.$message(res.msg);
          }
        })
      }else {
        app.$message('本系统为广州港船务有限公司驳船主使用，如需使用，请授权手机号！');
      }
  },

  // 点击选择身份的按钮
  tapDialogButton(e) {
    let that = this 
    if(!that.data.checkedUserType){
      app.$message("请选择您当前身份")
      return
    }
    that.setData({
      showIdentityDialog: false
    })
    // 点击取消按钮
    if(e.detail.index == 0) {
    }else {
      //选择身份后，立刻调修改身份的接口
      // 点击确定按钮(选择管理员14，跳转备案页面)
      /**
       * 首次进入小程序，需要选择角色:
       * 1、选择角色为管理员，就跳转到备案页面，备案提交后，就回到登录页面。此时再次点击登录按钮，会提示“备案正在审核中”；
          如果备案审核不通过，就提示“备案审核不通过，是否重新提交审核”，有按钮“取消、确定”；点击取消就还是在登录页面，点击确定就跳转到备案页面，可重新提交。
         2、选择角色为普通用户，就提示“请先联系驳船主添加船员”，还在登录页面
       */
      if(that.data.checkedUserType == "14"){
        that.modifyUserType(that.data.checkedUserType,()=>{
          that.handleLogin()
          // setTimeout(()=>{
          //   wx.navigateTo({url: '/pages/minepage/record/record'});
          // },3000)
          
        })   
      }else if(that.data.checkedUserType == "15"){
      //（选择普通用户15）提示绑船弹窗
        that.modifyUserType(that.data.checkedUserType,()=>{
          that.handleLogin()
          that.setData({
            showNormalDialog:true
          }) 
        })   
       
      }
    }
    console.log(e);
  },

  //修改用户类型
  modifyUserType(type,callback){
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):"";//获取用户信息
    let param = {
      userId:userInfos.userId,//用户id
      userType:type?Number(type):null,//用户类型
    }
    app.$post(app.$url.mine.updateUserType,param).then(res=>{
      if(res.code == 200){
        callback()
      }else{
        app.$message("绑定失败")
      }
    })
  },
  // 船舶委托书模板下载
  handleDownload(){
    wx.downloadFile({
      url: 'https://scbs.gzport.com/bargeRecord/0/weituoshu.docx',
      success: function (res) {
        const filePath = res.tempFilePath
        wx.openDocument({
          filePath: filePath,
          showMenu: true,
          success: function (res) {
            console.log('打开文档成功')
          }
        })
      }
    })
  },
  // 跳转到协议页面
  handleProtocolPage() {
    wx.navigateTo({
      url: "/pages/protocol/protocol"
    })
  },
  //关闭普通用户提示绑船弹窗
  cancelNormalDialog(e){
    let that = this 
    let index = e.currentTarget.dataset.index 

    let isRecorded = wx.getStorageSync('isRecorded') ? JSON.parse(wx.getStorageSync('isRecorded')) : "" // 首次备案是否成功
    //判断备案状态recordStatus备案状态： 1-审核中、2-审核通过、3-审核不通过、4-没有进行驳船备案
    let recordStatus = wx.getStorageSync('recordStatus') //当前备案状态recordStatus

    //取消按钮
    if(index == 0){
      that.setData({
        showNormalDialog: false
      })
    }else{

      
    }
 
  },

  //重选身份
  reselectIdentity(){
    let that = this
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):"";//获取用户信息
    let userType = userInfos.userType // 用户类型

    that.setData({
      showNormalDialog: false,
      showUnpassedDialog:false,
      showIdentityDialog: true,
      checkedUserType: userType,
    })
  },

  //弹窗选择角色
  identityChange(e){
    let that = this 
    that.setData({
      checkedUserType:e.detail.value
    })
 
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
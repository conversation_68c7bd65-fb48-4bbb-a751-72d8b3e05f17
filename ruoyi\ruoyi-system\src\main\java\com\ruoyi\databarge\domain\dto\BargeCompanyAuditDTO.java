package com.ruoyi.databarge.domain.dto;

import com.ruoyi.databarge.domain.Pb6BargeinfoAudit;
import com.ruoyi.databarge.domain.vo.BargeCompanyResultVO;
import com.ruoyi.databarge.domain.vo.Pb6BargeCompanyVO;
import lombok.Getter;
import lombok.Setter;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/9/10.
 * @Date: 2020/9/10 17:15
 */
@Getter
@Setter
public class BargeCompanyAuditDTO {

    private BargeCompanyResultVO bargeCompanyResultVO;

    private Pb6BargeCompanyVO pb6BargeCompanyVO;

    private Long pb6BargeCheckMessageId;

    private Integer checkStatus;

    private String failureReasons;
}

package com.ruoyi.databarge.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.databarge.domain.Pb6Bargeinfo;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/12/31.
 * @Date: 2020/12/31 9:08
 */
@Data
public class BargeCompanyResultVO extends Pb6Bargeinfo {
    private Long comid; //驳船挂靠表的ID

    private String cfullname; //挂靠公司名

    private Integer bindingtype;

    private Integer status;

    private Integer updatebindingtype;

    private Integer isaudit;

    private Integer auditstatus;

    private Integer checkFlag;

    private Integer recordCheck;

    private Integer updateCheck;

    private String urlIds;

    private String bargeAuditByName;

    private Long bargeAuditById;

    private String bargeAuditTime;

    private Integer isDelete;

    private String identityId;

    private String phonenumber;

    private String nickName;

    private Long pb6BargeInfoId;

    //private String bargename;
}

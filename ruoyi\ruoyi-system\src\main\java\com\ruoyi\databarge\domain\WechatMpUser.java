package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.databarge.wechat.ListLongJacksonTypeHandler;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: 13965
 * @Date: 2019/12/27 14:45
 * @Description: backend-com.ruoyi.wechat.entity.dataobject
 */
@TableName( value = "wechat_mp_user")
@KeySequence("SEQ_WECHAT_MP_USER")
@Getter
@Setter
public class WechatMpUser extends Model<WechatMpUser> {
    @TableId(value = "id", type = IdType.INPUT)
    @NotNull(message = "不能为空!")
    private Long id;

    @TableField("subscribe")
    private Integer subscribe;

    @TableField("openid")
    private String openid;

    @TableField("nickname")
    private String nickname;

    @TableField("sex")
    private Integer sex;

    @TableField("language")
    private String language;

    @TableField("city")
    private String city;

    @TableField("province")
    private String province;

    @TableField("country")
    private String country;

    @TableField("headimgurl")
    private String headimgurl;

    @TableField("subscribe_time")
    private Long subscribeTime;

    @TableField(exist = false)
    private String subscribe_time;

    @TableField("unionid")
    private String unionid;

    @TableField("remark")
    private String remark;

    @TableField("groupid")
    private Long groupid;

    @TableField(value = "tagid_list",typeHandler = ListLongJacksonTypeHandler.class)
    private List<Long> tagidList;

    @TableField(exist = false)
    private List<Long> tagid_list;

    @TableField("subscribe_scene")
    private String subscribeScene;

    @TableField(exist = false)
    private String subscribe_scene;

    @TableField("qr_scene")
    private Long qrScene;

    @TableField(exist = false)
    private Long qr_scene;

    @TableField("qr_scene_str")
    private String qrSceneStr;

    @TableField(exist = false)
    private String qr_scene_str;

    @Override
    protected Serializable pkVal() {
        return id;
    }
}

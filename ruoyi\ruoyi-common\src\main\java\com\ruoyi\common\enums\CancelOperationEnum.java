package com.ruoyi.common.enums;

/**
 *  托运单改单/退单 取消操作
 *
 * @Description 托运单改单/退单 取消操作
 * <AUTHOR>
 * @Date 2020/8/10  10:56
 */
public enum CancelOperationEnum {

    /**
     * 1.取消改单, 2.取消退单
     */
    CANCEL_TO_CHANGE_SINGLE(1, "取消改单"),
    CANCEL_OUT_THE_SINGLE(2, "取消退单");

    private final Integer code;

    private final String codeName;

    CancelOperationEnum(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

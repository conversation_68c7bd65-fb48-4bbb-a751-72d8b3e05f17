package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/5 9:56
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
@TableName("SYS_MESSAGE_MODEL")
@KeySequence("SEQ_SYS_MESSAGE_MODEL")
public class SysMessageModel {

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 消息内容
     */
    @TableField("CONTENT")
    private String content;

    /**
     * 模板标识
     */
    @TableField("CODE")
    private String code;

    /**
     * 模板标题
     */
    @TableField("title")
    private String title;
}

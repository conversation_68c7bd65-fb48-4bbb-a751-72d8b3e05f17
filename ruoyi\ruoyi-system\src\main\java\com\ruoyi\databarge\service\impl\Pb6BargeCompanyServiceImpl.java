package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.databarge.domain.Pb1Customer;
import com.ruoyi.databarge.domain.Pb6BargeCompany;
import com.ruoyi.databarge.domain.dto.Pb6BargeinfoAuditSearchDTO;
import com.ruoyi.databarge.domain.vo.Pb6BargeCompanyVO;
import com.ruoyi.databarge.mapper.Pb6BargeCompanyMapper;
import com.ruoyi.databarge.service.Pb1CustomerService;
import com.ruoyi.databarge.service.Pb6BargeCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/9/1.
 * @Date: 2020/9/1 11:39
 */
@Service
public class Pb6BargeCompanyServiceImpl  extends ServiceImpl<Pb6BargeCompanyMapper, Pb6BargeCompany> implements Pb6BargeCompanyService {

    @Autowired
    private Pb1CustomerService pb1CustomerService;

    @Override
    public Pb6BargeCompanyVO searchByBargeId(Long bargeid) {
        List<Pb6BargeCompanyVO> pb6BargeCompanyVOList = super.baseMapper.searchByBargeId(bargeid);
        if(pb6BargeCompanyVOList.size() > 1){
            throw new CustomException("驳船 " + pb6BargeCompanyVOList.get(0).getBargename() + " 发现多条挂靠数据，请核对！");
        } else if(pb6BargeCompanyVOList.size() == 1){
            return pb6BargeCompanyVOList.get(0);
        } else {
            return null;
        }
    }

    @Override
    public Pb6BargeCompany searchPb6BargeCompanyByBargeId(Long bargeid) {
        QueryWrapper<Pb6BargeCompany> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb6BargeCompany::getBargeid, bargeid)
                .eq(Pb6BargeCompany::getIsDelete, 1)
                .eq(Pb6BargeCompany::getStatus, 1);
        List<Pb6BargeCompany> pb6BargeCompanyList = super.list(queryWrapper);
        if(pb6BargeCompanyList.size() > 1){
            throw new CustomException("查到该驳船已挂靠多个公司，请联系管理员！");
        } else if(pb6BargeCompanyList.size() == 1){
            return pb6BargeCompanyList.get(0);
        }
        return null;
    }

    @Override
    public void check(Pb6BargeCompanyVO pb6BargeCompanyVO, Integer checkStatus) {
        Pb6BargeCompany pb6BargeCompany = super.getById(pb6BargeCompanyVO.getId());

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        if(checkStatus.equals(1)){
            if(pb6BargeCompany.getIsaudit() == 1){ //备案挂靠

                //判断是否已经有挂靠，一条船只能挂靠一个公司
                LambdaQueryWrapper<Pb6BargeCompany> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Pb6BargeCompany::getBargeid, pb6BargeCompany.getBargeid())
                        .eq(Pb6BargeCompany::getStatus, 1)
                        .eq(Pb6BargeCompany::getIsDelete, 1);
                List<Pb6BargeCompany> pb6BargeCompanyList = super.list(queryWrapper);

                //发现有生效的挂靠数据，不管多少都失效删除，再挂靠新的
                if(pb6BargeCompanyList.size() > 0){
                    for (Pb6BargeCompany bargeCompany: pb6BargeCompanyList) {
                        bargeCompany.setStatus(0); //0无效
                        bargeCompany.setUpdatebyid(sysUser.getUserId());
                        bargeCompany.setUpdateByName(sysUser.getUserName());
                        bargeCompany.setUpdatetime(DateUtils.getTime());
                        bargeCompany.setIsDelete(0);
                        super.updateById(bargeCompany);
                    }
                }
                Pb1Customer pb1Customer = pb1CustomerService.searchByCFullNameAndCType(pb6BargeCompanyVO.getCfullname());
                if(pb1Customer == null){
                    throw new CustomException("客户不存在或非船公司，请检查！");
                }
                pb6BargeCompany.setCompanyid(pb1Customer.getId());
                pb6BargeCompany.setBindingtype(pb6BargeCompanyVO.getBindingtype());
                pb6BargeCompany.setStatus(1);//1为生效
                pb6BargeCompany.setAuditbyid(sysUser.getUserId());
                pb6BargeCompany.setAuditByName(sysUser.getUserName());
                pb6BargeCompany.setAudittime(DateUtils.getTime());
                pb6BargeCompany.setAuditstatus(1);
                pb6BargeCompany.setIsDelete(1); //1为生效
            } else if(pb6BargeCompany.getIsaudit() == 0){
                //修改挂靠
                if(pb6BargeCompany.getUpdatebindingtype() == null){
                    throw new CustomException("新挂靠类型不能为空，请检查！");
                }
                pb6BargeCompany.setBindingtype(pb6BargeCompany.getUpdatebindingtype());
                pb6BargeCompany.setAuditbyid(sysUser.getUserId());
                pb6BargeCompany.setAuditByName(sysUser.getUserName());
                pb6BargeCompany.setAudittime(DateUtils.getTime());
                pb6BargeCompany.setAuditstatus(1);
            } else {
                pb6BargeCompany.setStatus(0);
                pb6BargeCompany.setAuditbyid(sysUser.getUserId());
                pb6BargeCompany.setAuditByName(sysUser.getUserName());
                pb6BargeCompany.setAudittime(DateUtils.getTime());
                pb6BargeCompany.setAuditstatus(1);
                pb6BargeCompany.setIsDelete(0);
            }
        } else {
            if(pb6BargeCompany.getIsaudit() == 1) { //备案挂靠
                pb6BargeCompany.setAuditbyid(sysUser.getUserId());
                pb6BargeCompany.setAuditByName(sysUser.getUserName());
                pb6BargeCompany.setAudittime(DateUtils.getTime());
                pb6BargeCompany.setAuditstatus(2);
                pb6BargeCompany.setIsDelete(0);
            } else{
                pb6BargeCompany.setAuditbyid(sysUser.getUserId());
                pb6BargeCompany.setAuditByName(sysUser.getUserName());
                pb6BargeCompany.setAudittime(DateUtils.getTime());
                pb6BargeCompany.setAuditstatus(2);
            }
        }
        super.updateById(pb6BargeCompany);
    }

    @Override
    public Pb6BargeCompanyVO searchById(Long id) {
        return super.baseMapper.searchById(id);
    }

    /**
     * 通过船公司ID，查找所有挂靠在该公司下的所有船信息
     * @param companyid 船公司ID
     * @return 挂靠列表
     */
    @Override
    public List<Pb6BargeCompanyVO> searchByCompanyId(Long companyid) {
        return super.baseMapper.searchByCompanyId(companyid);
    }

    @Override
    public boolean updateCompanyName(Long bargeId,Long updatebyid, String updatetime, String updateByName, String companyName, Long id) {
        return super.baseMapper.updateCompanyName(bargeId,updatebyid,updatetime,updateByName,companyName,id);
    }
}

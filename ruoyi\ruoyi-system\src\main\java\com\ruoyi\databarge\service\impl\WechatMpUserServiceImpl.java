package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.JacksonUtil;
import com.ruoyi.databarge.domain.WechatMpUser;
import com.ruoyi.databarge.mapper.WechatMpUserMapper;
import com.ruoyi.databarge.service.WechatMpUserService;
import com.ruoyi.util.Vo.AccessTokenVo;
import com.ruoyi.util.Vo.UserListVo;
import com.ruoyi.wechat.service.impl.WechatMpAccessTokenServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class WechatMpUserServiceImpl extends ServiceImpl<WechatMpUserMapper, WechatMpUser> implements WechatMpUserService {

    private Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    WechatMpAccessTokenServiceImpl wechatMpAccessTokenService;

    @Autowired
    WechatMpUserServiceImpl wechatMpUserService;


    private static final String user_info_url = "https://api.weixin.qq.com/cgi-bin/user/info";

    private static final String user_list_url = "https://api.weixin.qq.com/cgi-bin/user/get";

    @Override
    public AjaxResult getUserInfo(String unionId) {

        String accessToken = wechatMpAccessTokenService.getAccessToken();

        UserListVo userListVo = null;

        WechatMpUser info = null;

        // 1、根据unionId查询数据库中是否有该用户的信息
        List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", unionId));

        // 如果查询结果不为空，则说明数据库中有该用户的信息，直接返回
        if(wechatMpUserList.size() != 0){
            return AjaxResult.success();
        }

        // 如果查询结果为空，则说明数据库中没有该用户的信息，需要调用微信接口获取用户信息
        // 根据accessToken调用获取关注者列表接口，得到所有人的openId
            String userListUrl = user_list_url + "?access_token=" + accessToken + "&next_openid=";

            HttpGet request = new HttpGet(userListUrl);
            HttpResponse response = null;
            try {
                @SuppressWarnings("resource")
                HttpClient client = new DefaultHttpClient();
                response = client.execute(request);
                if (response.getStatusLine().getStatusCode() == HttpStatus.SUCCESS) {
                    String strResult = EntityUtils.toString(response.getEntity());
                    if (!StringUtils.isEmpty(strResult)) {
                        userListVo = JacksonUtil.defaultInstance().json2pojo(strResult, UserListVo.class);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
                LOGGER.error("获取关注列表失败");
            }

            List<String> openids = userListVo.getData().getOpenid();

            // 遍历openId列表，如果openId在数据库中不存在，则调用获取用户基本信息接口，获取用户信息并插入数据库

        // 数据库中所有的openId hashmap
        List<WechatMpUser> wechatMpUserListAll = wechatMpUserService.list();
        HashMap openIdMap = new HashMap();
        for(WechatMpUser wechatMpUser : wechatMpUserListAll){
            openIdMap.put(wechatMpUser.getOpenid(),wechatMpUser.getOpenid());
        }

            for(String openIdTemp :openids){

                openIdMap.containsKey(openIdTemp);

                // List<WechatMpUser> wechatMpUserListOpenid = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("openid", openIdTemp));

                if(!openIdMap.containsKey(openIdTemp)){

                    String url = user_info_url + "?access_token=" + accessToken + "&openid=" + openIdTemp + "&lang=zh_CN";

                    HttpGet requestInfo = new HttpGet(url);
                    HttpResponse responseInfo = null;
                    try {
                        @SuppressWarnings("resource")
                        HttpClient client = new DefaultHttpClient();
                        responseInfo = client.execute(requestInfo);
                        if (responseInfo.getStatusLine().getStatusCode() == HttpStatus.SUCCESS) {
                            String strResult = EntityUtils.toString(responseInfo.getEntity());
                            if (!StringUtils.isEmpty(strResult)) {
                                info = JacksonUtil.defaultInstance().json2pojo(strResult, WechatMpUser.class);
                            }
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                        LOGGER.error("获取用户信息失败");
                    }

                    // 插入数据库
                    wechatMpUserService.save(info);

                }

            }

            // 如果openId在数据库中存在，则不调用获取用户基本信息接口

        return AjaxResult.success();
    }
}

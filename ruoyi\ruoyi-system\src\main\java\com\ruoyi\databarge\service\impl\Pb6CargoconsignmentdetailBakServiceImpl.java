package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.databarge.domain.Pb6CargoconsignmentdetailBak;
import com.ruoyi.databarge.mapper.Pb6CargoconsignmentdetailBakMapper;
import com.ruoyi.databarge.service.Pb6CargoconsignmentdetailBakService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 驳船托运单明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Service
public class Pb6CargoconsignmentdetailBakServiceImpl extends ServiceImpl<Pb6CargoconsignmentdetailBakMapper, Pb6CargoconsignmentdetailBak> implements Pb6CargoconsignmentdetailBakService {

}

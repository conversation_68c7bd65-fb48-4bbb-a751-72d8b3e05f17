package com.ruoyi.app.controller.databarge;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.databarge.service.Pb6BargeCheckNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 驳船单证退单改单审核流程表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-23
 */
@RestController
@RequestMapping("/barge/barge-check-node")
public class Pb6BargeCheckNodeController {

    @Autowired
    private Pb6BargeCheckNodeService pb6BargeCheckNodeService;

    @PostMapping("/searchByPb6CargoconsignmentDetailId")
    public AjaxResult searchByPb6CargoconsignmentDetailId(@RequestBody Long pb6CargoconsignmentDetailId){
        return AjaxResult.success(pb6BargeCheckNodeService.searchByPb6CargoconsignmentDetailId(pb6CargoconsignmentDetailId));
    }
}

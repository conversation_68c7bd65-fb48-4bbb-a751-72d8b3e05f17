package com.ruoyi.databarge.pay;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * Description: 退款申请请求DTO
 *
 * @Author: ChenJin on 2021/1/28.
 * @Date: 2021/1/28 15:13
 */
@Getter
@Setter
@Builder
public class PayRefundDTO {

    private Reqmsg reqmsg;

    @Getter
    @Setter
    @Builder
    public static class Reqmsg{
        private Reqhead reqhead;
        private String cdtrid;
        private String refundamt;
        private String chanflow;
        private String operuser;
        private String remark;
        private String signature;
    }
}

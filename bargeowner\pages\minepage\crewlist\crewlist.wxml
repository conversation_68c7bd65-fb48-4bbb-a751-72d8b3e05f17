<!--pages/minepage/crewlist/crewlist.wxml-->
<view class="crew-page" hover-class="none" hover-stop-propagation="false">
  <view class="search-warp" hover-class="none" hover-stop-propagation="false">
    <view class="search-area" hover-class="none" hover-stop-propagation="false">
      <input type="text" class="search-ipt" placeholder="姓名/手机号" value="{{searchValue}}" bindinput="searchHandle"/>
      <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-icon" wx:if="{{searchValue}}" bindtap="deleteSearchValue"></mp-icon>
      <mp-icon class="search-icon" type="field" icon="search" color="#aaaaaa" size="{{30}}" wx:if="{{!searchValue}}"></mp-icon>
    </view>
  </view>

  <view class="crew-list" hover-class="none" hover-stop-propagation="false">
    <checkbox-group bindchange="checkboxChange">
      <label class="crew-item" hover-class="none" hover-stop-propagation="false" wx:for="{{crewList}}" wx:key="index">
        <view class="weui-cell__hd">
          <checkbox value="{{item.userId}}" checked="{{item.checked}}"/>
        </view>
        <view class="crew-item-message" hover-class="none" hover-stop-propagation="false">
          <view class="iconfont iconchuanyuan crew-icon" hover-class="none" hover-stop-propagation="false"></view>
          <text class="crew-item-text" selectable="false" space="false" decode="false">{{item.nickName}}</text>
          <view class="iconfont iconshoujihaoma crew-icon" hover-class="none" hover-stop-propagation="false"></view>
          <text class="crew-item-text" selectable="false" space="false" decode="false">{{item.phonenumber}}</text>
        </view>
      </label>
    </checkbox-group>
  </view>

  <view class="search-bottom" hover-class="none" hover-stop-propagation="false">
    <!-- <button class="confirm-btn" bindtap="submibtEvent">确定</button> -->
    <button class="confirm-btn" bindtap="handleAddCrew" wx:if="{{userInfos.userType == 14}}">添加</button>
    <button class="add-btn" bindtap="handleDeleteCrew" wx:if="{{userInfos.userType == 14}}">删除</button>
  </view>
</view>

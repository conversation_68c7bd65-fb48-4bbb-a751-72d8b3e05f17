package com.ruoyi.basic.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.basic.domain.BasicCustomer;
import com.ruoyi.basic.domain.BasicRate;
import com.ruoyi.basic.domain.BasicShip;
import com.ruoyi.basic.domain.BasicTerminal;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【BASIC_RATE】的数据库操作Service
* @createDate 2023-11-20 09:31:59
*/
public interface BasicRateService extends IService<BasicRate> {

    // 费率List
    List<BasicRate> selectBasicRateList(BasicRate basicRate);


    // 新增费率
    AjaxResult addBasicRate(BasicRate basicRate);

    // 修改费率
    AjaxResult updateBasicRate(BasicRate basicRate);

    // 删除费率
    AjaxResult deleteBasicRate(Long rateId);

    // 客户列表
    List<BasicCustomer> selectBasicCustomerList();

    // 船舶List
    List<BasicShip> selectBasicShipList();

    // 码头List
    List<BasicTerminal> selectBasicTerminalList();
}

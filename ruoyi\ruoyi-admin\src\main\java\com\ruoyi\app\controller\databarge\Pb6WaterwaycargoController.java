package com.ruoyi.app.controller.databarge;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.DataScope;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.domain.dto.Pb6WaterCargoForMobileSearchDTO;
import com.ruoyi.databarge.domain.dto.Pb6WaterCargoSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeFileEmailVo;
import com.ruoyi.databarge.domain.vo.ConfrimPb6WaterwaycargoVo;
import com.ruoyi.databarge.domain.vo.Pb6WaterCargoVO;
import com.ruoyi.databarge.domain.vo.WaterWayCargoH5Vo;
import com.ruoyi.databarge.service.Pb6WaterCargoMobileService;
import com.ruoyi.databarge.service.Pb6WaterwaycargoService;
import com.ruoyi.web.utils.BargeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/12/15.
 * @Date: 2020/12/15 11:42
 */
@Slf4j
@RestController
@RequestMapping("/barge/watercargo")
public class Pb6WaterwaycargoController extends BaseController {

    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;

    @Autowired
    private Pb6WaterCargoMobileService pb6WaterCargoMobileService;

    @Autowired
    private Pb6BargeworkController pb6BargeworkController;

    @PostMapping("/searchPage")
    public AjaxResult searchPagePb6WaterCaogo(@RequestBody Pb6WaterCargoSearchDTO pb6WaterCargoSearchDTO){
        DataScope.getDataScope("t", pb6WaterCargoSearchDTO, Boolean.FALSE);
        IPage<Pb6WaterCargoVO> page = pb6WaterwaycargoService.searchPagePb6WaterCargo(pb6WaterCargoSearchDTO);
        List<Pb6WaterCargoVO> list = page.getRecords();
        AtomicReference<Long> comId = new AtomicReference<>();
        list.forEach(item ->{
            if(item.getBeginport().equals("南沙粮食通用码头")){
                comId.set(16L);
            }
            if(item.getBeginport().equals("新沙")){
                comId.set(1L);
            }
            if (item.getBeginport().equals("新港")){
                comId.set(3L);
            }
            if(item.getBeginport().equals("西基")){
                comId.set(4L);
            }

            try {
                if(pb6BargeworkController.isGreenBarge(comId.get(),item.getBargename())){
                    item.setFiveDayReporting("是");
                }
                else {
                    item.setFiveDayReporting("否");
                }
            }
            catch (Exception e){
                item.setFiveDayReporting("否");
            }
        });
        return AjaxResult.success(page);
    }

    @GetMapping("/download")
    public void download(@Validated Pb6WaterCargoSearchDTO pb6WaterCargoSearchDTO, HttpServletResponse response) throws Exception{
        DataScope.getDataScope("t", pb6WaterCargoSearchDTO, Boolean.FALSE);
        List<Pb6WaterCargoVO> pb6WaterCargoVOList = pb6WaterwaycargoService.searchListPb6WaterCargo(pb6WaterCargoSearchDTO);

        TemplateExportParams templateExportParams = new TemplateExportParams("水路货运单列表.xls");

        Map<String, Object> map = new HashMap<>();

        List<Map<String, String>> list = new ArrayList<>();

        for (Pb6WaterCargoVO pb6WaterCargoVO: pb6WaterCargoVOList){

            Map<String, String> temp = new HashMap<>();
            temp.put("waterwaycargoid",pb6WaterCargoVO.getWaterwaycargoid());
            temp.put("bargename",pb6WaterCargoVO.getBargename());
            temp.put("outorinformid",pb6WaterCargoVO.getOutorinformid());
            temp.put("uniqecode",pb6WaterCargoVO.getUniqecode());
            temp.put("shipname",pb6WaterCargoVO.getShipname());
            temp.put("rationweight",pb6WaterCargoVO.getRationweight().divide(new BigDecimal(1000)).toString());
            temp.put("cargename",pb6WaterCargoVO.getCargename());
            temp.put("consignee",pb6WaterCargoVO.getConsignee());
            temp.put("beginport",pb6WaterCargoVO.getBeginport());
            temp.put("midport",pb6WaterCargoVO.getMidport());
            temp.put("endport",pb6WaterCargoVO.getEndport());
            temp.put("cargosize",pb6WaterCargoVO.getCargosize());
            temp.put("consigner",pb6WaterCargoVO.getConsigner());
            temp.put("chargebalancetype",pb6WaterCargoVO.getChargebalancetype());
            temp.put("businessagentchargerate",pb6WaterCargoVO.getBusinessagentchargerate().toString());
            temp.put("businessagentcharge",pb6WaterCargoVO.getBusinessagentcharge().toString());
            temp.put("totalcharge",pb6WaterCargoVO.getTotalcharge().toString());
            temp.put("rationprincipal",pb6WaterCargoVO.getRationprincipal());
            switch (pb6WaterCargoVO.getFlagbargestate()){
                case 0:
                    temp.put("flagbargestate", "已配载，没有报到信息");
                    break;
                case 1:
                    temp.put("flagbargestate", "已报到");
                    break;
                case 2:
                    temp.put("flagbargestate", "已离港");
                    break;
                case 3:
                    temp.put("flagbargestate", "已取消报到，处于配载状态，并且已有报到信息");
                    break;
                case 4:
                    temp.put("flagbargestate", "已退单");
                    break;
                case 5:
                    temp.put("flagbargestate", "已停止");
                    break;
            }
            temp.put("bargelinkman",pb6WaterCargoVO.getBargelinkman());
            temp.put("contactphone",pb6WaterCargoVO.getContactphone());
            temp.put("consignflag",pb6WaterCargoVO.getConsignflag());
            temp.put("confirmloadingover",pb6WaterCargoVO.getConfirmloadingover());
            temp.put("tscargoweightvalue",pb6WaterCargoVO.getTscargoweightvalue());

            list.add(temp);
        }

        map.put("list",list);

        map.put("date", DateUtils.getTime());
        map.put("man", SecurityUtils.getLoginUser().getUser().getNickName());

        Workbook workbook = ExcelExportUtil.exportExcel(templateExportParams, map);
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.close();
    }

    @PostMapping("/findByPb6WaterwaycargoFuzzy")
    public AjaxResult findByPb6WaterwaycargoFuzzy(@RequestBody String waterwaycargoid){
        return AjaxResult.success(pb6WaterwaycargoService.findByPb6WaterwaycargoFuzzy(waterwaycargoid));
    }

    @PostMapping("/searchPb6WaterCargoForMobile")
    public AjaxResult searchPb6WaterCargoForMobile(@RequestBody Pb6WaterCargoForMobileSearchDTO pb6WaterCargoForMobileSearchDTO){
        if(pb6WaterCargoForMobileSearchDTO.getStatus() == 2){
            return AjaxResult.success(pb6WaterwaycargoService.searchPb6WaterCargoForMobileWorking(pb6WaterCargoForMobileSearchDTO.getKeyWord()));
        }
        return AjaxResult.success(pb6WaterCargoMobileService.searchPb6WaterCargoForMobile(pb6WaterCargoForMobileSearchDTO));
    }

//    @Scheduled(cron = "0 */15 * * * ?")
    public void updatePb6WaterCargoMobile() {
        log.info("开始调用驳船更新方法");
        pb6WaterCargoMobileService.updatePb6WaterCargoMobile();
    }

    @PostMapping("/updateGreen")
    public AjaxResult updateGreen(@RequestBody String watercargoId){
        return AjaxResult.success(pb6WaterwaycargoService.update(new UpdateWrapper<Pb6Waterwaycargo>().set("ISREPORTING","N").eq("WATERWAYCARGOID",watercargoId)));
    }

    /**
     * @param
     * @return
     * @description 水路运单列表
     * <AUTHOR>
     * @date 2024/1/27 14:57
     */
    @PostMapping("/selectPb6WaterwaycargoList")
    public TableDataInfo selectPb6WaterwaycargoList(Pb6Waterwaycargo pb6Waterwaycargo){
        startPage();
        List<Pb6Waterwaycargo> list = pb6WaterwaycargoService.selectPb6WaterwaycargoList(pb6Waterwaycargo);
        return getDataTable(list);
    }

    /**
     * @param
     * @return
     * @description 更新水路运单
     * <AUTHOR>
     * @date 2024/1/27 19:04
     */
    @PostMapping("/updatePb6Waterwaycargo")
    public AjaxResult updatePb6Waterwaycargo(@RequestBody Pb6Waterwaycargo pb6Waterwaycargo){
        return pb6WaterwaycargoService.updatePb6Waterwaycargo(pb6Waterwaycargo);
    }

    /**
     * @param
     * @return
     * @description 确认驳船实装数
     * <AUTHOR>
     * @date 2024/1/27 21:14
     */
    @PostMapping("/confirmPb6Waterwaycargo")
    public AjaxResult confirmPb6Waterwaycargo(@RequestBody Pb6Waterwaycargo pb6Waterwaycargo){
        return pb6WaterwaycargoService.confirmPb6Waterwaycargo(pb6Waterwaycargo);
    }

    /**
     * @param
     * @return
     * @description 指导员确认是否放行水路运单
     * <AUTHOR>
     * @date 2025/2/25 15:09
     */
    @PostMapping("/confirmRelease")
    public AjaxResult confirmRelease(@RequestBody Pb6Waterwaycargo pb6Waterwaycargo){
        return pb6WaterwaycargoService.confirmRelease(pb6Waterwaycargo);
    }


    /**
     * @param
     * @return
     * @description 指导员端查询水路运单列表
     * <AUTHOR>
     * @date 2024/3/6 15:10
     */
    @PostMapping("/selectPb6WaterwaycargoListH5")
    public TableDataInfo selectPb6WaterwaycargoListH5(@RequestBody Pb6Waterwaycargo pb6Waterwaycargo){
        startPage();
        List<WaterWayCargoH5Vo> list = pb6WaterwaycargoService.selectPb6WaterwaycargoListH5(pb6Waterwaycargo);
        return getDataTable(list);
    }


    /**
     * @param
     * @return
     * @description  告知书盖章
     * <AUTHOR>
     * @date 2024/12/19 15:58
     */
    @PostMapping("/defrayStampNoticeFile")
    public AjaxResult defrayStampNoticeFile(@RequestBody Pb6Waterwaycargo pb6Waterwaycargo){
        BargeUtils.defrayStampNotice(pb6Waterwaycargo);

        // 告知书盖章完成，更新水路运单是否盖章告知书状态
        Pb6Waterwaycargo pb6Waterwaycargo1 = pb6WaterwaycargoService.getById(pb6Waterwaycargo.getId());
        pb6Waterwaycargo1.setIsStampNotice("1");
        pb6WaterwaycargoService.updateById(pb6Waterwaycargo1);

        return AjaxResult.success();
    }

    /**
     * @param
     * @return
     * @description 发送驳船文件邮件
     * @date 2025/1/16 21:29
     */
    @PostMapping("/sendEmail")
    public AjaxResult sendEmail(@RequestBody BargeFileEmailVo bargeFileEmailVo) {
        try {
            List<Long> ids = bargeFileEmailVo.getIds();
            String email = bargeFileEmailVo.getMail();

            if (ids == null || ids.isEmpty()) {
                return AjaxResult.error("请选择要发送的文件");
            }

            if (StringUtils.isEmpty(email)) {
                return AjaxResult.error("请提供接收邮箱");
            }

            // 异步发送邮件
            pb6WaterwaycargoService.sendBargeFilesViaEmail(bargeFileEmailVo);

            return AjaxResult.success("邮件发送任务已开始，请稍后查收");
        } catch (Exception e) {
            log.error("邮件发送失败", e);
            return AjaxResult.error("邮件发送失败：" + e.getMessage());
        }
    }







}

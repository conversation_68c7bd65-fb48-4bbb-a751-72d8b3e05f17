package com.ruoyi.common.utils.message.request.weappTem;

import com.ruoyi.common.utils.message.request.mpTem.DataValue;
import com.ruoyi.common.utils.message.request.mpTem.Miniprogram;
import lombok.Data;


import java.util.Map;

@Data
public class WechatTemplate {
	
	   private String touser;
	  
	    private String template_id;
	 
	    private String url;
	    
	    private Miniprogram miniprogram;
	 
	    private Map<String, DataValue> data;
	 
	
}

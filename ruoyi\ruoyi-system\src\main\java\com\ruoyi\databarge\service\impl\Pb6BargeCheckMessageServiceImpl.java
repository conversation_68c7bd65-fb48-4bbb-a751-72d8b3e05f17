package com.ruoyi.databarge.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.SysMessage;
import com.ruoyi.common.domain.SysUserMessage;
import com.ruoyi.common.domain.bo.UserMessageBO;
import com.ruoyi.common.domain.vo.UserMessageVO;
import com.ruoyi.common.enums.BargeCheckMessageType;
import com.ruoyi.common.enums.CheckEnum;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.SysMessageMapper;
import com.ruoyi.common.service.AppNoticeService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.message.WechatMessageUtil;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.dto.Pb6BargeCheckMessageSearchDTO;
import com.ruoyi.databarge.domain.dto.XGPdfDTO;
import com.ruoyi.databarge.domain.vo.Pb6BargeCheckMessageVO;
import com.ruoyi.databarge.domain.vo.PortLoadingMsgVO;
import com.ruoyi.databarge.mapper.Pb6BargeCheckMessageMapper;
import com.ruoyi.databarge.service.*;
import com.ruoyi.databarge.wechat.MpMessageDTO;
import com.ruoyi.databarge.wechat.MpMessageResult;
import com.ruoyi.databarge.wechat.MpUtil;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.wechat.service.WechatMpAccessTokenService;
import com.ruoyi.wechat.service.impl.WechatMpAccessTokenServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 驳船审核消息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Service
@Slf4j
public class Pb6BargeCheckMessageServiceImpl extends ServiceImpl<Pb6BargeCheckMessageMapper, Pb6BargeCheckMessage> implements Pb6BargeCheckMessageService {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private WechatMpUserService wechatMpUserService;

    @Autowired
    private WechatMessageUtil wechatMessageUtil;

    @Autowired
    private MpUtil mpUtil;

    @Autowired
    private Pb6CargoconsignmentdetailService pb6CargoconsignmentdetailService;

    @Autowired
    private AppNoticeService appNoticeService;

    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private AgentDeliveryService agentDeliveryService;

    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;

    private SysMessageMapper sysMessageMapper;

    @Autowired
    private WechatMpAccessTokenService wechatMpAccessTokenService;

    @Override
    public IPage<Pb6BargeCheckMessage> searchPage(Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO) {
        Long pageNum = Optional.ofNullable(pb6BargeCheckMessageSearchDTO.getPageNum()).orElse(0L);
        Long pageSize = Optional.ofNullable(pb6BargeCheckMessageSearchDTO.getPageSize()).orElse(-1L);
        return super.baseMapper.searchPage(new Page<>(pageNum, pageSize), pb6BargeCheckMessageSearchDTO);
    }
    @Override
    public IPage<Pb6BargeCheckMessage> searchPagePort(Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO) {
        Long pageNum = Optional.ofNullable(pb6BargeCheckMessageSearchDTO.getPageNum()).orElse(0L);
        Long pageSize = Optional.ofNullable(pb6BargeCheckMessageSearchDTO.getPageSize()).orElse(-1L);
        return super.baseMapper.searchPagePort(new Page<>(pageNum, pageSize), pb6BargeCheckMessageSearchDTO);
    }
    @Override
    public int stowageMsgNum() {
        QueryWrapper<Pb6BargeCheckMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb6BargeCheckMessage::getMtype, 11)
                .eq(Pb6BargeCheckMessage::getAuditflag, 0);
        return super.list(queryWrapper).size();
    }

    @Override
    public void sendMessageForAddOrUpdateBargeInfo(Pb6BargeCheckMessage pb6BargeCheckMessage) throws URISyntaxException {
        SysUser sysUser = sysUserService.selectUserById(pb6BargeCheckMessage.getApplymanid());
        if (sysUser != null && StringUtils.isNotBlank(sysUser.getUnionId())) {

            // 查询前，先根据openId去查询用户信息
            wechatMpUserService.getUserInfo(sysUser.getUnionId());

            List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
            if (wechatMpUserList.size() == 0) {
                throw new CustomException("该用户未关注广州港船务有限公司!");
            } else if (wechatMpUserList.size() == 1) {

                // 1、发送小程序消息
                Map<String, Object> params = new HashMap<>();
                params.put("first", "您的驳船'" + pb6BargeCheckMessage.getBargename() + "'" +
                        BargeCheckMessageType.getCodeName(pb6BargeCheckMessage.getMtype()) + "申请" + CheckEnum.getCodeName(pb6BargeCheckMessage.getAuditflag()));
                params.put("keyword1", BargeCheckMessageType.getCodeName(pb6BargeCheckMessage.getMtype()));
                params.put("keyword2", CheckEnum.getCodeName(pb6BargeCheckMessage.getAuditflag()));
                params.put("remark", pb6BargeCheckMessage.getAuditflag() == 1 ? "马上登录小程序吧" : pb6BargeCheckMessage.getFailurereasons());

                params.put("thing1",pb6BargeCheckMessage.getBargename());
                // 根据审核标识判断是审核通过还是审核不通过
                if(pb6BargeCheckMessage.getAuditflag() == 1){
                    params.put("const2","审核通过");
                } else {
                    params.put("const2","审核不通过");
                }
                // 获取当前时间
                Date date = new Date();
                // 格式化时间
                String time = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", date);
                params.put("time3",time);
                // 审核不通过参数
                params.put("time2",time);
                // 审核失败原因不能超过200个字符，超过截取前200个字符
                if(pb6BargeCheckMessage.getFailurereasons().length() > 20){
                    pb6BargeCheckMessage.setFailurereasons(pb6BargeCheckMessage.getFailurereasons().substring(0,20));
                }
                params.put("thing3",pb6BargeCheckMessage.getFailurereasons());

                UserMessageBO userMessageBO = new UserMessageBO();
                userMessageBO.setParams(params);
                List<Long> userIds = new ArrayList<>();
                userIds.add(sysUser.getUserId());
                userMessageBO.setUserIds(userIds);
                userMessageBO.setSendMessageType("_05C95a46vQb36hLLpxAplAEUiCMCWAiEf-gorMdLUE");
                appNoticeService.sendMessage(userMessageBO);

                // String accessToken = wechatMpAccessTokenService.getAccessToken();
                String accessToken = wechatMpAccessTokenService.getAccessToken();
                // 2、发送公众号消息，成功和失败两个模板
                if(pb6BargeCheckMessage.getAuditflag() == 1){
                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                            .template_id("_05C95a46vQb36hLLpxAplAEUiCMCWAiEf-gorMdLUE")
                            .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing1").toString()).color("#000000").build())
                            .data("const2", MpMessageDTO.MpMessageDataField.builder().value(params.get("const2").toString()).color("#000000").build())
                            .data("time3", MpMessageDTO.MpMessageDataField.builder().value(params.get("time3").toString()).color("#000000").build())
                            .build();
                    System.out.println("accessToken: " + accessToken);
                    MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);

                    log.info("发送历史消息：{}",mpMessageResult);
                } else {
                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                            .template_id("8AbWl2WnATXh8LFf4MeIQyqkM_CqTXt10jzgggm0pGQ")
                            .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing1").toString()).color("#000000").build())
                            .data("time2", MpMessageDTO.MpMessageDataField.builder().value(params.get("time2").toString()).color("#000000").build())
                            .data("thing3", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing3").toString()).color("#000000").build())
                            .build();
                    System.out.println("accessToken: " + accessToken);
                    MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);

                    log.info("发送历史消息：{}",mpMessageResult);
                }

            } else {
                throw new CustomException("数据库数据错误!");
            }
        } else {
            log.warn("用户不存在或没有unionid，发送消息失败！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendMessageForPortLoadingOver(String waterWayCargoId, XGPdfDTO xgPdfDTO) throws URISyntaxException {
        List<Pb6Cargoconsignmentdetail> pb6CargoconsignmentdetailList = pb6CargoconsignmentdetailService
                .list(new QueryWrapper<Pb6Cargoconsignmentdetail>().lambda().eq(Pb6Cargoconsignmentdetail::getWaterwaycargoid, waterWayCargoId));
        if(pb6CargoconsignmentdetailList.size() != 1){
            log.error("没有找到水路运单对应的托运单或对应托运单不唯一!");
            throw new CustomException("1");
//            throw new CustomException("没有找到水路运单对应的托运单或对应托运单不唯一!");
        }
        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailList.get(0);

        //2021.09.13 jinn 洪小林要求去掉这个判断，没有预约也发送
//        if(pb6Cargoconsignmentdetail.getWxointmentmid() == null){
//            log.error("缺少预约人信息!");
//            throw new CustomException("2");
//        }

        List<SysUser> sysUserList = sysUserBargeMapper.queryAllUserByBargeId(pb6Cargoconsignmentdetail.getBargeid());

        if(StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWxrationcontactnumber())){
            List<SysUser> ration = sysUserMapper.selectCargoConsignPersonByPhone(pb6Cargoconsignmentdetail.getWxrationcontactnumber());
            sysUserList.addAll(ration);
        }

        PortLoadingMsgVO portLoadingMsgVO = this.searchPortLoadingInfo(waterWayCargoId);
        //实装件数
        Long realAmt = this.searchPortLoadingAmt(waterWayCargoId);

        StringBuilder stringBuilder = new StringBuilder();
        if(realAmt != null && realAmt != 0){
            //实装件数不为0，查询配载件数
            Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.searchWaterwaycargoByWaterwaycargoid(waterWayCargoId);
            if(pb6Waterwaycargo != null && pb6Waterwaycargo.getRationpiece() != null){
                stringBuilder.append("；配载件数：").append((long)Math.floor(Double.parseDouble(pb6Waterwaycargo.getRationpiece())))
                        .append("；实装件数:").append(realAmt);
            }

        }
        //小程序发送实装件数  2022/03/08
        String totalAmt = "";
        if(xgPdfDTO.getTotalAmt() !=0 ){
            totalAmt = "。仓库件数："+xgPdfDTO.getCkszjs()+"；理货件数："+xgPdfDTO.getLhszjs()+"；总件数："+xgPdfDTO.getTotalAmt();
        }

        // TODO: 2020/12/12 添加实装数修改
        // 1、发小程序消息
        Map<String, Object> params = new HashMap<>();
        params.put("first", "订单完成通知");
        params.put("keyword1", pb6Cargoconsignmentdetail.getWaterwaycargoid());
        params.put("keyword2", "驳船提货作业" + "(" + portLoadingMsgVO.getBargename() + ")");
        params.put("keyword3", DateUtils.getTime());
//            params.put("remark", "您的驳船作业已完成，请及时确认实装数，确认后可下载水路运单、交接凭证等。货名："
//                    + portLoadingMsgVO.getCargename() + "；配载吨数：" + portLoadingMsgVO.getRationweight() + "；实装吨数：111");

        BigDecimal rationWeight = new BigDecimal(portLoadingMsgVO.getRationweight());
        BigDecimal tsCargoWeight = new BigDecimal(portLoadingMsgVO.getTscargoweightValue());
        rationWeight = rationWeight.setScale(3, BigDecimal.ROUND_HALF_UP);
        tsCargoWeight = tsCargoWeight.setScale(3, BigDecimal.ROUND_HALF_UP);
        params.put("remark", "您的驳船作业已完成，请及时确认实装数，确认后可下载水路运单、交接凭证等。货名："
                + portLoadingMsgVO.getCargename() + "；配载吨数：" + rationWeight + "；实装吨数：" + tsCargoWeight + stringBuilder.toString()+totalAmt);

        UserMessageBO userMessageBO = new UserMessageBO();
        userMessageBO.setParams(params);
        List<Long> userIds = new ArrayList<>();
        //遍历需要发送信息的用户集合获取id用于插入sys_message_user表
        for (SysUser sysUser : sysUserList){
            userIds.add((sysUser.getUserId()));
        }
        userMessageBO.setUserIds(userIds);
        userMessageBO.setSendMessageType("AvEWtXO3RxiXS4XJ0Nx0FFsuvo6GnHOjYQCFyWtqJPw");
        //2021.08.27 jinn 添加水路运单号
        userMessageBO.setWaterCargoId(waterWayCargoId);
        //先往sys_message中插入数据，将返回的message_id和userIds中的id(user_id)插入sys_message_user
        AjaxResult ajaxResult = appNoticeService.sendMessage(userMessageBO);
        //获取返回的message_id
        Long messageId = (Long) ajaxResult.get("data");

        for(SysUser sysUser: sysUserList){

            if (StringUtils.isNotBlank(sysUser.getUnionId())) {

                // 2、发公众号消息
                List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                if (wechatMpUserList.size() == 0) {
                    log.warn("该用户未关注广州港船务有限公司!");
//                    throw new CustomException("5");
                } else if (wechatMpUserList.size() == 1) {
                    String accessToken = wechatMpAccessTokenService.getAccessToken();
                    //通过message_id和user_id 去sys_message_user表和sys_message表、SYS_MESSAGE_MODEL表级联查询小程序前端所需参数currentObj（UserMessageVO类）
                    SysUserMessage sysUserMessage = new SysUserMessage();
                    sysUserMessage.setUserId(sysUser.getUserId());
                    sysUserMessage.setMessageId(messageId);
                    UserMessageVO userMessageVO = super.baseMapper.officialAccountInfor(sysUserMessage);
                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                            .template_id("AvEWtXO3RxiXS4XJ0Nx0FFsuvo6GnHOjYQCFyWtqJPw")
                            .miniprogram(MpMessageDTO.MpMessageMiniProgramDTO.builder().appid("wx3f62ce7e9e20f483").pagepath("pages/minepage/messagedetail/messagedetail?currentObj="+ JSON.toJSONString(userMessageVO)).build())//公众号前端需要接收currentObj参数
                            .data("first", MpMessageDTO.MpMessageDataField.builder().value(params.get("first").toString()).color("#000000").build())
                            .data("keyword1", MpMessageDTO.MpMessageDataField.builder().value(params.get("keyword1").toString()).color("#000000").build())
                            .data("keyword2", MpMessageDTO.MpMessageDataField.builder().value(params.get("keyword2").toString()).color("#000000").build())
                            .data("keyword3", MpMessageDTO.MpMessageDataField.builder().value(params.get("keyword3").toString()).color("#000000").build())
                            .data("remark", MpMessageDTO.MpMessageDataField.builder().value(params.get("remark").toString()).color("#000000").build())
                            .build();
                    MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);

                    log.info("发送历史消息：{}",mpMessageResult);
                } else {
                    throw new CustomException("数据库数据错误!");
                }
            } else {
                log.warn("该用户没有unionid，发送实装数失败！");
//                throw new CustomException("4");
            }
        }
    }

    @Override
    public String sendMessageForRefundOrUpdateCargoConsignment(Pb6BargeCheckMessage pb6BargeCheckMessage) throws URISyntaxException {
        SysUser sysUser = sysUserService.selectUserById(pb6BargeCheckMessage.getApplymanid());
        if (sysUser != null && StringUtils.isNotBlank(sysUser.getUnionId())) {
            List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
            if (wechatMpUserList.size() == 0) {
                return "该用户未关注广州港船务有限公司!";
            } else if (wechatMpUserList.size() == 1) {
                String accessToken = wechatMpAccessTokenService.getAccessToken();
                String sign = "";
                if(pb6BargeCheckMessage.getMtype() == 6 || pb6BargeCheckMessage.getMtype() == 7){
                    sign = "退单";
                } else if(pb6BargeCheckMessage.getMtype() == 8 || pb6BargeCheckMessage.getMtype() == 9){
                    sign = "改单";
                } else {
                    return "当前消息标识不正确";
                }
                // 1、发送小程序消息
                Map<String, Object> params = new HashMap<>();
                params.put("first", "您的托运单'" + pb6BargeCheckMessage.getConsignflag() + "'" +
                        sign + "申请" + CheckEnum.getCodeName(pb6BargeCheckMessage.getAuditflag()));
                params.put("keyword1", sign + "申请");
                params.put("keyword2", CheckEnum.getCodeName(pb6BargeCheckMessage.getAuditflag()));
                params.put("remark", pb6BargeCheckMessage.getAuditflag() == 1 ? "马上登录小程序吧" : pb6BargeCheckMessage.getFailurereasons());
                UserMessageBO userMessageBO = new UserMessageBO();
                userMessageBO.setParams(params);
                List<Long> userIds = new ArrayList<>();
                userIds.add(sysUser.getUserId());
                userMessageBO.setUserIds(userIds);
                userMessageBO.setSendMessageType("Chargeback");
                appNoticeService.sendMessage(userMessageBO);

                // 2、发送公众号消息
                MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                        .template_id("iGHNv91Hr9ynpe8Uopwu5NomoWMEheqdMa4RbU7wrm0")
                        .data("first", MpMessageDTO.MpMessageDataField.builder().value(params.get("first").toString()).color("#000000").build())
                        .data("keyword1", MpMessageDTO.MpMessageDataField.builder().value(params.get("keyword1").toString()).color("#000000").build())
                        .data("keyword2", MpMessageDTO.MpMessageDataField.builder().value(params.get("keyword2").toString()).color("#000000").build())
                        .data("remark", MpMessageDTO.MpMessageDataField.builder().value(params.get("remark").toString()).color("#000000").build())
                        .build();

                MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                log.info("发送历史消息：{}",mpMessageResult);
            } else {
                return "数据库数据错误!";
            }
        } else {
            log.warn("用户不存在或没有unionid，发送消息失败！");
        }
        return null;
    }

    @Override
    public PortLoadingMsgVO searchPortLoadingInfo(String waterWayCargoId) {
        return super.baseMapper.searchPortLoadingInfo(waterWayCargoId);
    }

    @Override
    public Long searchPortLoadingAmt(String waterwaycargoid) {
        return baseMapper.searchPortLoadingAmt(waterwaycargoid);
    }

    @Override
    public void sendMessageForCargoConsignmentChecked(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail, Long bargeid) throws URISyntaxException {
        List<SysUser> sysUserList = sysUserBargeMapper.queryAllUserByBargeId(bargeid);
        for(SysUser sysUser: sysUserList){
            if(StringUtils.isNotBlank(sysUser.getUnionId())){
                List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                if (wechatMpUserList.size() == 0) {
                    log.info("用户" + sysUser.getUserName() + "未关注广州港船务有限公司!");
                } else if (wechatMpUserList.size() == 1) {
                    String accessToken = wechatMpAccessTokenService.getAccessToken();
                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                            .template_id("AvEWtXO3RxiXS4XJ0Nx0FFsuvo6GnHOjYQCFyWtqJPw")
                            .data("first", MpMessageDTO.MpMessageDataField.builder().value("订单完成通知").color("#000000").build())
                            .data("keyword1", MpMessageDTO.MpMessageDataField.builder().value(pb6Cargoconsignmentdetail.getConsignflag()).color("#000000").build())
                            .data("keyword2", MpMessageDTO.MpMessageDataField.builder().value(pb6Cargoconsignmentdetail.getFlagbargestate().equals("1") ? "托运单审核通过" : "托运单审核不通过").color("#000000").build())
                            .data("keyword3", MpMessageDTO.MpMessageDataField.builder().value(DateUtils.getTime()).color("#000000").build())
                            .data("remark", MpMessageDTO.MpMessageDataField.builder().value(pb6Cargoconsignmentdetail.getFlagbargestate().equals("1") ? "您有一份新的驳船托运单，请及时确认，确认成功后船代会立刻配载。" : pb6Cargoconsignmentdetail.getCargoconsigncheckreason()).color("#000000").build())
                            .build();
                    log.info("开始发送消息内容： {}", mpMessageDTO.toString());
                    try {
                        MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                        log.info("发送历史消息成功：{}",mpMessageResult);
                    } catch (Exception e){
                        log.info("发送历史消息失败");
                    }
                } else {
                    log.info("数据库数据错误!");
                }
            } else {
                log.warn("用户" + sysUser.getUserName() + "没有unionid，发送消息失败！");
            }
        }
    }

    @Override
    public IPage<Pb6BargeCheckMessageVO> searchPortMessagePage(Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO) {
        Long pageNum = Optional.ofNullable(pb6BargeCheckMessageSearchDTO.getPageNum()).orElse(0L);
        Long pageSize = Optional.ofNullable(pb6BargeCheckMessageSearchDTO.getPageSize()).orElse(-1L);
        return super.baseMapper.searchPortMessagePage(new Page<>(pageNum, pageSize),pb6BargeCheckMessageSearchDTO);
    }

    @Override
    public void sendMessageForAgentDeliveryChecked(Pb6BargeCheckMessage pb6BargeCheckMessage) throws URISyntaxException {
        SysUser sysUser = sysUserService.selectUserById(pb6BargeCheckMessage.getApplymanid());
        if (sysUser != null && StringUtils.isNotBlank(sysUser.getUnionId())) {
            List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
            if (wechatMpUserList.size() == 0) {
                throw new CustomException("该用户未关注广州港船务有限公司!");
            } else if (wechatMpUserList.size() == 1) {

                AgentDelivery agentDelivery = agentDeliveryService.getById(pb6BargeCheckMessage.getAgentdeliveryid());
                String accessToken = wechatMpAccessTokenService.getAccessToken();
                String result = pb6BargeCheckMessage.getAuditflag() == 1 ? "审核通过" : "审核不通过";
                // 2、发送公众号消息
                MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                        .template_id("iGHNv91Hr9ynpe8Uopwu5NomoWMEheqdMa4RbU7wrm0")
                        .data("first", MpMessageDTO.MpMessageDataField.builder().value("您的代理发货编号 " + agentDelivery.getDeliveryNo() +
                                " " + result).color("#000000").build())
                        .data("keyword1", MpMessageDTO.MpMessageDataField.builder().value("代理发货").color("#000000").build())
                        .data("keyword2", MpMessageDTO.MpMessageDataField.builder().value(result).color("#000000").build())
                        .data("remark", MpMessageDTO.MpMessageDataField.builder().value(pb6BargeCheckMessage.getAuditflag() == 1 ? "码头办理托运单中，请及时查看托运单" :
                                pb6BargeCheckMessage.getFailurereasons()).color("#000000").build())
                        .build();

                MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                log.info("代理发货编号 {} {}", agentDelivery.getDeliveryNo(), result);
                log.info("发送历史消息：{}",mpMessageResult);
            } else {
                throw new CustomException("数据库数据错误!");
            }
        } else {
            log.warn("用户不存在或没有unionid，发送消息失败！");
        }
    }

    @Override
    public void sendMessageForGreenBarge(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail) throws URISyntaxException{
         SysUser sysUser=sysUserService.selectUserById(pb6Cargoconsignmentdetail.getWxointmentmid());
        if (sysUser != null && StringUtils.isNotBlank(sysUser.getUnionId())) {
            List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
            if (wechatMpUserList.size() == 0) {
                throw new CustomException("该用户未关注广州港船务有限公司!");
            } else if (wechatMpUserList.size() == 1) {
                String accessToken = wechatMpAccessTokenService.getAccessToken();
                // 2、发送公众号消息
                MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                        .template_id("iGHNv91Hr9ynpe8Uopwu5NomoWMEheqdMa4RbU7wrm0")
                        .url("https://vcbooking.gzport.com/prod-api/profile/lsdt/lsbc.jpg")
                        .data("first", MpMessageDTO.MpMessageDataField.builder().value("您的驳船（"+pb6Cargoconsignmentdetail.getBargename()+
                                "）无法报到，因为未做绿色驳船申报").color("#000000").build())
                        .data("keyword1", MpMessageDTO.MpMessageDataField.builder().value("无法报到").color("#000000").build())
                        .data("keyword2", MpMessageDTO.MpMessageDataField.builder().value("未报到").color("#000000").build())
                        .data("remark", MpMessageDTO.MpMessageDataField.builder().value("请点击链接前往绿色驳船做申报").color("#000000").build())
                        .build();

                MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                log.info("发送成功");
            } else {
                throw new CustomException("数据库数据错误!");
            }
        } else {
            log.warn("用户不存在或没有unionid，发送消息失败！");
        }
    }
}

package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.databarge.domain.Pb30OlUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/11/16.
 * @Date: 2020/11/16 8:32
 */
public interface Pb30OlUserMapper extends BaseMapper<Pb30OlUser> {

    IPage<Pb30OlUser> searchCheckedBargeCompany(Page<Pb30OlUser> page, @Param("company") String company);

    IPage<Pb30OlUser> searchNotCheckBargeCompany(Page<Pb30OlUser> page, @Param("company") String company);

    List<Pb30OlUser> searchAllClerksByCompanyId(@Param("companyId") Long companyId);
}

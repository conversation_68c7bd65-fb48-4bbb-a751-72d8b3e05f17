package com.ruoyi.app.controller.consignor;

import com.ruoyi.barge.domain.bo.BargeCapacityPublishBO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.consignor.domain.bo.CargoSourceBO;
import com.ruoyi.consignor.service.ConsignorCargoSourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Description 货主的货源控制层
 * <AUTHOR>
 * @Date 2020/7/29 13:55
 */
@Api(value = "货主-主页-api", tags = "货主-主页-api")
@Slf4j
@RestController
@RequestMapping("/consignor/cargoSource")
public class ConsignorCargoSourceController {

    @Autowired
    private ConsignorCargoSourceService consignorCargoSourceService;


    /**
     * 添加货源信息
     *
     * @param cargoSourceBO
     * @return
     */
    @ApiOperation("新增货源")
    @PostMapping("/save")
    public AjaxResult saveCargoSource(@RequestBody @Valid CargoSourceBO cargoSourceBO, BindingResult result) {

        consignorCargoSourceService.saveCargoSource(cargoSourceBO, result);

        return AjaxResult.success();

    }

    /**
     *  修改货源信息
     * @param cargoSourceBO
     * @return
     */
    @ApiOperation("修改货源")
    @PutMapping("/update")
    public AjaxResult updateCargoSource(@RequestBody @Valid CargoSourceBO cargoSourceBO, BindingResult result) {

        if (StringUtils.isNull(cargoSourceBO)) {
            return AjaxResult.error("货源数据不能为空");
        }
        consignorCargoSourceService.updateCargoSource(cargoSourceBO, result);

        return AjaxResult.success();

    }

    /**
     *  货源查询
     * @param cargoSourceBO
     * @return
     */
    @ApiOperation("货源查询")
    @PostMapping("/search")
    public AjaxResult selectCargoSource(@RequestBody CargoSourceBO cargoSourceBO) {

        return AjaxResult.success(consignorCargoSourceService.selectCargoSource(cargoSourceBO));

    }

    /**
     *  根据id删除货源
     * @param id
     * @return
     */
    @ApiOperation("删除货源")
    @DeleteMapping("/delete")
    public AjaxResult deleteCargoSourceById(Long id) {

        consignorCargoSourceService.deleteCargoSourceById(id);

        return AjaxResult.success();
    }


    /**
     *  动力查询
     * @param bargeCapacityPublishBO
     * @return
     */
    @ApiOperation(value = "根据条件查询动力")
    @PostMapping("/select")
    public AjaxResult selectImpetusData(@RequestBody BargeCapacityPublishBO bargeCapacityPublishBO) {
        return AjaxResult.success(consignorCargoSourceService.selectImpetusData(bargeCapacityPublishBO));
    }

}

package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 水路运单实体类
 * <AUTHOR>
 * @Date 2020/8/20  11:59
 */
@Setter
@Getter
@ToString
@TableName(value = "PB6_WATERWAYCARGO")
public class WaterwayCargo {


    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long ID;

    /**
     * 水路货物运单编号
     */
    @TableField("WATERWAYCARGOID")
    private String waterwayCargoId;

    /**
     * 出入库单号
     */
    @TableField("OUTORINFORMID")
    private String OUTORINFORMID;

    /**
     * 地磅单号
     */
    @TableField("LOADOMETERID")
    private String LOADOMETERID;

    /**
     * 航次
     */
    @TableField("BARGE_NUMBER")
    private String bargeNumber;

    /**
     * 发货人
     */
    @TableField("CONSIGNER")
    private String CONSIGNER;

    /**
     * 收货人
     */
    @TableField("CONSIGNEE")
    private String CONSIGNEE;

    /**
     * 装货地点
     */
    @TableField("SHIPMENTPLACE")
    private String SHIPMENTPLACE;

    /**
     * 航区
     */
    @TableField("SAILAREA")
    private String SAILAREA;

    /**
     * 发货符号
     */
    @TableField("CONSIGNMENTFLAG")
    private String CONSIGNMENTFLAG;

    /**
     * 配载件数
     */
    @TableField("RATIONPIECE")
    private String RATIONPIECE;

    /**
     * 配载重量
     */
    @TableField("RATIONWEIGHT")
    private String RATIONWEIGHT;

    /**
     * 费用结算方式
     */
    @TableField("CHARGEBALANCETYPE")
    private String CHARGEBALANCETYPE;

    /**
     * 计费吨
     */
    @TableField("CHARGEWEIGHT")
    private String chargeWeight;

    /**
     * 运费
     */
    @TableField("TRANSPORTCHARGE")
    private String transportCharge;

    /**
     * 货港费
     */
    @TableField("CARGOPORTCHARGE")
    private String cargoPortCharge;

    /**
     * 服务代理费
     */
    @TableField("SERVICEAGENTCHARGE")
    private String serviceAgentCharge;

    /**
     * 业务代理费
     */
    @TableField("BUSINESSAGENTCHARGE")
    private String businessAgentCharge;

    /**
     * 费用总合计
     */
    @TableField("TOTALCHARGE")
    private String totalCharge;

    /**
     * 特约事项
     */
    @TableField("SPECIALPROCEEDING")
    private String SPECIALPROCEEDING;

    /**
     * (用于存显示的港口endportshow)
     */
    @TableField("VALIDDATE")
    private String VALIDDATE;

    /**
     * 配载日期
     */
    @TableField("RATIONDATE")
    private String RATIONDATE;

    /**
     * 配载负责人
     */
    @TableField("RATIONPRINCIPAL")
    private String RATIONPRINCIPAL;

    /**
     * 退单负责人
     */
    @TableField("CANCELRATIONPRINCIPAL")
    private String CANCELRATIONPRINCIPAL;

    /**
     * 退单日期
     */
    @TableField("CANCELRATIONDATE")
    private String CANCELRATIONDATE;

    /**
     * 出入库单号代理主键
     */
    @TableField("OUTORINFORMIDID")
    private Long OUTORINFORMIDID;

    /**
     * 地磅单号代理主键
     */
    @TableField("LOADOMETERIDID")
    private Long LOADOMETERIDID;

    /**
     * 驳船基本信息代理主键
     */
    @TableField("BARGEIDID")
    private Long BARGEIDID;

    /**
     * 起运港代理主键
     */
    @TableField("BEGINPORTID")
    private Long BEGINPORTID;

    /**
     * 中转港代理主键
     */
    @TableField("MIDPORTID")
    private Long MIDPORTID;

    /**
     * 目的港代理主键
     */
    @TableField("ENDPORTID")
    private Long ENDPORTID;

    /**
     * 货物名称代理主键
     */
    @TableField("CARGENAMEID")
    private Long CARGENAMEID;

    /**
     * 配载人代理主键
     */
    @TableField("RATIONPRINCIPALID")
    private Long RATIONPRINCIPALID;

    /**
     * 退单负责人代理主键
     */
    @TableField("CANCELRATIONPRINCIPALID")
    private Long CANCELRATIONPRINCIPALID;

    /**
     * 作业区代理主键
     */
    @TableField("WORKID")
    private Long WORKID;

    /**
     * 公司代理主键
     */
    @TableField("COMID")
    private Long COMID;

    /**
     * 修改人代理主键
     */
    @TableField("MODIFYMANID")
    private Long MODIFYMANID;

    /**
     * 修改人
     */
    @TableField("MODIFYMAN")
    private String MODIFYMAN;

    /**
     * 修改日期
     */
    @TableField("MODIFYDATE")
    private String MODIFYDATE;

    /**
     * 工本费
     */
    @TableField("DOCHARGE")
    private String DOCHARGE;

    /**
     * 驳船状态，0：已配载，没有报到信息 1：已报到；2：已离港；3：已取消报到，处于配载状态，并且已有报到信息 4：已退单 5:已停止
     */
    @TableField("FLAGBARGESTATE")
    private String FLAGBARGESTATE;

    /**
     * 装卸类型
     */
    @TableField("FLAGLOADORUNLOAD")
    private String FLAGLOADORUNLOAD;

    /**
     * 运费费率
     */
    @TableField("TRANSPORTCHARGERATE")
    private String TRANSPORTCHARGERATE;

    /**
     * 货港费率
     */
    @TableField("CARGOPORTCHARGERATE")
    private String CARGOPORTCHARGERATE;

    /**
     * 服务代理费率
     */
    @TableField("SERVICEAGENTCHARGERATE")
    private String SERVICEAGENTCHARGERATE;

    /**
     * 业务代理费率
     */
    @TableField("BUSINESSAGENTCHARGERATE")
    private String BUSINESSAGENTCHARGERATE;

    /**
     * 托号
     */
    @TableField("CONSIGNFLAG")
    private String CONSIGNFLAG;

    /**
     * 一单多票
     */
    @TableField("FLAGMANYFORMID")
    private String FLAGMANYFORMID;

    /**
     * 水路货物运单编号no
     */
    @TableField("WATERWAYCARGONO")
    private String WATERWAYCARGONO;

    /**
     * 是否分量配载
     */
    @TableField("FLAGREG")
    private String FLAGREG;

    /**
     * 包装方式
     */
    @TableField("PACKAGETYPE")
    private String PACKAGETYPE;

    /**
     * 包装方式代理主键
     */
    @TableField("PACKAGETYPEID")
    private String PACKAGETYPEID;

    /**
     * 货物名称
     */
    @TableField("CARGENAME")
    private String CARGENAME;

    /**
     * 分量配载货重
     */
    @TableField("DISRATIONWEIGHT")
    private String DISRATIONWEIGHT;

    /**
     * 分量配载件数
     */
    @TableField("DISRATIONPIECE")
    private String DISRATIONPIECE;

    /**
     * 是否申请到验号
     */
    @TableField("ISAPPLYUNIQUECODE")
    private String ISAPPLYUNIQUECODE;

    /**
     * 是否为集装箱
     */
    @TableField("ISCONTAINER")
    private String ISCONTAINER;

    /**
     * 录入时间
     */
    @TableField("RECORDDATE")
    private String recordDate;

    /**
     * 统计标记
     */
    @TableField("STAS_REMARK")
    private String STAS_REMARK;

    /**
     * 货物体积(收款人)
     */
    @TableField(value = "CARGOSIZE",updateStrategy = FieldStrategy.NOT_NULL)
    private String CARGOSIZE;

    /**
     * 是否为大船
     */
    @TableField("FLAGSHIP")
    private String FLAGSHIP;

    /**
     * 是否为沿海船
     */
    @TableField("COASTALVESSEL")
    private String COASTALVESSEL;

    /**
     * 系解缆费
     */
    @TableField("MOORINGCHARGE")
    private String MOORINGCHARGE;

    /**
     * 开关仓费
     */
    @TableField("OCHATCHCHARGE")
    private String OCHATCHCHARGE;

    /**
     * 停泊费
     */
    @TableField("BERTHCHARGE")
    private String berthCharge;

    /**
     * 甚高频费
     */
    @TableField("VHFCHARGE")
    private String VHFCHARGE;

    /**
     * 系解缆费率
     */
    @TableField("MOORINGCHARGERATE")
    private String MOORINGCHARGERATE;

    /**
     * 开关仓费率
     */
    @TableField("OCHATCHCHARGERATE")
    private String OCHATCHCHARGERATE;

    /**
     * 停泊费率
     */
    @TableField("BERTHCHARGERATE")
    private String BERTHCHARGERATE;

    /**
     * 甚高频费率
     */
    @TableField("VHFCHARGERATE")
    private String VHFCHARGERATE;

    /**
     * 停泊天数
     */
    @TableField("BERTHDAYS")
    private String BERTHDAYS;

    /**
     * 流向
     */
    @TableField("FLOW")
    private String FLOW;

    /**
     * 大船名
     */
    @TableField("BARGE_NAME")
    private String bargeName;

    /**
     * 世德申请水路货物运单编号
     */
    @TableField("APPLYCARGOCONSIGNMENT")
    private String APPLYCARGOCONSIGNMENT;

    /**
     * 是否核销(Y;N)
     */
    @TableField("ISVERIFICATE")
    private String ISVERIFICATE;

    /**
     * 核销原因
     */
    @TableField("VERIFICATEREASON")
    private String VERIFICATEREASON;

    /**
     * 保存发送支付信息的电话号码
     */
    @TableField("SENDCELLNUM")
    private String SENDCELLNUM;

    /**
     * 确认实装数 Y 确认实装， N是没有确认实装
     */
    @TableField("CONFIRMLOADINGOVER")
    private String confirmloadingover;

    /**
     * 确认实装的驳船主id（sys_user.user_id）
     */
    @TableField("CONFIRMBARGEEID")
    private Long CONFIRMBARGEEID;

    /**
     * 确认实装的时间
     */
    @TableField("CONFIRMTIME")
    private String confirmtime;

    // 大船名
    private String shipName;
}

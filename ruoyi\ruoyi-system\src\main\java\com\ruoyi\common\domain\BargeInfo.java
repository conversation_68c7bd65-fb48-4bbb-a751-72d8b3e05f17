package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 驳船备案
 * @Description
 * <AUTHOR>
 * @Date 2020/7/24 15:24
 */
@Getter
@Setter
@ToString
@TableName( value = "pb6_bargeinfo")
@KeySequence("SEQ_PB6_BARGEINFO")
public class BargeInfo {

    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    @TableField(value = "BARGEID")
    private String bargeId;

    @TableField(value = "BARGENAME")
    private String bargeName;

    @TableField(value = "BARGELOADA")
    private String bargeLoadA;

    @TableField(value = "BARGELOADB")
    private String bargeLoadB;

    @TableField(value = "VALIDSAILDATE")
    private String validSailDate;

    @TableField(value = "BELONGAREA")
    private String belongArea;

    @TableField(value = "BARGEOWNER")
    private String bargeOwner;

    @TableField(value = "CONTACTPHONE")
    private String contactPhone;

    @TableField(value = "BARGETYPE")
    private String bargeType;

    @TableField(value = "FLAGBLACKLIST")
    private String flagBlacklist;

    @TableField(value = "RECORDER")
    private String recorder;

    @TableField(value = "RECORDDATE")
    private String recordDate;

    @TableField(value = "BARGELINKMAN")
    private String bargeLinkMan;

    @TableField(value = "REMAKEDATE")
    private String remakeDate;

    @TableField(value = "BARGECALL")
    private String bargeCall;

    @TableField(value = "MAKEFACTORY")
    private String makeFactory;

    @TableField(value = "REMAKEFACTORY")
    private String remakeFactory;

    @TableField(value = "BARGEOPERATOR")
    private String bargeOperator;

    @TableField(value = "TOTALLENGTH")
    private String totalLength;

    @TableField(value = "BARGELENGTH")
    private String bargeLength;

    @TableField(value = "BARGEWIDTH")
    private String bargeWidth;

    @TableField(value = "MODELDEEP")
    private String modelDeep;

    @TableField(value = "MAXBARGEWIDTH")
    private String maxBargeWidth;

    @TableField(value = "MAXBARGEHIGH")
    private String maxBargeHigh;

    @TableField(value = "LOADWATERLINELENGTH")
    private String loadWaterLineLength;

    @TableField(value = "UNLOADDRAUGHT")
    private String unloadDraught;

    @TableField(value = "LOADDRAUGHT")
    private String loadDraught;

    @TableField(value = "UNLOADDISPLACEMENT")
    private String unloadDisplacement;

    @TableField(value = "LOADDISPLACEMENT")
    private String loadDisplacement;

    @TableField(value = "STRUCTURE")
    private String structure;

    @TableField(value = "BARGEINDEX")
    private String bargeIndex;

    @TableField(value = "BARGECHECKID")
    private String bargeCheckId;

    @TableField(value = "EMPLACEKEELDATE")
    private String emplaceKeelDate;

    @TableField(value = "RECORDERID")
    private Long recorderId;

    @TableField(value = "MODIFYMAN")
    private String modifyMan;

    @TableField(value = "MODIFYMANID")
    private Long modifyManId;

    @TableField(value = "MODIFYDATE")
    private String modifyDate;

    @TableField(value = "PYCAP")
    private String pycap;

    @TableField(value = "ISIMPORT")
    private String isImport;

    @TableField(value = "MEMO")
    private String memo;

    @TableField(value = "TRANSPORTNUM")
    private String transportNum;

    @TableField(value = "BUSINESSSCOPE")
    private String businessScope;

    @TableField(value = "ANNUALVALIDSAILDATE")
    private String annualValidSailDate;

    @TableField(value = "BARGEWEIGHT")
    private String bargeWeight;

    @TableField(value = "SPECIALCUSTOMER")
    private String specialCustomer;

    @TableField(value = "SPECIALCUSDATE")
    private String specialCusDate;

    @TableField(value = "SPECIALID")
    private String specialId;

    @TableField(value = "BARGETOTALWEIGHT")
    private String bargeTotalWeight;

    @TableField(value = "MMSI")
    private String MMSI;

    @TableField(value = "ISOLORDER")
    private String isOlOrder;

    @TableField(value = "NEWCONTACTNUM")
    private String newContactNum;

    @TableField(value = "OPENID")
    private String openId;

    // 装载重量最小
    @TableField("LOADING_WEIGHT_MIN")
    private String loadingWeightMin;

    // 装载重量最大
    @TableField("LOADING_WEIGHT_MAX")
    private String loadingWeightMax;

    // 不允许装载货物
    @TableField("CARGO_NOT_ALLOWED")
    private String cargoNotAllowed;

    /**
     * 是否使用
     */
    @TableField("ISUSE")
    private String isuse;

}

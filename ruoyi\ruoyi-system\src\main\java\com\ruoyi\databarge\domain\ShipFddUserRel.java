package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 驳船-FDD用户关系表
 * <AUTHOR>
 * @Date 2020/10/24 19:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SHIP_FDD_USER_REL")
@KeySequence(value = "SEQUENCE_SHIP_FDD_USER_REL")
public class ShipFddUserRel implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;
    /**
     * 用户类型,1驳船主,2船公司,3船公司帮驳船主备案,4托运单联系人（物流公司上传）
     */
    @TableField("TYPE")
    private String type;

    /**
     * 驳船用户id
     */
    @TableField("SHIP_USER_ID")
    private Long shipUserId;
    /**
     * 驳船id
     */
    @TableField("SHIP_ID")
    private Long shipId;
    /**
     * FDD账户
     */
    @TableField("FDD_ACCOUNT_ID")
    private String fddAccountId;
    /**
     * 印章图片url
     */
    @TableField("SEAL_URL")
    private String sealUrl;
    /**
     * 印章图片url
     */
    @TableField(value = "NEW_SEAL_URL")
    private String newSealUrl;
    /**
     * 认证序列号
     */
    @TableField("VERIFIED_SERIAL_NO")
    private String verifiedSerialNo;

    /**
     * 审核状态,0未审核,1审核通过,2审核不通过
     */
    @TableField("REVIEW_STATUS")
    private String reviewStatus;

    /**
     * 备注
     */
    @TableField("REMARKS")
    private String remarks;

    /**
     * 船章上的船公司名
     */
    @TableField("SHIP_COMPANY_NAME")
    private String shipCompanyName;

    /**
     * 认证url
     */
    @TableField("URL")
    private String url;

    @TableField("SHIP_COMPANY_ISAPPLY")
    private String shipCompanyIsApply;

    @TableField(exist = false)
    private String fileBase64;// 印章图片base64编码
    @TableField(exist = false)
    private String shipName;// 驳船名
//    @TableField(exist = false)
//    private String shipCompanyName;// 船公司名

}

package com.ruoyi.web.controller.system;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController
{
    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysDictData dictData)
    {
        startPage();
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        return getDataTable(list);
    }

    @Log(title = "字典数据", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:dict:export')")
    @GetMapping("/export")
    public AjaxResult export(SysDictData dictData)
    {
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        ExcelUtil<SysDictData> util = new ExcelUtil<SysDictData>(SysDictData.class);
        return util.exportExcel(list, "字典数据");
    }

    /**
     * 查询字典数据详细
     */
//    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{dictCode}")
    public AjaxResult getInfo(@PathVariable Long dictCode)
    {
        return AjaxResult.success(dictDataService.selectDictDataById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/dictType/{dictType}")
    public AjaxResult dictType(@PathVariable String dictType)
    {
        return AjaxResult.success(dictTypeService.selectDictDataByType(dictType));
    }

    /**
     * 新增字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDictData dict)
    {
        dict.setCreateBy(SecurityUtils.getUsername());
        return toAjax(dictDataService.insertDictData(dict));
    }

    /**
     * 修改保存字典类型
     */
//    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDictData dict)
    {
        dict.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(dictDataService.updateDictData(dict));
    }

    /**
     * 删除字典类型
     */
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    public AjaxResult remove(@PathVariable Long[] dictCodes)
    {
        return toAjax(dictDataService.deleteDictDataByIds(dictCodes));
    }

    /**
     * 更新差额吨占比
     */
    @PreAuthorize("@ss.hasPermi('pay:refund:update')")
    @Log(title = "差额吨占比", businessType = BusinessType.UPDATE)
    @PostMapping("/refundTonPercent/update")
    public AjaxResult updateRefundTonPercent(@RequestBody String value)
    {
        List<SysDictData> dictDataList = dictTypeService.selectDictDataByType("refund_ton_percent");
        if(dictDataList.size() == 0){
            return AjaxResult.error("没有找到差额吨占比数据");
        } else if(dictDataList.size() >= 2){
            return AjaxResult.error("找到多条差额吨占比数据，请核对！");
        } else {
            SysDictData sysDictData = dictDataList.get(0);
            sysDictData.setDictValue(value);
            return toAjax(dictDataService.updateDictData(sysDictData));
        }
    }

//    @PreAuthorize("@ss.hasPermi('Invoice:StopKpdate:update')")
    @Log(title = "发票停止开票日期", businessType = BusinessType.UPDATE)
    @PostMapping("/InvoiceStopKpdate/update")
    public AjaxResult updateInvoiceStopKpdate(@RequestBody String value)
    {
        List<SysDictData> dictDataList = dictTypeService.selectDictDataByType("invoice_stop_kpdate");
        if(dictDataList.size() == 0){
            return AjaxResult.error("没有找到发票停止开票日期数据");
        } else if(dictDataList.size() >= 2){
            return AjaxResult.error("找到多条发票停止开票日期数据，请核对！");
        } else {
            SysDictData sysDictData = dictDataList.get(0);
            sysDictData.setDictValue(value);
            dictDataService.updateDictData(sysDictData);
            return AjaxResult.success("修改成功");
        }
    }
}

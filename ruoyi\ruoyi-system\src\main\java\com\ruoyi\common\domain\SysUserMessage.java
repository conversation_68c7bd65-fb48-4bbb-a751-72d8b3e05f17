package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 用户消息 实体类
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/5 10:32
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
@TableName("SYS_USER_MESSAGE")
@KeySequence("SEQ_SYS_USER_MESSAGE")
public class SysUserMessage {

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 订阅用户id
     */
    @TableField("USER_ID")
    private Long userId;

    /**
     * 消息id
     */
    @TableField("MESSAGE_ID")
    private Long messageId;

    /**
     * 读取状态（0：未读，1：已读）
     */
    @TableField("IS_READ")
    private String isRead;

    /**
     * 删除标识，默认0未删除， 1已删除
     */
    @TableField("DEL_FLAG")
    private Integer delFlag;
}

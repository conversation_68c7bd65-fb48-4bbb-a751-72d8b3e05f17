package com.ruoyi.businessFile.controller;

import com.ruoyi.businessFile.domain.BusinessFileType;
import com.ruoyi.businessFile.service.BusinessFileTypeService;
import com.ruoyi.businessFile.service.SysOssService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/10 12:58
 */
@RestController
@RequestMapping("/file")
public class BusinessFileTypeController extends BaseController {

    @Autowired
    BusinessFileTypeService businessFileTypeService;

    @Autowired
    private SysOssService sysOssService;


    @GetMapping("/listFileTypeName")
    public AjaxResult listFileTypeName(){
        return AjaxResult.success(businessFileTypeService.getFileTypeName());
    }
    /*
     * 列表查询
     */
    @GetMapping("/list")
    public TableDataInfo list(BusinessFileType businessFileType){
        startPage();
        List<BusinessFileType> businessFileTypes=businessFileTypeService.list(businessFileType);
        return getDataTable(businessFileTypes);
    }

    @PostMapping("/addFileType")
    public AjaxResult addFileType(BusinessFileType businessFileType){
        return businessFileTypeService.insert(businessFileType);
    }

    @GetMapping("/{id}")
    public AjaxResult getBusinessTyprById(@PathVariable String id){
        BusinessFileType businessFileType=businessFileTypeService.getById(id);
        return AjaxResult.success(businessFileType);
    }

    @PutMapping("/update")
    public AjaxResult update(@RequestBody BusinessFileType businessFileType){
        return businessFileTypeService.update(businessFileType);
    }

    @DeleteMapping("/{ids}")
    public AjaxResult delete(@PathVariable String[] ids){
        return businessFileTypeService.removeByIds(Arrays.asList(ids))?AjaxResult.success():AjaxResult.error();
    }


}

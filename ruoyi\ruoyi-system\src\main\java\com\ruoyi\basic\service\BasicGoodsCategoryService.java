package com.ruoyi.basic.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.basic.domain.BasicGoodsCategory;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【BASIC_GOODS_CATEGORY】的数据库操作Service
* @createDate 2023-11-16 11:35:21
*/
public interface BasicGoodsCategoryService extends IService<BasicGoodsCategory> {

    // 货类List
    List<BasicGoodsCategory> selectBasicGoodsCategoryList(BasicGoodsCategory basicGoodsCategory);

    // 新增货类
    AjaxResult addBasicGoodsCategory(BasicGoodsCategory basicGoodsCategory);

    // 修改货类
    AjaxResult updateBasicGoodsCategory(BasicGoodsCategory basicGoodsCategory);

    // 删除货类
    AjaxResult deleteBasicGoodsCategory(String goodsId);

    // 根据货名查看所属货类
    BasicGoodsCategory selectBasicGoodsCategoryByGoodsName(String goodsName);

}

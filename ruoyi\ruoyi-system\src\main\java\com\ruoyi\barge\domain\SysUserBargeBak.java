package com.ruoyi.barge.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "SYS_USER_BARGE_BAK")
public class SysUserBargeBak{

    private static final long serialVersionUID = 1L;

    //用户id
    @TableField("USER_ID")
    private Long userId;

    //驳船id
    @TableField("BARGE_ID")
    private Long bargeId;

    //驳船审核id
    @TableField("BARGE_AUDIT_ID")
    private Long bargeAuditId;

}

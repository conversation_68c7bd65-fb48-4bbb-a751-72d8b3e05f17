package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.AgentDelivery;
import com.ruoyi.databarge.domain.Pb6BargeCheckMessage;
import com.ruoyi.databarge.mapper.AgentDeliveryMapper;
import com.ruoyi.databarge.service.AgentDeliveryService;
import com.ruoyi.databarge.service.Pb6BargeCheckMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2021/7/13.
 * @Date: 2021/7/13 9:49
 */
@Service
public class AgentDeliveryServiceImpl extends ServiceImpl<AgentDeliveryMapper, AgentDelivery> implements AgentDeliveryService {

    @Autowired
    private Pb6BargeCheckMessageService pb6BargeCheckMessageService;
    @Override
    public List<AgentDelivery> selectList(String str) {
        LambdaQueryWrapper<AgentDelivery> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(StringUtils.isNotBlank(str), i -> i.like(AgentDelivery::getCoutFormId, str).or().like(AgentDelivery::getDeliveryNo, str))
                .eq(AgentDelivery::getDeleteFlag, 0)
                .eq(AgentDelivery::getBargeName, this.selectUserBargeName())
                .orderByDesc(AgentDelivery::getUpdateTime);
        return list(queryWrapper);
    }

    @Override
    public Long addAgentDelivery(AgentDelivery agentDelivery) {

        Timestamp currentTime = DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        agentDelivery.setAuditSign(0);
//        agentDelivery.setAuditTime(DateUtils.strToSqlDate(DateUtils.getDate(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        agentDelivery.setDeleteFlag(0);
        agentDelivery.setCreateTime(currentTime);
        agentDelivery.setUpdateTime(currentTime);
        save(agentDelivery);

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        Pb6BargeCheckMessage pb6BargeCheckMessage = new Pb6BargeCheckMessage();
        pb6BargeCheckMessage.setMtype(14);
        pb6BargeCheckMessage.setApplymanid(sysUser.getUserId());
        pb6BargeCheckMessage.setApplyman(sysUser.getNickName());
        pb6BargeCheckMessage.setApplytime(currentTime);
        pb6BargeCheckMessage.setAuditflag(0);
        pb6BargeCheckMessage.setBargename(agentDelivery.getBargeName());
        pb6BargeCheckMessage.setAgentdeliveryid(agentDelivery.getId());
        pb6BargeCheckMessageService.save(pb6BargeCheckMessage);
        return agentDelivery.getId();
    }

    @Override
    public void deleteAgentDelivery(Long id) {
        AgentDelivery agentDelivery = getById(id);
        if(agentDelivery.getAuditSign() == 0){
            agentDelivery.setDeleteFlag(1);
            agentDelivery.setUpdateTime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            updateById(agentDelivery);
        } else {
            throw new CustomException("该记录已审核，不可删除");
        }
    }

    @Override
    public void updateAgentDelivery(AgentDelivery agentDelivery) {
        AgentDelivery agentDelivery1 = getById(agentDelivery.getId());
        if(agentDelivery1.getAuditSign() == 0){
            agentDelivery1.setCoutFormId(agentDelivery.getCoutFormId());
            agentDelivery1.setDeliveryNo(agentDelivery.getDeliveryNo());
            agentDelivery1.setBargeName(agentDelivery.getBargeName());
            agentDelivery1.setPlanWeight(agentDelivery.getPlanWeight());
            agentDelivery1.setUpdateTime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            updateById(agentDelivery1);
        } else {
            throw new CustomException("该记录已审核，不可修改");
        }
    }

    @Override
    public String selectUserBargeName() {
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        if(sysUser != null){
            return baseMapper.selectUserBargeName(sysUser.getUserId());
        }
        return null;
    }
}

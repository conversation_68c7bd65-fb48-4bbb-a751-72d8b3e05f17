package com.ruoyi.basic.mapper;



import com.ruoyi.api.domain.AcceptOrderUser;
import com.ruoyi.basic.domain.BasicCustomer;
import com.ruoyi.basic.domain.BasicShip;
import com.ruoyi.basic.domain.BasicTerminal;
import com.ruoyi.basic.domain.vo.ShipPersonVo;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

import java.util.List;

/**
 * <AUTHOR>
 * @description 从库，船务系统主库操作
 * @date 2023/11/20 9:38
 */
@DataSource(value = DataSourceType.SLAVE)
public interface SlaveMapper {

    // 客户List
    List<BasicCustomer> selectBasicCustomerList();

    // 船舶List
    List<BasicShip> selectBasicShipList();

    // 码头List
    List<BasicTerminal> selectBasicTerminalList();

    // 船舶人员List
    List<ShipPersonVo> selectShipPersonList(ShipPersonVo shipPersonVo);

    // 安通系统，接单操作员List
    List<AcceptOrderUser> selectAcceptOrderUserList();

    // 根据安通码头代码查询船务码头名称
    String selectPortameByAtPortCode(String portCode);



}

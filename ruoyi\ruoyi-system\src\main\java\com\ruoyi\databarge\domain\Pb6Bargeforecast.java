package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 接受驳船预报信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PB6_BARGEFORECAST")
public class Pb6Bargeforecast implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 水路货运单号
     */
    @TableField("WATERWAYCARGOID")
    private String waterwaycargoid;

    /**
     * 出库单号
     */
    @TableField("OUTORINFORMID")
    private String outorinformid;

    /**
     * 驳船名称
     */
    @TableField("BARGENAME")
    private String bargename;

    /**
     * 预报时间
     */
    @TableField("STARTTIME")
    private String starttime;

    /**
     * 预计抵达时间（报到时填）
     */
    @TableField("ENDTIME")
    private String endtime;

    /**
     * 驳船状态：0为已预报；1为已抵达；2为已取消预报；3为已报到；4为开始作业；5为作业完成；6为已离港;7为已取消报到
     */
    @TableField("TYPE")
    private String type;

    /**
     * 作业开始时间
     */
    @TableField("BEGINWORKTIME")
    private String beginworktime;

    /**
     * 作业结束时间
     */
    @TableField("ENDWORKTIME")
    private String endworktime;

    /**
     * 报到时间
     */
    @TableField("REGISTERTIME")
    private String registertime;

    /**
     * 驳船主联系方式
     */
    @TableField("BARGETEL")
    private String bargetel;

    /**
     * 公司ID
     */
    @TableField("COMID")
    private Long comid;

    /**
     * 作业码头
     */
    @TableField("WORKPORT")
    private String workport;

    /**
     * 托运单号
     */
    @TableField("CONSIGNFLAG")
    private String consignflag;

    /**
     * MMSI标识
     */
    @TableField("MMSI")
    private String mmsi;

    /**
     * 结算方式:1为月结；2为现结
     */
    @TableField("PAYTYPE")
    private String paytype;

    /**
     * 抵达时间
     */
    @TableField("FORCASTTIME")
    private String forcasttime;

    /**
     * 派船明细顺序号
     */
    @TableField("SERIALNUMBER")
    private String serialnumber;

    /**
     * 驳船公司
     */
    @TableField("SHIPPINGCONAME")
    private String shippingconame;

    /**
     * 是否加急
     */
    @TableField("ISHARRY")
    private String isharry;

    /**
     * 加急原因
     */
    @TableField("ISHARRYREASON")
    private String isharryreason;

    /**
     * 配载重量
     */
    @TableField("RATIONWEIGHT")
    private String rationweight;

    /**
     * 实装重量
     */
    @TableField("REALWEIGHT")
    private String realweight;

    /**
     * 是否发送推送
     */
    @TableField("AREA")
    private String area;

    /**
     * 微信ID
     */
    @TableField("OPENID")
    private String openid;


}

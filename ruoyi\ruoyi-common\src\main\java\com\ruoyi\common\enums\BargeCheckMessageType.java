package com.ruoyi.common.enums;

/**
 * @Description 记录操作消息类型
 * <AUTHOR>
 * @Date 2020/8/12 14:59
 */
public enum BargeCheckMessageType {

    /**
     *  消息类型：
     *  0、驳船备案审核；1、驳船备案加挂靠； 2、驳船信息修改审核； 3、驳船添加挂靠；
     *  4、驳船挂靠修改审核； 5、驳船取消挂靠；
     * 6、退单物流公司审核；7、退单码头审核； 8、改单物流公司审核；
     * 9、改单码头审核； 10、差额退款审核
     */
    BARGE_FILING_AUDIT(0,"驳船备案审核"),
    BARGE_WAS_REGISTERED_AND_CHECK(1,"驳船备案加挂靠"),
    BARGE_INFORMATION_MODIFICATION_REVIEW(2,"驳船信息修改审核"),
    ADD_BARGE_ADDITIONAL(3,"驳船添加挂靠"),
    BARGE_ATTACHMENT_MODIFICATION_REVIEW(4,"驳船挂靠修改审核"),
    THE_BARGE_WAS_CANCELLED(5,"驳船取消挂靠"),
    RETURNED_ORDER_LOGISTICS_COMPANY_CHECK(6,"退单物流公司审核"),
    CHARGEBACK_DOCK_AUDIT(7,"退单码头审核"),
    CHANGE_LOGISTICS_COMPANY_AUDIT(8,"改单物流公司审核"),
    CHANGE_DOCK_AUDIT(9,"改单码头审核"),
    BALANCE_REFUND_AUDIT(10,"差额退款审核");

    private final Integer code;
    private final String codeName;
    BargeCheckMessageType(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }

    public static String getCodeName(int code){
        BargeCheckMessageType[] messageTypes = values();
        for(BargeCheckMessageType bargeCheckMessageType: messageTypes){
            if(bargeCheckMessageType.getCode() == code){
                return bargeCheckMessageType.getCodeName();
            }
        }
        return null;
    }
}

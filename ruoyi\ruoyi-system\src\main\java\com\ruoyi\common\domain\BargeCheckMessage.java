package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/20 14:19
 */
/**
    * 驳船审核消息
    */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "PB6_BARGE_CHECK_MESSAGE")
@KeySequence("SEQ_PB6_BARGE_CHECK_MESSAGE")
public class BargeCheckMessage {
    /**
    * ID
    */
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    /**
     消息类型：0、驳船备案审核；1、驳船备案加挂靠； 2、驳船信息修改审核； 3、驳船挂靠修改审核； 4、驳船取消挂靠； 5、退单物流公司审核；
     6、退单码头审核； 7、改单物流公司审核； 8、改单码头审核
     */
    @TableField(value = "MTYPE")
    private Short mType;

    /**
    * 申请人ID
    */
    @TableField(value = "APPLYMANID")
    private Long applyManId;

    /**
    * 申请人
    */
    @TableField(value = "APPLYMAN")
    private String applyMan;

    /**
    * 申请时间
    */
    @TableField(value = "APPLYTIME")
    private Date applyTime;

    /**
    * 审核人ID
    */
    @TableField(value = "AUDITMANID")
    private Long auditManId;

    /**
    * 审核人
    */
    @TableField(value = "AUDITMAN")
    private String auditMan;

    /**
    * 审核时间
    */
    @TableField(value = "AUDITTIME")
    private Date auditTime;

    /**
    * 审核标识： 0未审核(未读)，1审核通过(已读)，2审核不通过
    */
    @TableField(value = "AUDITFLAG")
    private Short auditFlag;

    /**
    * 驳船名
    */
    @TableField(value = "BARGENAME")
    private String bargeName;

    /**
    * 水路运单号（不是ID，直接存运单号）
    */
    @TableField(value = "WATERWAYCARGOID")
    private String waterWayCargoId;

    /**
    * 驳船信息备份表ID
    */
    @TableField(value = "PB6BARGEINFOAUDITID")
    private Long pb6BargeInfoAuditId;

    /**
    * 驳船挂靠信息表ID
    */
    @TableField(value = "PB6BARGECOMPANYID")
    private Long pb6BargeCompanyId;


    /**
     * 货物托运单明细表ID
     */
    @TableField(value = "PB6CARGOCONSIGNMENTDETAILID")
    private Long pb6CargoConsignmentDetailId;

    /**
     * 托运单号（不是ID，直接存托运单号）
     */
    @TableField(value = "CONSIGNFLAG")
    private String consignFlag;

    /**
     * 备注（消息详情）
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 审核不通过原因
     */
    @TableField(value = "FAILUREREASONS")
    private String failureReasons;

    /**
     * 印章表主键id
     */
    @TableField(value = "SHIPFDDUSERRELID")
    private Long shipFddUserRelId;
}
package com.ruoyi.app.controller.support.ftp;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Date 2020/11/18 10:38
 * @Description:
 */
@Configuration
@ConfigurationProperties(prefix = "ship.ftp")
@Getter
@Setter
public class FtpProperties {

    private String host;
    private int port;
    private String username;
    private String password;
    private String controlEncoding;
    private int clientMode;
    private int fileType;
    private int bufferSize;
    private int sessionCacheSize;
    private long sessionWaitTimeout;
    private boolean testSession;

}

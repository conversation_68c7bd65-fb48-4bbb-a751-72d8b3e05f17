package com.ruoyi.databarge.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.Pb6BargeCheckMessage;
import com.ruoyi.databarge.domain.Pb6Bargework;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.domain.dto.Pb6BargeWorkDTO;
import com.ruoyi.databarge.domain.dto.ShipxyDTO;
import com.ruoyi.databarge.domain.vo.Pb6BargeWorkVo;
import com.ruoyi.databarge.mapper.Pb6BargeworkMapper;
import com.ruoyi.databarge.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.awt.geom.Point2D;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class Pb6BargeworkServiceImpl  extends ServiceImpl<Pb6BargeworkMapper, Pb6Bargework> implements Pb6BargeworkService {

    @Autowired
    private Pb6CargoconsignmentdetailService pb6CargoconsignmentdetailService;

    @Autowired
    private Pb6BargeinfoService pb6BargeinfoService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;

    @Autowired
    private Pb6BargeworkService pb6BargeworkService;

    @Autowired
    private Pb6BargeCheckMessageService pb6BargeCheckMessageService;


    @Override
    public String searchUniqecode(String coutformid){
        String uniqecode=super.baseMapper.searchUniqecode(coutformid);
        return uniqecode;
    }

    @Override
    public IPage<Pb6BargeWorkVo> searchPagePb6WaterCargo(Pb6BargeWorkDTO pb6BargeWorkDTO) {
        Long pageNum = Optional.ofNullable(pb6BargeWorkDTO.getPageNum()).orElse(0L);
        Long pageSize = Optional.ofNullable(pb6BargeWorkDTO.getPageSize()).orElse(-1L);
        pb6BargeWorkDTO.setBegindate(StringUtils.isNotBlank(pb6BargeWorkDTO.getBegindate()) ? pb6BargeWorkDTO.getBegindate() + " 00:00:00" : null);
        pb6BargeWorkDTO.setEnddate(StringUtils.isNotBlank(pb6BargeWorkDTO.getEnddate()) ? pb6BargeWorkDTO.getEnddate() + " 23:59:59" : null);
        return super.baseMapper.searchPagePb6BargeWorkVo(new Page<>(pageNum, pageSize), pb6BargeWorkDTO);
    }

    @Override
    public String findCargoTypeByCoutformid(String coutformid) {
        return super.baseMapper.findCargoTypeByCoutformid(coutformid);
    }
    @Override
    public List<Map<String,Object>> findCargoNameByCoutformid(String coutformid){
        return super.baseMapper.findCargoNameByCoutformid(coutformid);
    }
}


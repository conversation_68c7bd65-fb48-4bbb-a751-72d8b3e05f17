package com.ruoyi.common.enums;

/**
 * 驳船状态 枚举
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/11 9:25
 */
public enum FlagBargeState {

    /**
     * 0-未审核，1-已审核，2-审核不通过，3-已配载，4-已报道，5-已离港，6-已退单
     */

    NO_CHECK("0", "未审核"),

    YES_CHECK("1", "已审核"),

    CHECK_FAIL("2", "审核不通过"),

    STOWAGE("3", "已配载"),

    REPORT("4", "已报道"),

    LEAVING("5", "已离港"),

    CHARGEBACK("6", "已退单"),

    ALTER("7", "已改单");



    private final String code;

    private final String codeName;

    FlagBargeState(String code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public String getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

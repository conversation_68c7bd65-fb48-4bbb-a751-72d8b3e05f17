package com.ruoyi.app.controller.support.invoice;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.Invoice.domain.bo.InvoiceBO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.WaterwayCargoMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.DataScope;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.domain.ShipInvoiceHistory;
import com.ruoyi.databarge.domain.dto.RushRedDTO;
import com.ruoyi.databarge.domain.dto.ShipInvoiceHistorySearchDTO;
import com.ruoyi.databarge.domain.dto.WebKPDTO;
import com.ruoyi.databarge.domain.vo.ShipInvoiceHistoryVO;
import com.ruoyi.databarge.mapper.ShipInvoiceHistoryMapper;
import com.ruoyi.databarge.service.Pb6WaterwaycargoService;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import com.ruoyi.databarge.service.ShipInvoiceHistoryService;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/8 19:35
 * @Description:
 */
@RestController
@RequestMapping("/ship/invoice")
@AllArgsConstructor
public class InvoiceController {
    @Autowired
    private InvoiceUtil invoiceUtil;

    @Autowired
    private ShipInvoiceHistoryMapper shipInvoiceHistoryMapper;

    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;
    @Autowired
    private WaterwayCargoMapper waterwayCargoMapper;

    @Autowired
    private ShipFddUserRelService shipFddUserRelService;

    private final ShipInvoiceHistoryService shipInvoiceHistoryService;

    @PostMapping("/search/page")
    public AjaxResult searchPage(@RequestBody @Validated ShipInvoiceHistorySearchDTO shipInvoiceHistorySearchDTO){
        DataScope.getDataScope("a", shipInvoiceHistorySearchDTO, Boolean.FALSE);
        IPage<ShipInvoiceHistoryVO> shipInvoiceHistoryIPage1=shipInvoiceHistoryService.searchPage(shipInvoiceHistorySearchDTO);
        shipInvoiceHistoryIPage1.getRecords().forEach(i->{
            if (StringUtils.isEmpty(i.getFphm())){
                InvoiceKpcxVO invoiceKpcxVO=invoiceUtil.kpcxByFpqqlsh(i.getFpqqlsh());
                if(invoiceKpcxVO.getResult().equals("success")){
                i.setFphm(invoiceKpcxVO.getList().get(0).get("c_fphm").toString());
                i.setFpdm(invoiceKpcxVO.getList().get(0).get("c_fpdm").toString());
                shipInvoiceHistoryService.updateById(i);
                }}
            });
        IPage<ShipInvoiceHistoryVO> shipInvoiceHistoryIPage=shipInvoiceHistoryService.searchPage(shipInvoiceHistorySearchDTO);

        return AjaxResult.success(shipInvoiceHistoryIPage);
    }

    @GetMapping("/download")
    public void download(@Validated ShipInvoiceHistorySearchDTO shipInvoiceHistorySearchDTO, HttpServletResponse response) throws Exception{

        if (StringUtils.isBlank(shipInvoiceHistorySearchDTO.getBegindate())){
            shipInvoiceHistorySearchDTO.setBegindate("2021-05-01 00:00:00");
        }
        if (StringUtils.isBlank(shipInvoiceHistorySearchDTO.getEnddate())){
            SimpleDateFormat dateFormat= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date=new Date();
            shipInvoiceHistorySearchDTO.setEnddate(dateFormat.format(date));
        }
        List<ShipInvoiceHistoryVO> shipInvoiceHistoryList=shipInvoiceHistoryMapper.searchDownload(shipInvoiceHistorySearchDTO);
        TemplateExportParams templateExportParams = new TemplateExportParams("物流对接电子单开发票统计表.xls");

        LocalDateTime localDateTime=LocalDateTime.now();
        Map<String, Object> map = new HashMap<>();

        List<Map<String, String>> list = new ArrayList<>();
        long total=0;// 总个数
        BigDecimal totalAllTaxfreeamt=BigDecimal.ZERO;// 总销项金额
        BigDecimal totalAllTax=BigDecimal.ZERO;// 总税额
        BigDecimal totalAllTaxamt=BigDecimal.ZERO;// 总价税合计金额
        DecimalFormat df = new DecimalFormat("#,#00.00#");
        for (ShipInvoiceHistoryVO shipInvoiceHistoryVO:shipInvoiceHistoryList){
            //查询发票代码是否为空 空的需要查询出来
            if(StringUtils.isEmpty(shipInvoiceHistoryVO.getFphm())){
                InvoiceKpcxVO invoiceKpcxVO=invoiceUtil.kpcxByFpqqlsh(shipInvoiceHistoryVO.getFpqqlsh());
                ShipInvoiceHistory shipInvoiceHistory=shipInvoiceHistoryMapper.selectOne(new QueryWrapper<ShipInvoiceHistory>().eq("ID",shipInvoiceHistoryVO.getId()));
                shipInvoiceHistoryVO.setFpdm(invoiceKpcxVO.getList().get(0).get("c_fpdm").toString());
                shipInvoiceHistoryVO.setFphm(invoiceKpcxVO.getList().get(0).get("c_fphm").toString());
                shipInvoiceHistory.setFphm(invoiceKpcxVO.getList().get(0).get("c_fphm").toString());
                shipInvoiceHistory.setFpdm(invoiceKpcxVO.getList().get(0).get("c_fpdm").toString());
                shipInvoiceHistoryService.updateById(shipInvoiceHistory);
            }

            BigDecimal allTaxfreeamt=shipInvoiceHistoryVO.getDetail().stream().map(detail -> new BigDecimal(detail.getTaxfreeamt())).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal allTax=shipInvoiceHistoryVO.getDetail().stream().map(detail -> new BigDecimal(detail.getTax())).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal allTaxamt=shipInvoiceHistoryVO.getDetail().stream().map(detail -> new BigDecimal(detail.getTaxamt())).reduce(BigDecimal.ZERO, BigDecimal::add);

            Map<String, String> temp = new HashMap<>();
            temp.put("index",""+(total+1));
            temp.put("businessSite",shipInvoiceHistoryVO.getComsname());
            temp.put("distributeNum","4号");
            temp.put("waterwaycargoid",shipInvoiceHistoryVO.getWaterwaycargoid());
            temp.put("fphm",shipInvoiceHistoryVO.getFphm());
            temp.put("fpdm",shipInvoiceHistoryVO.getFpdm());
            temp.put("allGoodsname",shipInvoiceHistoryVO.getDetail().stream().map(ShipInvoiceHistory.Detail::getGoodsname).collect(Collectors.joining(",")));
            temp.put("invoicedate",shipInvoiceHistoryVO.getInvoicedate());
            temp.put("allTaxfreeamt",df.format(allTaxfreeamt));
            temp.put("allTax",df.format(allTax));
            temp.put("allTaxamt",df.format(allTaxamt));
            temp.put("clerk",shipInvoiceHistoryVO.getClerk());
            temp.put("buyername",shipInvoiceHistoryVO.getBuyername());
            temp.put("taxnum",shipInvoiceHistoryVO.getTaxnum());

            totalAllTaxfreeamt=totalAllTaxfreeamt.add(allTaxfreeamt);
            totalAllTax=totalAllTax.add(allTax);
            totalAllTaxamt=totalAllTaxamt.add(allTaxamt);
            total++;

            list.add(temp);
        }

        map.put("begindate",shipInvoiceHistorySearchDTO.getBegindate().substring(0,10));
        map.put("enddate",shipInvoiceHistorySearchDTO.getEnddate().substring(0,10));
        map.put("list",list);
        map.put("totalAllTaxfreeamt",df.format(totalAllTaxfreeamt));
        map.put("totalAllTax",df.format(totalAllTax));
        map.put("totalAllTaxamt",df.format(totalAllTaxamt));
        map.put("total",total);
        map.put("date", localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("person",SecurityUtils.getLoginUser().getUser().getNickName());

        Workbook workbook = ExcelExportUtil.exportExcel(templateExportParams, map);
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.close();
    }

    @PostMapping("/updateState")
    public AjaxResult updateState(@RequestBody ShipInvoiceHistory shipInvoiceHistory){
        return AjaxResult.success(shipInvoiceHistoryService.updateById(shipInvoiceHistory));
    }

    @GetMapping("/kpcx")
    public AjaxResult kpcx(@RequestParam(value="fpqqlsh",required=false) String fpqqlsh,@RequestParam(value="orderno",required=false) String orderno){
        InvoiceKpcxVO invoiceKpcxVO=null;
        if(StringUtils.isNotEmpty(fpqqlsh)){
            invoiceKpcxVO=invoiceUtil.kpcxByFpqqlsh(fpqqlsh);
        }else if(StringUtils.isNotEmpty(orderno)){
            invoiceKpcxVO=invoiceUtil.kpcxByOrderno(orderno);
        }else{
            throw new CustomException("fpqqlsh和orderno不能都为空!");
        }
        if(invoiceKpcxVO.getResult().equals("success")){
            ShipInvoiceHistory shipInvoiceHistory=shipInvoiceHistoryMapper.selectOne(new QueryWrapper<ShipInvoiceHistory>().eq("FPQQLSH",fpqqlsh));
            if (StringUtils.isBlank(shipInvoiceHistory.getFphm())){
                shipInvoiceHistory.setFphm(invoiceKpcxVO.getList().get(0).get("c_fphm").toString());
                shipInvoiceHistory.setFpdm(invoiceKpcxVO.getList().get(0).get("c_fpdm").toString());
                shipInvoiceHistoryService.updateById(shipInvoiceHistory);
            }
            return AjaxResult.success(invoiceKpcxVO);
        }else{
            return AjaxResult.success(invoiceKpcxVO);
        }
    }

    @PostMapping("/rushRed")
    public AjaxResult rushRed(@RequestBody RushRedDTO rushRedDTO){
        ShipInvoiceHistory shipInvoiceHistory=shipInvoiceHistoryService.getById(rushRedDTO.getId());
        ShipInvoiceHistory newShipInvoiceHistory=shipInvoiceHistoryService.getById(rushRedDTO.getId());
        newShipInvoiceHistory.setKptype("2");
        newShipInvoiceHistory.setId(null);
        newShipInvoiceHistory.setState("1");
        newShipInvoiceHistory.setInvoicedate(DateUtils.getTime());
        newShipInvoiceHistory.setBlue_fpdm(rushRedDTO.getFpdm());
        newShipInvoiceHistory.setBlue_fphm(rushRedDTO.getFphm());
        List<ShipInvoiceHistory.Detail> detailList=newShipInvoiceHistory.getDetail();
        if (StringUtils.isNotNull(detailList)){
            detailList.forEach(detail -> {
                detail.setNum("-"+detail.getNum());
                detail.setTaxfreeamt(Optional.ofNullable("-"+detail.getTaxfreeamt()).orElse(""));
                detail.setTax(Optional.ofNullable("-"+detail.getTax()).orElse(""));
                detail.setTaxamt(Optional.ofNullable("-"+detail.getTaxamt()).orElse(""));
            });
        }

        InvoiceDTO invoiceDTO = new InvoiceDTO();
        invoiceDTO.setIdentity(InvoiceUtil.IDENTITY);
        InvoiceDTO.Order order = new InvoiceDTO.Order();
        order.setBuyername(newShipInvoiceHistory.getBuyername());
        order.setTaxnum(Optional.ofNullable(newShipInvoiceHistory.getTaxnum()).orElse(""));
        order.setPhone(Optional.ofNullable(newShipInvoiceHistory.getPhone()).orElse(""));
        order.setAddress(Optional.ofNullable(newShipInvoiceHistory.getAddress()).orElse(""));
        order.setAccount(Optional.ofNullable(newShipInvoiceHistory.getAccount()).orElse(""));
        order.setTelephone(Optional.ofNullable(newShipInvoiceHistory.getTelephone()).orElse(""));
        order.setOrderno(getRandomOrderNo());
        order.setBillinfono("");
        order.setClerk(Optional.ofNullable(shipInvoiceHistory.getClerk()).orElse(""));
        order.setSaleaccount(Optional.ofNullable(shipInvoiceHistory.getSaleaccount()).orElse(""));
        order.setSalephone(Optional.ofNullable(shipInvoiceHistory.getSalephone()).orElse(""));
        order.setSaleaddress(Optional.ofNullable(shipInvoiceHistory.getSaleaddress()).orElse(""));
        order.setSaletaxnum(InvoiceUtil.SALETAXNUM);
        order.setKptype(newShipInvoiceHistory.getKptype());// 1正票2红牌
        order.setMessage("对应正数发票代码:"+rushRedDTO.getFpdm()+"号码:"+rushRedDTO.getFphm());// ************为c_fpdm，********为c_fphm
        order.setPayee(Optional.ofNullable(newShipInvoiceHistory.getPayee()).orElse(""));
        order.setChecker(Optional.ofNullable(newShipInvoiceHistory.getChecker()).orElse(""));
        order.setFpdm(rushRedDTO.getFpdm());
        order.setFphm(rushRedDTO.getFphm());
        order.setInvoicedate(DateUtils.getTime());
        order.setTsfs(Optional.ofNullable(newShipInvoiceHistory.getTsfs()).orElse(""));
        order.setEmail(Optional.ofNullable(newShipInvoiceHistory.getEmail()).orElse(""));
        order.setQdbz(Optional.ofNullable(newShipInvoiceHistory.getQdbz()).orElse(""));
        order.setQdxmmc(Optional.ofNullable(newShipInvoiceHistory.getQdxmmc()).orElse(""));
        order.setDkbz(Optional.ofNullable(newShipInvoiceHistory.getDkbz()).orElse(""));
        order.setDeptid(Optional.ofNullable(newShipInvoiceHistory.getDeptid()).orElse(""));
        order.setClerkid(Optional.ofNullable(newShipInvoiceHistory.getClerkid()).orElse(""));
        order.setInvoiceLine(Optional.ofNullable(newShipInvoiceHistory.getInvoiceline()).orElse(""));
        order.setCpybz(Optional.ofNullable(newShipInvoiceHistory.getCpybz()).orElse(""));
        order.setBillinfono(Optional.ofNullable(newShipInvoiceHistory.getBillinfono()).orElse(""));


        List<InvoiceDTO.Detail> details = new ArrayList<>();
        if (StringUtils.isNotNull(detailList)) {
            detailList.forEach(detail -> {
                InvoiceDTO.Detail detailDTO = new InvoiceDTO.Detail();
//                BeanUtils.copyBeanProp(detailDTO, detail);
                detailDTO.setNum(detail.getNum());
                detailDTO.setGoodsname(detail.getGoodsname());
                detailDTO.setPrice(detail.getPrice());
                detailDTO.setHsbz(Optional.ofNullable(detail.getHsbz()).orElse(""));
                detailDTO.setTaxrate(Optional.ofNullable(detail.getTaxrate()).orElse(""));
                detailDTO.setSpec(Optional.ofNullable(detail.getSpec()).orElse(""));
                detailDTO.setUnit(Optional.ofNullable(detail.getUnit()).orElse(""));
                detailDTO.setSpbm(Optional.ofNullable(detail.getSpbm()).orElse(""));
                detailDTO.setZsbm(Optional.ofNullable(detail.getZsbm()).orElse(""));
                detailDTO.setFphxz(Optional.ofNullable(detail.getFphxz()).orElse(""));
                detailDTO.setYhzcbs(Optional.ofNullable(detail.getYhzcbs()).orElse(""));
                detailDTO.setZzstsgl(Optional.ofNullable(detail.getZzstsgl()).orElse(""));
                detailDTO.setLslbs(Optional.ofNullable(detail.getLslbs()).orElse(""));
                detailDTO.setKce(Optional.ofNullable(detail.getKce()).orElse(""));
                detailDTO.setTaxfreeamt(Optional.ofNullable(detail.getTaxfreeamt()).orElse(""));
                detailDTO.setTax(Optional.ofNullable(detail.getTax()).orElse(""));
                detailDTO.setTaxamt(Optional.ofNullable(detail.getTaxamt()).orElse(""));
                details.add(detailDTO);
            });
        }
//        BeanUtils.copyBeanProp(details,shipInvoiceHistory);
        order.setDetail(details);
        invoiceDTO.setOrder(order);
        InvoiceKpVO invoiceKpVO = invoiceUtil.kp(invoiceDTO);
        if (invoiceKpVO.getStatus().equals("0000")){
            newShipInvoiceHistory.setFpqqlsh(invoiceKpVO.getFpqqlsh());
            newShipInvoiceHistory.setFpdm(null);
            newShipInvoiceHistory.setFphm(null);
            newShipInvoiceHistory.setCreate_time(DateUtils.getTime());
            shipInvoiceHistoryService.save(newShipInvoiceHistory);
            shipInvoiceHistory.setState("2");
            shipInvoiceHistoryService.updateById(shipInvoiceHistory);
            return AjaxResult.success(invoiceDTO);
        }else{
             throw new CustomException(invoiceKpVO.getMessage());
        }
    }

    @GetMapping("/searchTaiTou")
    public  AjaxResult searchTaiTou(@RequestParam(value="waterwaycargoid",required=false) String waterwaycargoid){
        List<Map<String,Object>> taitouList=shipInvoiceHistoryMapper.searchTaiTou(waterwaycargoid);
        Pb6Waterwaycargo pb6Waterwaycargo=pb6WaterwaycargoService.getOne(Wrappers.<Pb6Waterwaycargo>lambdaQuery().
                eq(Pb6Waterwaycargo::getWaterwaycargoid,waterwaycargoid));

        //查询
        if (StringUtils.isNotEmpty(pb6Waterwaycargo.getBargeidid().toString())){
            ShipFddUserRel shipFddUserRel =shipFddUserRelService.getOne(Wrappers.<ShipFddUserRel>lambdaQuery().
                    eq(ShipFddUserRel::getType,1)
                    .eq(ShipFddUserRel::getReviewStatus,1)
                    .eq(ShipFddUserRel::getShipId,pb6Waterwaycargo.getBargeidid()));
            if (shipFddUserRel!=null){
                taitouList.get(0).put("bargeowner",shipFddUserRel.getShipCompanyName());
            }else{
                ShipFddUserRel shipFddUserRelWLGS =shipFddUserRelService.getOne(Wrappers.<ShipFddUserRel>lambdaQuery().
                        eq(ShipFddUserRel::getType,4)
                        .eq(ShipFddUserRel::getReviewStatus,1)
                        .eq(ShipFddUserRel::getShipId,pb6Waterwaycargo.getBargeidid()));
                if (shipFddUserRelWLGS!=null){
                    taitouList.get(0).put("bargeowner",shipFddUserRelWLGS.getShipCompanyName());
                }
            }
        }
        return AjaxResult.success(taitouList);
    }

    @GetMapping("/searchShipInvoiceHistoryBybuyername")
    public AjaxResult searchShipInvoiceHistoryBybuyername(@RequestParam(value="buyername",required=false) String buyername,@RequestParam(value="type",required=false) String type){
        return AjaxResult.success(shipInvoiceHistoryMapper.searchShipInvoiceHistoryBybuyername(buyername,type));
    }
    @PostMapping("/kp")
    public AjaxResult kp(@RequestBody WebKPDTO webKPDTO ){
        InvoiceBO invoiceBO=new InvoiceBO();
        invoiceBO.setKptype("1");//正票
        invoiceBO.setInvoicetype(webKPDTO.getInvoicetype());
        invoiceBO.setWaterwayCargoId(webKPDTO.getWaterwaycargoid());
        invoiceBO.setType(webKPDTO.getType());
        invoiceBO.setBuyername(webKPDTO.getBuyername());
        invoiceBO.setPhone(webKPDTO.getPhone());
        invoiceBO.setEmail(webKPDTO.getEmail());
        invoiceBO.setTaxnum(webKPDTO.getTaxnum());
        invoiceBO.setAddress(webKPDTO.getAddress());
        invoiceBO.setAccount(webKPDTO.getAccount());
        invoiceBO.setClerk(webKPDTO.getClerk());
        invoiceBO.setPayee(webKPDTO.getPayee());
        invoiceBO.setChecker(webKPDTO.getChecker());
        invoiceBO.setMessage(webKPDTO.getMessage());
        InvoiceBO.Detail detail=new InvoiceBO.Detail();
        invoiceBO.setDetail(Arrays.asList(detail));
        return AjaxResult.success(invoiceUtil.invoicekp(invoiceBO));
    }

    //查询待开票发票的信息
    @PostMapping("/DKP")
    public AjaxResult DKP(@RequestBody String waterwaycargoid){
        WaterwayCargo waterwayCargo = waterwayCargoMapper.selectOne(new QueryWrapper<WaterwayCargo>()
                .eq("WATERWAYCARGOID", waterwaycargoid));
        ShipInvoiceHistory shipInvoiceHistory=shipInvoiceHistoryService.getOne(Wrappers.<ShipInvoiceHistory>lambdaQuery()
                .eq(ShipInvoiceHistory::getPb6WaterwaycargoId,waterwayCargo.getID())
                .eq(ShipInvoiceHistory::getState,3));
        if (shipInvoiceHistory!=null){
            return AjaxResult.success(shipInvoiceHistory);
        }else{
            return AjaxResult.success("无待开票");
        }
    }
    @PostMapping("/DKPautomatic")
    public AjaxResult DKPautomatic(@RequestBody String waterwaycargoid){
        WaterwayCargo waterwayCargo = waterwayCargoMapper.selectOne(new QueryWrapper<WaterwayCargo>()
                .eq("WATERWAYCARGOID", waterwaycargoid));
        ShipInvoiceHistory shipInvoiceHistory=shipInvoiceHistoryService.getOne(Wrappers.<ShipInvoiceHistory>lambdaQuery()
                .eq(ShipInvoiceHistory::getPb6WaterwaycargoId,waterwayCargo.getID())
                .eq(ShipInvoiceHistory::getState,3));
        return  AjaxResult.success("还没有写完");
    }

    public static String getRandomOrderNo(){
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))+new Random().nextInt(1000000);
    }
}

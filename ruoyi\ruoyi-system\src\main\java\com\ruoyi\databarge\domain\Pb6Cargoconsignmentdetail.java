package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 驳船托运单明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PB6_CARGOCONSIGNMENTDETAIL")
@KeySequence("seq_pb6_cargoconsignmentdetail")
public class Pb6Cargoconsignmentdetail extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 船名
     */
    @TableField("BARGENAME")
    private String bargename;

    /**
     * 托号
     */
    @TableField("CONSIGNFLAG")
    private String consignflag;

    /**
     * 托号Id
     */
    @TableField("CONSIGNID")
    private Long consignid;

    /**
     * 托运申请件数
     */
    @TableField("RATIONPIECE")
    private String rationpiece;

    /**
     * 托运单重量
     */
    @TableField("RATIONWEIGHT")
    private String rationweight;

    /**
     * 退单申请流水号
     */
    @TableField("CHARGEBACK")
    private String chargeback;

    /**
     * 最终核销意见
     */
    @TableField("ISVERIFICATE")
    private String isverificate;

    /**
     * 长航审批意见
     */
    @TableField("ISVERIFICATECH")
    private String isverificatech;

    @TableField("ISVERIFICATEMARITIME")
    private String isverificatemaritime;

    /**
     * 长航审批人
     */
    @TableField("VERIFICATEMAN")
    private String verificateman;

    @TableField("VERIFICATEMANMARI")
    private String verificatemanmari;

    @TableField("VERIFICATEREASON")
    private String verificatereason;

    @TableField("VERIFICATEREASONMARI")
    private String verificatereasonmari;

    /**
     * 驳船标识
     */
    @TableField("VERIFICATETIME")
    private String verificatetime;

    @TableField("VERIFICATETIMEMARI")
    private String verificatetimemari;

    @TableField("ISFINISHED")
    private String isfinished;

    /**
     * 办理方式(1为网页端,2为app端)
     */
    @TableField("ONINERESOURSE")
    private String onineresourse;

    /**
     * 驳船状态，0：未审核 1：已审核 2：审核不通过 3：已配载 4：已报到 5：已离港 6:已退单 7:已改单
     */
    @TableField("FLAGBARGESTATE")
    private String flagbargestate;

    /**
     * 水路货物运单编号
     */
    @TableField("WATERWAYCARGOID")
    private String waterwaycargoid;

    /**
     * 托运单审核意见
     */
    @TableField("CARGOCONSIGNCHECKREASON")
    private String cargoconsigncheckreason;

    /**
     * 驳船联系电话
     */
    @TableField("BARGETEL")
    private String bargetel;

    /**
     * 营业厅申请改单/退单（0为申请退单中，1为申请退单通过，2为申请退单失败,3为申请改单中，4为申请改单成功，5为申请改单失败
     */
    @TableField("APPLYMODIFY")
    private String applymodify;

    /**
     * 确认月结客户
     */
    @TableField("SHIPOWNER")
    private String shipowner;

    /**
     * 费用结算方式
     */
    @TableField("CHARGEBALANCETYPE")
    private String chargebalancetype;

    /**
     * 承运船舶公司
     */
    @TableField("SHIPPINGCONAME")
    private String shippingconame;

    /**
     * 是否确认（0为申请中，Y为已确认，N为驳回）
     */
    @TableField("ISCONFIRM")
    private String isconfirm;

    /**
     * 驳船MMSI
     */
    @TableField("MMSI")
    private String mmsi;

    /**
     * 顺序号
     */
    @TableField("SERIALNUMBER")
    private String serialnumber;

    /**
     * 申请改单/申请退单原因
     */
    @TableField("MODIFYREASON")
    private String modifyreason;

    /**
     * 派船联系方式
     */
    @TableField("CUSTOMERUSERID")
    private String customeruserid;

    /**
     * 申请时间（精确到时分秒）
     */
    @TableField("APPLYTIME")
    private String applytime;

    /**
     * 是否加急（是为Y）
     */
    @TableField("ISHARRY")
    private String isharry;

    /**
     * 托运单实际装货量
     */
    @TableField("WORKWEIGHT")
    private String workweight;

    /**
     * 小程序操作状态(0:待驳船主确认 1:待支付  2:待船公司审批(月结)，3船公司审批不通过(月结) 4:待驳船主预约 5:驳船主已预约 6:取消预约 7:待审核(退单改单) 8:审核通过(退单改单) 9:审核不通过(退单改单))
     */
    @TableField("WXOPERATESTATE")
    private Integer wxoperatestate;

    /**
     * 是否托运单办理人审核(差额退款申请审核专用) 0：待审核，1：审核通过，2：审核不通过
     */
    @TableField("ISWXCHECK")
    private Integer iswxcheck;

    /**
     * 物流公司差额退款审核（0：待审核，1：审核通过，2：审核不通过）
     */
    @TableField("WXCONFIRMCHECK")
    private Integer wxconfirmcheck;

    /**
     * 退改单审核不通过原因
     */
    @TableField("FAILUREREASONS")
    private String failurereasons;

    /**
     * 退改单当前流程节点： 0物流公司直接审核，1码头审核， 2物流公司间接审核
     */
    @TableField("WXNODE")
    private String wxnode;

    /**
     * 驳船预约时间
     */
    @TableField("WXAPPOINTMENTTIME")
    private String wxappointmenttime;

    /**
     * 预约人
     */
    @TableField("WXOINTMENTMID")
    private Long wxointmentmid;

    /**
     * 月结算审核单位(船公司id)
     */
    @TableField("WXMONTHCHAGEBYID")
    private Long wxmonthchagebyid;

    /**
     * 月结算审核单位(船公司名称)
     */
    @TableField("WXMONTHCHAGEBYNAME")
    private String wxmonthchagebyname;

    /**
     * 托运单联系人电话
     */
    @TableField("WXRATIONCONTACTNUMBER")
    private String wxrationcontactnumber;

    /**
     * 创建人
     */
    @TableField("WXCREATEBYID")
    private Long wxcreatebyid;

    /**
     * 创建时间
     */
    @TableField("WXCREATETIME")
    private String wxcreatetime;

    /**
     * 修改人
     */
    @TableField("WXUPDATEBYID")
    private Long wxupdatebyid;

    /**
     * 修改时间
     */
    @TableField("WXUPDATETIME")
    private String wxupdatetime;

    /**
     * 驳船主键id
     */
    @TableField("BARGEID")
    private Long bargeid;

    /**
     * 预约操作时间
     */
    @TableField("WXOPERATETIME")
    private String wxoperatetime;

    /**
     * 退改单当前流程节点审核状态： 0未审核，1审核不通过
     */
    @TableField("AUDITSTATES")
    private Integer auditstates;

    /**
     * 生成的新的货物托运单明细表ID
     */
    @TableField("NEWDETAILID")
    private Long newdetailid;

    /**
     * 支付人
     */
    @TableField("PAYUSERID")
    private Long payUserId;

    /**
     * 新托运单标志(0：正常办理，1：新生成的托运单)
     */
    @TableField("NEWWAYBILL")
    private String newWaybill;

    /**
     * 审核托运单的人
     */
    @TableField("CHECKMAN")
    private String checkman;

    /**
     * 审核托运单的人
     */
    @TableField("CHECKTIME")
    private String checktime;

    /**
     * 线上线下
     */
    @TableField("ISOFFLINE")
    private String isoffline;


    @TableField(exist = false)
    private String chargetype; //费用结算方式，来源 pb6_watercargo chargebalancetype

    @TableField(exist = false)
    private Integer state; //发票状态，来源 SHIP_INVOICE_HISTORY state

    @TableField(exist = false)
    private String auditstatus;

    @TableField(exist = false)
    private String contactphone; //驳船的联系方式

    //取消预约标志
    @TableField("CANCELRESERVATIONREASON")
    private String cancelreservationreason;

    //取消预约时间
    @TableField("CANCELTIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp canceltime;

    // excel导入字段
    // 航次 作业区域A级orB级 货名 包装方式
    @TableField("VOYAGE")
    private String voyage;

    @TableField("WORK_AREA")
    private String workArea;

    @TableField(exist = false)
    private String cargoName;

    @TableField(exist = false)
    private String packingMethod;

    // 起运港
    @TableField(exist = false)
    private String startPort;

    // 目的港
    @TableField(exist = false)
    private String destinationPort;

    // 新增字段
    /**
     * 船头吃水
     */
    @TableField("DRAFT_FWD")
    private String draftFwd;

    /**
     * 船中吃水
     */
    @TableField("DRAFT_MID")
    private String draftMid;

    /**
     * 船尾吃水
     */
    @TableField("DRAFT_AFT")
    private String draftAft;

    // @TableField("REMARK")
    // private String remark;

    @TableField("special_notice")
    private String specialNotice;

    // 运单生成失败原因
    @TableField("WATER_WAY_REMARK")
    private String waterWayRemark;

    @TableLogic
    private String delFlag;

    // 驳船名和航次的拼接，用于复载判断
    @TableField(exist = false)
    private String bargeVoyage;

    // 大船id
    @TableField(exist = false)
    private Long shipInfoId;

    // 指导员确认完工时间
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    // 提单号
    private String billNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date wxConfirmTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date wxGenerateTime;

}

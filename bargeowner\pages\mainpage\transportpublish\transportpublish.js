// pages/mainpage/transportpublish/transportpublish.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    form:{
      bargeName: '', // 驳船名称
      bargeIdentifier:"",// 驳船识别号
      phone:"",//联系电话
      capacity:"",//承载吨数
      remark:"",//备注
      // startTime:"",//开始时间
      // endTime:"",//结束时间
    },
    startDate:"",//开始日期
    startTime:"",//开始时间
    endDate:"",//结束日期
    endTime:"",//结束时间
  },

  /**
   * 
   *方法
   */
  //开始日期change
  startDateChange(e){
    this.setData({
      startDate: e.detail.value
    })
  },
  //开始时间change
  startTimeChange(e){
    this.setData({
      startTime: e.detail.value + ":00"
    })
  },
  //结束日期
  endDateChange(e){
    this.setData({
      endDate: e.detail.value
    })
  },
  //结束时间
  endTimeChange(e){
    this.setData({
      endTime: e.detail.value + ":00"
    })
  },
  //发布
  publishHanlde(){
    let beginDateTime = this.data.startDate + " " + this.data.startTime // 开始日期时间
    let endDateTime = this.data.endDate + " " + this.data.endTime // 结束日期时间
    let param = {
      ...this.data.form,
      beginTime:beginDateTime,// 开始日期时间
      endTime:endDateTime, // 结束日期时间

    }
    // param.beginTime = param.startTime
    // delete param.startTime

    if(!param.bargeName){
      app.$message("请输入驳船名称")
      return
    }
    if(!param.bargeIdentifier){
      app.$message("请输入船舶识别号")
      return
    }
    if(!param.phone){
      app.$message("请输入联系电话")
      return
    }
    //手机校验
    let phoneReg = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/
    if(param.phone && !phoneReg.test(param.phone)){
      app.$message("联系电话输入格式不正确")
      return;
    }
    if(!param.capacity){
      app.$message("请输入承载吨数")
      return
    }
    // if(!param.beginTime){
    //   app.$message("请选择开始时间")
    //   return
    // }
    // if(!param.endTime){
    //   app.$message("请选择结束时间")
    //   return
    // }
    if(!this.data.startDate){
      app.$message("请选择开始日期")
      return
    }
    if(!this.data.startTime){
      app.$message("请选择开始时间")
      return
    }
    if(!this.data.endDate){
      app.$message("请选择结束日期")
      return
    }
    if(!this.data.endTime){
      app.$message("请选择结束时间")
      return
    }
    //时间撮比较日期大小
    let endTime = new Date(endDateTime).getTime()// 结束日期时间
    let startTime =  new Date(beginDateTime).getTime()// 开始日期时间
    if(startTime && endTime && endTime < startTime){
      app.$message("结束时间需大于或等于开始时间")
      return
    }


    // if(!param.remark){
    //   app.$message("请输入备注")
    //   return
    // }
    app.$post(app.$url.main.addCapacity,param).then(res=>{
      if(res.code == 200){
        console.log("param",param)
        app.$message(res.msg)
        wx.navigateBack({
          delta: 1
        });
      }else {
      app.$message(res.msg)
      }
    })
   
  },

  //驳船名称input
  bargeNameInput(e){
    this.setData({
      "form.bargeName":e.detail.value
    })
  },
  //船舶识别号input
  bargeIdentifierInput(e){
    this.setData({
      "form.bargeIdentifier":e.detail.value
    })
  },
  //联系电话input
  phoneInput(e){
    this.setData({
      "form.phone":e.detail.value
    })
  },
  //承载吨数input
  capacityInput(e){
    this.setData({
      "form.capacity":e.detail.value
    })
  },

  // 开始时间回调
  // handleBeginDateChange(e) {
  //   //时间撮比较日期大小
  //   let startTime = new Date(e.detail.value).getTime() //开始时间
  //   let endTime =  new Date(this.data.form.endTime).getTime()//结束时间
  //   if(startTime && endTime && startTime > endTime){
  //     app.$message("开始时间需小于或等于截止时间")
  //     this.setData({
  //       'form.startTime':""
  //     })
  //     return
  //   }
  //   this.setData({
  //     'form.startTime': e.detail.value
  //   })
  // },

  // 结束时间回调
  // handleEndDateChange(e) {
    //时间撮比较日期大小
  //   let endTime = new Date(e.detail.value).getTime()//结束时间
  //   let startTime =  new Date(this.data.form.startTime).getTime()//开始时间
  //   if(startTime && endTime && endTime < startTime){
  //     app.$message("结束时间需大于或等于开始时间")
  //     this.setData({
  //       'form.endTime':""
  //     })
  //     return
  //   }
  //   this.setData({
  //     'form.endTime': e.detail.value
  //   })
  // },
  
  //备注input
  remarkInput(e){
    this.setData({
      "form.remark":e.detail.value
    })
  },
  
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
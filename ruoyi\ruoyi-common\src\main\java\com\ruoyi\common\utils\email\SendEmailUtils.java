package com.ruoyi.common.utils.email;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ftp.FtpUtils;
import com.ruoyi.common.utils.uuid.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.io.*;
import java.util.*;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/13 11:51
 */
@Slf4j
@Component
public class SendEmailUtils {

	@Autowired
	private JavaMailSender mailSender;
	@Value("${spring.mail.username}")
	private String username;
	@Autowired
	private FtpUtils ftpUtils;
	@Value("${ftp.ftpBasePath}")
	private String basePath;

	/**
	 * 发送邮件
	 * @param toEmail 收件人
	 * @param subject 主题
	 * @param content 内容
	 * @return
	 */
	public AjaxResult sendSimpleMail(String toEmail, String subject, String content, HashMap<String, byte[]> map) {

		SysUser loginUser = SecurityUtils.getLoginUser().getUser();

		/**
		 * 发送email之前检验email是否真是存在
		 */
		String regex = "^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$";
		if(toEmail.matches(regex)){

			MimeMessage mimeMessage = mailSender.createMimeMessage();
			MimeMessageHelper helper;
			try {
				// 设置utf-8或GBK编码，否则邮件会有乱码，true表示为multipart邮件
				helper = new MimeMessageHelper(mimeMessage,true, "UTF-8");

				// 设置发送人邮件地址
				helper.setFrom(username);

				// 邮件接收地址
				helper.setTo(toEmail);

				// 设置抄送 邮箱地址
				// helper.setBcc(new InternetAddress());
				// 设置发送邮件的标题
				helper.setSubject(subject);

				// 设置邮件内容，参数true表示启用html格式
				helper.setText(content, true);

				Set<HashMap.Entry<String,byte[]>> set=map.entrySet();
				for(HashMap.Entry<String,byte[]> item : set){
					String fileName = item.getKey();
					byte[] bytes = item.getValue();

					// 输出文件
					File file = File.createTempFile(UUID.randomUUID().toString(), fileName.substring(fileName.indexOf(".")));
					file.deleteOnExit();

					OutputStream os = new FileOutputStream(file);
					InputStream is = new ByteArrayInputStream(bytes);
					int bytesRead = 0;
					byte[] buffer = new byte[8192];
					while ((bytesRead = is.read(buffer, 0, 8192)) != -1) {
						os.write(buffer, 0, bytesRead);
					}

					// 第一个参数附件名，第二个参数附件
					helper.addAttachment(MimeUtility.encodeWord(fileName, "UTF-8", "B"), new FileSystemResource(file));
					os.close();
					is.close();
				}

				log.warn("发送邮件：" + subject + "至" + toEmail);

				mailSender.send(mimeMessage);
			} catch (Exception e) {
				log.warn("邮件发送失败");
				e.printStackTrace();
				return AjaxResult.error("邮件发送失败");
			}
		}else {
			log.warn("邮箱格式不正确");
			return AjaxResult.error("邮箱格式不正确");
		}
		return AjaxResult.success("邮件发送成功");
	}
}

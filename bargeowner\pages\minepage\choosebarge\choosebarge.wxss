/* pages/minepage/choosebarge/choosebarge.wxss */

.choose-page{
  padding-top: 100rpx;
}
.search-warp{
  position: fixed;
  top: 0rpx;
  left: 0rpx;
  right: 0rpx;
  height: 100rpx;
  background: #ffffff;
  z-index: 100;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #ddd;
  padding: 0 20rpx;
}
.search-warp .add-btn{
  height: 60rpx;
  width: 180rpx;
  margin-left: 20rpx;
  line-height: 60rpx;
  padding: 0rpx;
  /* background: #00426B; */
  background: rgb(6, 143, 13);
  color: #ffffff;
  font-size: 28rpx;
  font-weight: normal;
}
.search-area{
  height: 70rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 710rpx;
}
.search-ipt{
  height: 70rpx;
  line-height: 70rpx;
  flex: 1;
  width: 100%;
  padding: 0 20rpx;
}
.search-icon{
  flex-shrink: 0;
}
.barge-list{
  padding: 20rpx 20rpx 160rpx 20rpx;
}
.barge-item{
  width: 100%;
  height: 100rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px solid #ddd;
  padding: 0 20rpx;
}
.search-bottom{
  position: fixed;
  left: 0rpx;
  bottom: 0rpx;
  right: 0rpx;
  height: 150rpx;
  background: #ffffff;
  z-index: 100;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  border-top: 1px solid #ddd;
}
.search-bottom .confirm-btn{
  width: 80%;
  height: 90rpx;
  padding: 0rpx;
  margin: 0rpx;
  border-radius: 10rpx;
  border: none;
  line-height: 90rpx;
  color: #fff;
  /* background: rgb(0, 66, 107); */
  background: rgb(6, 143, 13);
  font-size: 30rpx;
  font-weight: normal;
}
.barge-name{
  padding-left: 0rpx;
}
.main-icon{
  color: rgb(0, 66, 107);
  font-size: 60rpx;
  padding: 0 20rpx;
}
.close-icon{
  flex-shrink: 0;
  width: 40rpx;
  text-align: center;
  height: 70rpx;
  line-height: 70rpx;
  color: #ddd;
  margin-right: 10rpx;
}
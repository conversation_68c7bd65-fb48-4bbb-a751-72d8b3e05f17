package com.ruoyi.barge.domain.bo;

import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.domain.UploadAddress;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/29 21:51
 */
@Data
public class BargeUserBO extends BaseEntity {

    /**
     * 用户id
     */
    private Long userId;


    /**
     * 用户类型
     */
    private String UserType;

    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空")
    private String nickName;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phonenumber;

    /**
     * 微信号
     */
    private String weChatNumber;

    /**
     * 邮箱
     */
    @Email
    private String email;

    private Long bargeId;

    private List<SysUserBarge> userBargeList;

    /**
     * 身份证号码
     */
    private String identityId;

    /**
     * 身份证正面 bse64图片
     */
    private String identityPositive;

    /**
     * 身份证反面 bse64图片
     */
    private String identityNegative;

    private List<UploadAddress> dataList;
}

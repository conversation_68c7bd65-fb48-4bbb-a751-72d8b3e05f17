package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.mapper.ShipFddUserRelMapper;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020/10/24 20:09
 */
@Service
public class ShipFddUserRelServiceImpl extends ServiceImpl<ShipFddUserRelMapper, ShipFddUserRel> implements ShipFddUserRelService {
    @Override
    public List<ShipFddUserRel> listByShipId(Long shipId) {
        return super.baseMapper.listByShipId(shipId);
    }

    @Override
    public List<String> getSealUrlByShipId(Long shipId) {
        return super.baseMapper.getSealUrlByShipId(shipId);
    }

    @Override
    public String getFddAccountIdByShipId(Long shipId) {
        return super.baseMapper.getFddAccountIdByShipId(shipId);
    }
}

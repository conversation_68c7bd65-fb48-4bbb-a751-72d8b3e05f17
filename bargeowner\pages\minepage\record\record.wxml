<!--pages/minepage/record/record.wxml-->
<view class="record-page" hover-class="none" hover-stop-propagation="false">
  <view class="record-head" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType == 14}}" id="record-head">
    <view class="{{activeTab == 1? 'record-head-item head-left active-tab': 'record-head-item head-left'}}" data-tabid="1" bindtap="handleChangeTab" hover-class="none" hover-stop-propagation="false">个人备案</view>
    <view class="{{activeTab == 2? 'record-head-item  head-left active-tab': 'record-head-item  head-left'}}" hover-class="none" data-tabid="2" bindtap="handleChangeTab" hover-stop-propagation="false">驳船备案</view>
    <view class="{{activeTab == 3? 'record-head-item active-tab': 'record-head-item'}}" hover-class="none" data-tabid="3" bindtap="handleChangeTab" hover-stop-propagation="false">电子签章</view>
  </view>
   <!-- 1 个人备案 -->
  <view class="record-content" wx:if="{{activeTab == 1}}" hover-class="none" hover-stop-propagation="false" style="{{userInfos.userType ==14 ? 'margin-top:100rpx':'margin-top:0rpx'}}">
    <view class="person-record" hover-class="none" hover-stop-propagation="false">
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>姓名：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入姓名" value="{{personForm.nickName}}" bindinput="nickNameInput"/>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star"></text>手机号：</view>
        <input type="number" class="add-message-ipt" placeholder="请输入手机号" value="{{personForm.phonenumber}}" bindinput="phonenumberInput" maxlength="11" disabled="{{true}}"/>
      </view>
      <!-- <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false">账号：</view>
        <input type="text" class="add-message-ipt"  placeholder="请输入账号" value="{{personForm.userName}}"  bindinput="userNameInput"/>
      </view> -->
      <!-- <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false">微信号：</view>
        <input type="text" class="add-message-ipt"  placeholder="请输入微信号" value="{{personForm.wxAccount}}"  bindinput="wxAccountInput"/>
      </view> -->
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star"></text>邮箱：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入邮箱" value="{{personForm.email}}"  bindinput="emailInput"/>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>身份证号码：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入身份证号码" value="{{personForm.identityId}}"  bindinput="identityIdInput"/>
      </view>

      <view class="add-message-item" hover-class="none" hover-stop-propagation="false" style="margin-top:20rpx;">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>驳船主身份证正面：</view>
        <view class="barge-value" hover-class="none" hover-stop-propagation="false">
          <view  bindtap="chooseBargeImg" data-datatype="51" class="upload-btn">上传</view>
          （<text class="red">{{IdentityPositiveImgArr.length}}</text>）
        </view>
        <view class="{{showIdentityPositiveImg ? 'iconfont icon-xiangxiajiantou detail-icon' : 'iconfont iconxiangshangjiantou1 detail-icon' }}" hover-class="none" hover-stop-propagation="false" data-datatype="51" catchtap="foldImg"></view> 
      </view>
      <!-- 驳船主身份证正面-照片列表 -->
      <view class="img-wrap" hover-class="none" hover-stop-propagation="false" wx:if="{{showIdentityPositiveImg && IdentityPositiveImgArr.length>=1}}">
        <view class="img-item" hover-class="none" hover-stop-propagation="false" wx:for="{{IdentityPositiveImgArr}}" wx:key="index">
          <image class="" src="{{item.url}}" mode="aspectFill" style="width:200rpx;height:200rpx;" bindtap="showBigImg"  data-arr="{{IdentityPositiveImgArr}}" data-currUrl="{{url}}" lazy-load="false" binderror="" bindload="">
          </image>
          <mp-icon icon="close2" color="#333" size="{{22}}" class="close-icon1" catchtap="deleteImg" data-datatype="51" data-item="{{item}}" data-index="{{index}}"></mp-icon>
        </view>
      </view>
     
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false" style="margin-top:20rpx;">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>驳船主身份证反面：</view>
        <view class="barge-value" hover-class="none" hover-stop-propagation="false">
          <view  bindtap="chooseBargeImg" data-datatype="52" class="upload-btn">上传</view>
          （<text class="red">{{IdentityNegativeImgArr.length}}</text>）
        </view>
        <view class="{{showIdentityNegativeImg ? 'iconfont icon-xiangxiajiantou detail-icon' : 'iconfont iconxiangshangjiantou1 detail-icon' }}" hover-class="none" hover-stop-propagation="false" data-datatype="52" catchtap="foldImg"></view> 
      </view>
      <!-- 驳船主身份证反面-照片列表 -->
      <view class="img-wrap" hover-class="none" hover-stop-propagation="false" wx:if="{{showIdentityNegativeImg && IdentityNegativeImgArr.length>=1}}">
        <view class="img-item" hover-class="none" hover-stop-propagation="false" wx:for="{{IdentityNegativeImgArr}}" wx:key="index">
          <image class="" src="{{item.url}}" mode="aspectFill" style="width:200rpx;height:200rpx;" bindtap="showBigImg"  data-arr="{{IdentityNegativeImgArr}}" data-currUrl="{{url}}" lazy-load="false" binderror="" bindload="">
          </image>
          <mp-icon icon="close2" color="#333" size="{{22}}" class="close-icon1" catchtap="deleteImg" data-datatype="52" data-item="{{item}}" data-index="{{index}}"></mp-icon>
        </view>
      </view>
      
    </view>

    <view class="person-operate" hover-class="none" hover-stop-propagation="false">
      <button class="person-btn" bindtap="personSave">保存</button>
    </view>
  </view>
   <!-- 2 驳船备案 -->
  <view class="record-content" wx:if="{{activeTab == 2}}" hover-class="none" hover-stop-propagation="false" style="{{userInfos.userType ==14 ? 'margin-top:100rpx':'margin-top:0rpx'}}">
    <view class="person-record" hover-class="none" hover-stop-propagation="false">
      <!-- 跳转到驳船页面 -->
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false"  wx:if="{{userInfos.userType == 15 }}">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>驳船名称：</view>
        <view  class="{{bargeParam.bargeName ?'barge-value1':'barge-value'}}" hover-class="none" hover-stop-propagation="false">{{bargeParam.bargeName}}</view>
      </view>

      <view class="add-message-item" hover-class="none" hover-stop-propagation="false"  bindtap="handleChooseBarge" wx:if="{{userInfos.userType == 14 }}">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>驳船名称：</view>
        <view  class="{{bargeParam.bargeName ?'barge-value1':'barge-value'}}" hover-class="none" hover-stop-propagation="false" >{{bargeParam.bargeName ? bargeParam.bargeName :"请选择驳船"}}</view>
        <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{18}}"></mp-icon>
      </view>

      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>船舶识别号：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入船舶识别号" value="{{bargeParam.bargeId}}" bindinput="bargeIdInput" wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.bargeId}}
        </view>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>MMSI（AIS）标识：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入MMSI（AIS）标识" value="{{bargeParam.mmsi}}" bindinput="mmsiInput" wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.mmsi}}
        </view>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>驳船净吨：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入驳船净吨" value="{{bargeParam.bargeWeight}}" bindinput="bargeWeightInput" wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.bargeWeight}}
        </view>
      </view>

      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false">最小散货密度(容量)：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入最小散货密度(容量)" value="{{bargeParam.loadingWeightMin}}" bindinput="loadingWeightMinInput" wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.loadingWeightMin}}
        </view>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false">最大散货密度(容量)：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入最大散货密度(容量)" value="{{bargeParam.loadingWeightMax}}" bindinput="loadingWeightMaxInput" wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.loadingWeightMax}}
        </view>
      </view>
      <view class="cargo-tip">
        <mp-icon icon="info" color="#ff0000" size="{{18}}"></mp-icon>
        <text class="tip-text">禁装货物是该船适航证不允许装载的货物类型，没有则不填</text>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false">禁装货物：</view>
        <!-- <input type="text" class="add-message-ipt" placeholder="请输入禁装货物" value="{{bargeParam.cargoNotAllowed}}" bindinput="cargoNotAllowedInput"  wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.cargoNotAllowed}}
        </view> -->
        <!-- <picker mode="selector" range="{{cargoNotAllowedOptions}}" value="{{cargoNotAllowedIndex}}" bindchange="cargoNotAllowedChange">
          <view class="add-message-ipt">{{cargoNotAllowedOptions[cargoNotAllowedIndex]}}</view>
        </picker> -->
        <checkbox-group bindchange="cargoNotAllowedChange">
          <block wx:for="{{cargoNotAllowedOptions}}" wx:key="index">
            <label>
              <checkbox value="{{item}}" checked="{{selectedValues[item]}}"/>
              {{item}}
            </label>
          </block>
        </checkbox-group>
      </view>

       <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>船舶载货量（A级）：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入船舶载货量（A级）" value="{{bargeParam.bargeLoadA}}" bindinput="bargeLoadAInput" wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.bargeLoadA}}
        </view>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>船舶载货量（B级）：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入船舶载货量（B级）" value="{{bargeParam.bargeLoadB}}" bindinput="bargeLoadBInput" wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.bargeLoadB}}
        </view>
      </view>

      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>适航证书有效期：</view>
        <picker class="add-message-ipt" mode="date" value="{{bargeParam.validSailDate}}" bindchange="validSailDateInput" wx:if="{{userInfos.userType == 14}}">
          <view class="{{bargeParam.validSailDate? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{bargeParam.validSailDate ? bargeParam.validSailDate :'请选择证书有效期'}}</view>
        </picker>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.validSailDate}}
        </view>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>船籍港：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入船籍港" value="{{bargeParam.belongArea}}" bindinput="belongAreaInput"  wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.belongArea}}
        </view>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>驳船所有人：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入驳船所有人" value="{{bargeParam.bargeOwner}}" bindinput="bargeOwnerInput" wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.bargeOwner}}
        </view>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>联系电话：</view>
        <input type="number" class="add-message-ipt" placeholder="请输入联系电话" value="{{bargeParam.contactPhone}}" bindinput="contactPhoneInput" maxlength="11" wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.contactPhone}}
        </view>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>驳船经营人：</view>
        <input type="text" class="add-message-ipt" placeholder="请输入驳船经营人" value="{{bargeParam.bargeOperator}}" bindinput="bargeOperatorInput" wx:if="{{userInfos.userType == 14}}"/>
        <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false" wx:if="{{userInfos.userType != 14}}">
          {{bargeParam.bargeOperator}}
        </view>
      </view>
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>船舶检验证书簿 <text>\n</text>（船舶总吨净吨页）：</view>
        <view class="barge-value" hover-class="none" hover-stop-propagation="false">
          <view  bindtap="chooseBargeImg" data-datatype="11" class="upload-btn" wx:if="{{userInfos.userType == 14}}">上传</view>
          （<text class="red">{{coverBargeImgArr.length}}</text>）
        </view>
        <view class="{{showCoverImg ? 'iconfont icon-xiangxiajiantou detail-icon' : 'iconfont iconxiangshangjiantou1 detail-icon' }}" hover-class="none" hover-stop-propagation="false" data-datatype="11" catchtap="foldImg"></view> 
      </view>
      <!--船舶检验证书簿封面-照片列表-->
      <view class="img-wrap" hover-class="none" hover-stop-propagation="false" wx:if="{{showCoverImg && coverBargeImgArr.length>=1}}">
        <view class="img-item" hover-class="none" hover-stop-propagation="false" wx:for="{{coverBargeImgArr}}" wx:key="index">
          <image class="" src="{{item.url}}" mode="aspectFill" style="width:200rpx;height:200rpx;" bindtap="showBigImg" data-arr="{{coverBargeImgArr}}" data-currUrl="{{url}}" lazy-load="false" binderror="" bindload="">
          </image>
          <mp-icon icon="close2" color="#333" size="{{22}}" class="close-icon1" catchtap="deleteImg" data-datatype="11" data-item="{{item}}" data-index="{{index}}"></mp-icon>
        </view>
      </view>
    
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>船舶主要项目内容：</view>
        <view class="barge-value" hover-class="none" hover-stop-propagation="false">
          <view  bindtap="chooseBargeImg" data-datatype="12" class="upload-btn" wx:if="{{userInfos.userType == 14}}">上传</view>
          （<text class="red">{{bargeMainItemImgArr.length}}</text>）
        </view>
        <view class="{{showMainItemImg ? 'iconfont icon-xiangxiajiantou detail-icon' : 'iconfont iconxiangshangjiantou1 detail-icon' }}" hover-class="none" hover-stop-propagation="false" data-datatype="12" catchtap="foldImg"></view> 
      </view> 
      <!--船舶主要项目内容-照片列表-->
      <view class="img-wrap" hover-class="none" hover-stop-propagation="false" wx:if="{{showMainItemImg && bargeMainItemImgArr.length>=1}}">
        <view class="img-item" hover-class="none" hover-stop-propagation="false" wx:for="{{bargeMainItemImgArr}}" wx:key="index">
          <image class="" src="{{item.url}}" mode="aspectFill" style="width:200rpx;height:200rpx;" bindtap="showBigImg" data-arr="{{bargeMainItemImgArr}}" data-currUrl="{{url}}" lazy-load="false" binderror="" bindload="">
          </image>
          <mp-icon icon="close2" color="#333" size="{{22}}" class="close-icon1" catchtap="deleteImg" data-datatype="12" data-item="{{item}}" data-index="{{index}}"></mp-icon>
        </view>
      </view>

      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>船舶适航证书：</view>
        <view class="barge-value" hover-class="none" hover-stop-propagation="false">
          <view  bindtap="chooseBargeImg" data-datatype="13" class="upload-btn" wx:if="{{userInfos.userType == 14}}">上传</view>
          （<text class="red">{{seaworthinessCertificateImgArr.length}}</text>）
        </view>
        <view class="{{showSeaworthinessImg ? 'iconfont icon-xiangxiajiantou detail-icon' : 'iconfont iconxiangshangjiantou1 detail-icon' }}" hover-class="none" hover-stop-propagation="false" data-datatype="13" catchtap="foldImg"></view> 
      </view>
      <!--船舶适航证书-照片列表-->
      <view class="img-wrap" hover-class="none" hover-stop-propagation="false" wx:if="{{showSeaworthinessImg && seaworthinessCertificateImgArr.length>=1}}">
        <view class="img-item" hover-class="none" hover-stop-propagation="false" wx:for="{{seaworthinessCertificateImgArr}}" wx:key="index">
          <image class="" src="{{item.url}}" mode="aspectFill" style="width:200rpx;height:200rpx;" bindtap="showBigImg" data-arr="{{seaworthinessCertificateImgArr}}" data-currUrl="{{url}}" lazy-load="false" binderror="" bindload="">
          </image>
          <mp-icon icon="close2" color="#333" size="{{22}}" class="close-icon1" catchtap="deleteImg" data-datatype="13" data-item="{{item}}" data-index="{{index}}"></mp-icon>
        </view>
      </view>

      <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>船舶自动识别系统AIS证书：</view>
        <view class="barge-value" hover-class="none" hover-stop-propagation="false">
          <view  bindtap="chooseBargeImg" data-datatype="14" class="upload-btn" wx:if="{{userInfos.userType == 14}}">上传</view>
          （<text class="red">{{automaticAisImgArr.length}}</text>）
        </view>
        <view class="{{showAutomaticImg ? 'iconfont icon-xiangxiajiantou detail-icon' : 'iconfont iconxiangshangjiantou1 detail-icon' }}" hover-class="none" hover-stop-propagation="false" data-datatype="14" catchtap="foldImg"></view> 
      </view>
      <!--船舶自动识别系统AIS证书-照片列表-->
      <view class="img-wrap" hover-class="none" hover-stop-propagation="false" wx:if="{{showAutomaticImg && automaticAisImgArr.length>=1}}">
        <view class="img-item" hover-class="none" hover-stop-propagation="false" wx:for="{{automaticAisImgArr}}" wx:key="index">
          <image class="" src="{{item.url}}" mode="aspectFill" style="width:200rpx;height:200rpx;" bindtap="showBigImg"  data-arr="{{automaticAisImgArr}}" data-currUrl="{{url}}" lazy-load="false" binderror="" bindload="">
          </image>
          <mp-icon icon="close2" color="#333" size="{{22}}" class="close-icon1" catchtap="deleteImg" data-datatype="14" data-item="{{item}}" data-index="{{index}}"></mp-icon>
        </view>
      </view>
      
      <!-- 租用信息 -->
      <!-- <view class="consign-top" hover-class="none" hover-stop-propagation="false">
        <view class="consign-title" hover-class="none" hover-stop-propagation="false">
          租用信息
        </view>
        <view class="add-message-item" hover-class="none" hover-stop-propagation="false">
          <view class="add-message-title" hover-class="none" hover-stop-propagation="false">租用状态：</view>
          <view class="add-message-ipt1" hover-class="none" hover-stop-propagation="false">
            {{bargeParam.auditStatus}}
          </view>
          <view class="add-message-title" hover-class="none" hover-stop-propagation="false">是否申请租用：</view>
          <checkbox-group bindchange="checkBindChange" class="box-right">
            <label>
              <checkbox value="{{isApplyBind}}" checked="{{isApplyBind}}"/>
            </label>
          </checkbox-group>
        </view>
        <picker bindchange="bargeTypeChange" value="{{bargeTypeIndex}}" range="{{bargeTypeArray}}" mode="selector" range-key="label"  wx:if="{{isApplyBind}}">
          <view class="add-message-item">
            <view class="add-message-title" hover-class="none" hover-stop-propagation="false">船支类型：</view>
            <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false">
              {{bargeParam.bargeType ? bargeTypeArray[bargeTypeIndex].label : "请选择船支类型"}}
            </view>
          </view>
        </picker>
        <picker bindchange="bingingTypeChange" value="{{bingingTypeIndex}}" range="{{bingingTypeArray}}" mode="selector" range-key="label" wx:if="{{isApplyBind && bargeParam.bargeType == 2}}">
          <view class="add-message-item">
            <view class="add-message-title" hover-class="none" hover-stop-propagation="false">租用方式：</view>
            <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false">
              {{bargeParam.bingingType ? bingingTypeArray[bingingTypeIndex].label : "请选择租用方式"}}
            </view>
          </view>
        </picker>
        <view class="add-message-item" hover-class="none" hover-stop-propagation="false" bindtap="wxMonthChargeByNameInput" wx:if="{{isApplyBind}}">
          <view class="add-message-title" hover-class="none" hover-stop-propagation="false">租用公司：</view>
          <view class="add-message-ipt" hover-class="none" hover-stop-propagation="false">
            {{bargeParam.companyName ? bargeParam.companyName : "请选择租用公司"}}
          </view>
          <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{18}}"></mp-icon>
        </view>
      </view> -->

    </view>

    <view class="person-operate" hover-class="none" hover-stop-propagation="false">
      <button class="person-btn" bindtap="shipNext" wx:if="{{userInfos.userType == 14}}">下一步</button>
    </view>
  </view>
  <!-- 3 电子签章 -->
  <view class="record-content" wx:if="{{activeTab == 3}}" hover-class="none" hover-stop-propagation="false" style="{{userInfos.userType ==14 ? 'margin-top:80rpx':'margin-top:0rpx'}}">
    <view class="person-record" hover-class="none" hover-stop-propagation="false">
      <view class="consign-top" hover-class="none" hover-stop-propagation="false">
        <view class="consign-title" hover-class="none" hover-stop-propagation="false">
          电子签章
        </view>
        <!--电子签章照片列表-->
        <view class="img-wrap" hover-class="none" hover-stop-propagation="false" wx:if="{{sealImg1}}">
          <view class="img-item" hover-class="none" hover-stop-propagation="false">
            <image src="{{sealImg1}}" mode="aspectFill" style="width:400rpx;height:400rpx;"  bindtap="showBigImg1" data-currUrl="{{sealImg1}}" lazy-load="false" binderror="" bindload="">
            </image>
          </view>
        </view>
      </view>
      <view class="consign-top" hover-class="none" hover-stop-propagation="false">
        <view class="consign-title" hover-class="none" hover-stop-propagation="false">
          使用协议
        </view>
        <view class="contract-text-wrap" hover-class="none" hover-stop-propagation="false">
          <view class="privacy-title" hover-class="none" hover-stop-propagation="false">《电子签章授权委托书》</view>
          <view class="normal-text" hover-class="none" hover-stop-propagation="false">本人确认电子签章签署功能的授权在签署本授权委托书后生效。</view>
          <view class="normal-text" hover-class="none" hover-stop-propagation="false">本人<text class="underline-text">{{personForm.nickName}}</text>（身份证号码 <text class="underline-text">{{personForm.identityId}}</text>），经营驳船名称 <text class="underline-text">{{bargeParam.bargeName}}</text>，授权广州港船务有限公司（统一社会信用代码：91440101190480462U）在“广州港船务水上过驳服务平台”范围内，无需经短信动态码、人脸识别等本人验证，即可由平台向法大大直接发送签署指令，以本人名义实施电子签章。本人已充分知悉上述授权可能存在的风险，对在上述授权范围内以本人名义实施电子签章签署的电子水路货物运单等数据电文的法律效力予以认可，并承担相应权利义务。
          </view>
          <view class="important-text" hover-class="none" hover-stop-propagation="false">
          本人与被授权方间的其他权利义务由双方另行约定，基于上述授权引起的业务纠纷由本人与被授权方自行解决，与法大大无关；由于法大大过错对本人造成的损失，与被授权方无关。
          </view>
        </view>
      </view>
          
      <view class="add-message-item" hover-class="none" hover-stop-propagation="false" style="margin-top:20rpx;">
        <view class="add-message-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>委托书：</view>
        <view class="barge-value" hover-class="none" hover-stop-propagation="false">
          <view  bindtap="downloadBargeImg" data-datatype="53" class="upload-btn" wx:if="{{userInfos.userType == 14}}">模板</view>
          <view  bindtap="chooseBargeImg" data-datatype="53" class="upload-btn" wx:if="{{userInfos.userType == 14}}">上传</view>
          （<text class="red">{{powerOfAttorneyImgArr.length}}</text>）
        </view>
        <view class="{{showAttorneyImg ? 'iconfont icon-xiangxiajiantou detail-icon' : 'iconfont iconxiangshangjiantou1 detail-icon' }}" hover-class="none" hover-stop-propagation="false" data-datatype="53" catchtap="foldImg"></view> 
      </view>
      <!-- 委托书-照片列表 -->
      <view class="img-wrap" hover-class="none" hover-stop-propagation="false" wx:if="{{showAttorneyImg && powerOfAttorneyImgArr.length>=1}}">
        <view class="img-item" hover-class="none" hover-stop-propagation="false" wx:for="{{powerOfAttorneyImgArr}}" wx:key="index">
          <image class="" src="{{item.url}}" mode="aspectFill" style="width:200rpx;height:200rpx;" bindtap="showBigImg"  data-arr="{{powerOfAttorneyImgArr}}" data-currUrl="{{url}}" lazy-load="false" binderror="" bindload="">
          </image>
          <mp-icon icon="close2" color="#333" size="{{22}}" class="close-icon1" catchtap="deleteImg" data-datatype="53" data-item="{{item}}" data-index="{{index}}"></mp-icon>
        </view>
      </view>

      <view class="contract-wrap" hover-class="none" hover-stop-propagation="false">
        <checkbox-group bindchange="checkContractChange">
          <label>
            <checkbox value="{{isContractChecked}}" checked="{{isContractChecked}}">本人同意以上协议</checkbox>
          </label>
        </checkbox-group>
      </view>

    </view>

    <view class="person-operate" hover-class="none" hover-stop-propagation="false">
      <button class="person-btn" bindtap="submitFinal" wx:if="{{userInfos.userType == 14}}">提交审核</button>
    </view>
  </view>
  <!-- canvas签章画图用 -->
  <view wx:if="{{canvaShow}}" style="width: 0;height: 0;overflow: hidden;">
    <canvas type="2d" id="canvas"  canvas-id="canvas"></canvas>
  </view>

  <!-- 出现弹窗时，出现遮罩 -->
  <view class="mask1" hover-class="none" hover-stop-propagation="false" wx:if="{{showSearchDialog}}" ></view>
  <!-- 项目输入框弹窗 -->
  <searchProjectDialog wx:if="{{showSearchDialog}}"  class="search-dialog" bindcloseDialog="closeSearchDialog" selectedProjectsArray="{{selectedProjectsArray}}"></searchProjectDialog>
  <canvas canvas-id='attendCanvasId' class='myCanvas'></canvas>
</view>

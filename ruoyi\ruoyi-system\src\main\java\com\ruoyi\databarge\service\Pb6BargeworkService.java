package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.Pb6Bargework;
import com.ruoyi.databarge.domain.dto.Pb6BargeWorkDTO;
import com.ruoyi.databarge.domain.vo.Pb6BargeWorkVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface Pb6BargeworkService extends IService<Pb6Bargework> {
    String searchUniqecode(String coutformid);
    IPage<Pb6BargeWorkVo> searchPagePb6WaterCargo(Pb6BargeWorkDTO pb6BargeWorkDTO);
    String findCargoTypeByCoutformid(String coutformid);
    List<Map<String,Object>> findCargoNameByCoutformid(String coutformid);
}

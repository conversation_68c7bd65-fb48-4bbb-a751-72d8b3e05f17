package com.ruoyi.common.utils.message.request.mpTem;

import java.util.Map;

/**
 * 公众号模板消息相关的信息
 * <AUTHOR>
 *
 */
public class MpTemplateMsg {

	//公众号appid，要求与小程序有绑定且同主体
	private String appid;
	//公众号模板id
	private String template_id;
	//公众号模板消息所要跳转的url
	private String url;
	//公众号模板消息所要跳转的小程序，小程序的必须与公众号具有绑定关系
	private Miniprogram miniprogram;
	//公众号模板消息的数据
	private Map<String, DataValue> data;
	
	
	public String getAppid() {
		return appid;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}
	public String getTemplate_id() {
		return template_id;
	}
	public void setTemplate_id(String template_id) {
		this.template_id = template_id;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public Miniprogram getMiniprogram() {
		return miniprogram;
	}
	public void setMiniprogram(Miniprogram miniprogram) {
		this.miniprogram = miniprogram;
	}
	public Map<String, DataValue> getData() {
		return data;
	}
	public void setData(Map<String, DataValue> data) {
		this.data = data;
	}
	
	
	
}

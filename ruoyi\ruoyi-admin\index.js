;
(function(global) {
    // post 请求头设置
    // axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded'
    axios.defaults.headers.post['Content-Type'] = 'application/json'
        // axios.defaults.withCredentials = true

    // 创建axios实例
    var service = axios.create({
        // baseURL: 'http://db.winjoinit.com:9514',
        // baseURL: 'http://localhost:8082/gzshipping/api/',
        //  baseURL: 'http://iscbs.gzport.com/gzshipping/api/', //正式外网环境
        //  baseURL: 'http://localhost:8082',// 本地开发环境
        // baseURL: 'http://*************:20051/',//测试内网环境
        // baseURL: 'http://*************:8083',
      //baseURL: '',
      timeout: 90000, // 请求超时时间
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    })

    axios.defaults.baseURL = service.defaults.baseURL

    let serviceArr = []
    service.interceptors.request.use(
        config => {
            if (serviceArr.length === 0) {
                store.dispatch('app/loading_status', true)
            }
            serviceArr.push('service')
            return config
        },
        err => {
            if (serviceArr.length === 1) {
                store.dispatch('app/loading_status', false)
            }
            serviceArr.pop()
            return Promise.reject(err)
        }
    )


    /**
     * http response 拦截器 两个对应不同的baseURL
     */
    var vueInstance = new Vue();

    service.interceptors.response.use(
        response => {
            // 导出
            // if(response.config && response.config.responseType == 'blob') {
            //   const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' }); //application/vnd.openxmlformats-officedocument.spreadsheetml.sheet这里表示xlsx类型
            //   let filename = 'excel.xls';
            //   if ('download' in document.createElement('a')) {
            //       const downloadElement = document.createElement('a');
            //       let href = ''; 
            //       if(window.URL) href = window.URL.createObjectURL(blob); 
            //       else href = window.webkitURL.createObjectURL(blob); 
            //   　　 downloadElement.href = href;
            //   　　 downloadElement.download = filename; 
            //   　　 document.body.appendChild(downloadElement);
            //   　　 downloadElement.click(); 
            //   　　 if(window.URL) window.URL.revokeObjectURL(href); 
            //       else window.webkitURL.revokeObjectURL(href); 
            //       document.body.removeChild(downloadElement);
            //   } else {
            //       navigator.msSaveBlob(blob, filename);
            //   }
            //   return;
            // }

            if (serviceArr.length === 1) {
                store.dispatch('app/loading_status', false)
            }
            serviceArr.pop()

            if (response.config.responseType == 'arraybuffer') {
                setCookie('checkOldCode', response.headers.checkcode)
                return response
            }


            if (Number(response.data.code) === 0) {
                vueInstance.$message({
                    message: response.data.msg,
                    type: 'error',
                    showClose: true
                })
            } else if (Number(response.data.code) === 1) {
                return response
            } else {
                vueInstance.$message({
                    message: response.data.msg,
                    type: 'warning',
                    showClose: true,
                    duration: 0
                })
            }
        },
        error => {
            if (serviceArr.length === 1) {
                store.dispatch('app/loading_status', false)
            }
            serviceArr.pop()
            var message = error.message;

            if (message.includes("timeout")) {

                message = "系统接口请求超时,请稍后再试";

            } else {

                message = '服务器错误，请联系管理员!'
            }

            vueInstance.$message({
                message: message,
                type: 'warning',
                showClose: true
            })
            return Promise.reject(error)
        }
    )

    function fileGet(url, params) {
        // const service = axios.create({
        //   baseURL: process.env.NODE_ENV === 'development' ? '' : window.api.baseURL,
        //   timeout: 10000 // 请求超时时间
        // })
        return new Promise((resolve, reject) => {
            service
                .get(timestamp(url), {
                    params: params,
                    responseType: 'arraybuffer'
                })
                .then(res => {
                    res && resolve(res)
                })
                .catch(err => {
                    reject(err)
                })
        })
    }

    /**
     * get方法，对应post请求,两个post对应不同的baseURL
     * @param {String} url [请求的url地址]
     * @param {Object} params [请求时携带的参数]
     */
    function get(url, params) {
        return new Promise((resolve, reject) => {
            service.get(url, {
                    params: params
                })
                .then(res => {
                    resolve(res.data)
                })
                .catch(err => {
                    reject(err)
                })
        })
    }


    /**
     * post方法，对应post请求,两个post对应不同的baseURL
     * @param {String} url [请求的url地址]
     * @param {Object} params [请求时携带的参数]
     */
    function post(url, params) {
        return new Promise((resolve, reject) => {
            service.post(url, JSON.stringify(params))
                .then(res => {
                    resolve(res.data)
                })
                .catch(err => {
                    reject(err)
                })
        })
    }

    function postFile(url, params) {
        return new Promise((resolve, reject) => {
            service
                .post(url, params, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    },
                    transformRequest: [
                        function(data) {
                            return data
                        }
                    ]
                })
                .then(res => {
                    res && resolve(res.data)
                })
                .catch(err => {
                    reject(err)
                })
        })
    }
    global.ajax = {
        get: get,
        post,
        postFile,
        fileGet
    }
})(window)
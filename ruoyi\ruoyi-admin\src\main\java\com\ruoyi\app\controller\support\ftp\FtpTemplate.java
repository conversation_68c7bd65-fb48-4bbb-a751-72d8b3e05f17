package com.ruoyi.app.controller.support.ftp;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.file.remote.session.CachingSessionFactory;
import org.springframework.integration.file.remote.session.Session;
import org.springframework.integration.ftp.session.DefaultFtpSessionFactory;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @Date 2020/11/18 10:19
 * @Description:
 */
@Configuration
public class FtpTemplate {

    private final CachingSessionFactory<FTPFile> ftpSessionFactory;

    public FtpTemplate(FtpProperties ftpProperties) {
        FtpSessionFactory ftpSessionFactory= new FtpSessionFactory();
        ftpSessionFactory.setHost(ftpProperties.getHost());
        ftpSessionFactory.setPort(ftpProperties.getPort());
        ftpSessionFactory.setUsername(ftpProperties.getUsername());
        ftpSessionFactory.setPassword(ftpProperties.getPassword());
        ftpSessionFactory.setControlEncoding(ftpProperties.getControlEncoding());
        ftpSessionFactory.setClientMode(ftpProperties.getClientMode());
        ftpSessionFactory.setFileType(ftpProperties.getFileType());
        ftpSessionFactory.setBufferSize(ftpProperties.getBufferSize());

        CachingSessionFactory<FTPFile> cachingSessionFactory=new CachingSessionFactory<>(ftpSessionFactory,ftpProperties.getSessionCacheSize());
        cachingSessionFactory.setSessionWaitTimeout(ftpProperties.getSessionWaitTimeout());
        cachingSessionFactory.setTestSession(ftpProperties.isTestSession());

        this.ftpSessionFactory=cachingSessionFactory;
    }

    @PreDestroy
    public void destroy(){
        this.ftpSessionFactory.destroy();
    }

    static class FtpSessionFactory extends DefaultFtpSessionFactory {
        @Override
        protected void postProcessClientAfterConnect(FTPClient ftpClient) throws IOException {
            super.postProcessClientAfterConnect(ftpClient);
        }

        @Override
        protected void postProcessClientBeforeConnect(FTPClient client) throws IOException {
            // 关闭远程 IP 校验（关键！）
            client.setRemoteVerificationEnabled(false);

            client.setActivePortRange(50000,500800);
            super.postProcessClientBeforeConnect(client);
        }
    }

    public void upload(String path, InputStream inputStream) throws IOException{
        Session<FTPFile> session=this.ftpSessionFactory.getSession();
        session.write(inputStream,path);
        session.close();
    }

    public void download(String path, OutputStream outputStream) throws IOException{
        Session<FTPFile> session=this.ftpSessionFactory.getSession();
        session.read(path,outputStream);
        session.close();
    }

    public boolean mkdir(String path) throws IOException{
        Session<FTPFile> session=this.ftpSessionFactory.getSession();
        boolean flag=session.mkdir(path);
        session.close();
        return flag;
    }

    public boolean exists(String path) throws IOException{
        Session<FTPFile> session=this.ftpSessionFactory.getSession();
        boolean flag=session.exists(path);
        session.close();
        return flag;
    }

    public boolean rmDir(String path) throws IOException{
        Session<FTPFile> session=this.ftpSessionFactory.getSession();
        boolean flag=session.rmdir(path);
        session.close();
        return flag;
    }

    public boolean rmFile(String filePath) throws IOException{
        Session<FTPFile> session=this.ftpSessionFactory.getSession();
        boolean flag=session.remove(filePath);
        session.close();
        return flag;
    }

}

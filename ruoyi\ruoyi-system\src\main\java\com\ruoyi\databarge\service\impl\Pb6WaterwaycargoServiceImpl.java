package com.ruoyi.databarge.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.basic.domain.vo.ShipPersonVo;
import com.ruoyi.basic.mapper.SlaveMapper;
import com.ruoyi.basic.mapper.ThirdMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.SysUserMessage;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.common.domain.bo.UserMessageBO;
import com.ruoyi.common.domain.vo.UserMessageVO;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.AppNoticeService;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.message.WechatMessageUtil;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.dto.Pb6WaterCargoSearchDTO;
import com.ruoyi.databarge.domain.vo.*;
import com.ruoyi.databarge.mapper.Pb6BargeCheckMessageMapper;
import com.ruoyi.databarge.mapper.Pb6WaterwaycargoMapper;
import com.ruoyi.databarge.service.*;
import com.ruoyi.databarge.shipxy.ShipxyUtil;
import com.ruoyi.databarge.wechat.MpMessageDTO;
import com.ruoyi.databarge.wechat.MpMessageResult;
import com.ruoyi.databarge.wechat.MpUtil;
import com.ruoyi.ship.domain.ShipInfo;
import com.ruoyi.ship.service.impl.ShipInfoServiceImpl;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.util.Vo.PinyinUtils;
import com.ruoyi.wechat.service.WechatMpAccessTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.Deflater;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

@Service
@Slf4j
public class Pb6WaterwaycargoServiceImpl extends ServiceImpl<Pb6WaterwaycargoMapper, Pb6Waterwaycargo> implements Pb6WaterwaycargoService {

    @Autowired
    private Pb3CoutformService pb3CoutformService;

    @Autowired
    private Pb6CargoconsignmentdetailService pb6CargoconsignmentdetailService;

    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    @Autowired
    private WechatMpUserService wechatMpUserService;

    @Autowired
    private WechatMessageUtil wechatMessageUtil;

    @Autowired
    private WechatMpAccessTokenService wechatMpAccessTokenService;

    @Autowired
    SysUserMapper sysUserMapper;

    @Autowired
    Pb6WaterwaycargoServiceImpl pb6WaterwaycargoService;

    @Autowired
    private AppNoticeService appNoticeService;

    @Autowired
    private Pb6BargeCheckMessageMapper pb6BargeCheckMessageMapper;

    @Autowired
    private MpUtil mpUtil;

    @Autowired
    private ShipxyUtil shipxyUtil;

    @Autowired
    private SlaveMapper slaveMapper;

    @Autowired
    private ThirdMapper thirdMapper;

    @Autowired
    private CargocmentdetailService cargocmentdetailService;

    @Autowired
    private Pb6CargoconsignmentServiceImpl pb6CargoconsignmentService;

    @Autowired
    private UploadAddressDomainServiceImpl uploadAddressDomainService;

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String mailFrom;

    @Autowired
    private ShipInfoServiceImpl shipInfoService;

    @Override
    public Pb6Waterwaycargo searchWaterwaycargoByWaterwaycargoid(String pb6Waterwaycargo) {
        QueryWrapper<Pb6Waterwaycargo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb6Waterwaycargo::getId, pb6Waterwaycargo);
        //要求实装数发送一年内的记录 ztw(20231127)
        Pb6Waterwaycargo waterwaycargo = super.getOne(queryWrapper);
        Calendar calendar = Calendar.getInstance();
        Date now = calendar.getTime();
        //一年前
        calendar.set(Calendar.YEAR,-1);
        Date old = calendar.getTime();
        // if (ObjectUtil.isNotNull(waterwaycargo) && ObjectUtil.isNotNull(waterwaycargo.getRationdate()) && DateUtils.dateTime("yyyy-MM-dd",waterwaycargo.getRationdate()).compareTo(old) > 0 && DateUtils.dateTime("yyyy-MM-dd",waterwaycargo.getRationdate()).compareTo(now) < 0){
        //     return waterwaycargo;
        // }
        // else {
        //     throw new CustomException("水路运单号:" + pb6Waterwaycargo + "不在一年有效时间内");
        // }
        return waterwaycargo;
    }

    @Override
    public IPage<Pb6WaterCargoVO> searchPagePb6WaterCargo(Pb6WaterCargoSearchDTO pb6WaterCargoSearchDTO) {
        Long pageNum = Optional.ofNullable(pb6WaterCargoSearchDTO.getPageNum()).orElse(0L);
        Long pageSize = Optional.ofNullable(pb6WaterCargoSearchDTO.getPageSize()).orElse(-1L);
        pb6WaterCargoSearchDTO.setRationbegindate(StringUtils.isNotBlank(pb6WaterCargoSearchDTO.getRationbegindate()) ? pb6WaterCargoSearchDTO.getRationbegindate() + " 00:00" : null);
        pb6WaterCargoSearchDTO.setRationenddate(StringUtils.isNotBlank(pb6WaterCargoSearchDTO.getRationenddate()) ? pb6WaterCargoSearchDTO.getRationenddate() + " 23:59" : null);
        IPage<Pb6WaterCargoVO> result =  super.baseMapper.searchPagePb6WaterCargo(new Page<>(pageNum, pageSize), pb6WaterCargoSearchDTO);
        List<Pb6WaterCargoVO> list = result.getRecords();
        list.forEach(item ->{
            if(item.getIsreporting() != null && item.getIsreporting().equals("N")){
                item.setIsreporting("否");
            }
            else {
                item.setIsreporting("是");
            }
        });
        return result;
    }

    @Override
    public List<Pb6WaterCargoVO> searchListPb6WaterCargo(Pb6WaterCargoSearchDTO pb6WaterCargoSearchDTO) {
        pb6WaterCargoSearchDTO.setRationbegindate(StringUtils.isNotBlank(pb6WaterCargoSearchDTO.getRationbegindate()) ? pb6WaterCargoSearchDTO.getRationbegindate() + " 00:00" : null);
        pb6WaterCargoSearchDTO.setRationenddate(StringUtils.isNotBlank(pb6WaterCargoSearchDTO.getRationenddate()) ? pb6WaterCargoSearchDTO.getRationenddate() + " 23:59" : null);
        return super.baseMapper.searchPagePb6WaterCargo(pb6WaterCargoSearchDTO);
    }

    @Override
    public List<Pb6Waterwaycargo> findByPb6WaterwaycargoFuzzy(String waterwaycargoid) {
        QueryWrapper<Pb6Waterwaycargo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().likeLeft(Pb6Waterwaycargo::getWaterwaycargoid, waterwaycargoid).orderByDesc(Pb6Waterwaycargo::getWaterwaycargoid);
        return super.list(queryWrapper);
    }

    @Override
    public List<Pb6WaterCargoMobile> searchPb6WaterCargoForMobile(int status) {
        return baseMapper.searchPb6WaterCargoForMobile(status);
    }

    @Override
    public List<Pb6WaterCargoMobile> searchPb6WaterCargoForMobileWorking(String keyWord) {
        return baseMapper.searchPb6WaterCargoForMobileWorking(keyWord);
    }

    @Override
    public Long searchLoadMeterId(String loadometerid) {
        return baseMapper.searchLoadMeterId(loadometerid);
    }

    /**
     * 判断是否能支付、预约及报到
     * @param pb6Waterwaycargo 水路运单
     * @param sign 标识，用于返回错误提示 0支付、1预约、2报到
     */
    @Override
    public void canOrderOrForcast(Pb6Waterwaycargo pb6Waterwaycargo, int sign) {
        String action = "支付";
        if(sign == 1){
            action = "预约";
        } else if(sign == 2){
            action = "报到";
        }

    }

    @Override
    public List<String> waterwayCargoIdStamp(String applytime){
        return baseMapper.waterwayCargoIdStamp(applytime);
    }

    @Override
    public List<Pb6WaterCargoVO> selectNLConfirmAuto(String startTime,String endTime) {
        return baseMapper.selectNLConfirmAuto(startTime,endTime);
    }

    @Override
    public boolean updateAuto(WaterwayCargo waterwayCargo) {
        return baseMapper.updateAuto(waterwayCargo);
    }

    @Override
    public List<Pb6Waterwaycargo> selectPb6WaterwaycargoList(Pb6Waterwaycargo pb6Waterwaycargo) {
        return baseMapper.selectPb6WaterwaycargoList(pb6Waterwaycargo);
    }

    @Override
    public List<WaterWayCargoH5Vo> selectPb6WaterwaycargoListH5(Pb6Waterwaycargo pb6Waterwaycargo) {
        // 当前登录用户
        String userName = SecurityUtils.getLoginUser().getUser().getUserName();
        String phonenumber = SecurityUtils.getLoginUser().getUser().getPhonenumber();
        ShipPersonVo shipPersonVo = new ShipPersonVo();
        shipPersonVo.setPersonName(userName);
        shipPersonVo.setPersonPhone(phonenumber);

        // // 测试用数据
        // shipPersonVo.setPersonName("张三");
        // shipPersonVo.setPersonPhone("15118856316");

        // 查询当前船员关联的所有船
        // List<ShipPersonVo> shipPersonVos = slaveMapper.selectShipPersonList(shipPersonVo);

        List<ShipPersonVo> shipPersonVos = thirdMapper.selectShipPersonList(shipPersonVo);

        // 处理数据，如果有大船名或者航次为空的数据，去掉
        shipPersonVos = shipPersonVos.stream().filter(item -> StringUtils.isNotBlank(item.getShipName()) && StringUtils.isNotBlank(item.getShipNumber())).collect(Collectors.toList());

        // 保留原来系统的逻辑
        List<ShipPersonVo> shipPersonVos1 = slaveMapper.selectShipPersonList(shipPersonVo);

        // 处理数据，如果有大船名或者航次为空的数据，去掉
        shipPersonVos1 = shipPersonVos1.stream().filter(item -> StringUtils.isNotBlank(item.getShipName()) && StringUtils.isNotBlank(item.getShipNumber())).collect(Collectors.toList());


        // 合并两个list，去重
        shipPersonVos.addAll(shipPersonVos1);
        // 去重逻辑：根据 shipName 和 shipNumber 去重
        shipPersonVos = shipPersonVos.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ShipPersonVo::getShipName)
                                .thenComparing(ShipPersonVo::getShipNumber))),
                        ArrayList::new)); // 转换成 ArrayList


        // 测试用，如果登录的用户是管理员，admin，则查询测试大船下的数据，配置管理员对应的指导员
        if("admin".equals(userName) || "tadmin".equals(userName)){
            ShipPersonVo shipPersonVo1 = new ShipPersonVo();
            shipPersonVo1.setShipName("测试大船");
            shipPersonVo1.setShipNumber("N132");
            shipPersonVos.add(shipPersonVo1);
        }

        // 如果当前用户没有关联的船，则返回空
        if(shipPersonVos.size() == 0){
            return new ArrayList<>();
        }

        // 查询当前大船的所有水路运单
        List<Pb6Waterwaycargo> pb6Waterwaycargos = baseMapper.selectPb6WaterwaycargoListByShipAndVoyage(shipPersonVos,pb6Waterwaycargo);

        // 遍历水路运单，添加货物类型字段
        for(Pb6Waterwaycargo pb6Waterwaycargo1 : pb6Waterwaycargos){
            // 查询托运单明细
            Cargoconsignmentdetail detail = cargocmentdetailService.getOne(new QueryWrapper<Cargoconsignmentdetail>()
                    .eq("waterwaycargoid", pb6Waterwaycargo1.getId()));
            // 如果托运单明细为空，跳过
            if(ObjectUtil.isNull(detail)){
                continue;
            }
            Pb6Cargoconsignment cargoconsignment = pb6CargoconsignmentService.getById(detail.getConsignId());
            pb6Waterwaycargo1.setCargoType(cargoconsignment.getCargoType());

            // 大船信息
            ShipInfo shipInfo = shipInfoService.getById(cargoconsignment.getShipInfoId());
            // 贸易类型
            pb6Waterwaycargo1.setTradeType(shipInfo.getTradeType());
        }

        // 将pb6Waterwaycargos根据大船名和航次进行分组
        Map<String, List<Pb6Waterwaycargo>> collect = pb6Waterwaycargos.stream().collect((Collectors.groupingBy(item->item.getShipName() + item.getShipNumber())));

        // 将分组后的数据转换成List<waterWayCargoH5>

        List<WaterWayCargoH5Vo> waterWayCargoH5Vos = new ArrayList<>();
        Long index = 1L;

        for(Map.Entry<String, List<Pb6Waterwaycargo>> entry : collect.entrySet()){
            // 分组的key
            String key = entry.getKey();

            // 驳船信息
            List<Pb6Waterwaycargo> pb6WaterwaycargoList = entry.getValue();

            WaterWayCargoH5Vo waterWayCargoH5Vo = new WaterWayCargoH5Vo();

            waterWayCargoH5Vo.setShipName(pb6WaterwaycargoList.get(0).getShipName());
            waterWayCargoH5Vo.setShipNumber(pb6WaterwaycargoList.get(0).getShipNumber());
            waterWayCargoH5Vo.setPb6WaterwaycargoList(pb6WaterwaycargoList);
            waterWayCargoH5Vo.setId(index++);

            waterWayCargoH5Vos.add(waterWayCargoH5Vo);

        }

        return waterWayCargoH5Vos;
    }

    @Override
    public AjaxResult updatePb6Waterwaycargo(Pb6Waterwaycargo pb6Waterwaycargo) {
        if(baseMapper.updateById(pb6Waterwaycargo) > 0){
            return AjaxResult.success();
        }
        else {
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult confirmPb6Waterwaycargo(Pb6Waterwaycargo pb6Waterwaycargo1) {

        String id = pb6Waterwaycargo1.getId().toString();

        // 将completeTime格式化为yyyy-MM-dd HH:mm:ss  原格式为Sun Apr 14 12:19:54 CST 2024
        Date completeTime = pb6Waterwaycargo1.getCompleteTime();

        Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getById(id);

        // 给驳船主发消息 小程序和公众号都要发
        List<Pb6Cargoconsignmentdetail> pb6CargoconsignmentdetailList = pb6CargoconsignmentdetailService
                .list(new QueryWrapper<Pb6Cargoconsignmentdetail>().lambda().eq(Pb6Cargoconsignmentdetail::getWaterwaycargoid, id));
        if(pb6CargoconsignmentdetailList.size() != 1){
            log.error("没有找到水路运单对应的托运单或对应托运单不唯一!");
            throw new CustomException("1");
        }
        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailList.get(0);

        // 水路运单状态要是已经确认
        if(pb6Cargoconsignmentdetail.getWxoperatestate() == 0){
            return AjaxResult.error("驳船未确认");
        }

        if(pb6Cargoconsignmentdetail.getFlagbargestate().equals("5")){
            return AjaxResult.error("驳船已离港");
        }



        List<SysUser> sysUserList = sysUserBargeMapper.queryAllUserByBargeId(pb6Cargoconsignmentdetail.getBargeid());

        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWxrationcontactnumber())){
            List<SysUser> ration = sysUserMapper.selectCargoConsignPersonByPhone(pb6Cargoconsignmentdetail.getWxrationcontactnumber());
            sysUserList.addAll(ration);
        }



        // TODO: 2020/12/12 添加实装数修改
        // 1、发小程序消息
        Map<String, Object> params = new HashMap<>();
        params.put("first", "订单完成通知");
        params.put("keyword1", pb6Cargoconsignmentdetail.getWaterwaycargoid());
        params.put("keyword2", "驳船提货作业" + "(" + pb6Cargoconsignmentdetail.getBargename() + ")");
        params.put("keyword3", DateUtils.getTime());
//            params.put("remark", "您的驳船作业已完成，请及时确认实装数，确认后可下载水路运单、交接凭证等。货名："
//                    + portLoadingMsgVO.getCargename() + "；配载吨数：" + portLoadingMsgVO.getRationweight() + "；实装吨数：111");

        BigDecimal rationWeight = new BigDecimal(pb6Waterwaycargo.getRationweight());
        rationWeight = rationWeight.setScale(3, BigDecimal.ROUND_HALF_UP);
        params.put("remark", "您的驳船作业已完成，请及时确认实装数，确认后可下载水路运单。货名："
                + pb6Waterwaycargo.getCargename() + "；配载吨数：" + rationWeight );

        // 业务编号
        params.put("character_string2", pb6Waterwaycargo.getWaterwaycargoid());
        // 船名/航次 其中船名需要变成字符
        String pinyin = PinyinUtils.getPingYin(pb6Cargoconsignmentdetail.getBargename());
        String characterString4 = pinyin + "/" + pb6Waterwaycargo.getBargeNumber();
        params.put("character_string4",characterString4);
        // 模板消息 船名/航次 大船船名 货名 配载吨数 完工时间
        // 船名航次
        String bargeVoyage = pb6Waterwaycargo.getBargeName() +"/" + pb6Waterwaycargo.getBargeNumber();
        params.put("thing1",bargeVoyage);
        // 大船船名
        params.put("thing2",pb6Waterwaycargo.getShipName());
        // 货名
        params.put("thing3",pb6Waterwaycargo.getCargename());
        // 配载吨数
        params.put("character_string4",pb6Waterwaycargo.getRationweight());
        // 完工时间
        // 将completeTime格式化为yyyy-MM-dd HH:mm:ss  原格式为Sun Apr 14 12:19:54 CST 2024
        String completeTimeStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm",completeTime);
        params.put("time5",completeTimeStr);

        UserMessageBO userMessageBO = new UserMessageBO();
        userMessageBO.setParams(params);
        List<Long> userIds = new ArrayList<>();
        //遍历需要发送信息的用户集合获取id用于插入sys_message_user表
        for (SysUser sysUser : sysUserList){
            userIds.add((sysUser.getUserId()));
        }
        userMessageBO.setUserIds(userIds);
        userMessageBO.setSendMessageType("yVwAtns-gVVFFXYvCmlRmYGcddTtq-8ocU1bhWrgQQ0");
        //2021.08.27 jinn 添加水路运单号
        userMessageBO.setWaterCargoId(id);
        //先往sys_message中插入数据，将返回的message_id和userIds中的id(user_id)插入sys_message_user
        AjaxResult ajaxResult = appNoticeService.sendMessage(userMessageBO);
        //获取返回的message_id
        Long messageId = (Long) ajaxResult.get("data");

        for(SysUser sysUser: sysUserList){

            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(sysUser.getUnionId())) {

                // 查询前，先根据openId去查询用户信息
                wechatMpUserService.getUserInfo(sysUser.getUnionId());

                // 2、发公众号消息
                List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                if (wechatMpUserList.size() == 0) {
                    log.warn("该用户未关注广州港船务有限公司!");
//                    throw new CustomException("5");
                } else if (wechatMpUserList.size() == 1) {
                    String accessToken = wechatMpAccessTokenService.getAccessToken();
                    //通过message_id和user_id 去sys_message_user表和sys_message表、SYS_MESSAGE_MODEL表级联查询小程序前端所需参数currentObj（UserMessageVO类）
                    SysUserMessage sysUserMessage = new SysUserMessage();
                    sysUserMessage.setUserId(sysUser.getUserId());
                    sysUserMessage.setMessageId(messageId);
                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                            .template_id("yVwAtns-gVVFFXYvCmlRmYGcddTtq-8ocU1bhWrgQQ0")
                            .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing1").toString()).color("#000000").build())
                            .data("thing2", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing2").toString()).color("#000000").build())
                            .data("thing3", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing3").toString()).color("#000000").build())
                            .data("character_string4", MpMessageDTO.MpMessageDataField.builder().value(params.get("character_string4").toString()).color("#000000").build())
                            .data("time5", MpMessageDTO.MpMessageDataField.builder().value(params.get("time5").toString()).color("#000000").build())
                            .build();
                    System.out.println("W H Y");
                    System.out.println(mpMessageDTO);
                    MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);

                    log.info("发送历史消息：{}",mpMessageResult);
                } else {
                    throw new CustomException("数据库数据错误!");
                }
            } else {
                log.warn("该用户没有unionid，发送实装数失败！");
//                throw new CustomException("4");
            }
        }
        // 驳船状态改为已报道（已完工）
        pb6Cargoconsignmentdetail.setFlagbargestate("4");

        // 水路运单确认状态改为已确认
        pb6Waterwaycargo1.setConfirmloadingover("Y");

        // 水路运单确认人设置为当前登录用户，CONFIRMLOADPEOPLE
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        pb6Waterwaycargo1.setConfirmloadpeople(loginUser.getNickName());

        // 更新水路运单完船时间和完船备注
        pb6WaterwaycargoService.updateById(pb6Waterwaycargo1);
        pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);

        return AjaxResult.success();
    }

    @Override
    public AjaxResult confirmRelease(Pb6Waterwaycargo pb6Waterwaycargo1) {

        String id = pb6Waterwaycargo1.getId().toString();

        // 是否放行
        String isRelease = pb6Waterwaycargo1.getIsRelease();
        // 放行时间为当前时间的字符串格式
        String releaseTime = DateUtils.getTime();
        pb6Waterwaycargo1.setReleaseTime(releaseTime);

        Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getById(id);

        // 给驳船主发消息 小程序和公众号都要发
        List<Pb6Cargoconsignmentdetail> pb6CargoconsignmentdetailList = pb6CargoconsignmentdetailService
                .list(new QueryWrapper<Pb6Cargoconsignmentdetail>().lambda().eq(Pb6Cargoconsignmentdetail::getWaterwaycargoid, id));
        if(pb6CargoconsignmentdetailList.size() != 1){
            log.error("没有找到水路运单对应的托运单或对应托运单不唯一!");
            throw new CustomException("1");
        }
        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailList.get(0);

        Map<String, Object> params = new HashMap<>();
        // 驳船船名
        params.put("thing10",pb6Cargoconsignmentdetail.getBargename());
        // 驳船航次
        params.put("character_string2",pb6Waterwaycargo.getBargeNumber());
        // 货名
        params.put("thing8",pb6Waterwaycargo.getCargename());
        // 放行状态，当放行，即为1是，赋值装载货物已放行，可离开监管区 当不放行，即为0时，赋值装载货物未放行，禁止离开监管区
        if(isRelease.equals("1")){
            params.put("const9","装载货物已放行，可离开监管区");
        }else{
            params.put("const9","装载货物未放行，禁止离开监管区");
        }

        List<SysUser> sysUserList = sysUserBargeMapper.queryAllUserByBargeId(pb6Cargoconsignmentdetail.getBargeid());

        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWxrationcontactnumber())){
            List<SysUser> ration = sysUserMapper.selectCargoConsignPersonByPhone(pb6Cargoconsignmentdetail.getWxrationcontactnumber());
            sysUserList.addAll(ration);
        }

        for(SysUser sysUser: sysUserList){

            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(sysUser.getUnionId())) {

                // 查询前，先根据openId去查询用户信息
                wechatMpUserService.getUserInfo(sysUser.getUnionId());

                // 2、发公众号消息
                List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                if (wechatMpUserList.size() == 0) {
                    log.warn("该用户未关注广州港船务有限公司!");
//                    throw new CustomException("5");
                } else if (wechatMpUserList.size() == 1) {
                    String accessToken = wechatMpAccessTokenService.getAccessToken();
                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                            .template_id("e1LlFOcDh6HUwfNLYSmB3mQ5xpiABvnuhrcK-dBfTCQ")
                            .data("thing10", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing10").toString()).color("#000000").build())
                            .data("character_string2", MpMessageDTO.MpMessageDataField.builder().value(params.get("character_string2").toString()).color("#000000").build())
                            .data("thing8", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing8").toString()).color("#000000").build())
                            .data("const9", MpMessageDTO.MpMessageDataField.builder().value(params.get("const9").toString()).color("#000000").build())
                            .build();
                    System.out.println("W H Y");
                    System.out.println(mpMessageDTO);
                    MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                    System.out.println("海关放行通知");
                    System.out.println(mpMessageResult);
                } else {
                    throw new CustomException("数据库数据错误!");
                }
            } else {
                log.warn("该用户没有unionid，发送海关放行通知失败！");
//                throw new CustomException("4");
            }
        }

        // 更新放行状态和放行时间
        pb6WaterwaycargoService.updateById(pb6Waterwaycargo1);

        return AjaxResult.success(pb6Waterwaycargo1);
    }

    @Override
    public void sendBargeFilesViaEmail(BargeFileEmailVo bargeFileEmailVo) throws IOException {

        List<Long> ids = bargeFileEmailVo.getIds();
        String toEmail = bargeFileEmailVo.getMail();

        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String tempDir = createTempDirectory();
        File zipFile = null;

        try {
            log.info("开始处理邮件附件 - 文件数量: {} - 接收邮箱: {}", ids.size(), toEmail);

            // 处理文件
            int processedFiles = 0;
            for (Long id : ids) {
                try {
                    processWaterWayBillFiles(id, tempDir);
                    processedFiles++;
                } catch (Exception e) {
                    log.error("处理水路运单文件失败 - ID: {} - 错误: {}", id, e.getMessage());
                }
            }

            if (processedFiles == 0) {
                throw new CustomException("没有可发送的文件");
            }

            // 创建ZIP文件
            zipFile = new File(tempDir, "水路运单附件_" + dateStr + ".zip");

            // 压缩文件
            int zipCount = createZipFile(new File(tempDir), zipFile);

            // 验证ZIP文件
            if (!zipFile.exists() || zipFile.length() == 0) {
                throw new CustomException("ZIP文件创建失败");
            }

            log.info("压缩完成 - 压缩文件数: {} - ZIP大小: {}MB",
                zipCount, zipFile.length() / (1024 * 1024));

            // 发送邮件
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setFrom(mailFrom);
            helper.setTo(toEmail);
            helper.setSubject("水路运单附件_" + dateStr);
            helper.setText("您好，\n\n附件为您请求的水路运单文件，请查收。\n\n此邮件为系统自动发送，请勿回复。");

            // 添加ZIP附件
            helper.addAttachment("水路运单附件_" + dateStr + ".zip", zipFile);

            // 发送邮件
            mailSender.send(message);

            log.info("邮件发送成功 - 接收邮箱: {} - 附件大小: {}MB",
                toEmail, zipFile.length() / (1024 * 1024));

        } catch (Exception e) {
            log.error("邮件发送失败: {}", e.getMessage(), e);
            throw new CustomException("邮件发送失败: " + e.getMessage());
        } finally {
            cleanupTempFiles(tempDir, zipFile);
        }
    }

    /**
     * 创建临时目录
     */
    private String createTempDirectory() throws IOException {
        String tempDir = System.getProperty("java.io.tmpdir") + File.separator +
            UUID.randomUUID().toString();
        File dir = new File(tempDir);
        if (!dir.mkdirs()) {
            throw new IOException("无法创建临时目录: " + tempDir);
        }
        log.debug("创建临时目录: {}", tempDir);
        return tempDir;
    }

    /**
     * 处理水路运单文件
     */
    private void processWaterWayBillFiles(Long id, String tempDir) throws IOException {
        // 获取水路运单信息
        Pb6Waterwaycargo waterWayBill = this.getById(id);
        if (waterWayBill == null) {
            log.warn("水路运单信息不存在: {}", id);
            return;
        }

        // 创建文件夹名称：水路运单编号_驳船名
        String folderName = waterWayBill.getWaterwaycargoid() + "_" + waterWayBill.getBargeName();
        // 移除文件名中的非法字符
        folderName = folderName.replaceAll("[\\\\/:*?\"<>|]", "_");

        File billDir = new File(tempDir, folderName);
        if (!billDir.mkdirs()) {
            throw new IOException("无法创建目录: " + folderName);
        }

        // 获取该水路运单的所有文件记录
        List<UploadAddressDomain> fileRecords = uploadAddressDomainService
            .searchBargeNeedRecordDownloadFile(id);

        if (fileRecords.isEmpty()) {
            log.warn("未找到水路运单相关文件记录: {}", id);
            throw new CustomException("未找到相关文件");
        }

        int successCount = 0;
        for (UploadAddressDomain record : fileRecords) {
            try {
                copyFileToTemp(record, billDir);
                successCount++;
            } catch (IOException e) {
                log.error("文件复制失败: {} - {}", record.getUrl(), e.getMessage());
            }
        }

        if (successCount == 0) {
            throw new CustomException("没有文件成功复制");
        }

        log.info("文件处理完成 - 运单ID: {} - 总文件数: {} - 成功复制: {}",
            id, fileRecords.size(), successCount);
    }

    /**
     * 创建ZIP文件
     */
    private int createZipFile(File sourceDir, File zipFile) throws IOException {
        int fileCount = 0;

        // 确保源目录存在
        if (!sourceDir.exists() || !sourceDir.isDirectory()) {
            throw new IOException("源目录不存在或不是目录: " + sourceDir.getAbsolutePath());
        }

        // 创建ZIP文件的父目录
        if (!zipFile.getParentFile().exists()) {
            zipFile.getParentFile().mkdirs();
        }

        try (FileOutputStream fos = new FileOutputStream(zipFile);
             BufferedOutputStream bos = new BufferedOutputStream(fos);
             ZipOutputStream zos = new ZipOutputStream(bos)) {

            // 设置压缩级别
            zos.setLevel(Deflater.BEST_SPEED);

            // 递归添加文件
            fileCount = addFilesToZip(sourceDir, "", zos);

            // 确保所有数据都写入
            zos.flush();
            bos.flush();
            fos.flush();
        }

        // 验证ZIP文件
        if (fileCount > 0 && (!zipFile.exists() || zipFile.length() == 0)) {
            throw new IOException("ZIP文件创建失败: " + zipFile.getAbsolutePath());
        }

        return fileCount;
    }

    /**
     * 递归添加文件到ZIP
     */
    private int addFilesToZip(File source, String parentPath, ZipOutputStream zos) throws IOException {
        int count = 0;
        byte[] buffer = new byte[8192];

        File[] files = source.listFiles();
        if (files == null) {
            return 0;
        }

        for (File file : files) {
            // 跳过ZIP文件本身
            if (file.getName().endsWith(".zip")) {
                continue;
            }

            // 构建ZIP内的路径
            String path = parentPath.isEmpty() ? file.getName() : parentPath + "/" + file.getName();

            if (file.isDirectory()) {
                // 递归处理目录
                count += addFilesToZip(file, path, zos);
                continue;
            }

            // 检查文件是否可读
            if (!file.canRead()) {
                log.warn("文件不可读，跳过: {}", file.getAbsolutePath());
                continue;
            }

            try {
                // 创建新的ZIP条目
                ZipEntry entry = new ZipEntry(path);
                entry.setTime(file.lastModified());
                zos.putNextEntry(entry);

                // 写入文件内容
                try (FileInputStream fis = new FileInputStream(file);
                     BufferedInputStream bis = new BufferedInputStream(fis)) {

                    int len;
                    while ((len = bis.read(buffer)) > 0) {
                        zos.write(buffer, 0, len);
                    }
                    count++;

                    log.debug("添加文件到ZIP: {} - 大小: {}KB", path, file.length() / 1024);
                }

                // 确保关闭当前条目
                zos.closeEntry();

            } catch (IOException e) {
                log.error("添加文件到ZIP失败: {} - 错误: {}", file.getAbsolutePath(), e.getMessage());
                throw e;
            }
        }

        return count;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(String tempDir, File zipFile) {
        try {
            // 删除ZIP文件
            if (zipFile != null && zipFile.exists()) {
                if (!zipFile.delete()) {
                    log.warn("无法删除ZIP文件: {}", zipFile.getAbsolutePath());
                }
            }

            // 删除临时目录
            if (tempDir != null) {
                File dir = new File(tempDir);
                if (dir.exists()) {
                    FileUtils.deleteDirectory(dir);
                    log.debug("清理临时目录: {}", tempDir);
                }
            }
        } catch (IOException e) {
            log.error("清理临时文件失败: {}", e.getMessage());
        }
    }

    /**
     * 复制文件到临时目录
     * @param record 文件记录
     * @param billDir 目标目录
     * @throws IOException 文件操作异常
     */
    private void copyFileToTemp(UploadAddressDomain record, File billDir) throws IOException {
        String filePath = record.getUrl();
        if (StringUtils.isEmpty(filePath)) {
            log.warn("文件路径为空: {}", record);
            throw new IOException("文件路径为空");
        }

        // 确保路径使用正确的分隔符
        filePath = filePath.replace('\\', '/');

        // 检查文件是否存在
        File sourceFile = new File(filePath);
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            // 尝试从根目录读取
            sourceFile = new File("/" + filePath);
            if (!sourceFile.exists() || !sourceFile.isFile()) {
                log.error("源文件不存在: {} - 绝对路径: {}", filePath, sourceFile.getAbsolutePath());
                throw new IOException("文件不存在: " + filePath);
            }
        }

        // 检查文件权限
        if (!sourceFile.canRead()) {
            try {
                log.error("文件没有读取权限: {} - 所有者: {} - 权限: {}",
                    filePath,
                    Files.getOwner(sourceFile.toPath()),
                    Files.getPosixFilePermissions(sourceFile.toPath()));
            } catch (Exception e) {
                log.error("无法获取文件权限信息: {}", e.getMessage());
            }
            throw new IOException("文件没有读取权限: " + filePath);
        }

        // 获取目标文件名并确保有.pdf后缀
        String targetFileName = record.getDataName();
        if (StringUtils.isEmpty(targetFileName)) {
            targetFileName = sourceFile.getName();
        }

        // 如果文件名没有.pdf后缀，添加它
        if (!targetFileName.toLowerCase().endsWith(".pdf")) {
            targetFileName += ".pdf";
        }

        // 确保目标目录存在
        if (!billDir.exists() && !billDir.mkdirs()) {
            throw new IOException("无法创建目标目录: " + billDir.getAbsolutePath());
        }

        File targetFile = new File(billDir, targetFileName);

        try {
            // 使用NIO复制文件
            Files.copy(sourceFile.toPath(), targetFile.toPath(),
                StandardCopyOption.REPLACE_EXISTING);

            // 验证复制后的文件
            if (!targetFile.exists() || targetFile.length() != sourceFile.length()) {
                throw new IOException("文件复制验证失败");
            }

            log.info("文件复制成功 - 源文件: {} ({}KB) -> 目标文件: {} ({}KB)",
                sourceFile.getAbsolutePath(), sourceFile.length() / 1024,
                targetFile.getAbsolutePath(), targetFile.length() / 1024);

        } catch (IOException e) {
            log.error("文件复制失败 - 源文件: {} -> 目标文件: {} - 错误: {}",
                sourceFile.getAbsolutePath(), targetFile.getAbsolutePath(), e.getMessage());
            throw e;
        }
    }

}

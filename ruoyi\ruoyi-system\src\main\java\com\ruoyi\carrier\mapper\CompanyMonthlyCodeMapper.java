package com.ruoyi.carrier.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.carrier.domain.CompanyMonthlyCode;
import com.ruoyi.carrier.domain.bo.CompanyMonthlyCodeBO;
import com.ruoyi.carrier.domain.vo.CompanyMonthlyCodeVO;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/12/24 11:27
 */
public interface CompanyMonthlyCodeMapper extends BaseMapper<CompanyMonthlyCode> {

    /**
     * 获取月结码列表
     * @param companyMonthlyCodeBO
     * @return
     */
    List<CompanyMonthlyCodeVO> getMonthlyCodeList(CompanyMonthlyCodeBO companyMonthlyCodeBO);

    int updateCode(CompanyMonthlyCode companyMonthlyCode);
}

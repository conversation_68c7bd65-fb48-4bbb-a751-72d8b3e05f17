package com.ruoyi.common.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/11 16:05
 */
@Setter
@Getter
@ToString
public class CoutformBO extends BaseEntity {

    /**
     * 出库单类型 1.本司 2.它司
     */
    private String wxOutorinformType;
    //客户名称
    private String customerName;
    //客户编号
    private String customerId;
    // 所属公司id
    private Long companyId;
    /**
     * 出库单号
     */
    private String coutformId;
}

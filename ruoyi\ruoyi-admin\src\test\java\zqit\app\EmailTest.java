package zqit.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.RuoYiApplication;
import com.ruoyi.common.domain.BargeInfoAudit;
import com.ruoyi.common.domain.OLPicture;
import com.ruoyi.common.domain.bo.CoutformBO;
import com.ruoyi.common.domain.bo.UploadBO;
import com.ruoyi.common.enums.CheckEnum;
import com.ruoyi.common.enums.CheckFlagEnum;
import com.ruoyi.common.mapper.BargeInfoAuditMapper;
import com.ruoyi.common.mapper.OlUserMapper;
import com.ruoyi.common.service.*;
import com.ruoyi.common.utils.message.WechatMessageUtil;
import com.ruoyi.common.utils.message.request.ReqPo;
import com.ruoyi.common.utils.message.request.mpTem.DataValue;
import com.ruoyi.common.utils.message.request.mpTem.Miniprogram;
import com.ruoyi.common.utils.message.request.weappTem.WechatTemplate;
import com.ruoyi.common.utils.message.responce.RespPo;
import com.ruoyi.common.utils.message.tmpl.impl.TmplBaseImpl;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.ssouser.domain.SSOUser;
import com.ruoyi.ssouser.service.SSOUserSerivce;
import com.ruoyi.system.mapper.SysMenuMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { RuoYiApplication.class })
public class EmailTest {
	//@Autowired
	CargocmentService cargocmentService;

	//@Autowired
	private OlPictureService olPictureService;

    //@Autowired
    WechatMessageUtil wechatMessageUtil;
    @Autowired
    SSOUserSerivce  ssoUserSerivce;
	@Autowired
	CoutformService coutformService;
	@Autowired
	private BargeInfoService bargeInfoService;
	@Autowired
	private BargeInfoAuditMapper bargeInfoAuditMapper;
	@Autowired
	private SysMenuMapper sysRoleMapper;
	@Autowired
	private FtpService ftpService;

	//@Autowired
	OlUserMapper olUserMapper;
	@Test
	public void test0(){
		String businessType="TD";
		Long comId=1L;
		//String date= DateUtils.dateTimeNow("yyMM");
		String date="1802";
		int length=4;
		String newSerialNo = cargocmentService.getNewSerialNo(businessType, comId, date, length);
		System.out.println(newSerialNo);
	}
	@Test
	public void test1(){
		List<OLPicture> list=new ArrayList<>();

		for(int i=0;i<2;i++){
			OLPicture olPicture=new OLPicture();
			olPicture.setUserId("admin00");
			olPicture.setImageInfo("sdade"+i);
			olPicture.setImageUrl("sdade"+i);
			olPicture.setImageName("sdade"+i);
			olPicture.setImageType("png");
			list.add(olPicture);

		}
		boolean b = olPictureService.saveBatch(list);
	}
	@Test
    public void test2() throws Exception {
        Miniprogram miniprogram =new Miniprogram();
        TmplBaseImpl tmplBase=new TmplBaseImpl(
                new DataValue("您好"),
                new DataValue("测试公司"),
                new DataValue("产品测试"),
                new DataValue("购买1吨"),
                new DataValue("2020.8.24"),
                new DataValue("参考模拟数据"),
                null,
                "8YJpH4isAwNg2oj96aeAJw4LvK2xgLwfPsDtFD91JtI"
        );
        ReqPo reqPo = wechatMessageUtil.packagePublicReqPo(tmplBase,"oImfA0Q2yoMLIVbnN0DF73idNNM8");

        System.out.println(reqPo.toString());
        RespPo respPo = wechatMessageUtil.uniformSend(reqPo);
    }
    @Test
    public void test3() throws Exception {
        Miniprogram miniprogram =new Miniprogram();
        TmplBaseImpl tmplBase=new TmplBaseImpl(
                new DataValue("您好"),
                new DataValue("测试公司"),
                new DataValue("产品测试"),
                new DataValue("购买1吨"),
                new DataValue("2020.8.24"),
                new DataValue("参考模拟数据"),
                miniprogram,
                "8YJpH4isAwNg2oj96aeAJw4LvK2xgLwfPsDtFD91JtI"
        );



        WechatTemplate wechatTemplate=new WechatTemplate();
        wechatTemplate.setTouser("oImfA0Q2yoMLIVbnN0DF73idNNM8");
        wechatTemplate.setTemplate_id("8YJpH4isAwNg2oj96aeAJw4LvK2xgLwfPsDtFD91JtI");
        wechatTemplate.setData(tmplBase.getData());
        String publicAccessToken = wechatMessageUtil.getPublicAccessToken();
        RespPo respPo = wechatMessageUtil.templateMsgSend(wechatTemplate, publicAccessToken);
        System.out.println(respPo.getErrcode());

    }
	@Test
    public void test4(){
		SSOUser ssoUser=new SSOUser();
		ssoUser.setAccount("username");
		SSOUser ssoUser1 = ssoUserSerivce.getSSOUser(ssoUser);
		System.out.println(ssoUser1);
		System.out.println(Md5Utils.MD5EncodeUtf8("User1234"));
		System.out.println(Md5Utils.MD5EncodeUtf8("123456"));
	}

	@Test
	public void test() {
		CoutformBO coutformBO = new CoutformBO();
		//coutformBO.setCompanyId(36947l);
		//coutformBO.setWxOutorinformType("1");
		coutformBO.setCustomerName("捷达国际");
		System.out.println(123);
		//List<CoutformVO> coutformList = coutformService.getCoutformList(coutformBO);
	}

	@Test
	public void test01() {
		/*Map<String,String> map = new HashMap<>();
		String url = "";*/
		//map.put("",);
		//String lrpResult = HttpRequest.post(url).body(array.toString()).execute().body();
		//JSONUtil.toBean(, .class);
		/*sysRoleMapper.selectMenuListByRoleId(3L);
		System.out.println(sysRoleMapper.selectMenuListByRoleId(3L));*/
		File file = new File("D:\\27963\\桌面\\测试-素材\\images\\测试_柴犬_02.jpg");
		UploadBO uploadBO = new UploadBO(4L, "40", 2452L, 41);
		ftpService.ftpUpload((MultipartFile) file, uploadBO);
	}

	@Test
	@Rollback(value = false)
	public void addBarge() {
		/*BargeInfoBO bargeInfoBO = new BargeInfoBO();
		bargeInfoBO.setBargeCompanyId(92242L);
		List<BargeInfoVO> bargeList = bargeInfoService.getBargeListByCompany(bargeInfoBO);*/

		QueryWrapper<BargeInfoAudit> wrapperBarge = new QueryWrapper<>();
		wrapperBarge
				.eq("UPDATECHECK", CheckEnum.PASS_CHECK.getCode())
				.eq("CHECKFLAG", CheckFlagEnum.UPDATE_BARGE_AUDIT.getCode())
				.eq("ISDELETE", 0)
				.eq("RECORDERID", 119)
				.or()
				.eq("RECORDCHECK", CheckEnum.PASS_CHECK.getCode())
				.eq("CHECKFLAG", CheckFlagEnum.BARGE_AUDIT.getCode())
				.eq("ISDELETE", 0)
				.eq("RECORDERID", 119);
		List<BargeInfoAudit> audit = bargeInfoAuditMapper.selectList(wrapperBarge);
	}

	@Test
	public void base64Test() {
		String url = "/root/gzgtest/bargeRecord/201/tmp_3284f1ec7ca4ed703bc7818a0455d62440aa32ae0fead6db.jpg";
		String[] u = url.split("/gzgtest");
		//File file = FileUtil.file("https://bulkcustomer.gzport.com" + u[1]);
		//FileReader fileReader = FileReader.create(file);

		try {
			URL urls =new URL("https://bulkcustomer.gzport.com" + u[1]); // 创建URL
			URLConnection urlconn = urls.openConnection(); // 试图连接并取得返回状态码
			urlconn.connect();
			InputStream inputStream = urlconn.getInputStream();
			ByteArrayOutputStream outStream =new ByteArrayOutputStream();
			byte[] b = new byte[2048];
			int len;
			while((len=inputStream.read(b))!=-1){
				outStream.write(b,0,len);
			}
			String base64 = Base64.encode(outStream.toByteArray());
			System.out.println(base64);
		} catch (IOException e) {
			e.printStackTrace();
		}


		//byte[] b = fileReader.readBytes();
		/*String url = "/var/www/olorder/ConsignPicture/232/tmp_562816749aaf43de4fd3115ac42f9e0ca0280c9ce0f392c7.jpg";
		String base64 = ftpUtils.downloadBase64(url);
		System.out.println(base64);*/

	}
}

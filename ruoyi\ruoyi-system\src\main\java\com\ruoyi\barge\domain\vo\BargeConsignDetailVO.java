package com.ruoyi.barge.domain.vo;

import com.ruoyi.common.domain.UploadAddress;
import lombok.*;

import java.util.List;

/**
 * 驳船主托运单详情VO
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/13 13:06
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class BargeConsignDetailVO {

    /**
     * 文件列表
     */
    List<UploadAddress> dataList;

    /**
     * 船名称
     */
    private String bargeName;

    /**
     * 托运单号
     */
    private String consignFlag;

    /**
     * 运单主表id
     */
    private Long consignId;

    /**
     * 月结公司Id
     */
    private String wxMonthChargeById;

    /**
     * 月结公司名称
     */
    private String wxMonthChargeByName;

    /**
     * 状态
     */
    private Integer wxOperateState;

    /**
     * 审核状态
     */
    private Integer wxConfirmCheck;

    /**
     * 创建时间-办单时间
     */
    private String applyDate;

    /**
     * 申请日期
     */
    private String recordDate;

    /**
     * 出库单号、
     */
    private String outOrInformId;

    /**
     * 货物名称、
     */
    private String cargeName;

    /**
     * 托运人公司id、
     */
    private String comId;

    /**
     * 托运人公司
     */
    private String shipPerDept;

    /**
     * 发货人consigner
     */
    private String consigner;

    /**
     * 地磅单号loadometerId
     */
    private String loadometerId;

    /**
     * 提单号consignmentFlag
     */
    private String consignmentFlag;

    /**
     * 收货人、
     */
    private String consignee;

    /**
     * 起运港、
     */
    private String beginPort;

    /**
     * 目的港、
     */
    private String endPort;

    /**
     *中转港、
     */
    private String midPort;

    /**
     *装货地点、
     */
    private String shipmentPlace;

    /**
     *卸货地点、
     */
    private String shipmentunPlace;

    /**
     * 包装方式、
     */
    private String packageType;

    /**
     * 可配载重量、
     */


    /**
     * 总配载重量、
     */


    /**
     * 承运船舶公司、
     */
    private String shippingCoName;

    /**
     * 托运申请件数、
     */
    private String rationPiece;

    /**
     * 船舶识别号、
     */
    private String bargeId;

    /**
     * 托运单重量、
     */
    private String rationWeight;

    /**
     * 托运单联系人、
     */
    private String wxRationContactNumber;

    /**
     * 驳船主联系电话、
     */
    private String bargeTel;

    /**
     * 驳船状态、
     */
    private String flagBargeState;

    /**
     * 托运单审核意见、
     */
    private String cargoConsignCheckReason;

    /**
     * 费用结算方式、
     */
    private String chargeBalanceType;

    /**
     * 月结客户、
     */
    private String shipowner;

    /**
     * 货方联系方式、
     */
    private String customerUserId;

    /**
     * 托运单实际装货量、
     */
    private String workWeight;

    /**
     * 是否加急、
     */
    private String isHarry;

    /**
     * 是否申请到验号、
     */
    private String isApplyUniqueCode;

    /**
     * 特约事项、
     */
    private String specialProceeding;

    /**
     * 预约时间
     */
    private String wxAppointmentTime;

    /**
     * 水路运单号
     */
    private String waterWayCargoId;

    /**
     * 计费吨、
     */
    private String chargeWeight;

    /**
     * 运费、
     */
    private String transportCharge;

    /**
     * 货物港务费
     */
    private String cargoPortCharge;

    /**
     * 围油栏费
     */
    private String serviceAgentCharge;

    /**
     * 代理费
     */
    private String businessAgentCharge;

    /**
     * 停泊费
     */
    private String berthCharge;

    /**
     * 总费用
     */
    private String totalCharge;

    /**
     * 退改单状态 0为申请退单中，1为申请退单通过，2为申请退单失败,3为申请改单中，4为申请改单成功，5为申请改单失败
     */
    private String applyModify;

    /**
     * 月结码
     */
    private String monthlyCode;

    /**
     *  其他 沿海/内河 港口（2021-02-07添加）
     */
    private String otherCoastalInlandPort;


    private String tscargoweightValue; //实装吨数

    /**
     * 驳船挂靠公司名称
     */
    private String bargeCallCompanyName;
}

package com.ruoyi.consignor.service;

import com.ruoyi.barge.domain.bo.BargeUserBO;
import com.ruoyi.barge.domain.vo.BargeCapacityPublishVO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.Cargoconsignment;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.Customer;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.bo.CargocmentBO;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.bo.CoutformBO;
import com.ruoyi.common.domain.dto.CargocmentDTO;
import com.ruoyi.common.domain.vo.BargeInfoVO;
import com.ruoyi.common.domain.vo.PubPortVo;
import com.ruoyi.common.domain.vo.WaterwayCargoVO;
import com.ruoyi.common.domain.vo.CoutformVO;
import com.ruoyi.consignor.domain.vo.ConsignorConsignVO;

import java.util.List;

/**
 * @Description 货主的托运业务层接口
 * <AUTHOR>
 * @Date 2020/7/30  10:53
 */
public interface ConsignorConsignService {

    // 查询托运单列表
    List<ConsignorConsignVO> selectConsignByStatus(Long status, String searchValue, String beginTime, String endTime);

    // 托运单详情
    CargocmentBO selectConsignById(Long consignId);

    // 修改托运单信息
    void updateBookingNote(CargocmentBO cargocmentBO);

    // 办理托运单
    void transactBookingNote(CargocmentBO cargocmentBO);

    // 托运单-改单/退单
    void modificationBookingNote(CargocmentDTO cargocmentDTO);

    // 匹配出库单
    List<CoutformVO> selectCoutformByCoutformId(CoutformBO coutformBO);

    // 匹配船公司显示
    List<Customer> selectShipCompanyByShipCompanyName(String shipCompanyName);

    // 查询用户派过的驳船
    List<BargeInfo> recordBarge();


    // 搜索驳船
    List<BargeInfoVO> searchBarge(String searchValue);

    // 搜索目的港
    List<PubPortVo> searchEndPort(String searchValue);

    // 水路运单详情
    WaterwayCargoVO waybillDetail(Long id, Integer status);

    // 查询历史收货人
    List<String> consignee();

    // 删除托运单
    void deleteBookingNote(CargocmentBO cargocmentBO);

    // 取消改单/退单操作
    void cancelOperation(CargocmentdetailBO cargocmentdetailBO);

    // 个人信息保存
    AjaxResult personRecord(BargeUserBO bargeUserBO);

    // 个人信息详情
    AjaxResult recordDetail();

    //查询出库单件数
    String checkAmt(String outOrInFormId);
}

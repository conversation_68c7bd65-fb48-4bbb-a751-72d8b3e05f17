package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @description 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/18 10:51
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName( value = "pb30_ol_user")
@KeySequence("SEQ_PB30_OL_USER")
public class OlUser {

    /**
    * 主键id
    */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
    * 账户名称，用户名
    */
    @TableField("USERID")
    private String userId;

    /**
    * 密码
    */
    @TableField("PASSWORD")
    private String password;

    /**
    * 客户类型:1货主 2货代. 3船代 4驳船主 5.船公司
    */
    @TableField("CUSTOMERTYPEID")
    private Long customerTypeId;

    /**
    * 联系人
    */
    @TableField("CONTACTPERSON")
    private String contactPerson;

    /**
    * 电话
    */
    @TableField("CONTACTCELLPHONE")
    private String contactCellPhone;

    /**
    * 角色id
    */
    @TableField("ROLEID")
    private Integer roleId;

    /**
    * 注册时间
    */
    @TableField("REGDATE")
    private String regDate;

    /**
    * 审核
    */
    @TableField("ISAUDIT")
    private String isAudit;

    /**
    * 审核人
    */
    @TableField("AUDITPERSON")
    private String auditPerson;

    /**
    * 审核时间
    */
    @TableField("AUDITDATE")
    private String auditDate;

    /**
    * 备注
    */
    @TableField("REMARK")
    private String remark;

    /**
    *
    */
    @TableField("COMID")
    private String comId;

    /**
    * 公司名称
    */
    @TableField("COMPANY")
    private String company;

    /**
    * 身份证id
    */
    @TableField("CID")
    private String cid;

    /**
    * 照片url
    */
    @TableField("PICTURE")
    private String picture;

    /**
    * 公司ID(关联PB1_customer主键ID)
    */
    @TableField("COMPANYID")
    private Long companyId;

    /**
    * 有效日期（出库单添加）
    */
    @TableField("ACTTIME")
    private String actTime;

    /**
    * 用户通过审核的港口ID
    */
    @TableField("COMAUDITID")
    private String comAuditId;

    /**
    * 将权限赋予此用户的用户id
    */
    @TableField("AUTHUSER")
    private String authUser;

    /**
    * 最后修改时间
    */
    @TableField("MODIFYTIMESTAMP")
    private String modifyTimestamp;

    /**
    * token
    */
    @TableField("TOKEN")
    private String token;

    /**
    * 用户合法时间记录
    */
    @TableField("LEGALDATE")
    private String legalDate;

    /**
    * 审核通过的港口
    */
    @TableField("PASSPORT")
    private String passport;

    /**
    * 驳船标志
    */
    @TableField("SHIPSIG")
    private String shipSig;

    /**
    * 驳船名称
    */
    @TableField("SHIPNAME")
    private String shipName;

    /**
    * 微信id
    */
    @TableField("OPENID")
    private String openid;

    @TableField("UPDATETIME")
    private String upDateTime;
}
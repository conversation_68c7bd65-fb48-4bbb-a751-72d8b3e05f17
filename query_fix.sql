-- 修正后的查询方案1：处理数据类型和空值问题
SELECT 
    BARGEOWNER,
    COUNT(DISTINCT TO_CHAR(RECORDERID)) as recorder_count,
    COUNT(DISTINCT ID) as id_count,
    COUNT(*) as total_records,
    LISTAGG(DISTINCT TO_CHAR(RECORDERID), ',') WITHIN GROUP (ORDER BY TO_CHAR(RECORDERID)) as recorder_ids,
    LISTAGG(TO_CHAR(ID), ',') WITHIN GROUP (ORDER BY ID) as barge_ids,
    LISTAGG(BARGENAME, ',') WITHIN GROUP (ORDER BY BARGENAME) as barge_names
FROM PB6_BARGEINFO 
WHERE BARGEOWNER IS NOT NULL
AND TRIM(BARGEOWNER) != ''
AND RECORDERID IS NOT NULL
AND RECORDERID != 0  -- 排除可能的0值
GROUP BY BARGEOWNER 
HAVING COUNT(DISTINCT TO_CHAR(RECORDERID)) > 1
ORDER BY recorder_count DESC, BARGEOWNER;

-- 修正后的查询方案2：更严格的条件检查
SELECT 
    BARGEOWNER,
    COUNT(DISTINCT RECORDERID) as recorder_count,
    COUNT(DISTINCT ID) as id_count,
    COUNT(*) as total_records,
    LISTAGG(DISTINCT RECORDERID, ',') WITHIN GROUP (ORDER BY RECORDERID) as recorder_ids,
    LISTAGG(ID, ',') WITHIN GROUP (ORDER BY ID) as barge_ids,
    LISTAGG(BARGENAME, ',') WITHIN GROUP (ORDER BY BARGENAME) as barge_names
FROM PB6_BARGEINFO 
WHERE BARGEOWNER IS NOT NULL
AND LENGTH(TRIM(BARGEOWNER)) > 0  -- 更严格的非空检查
AND RECORDERID IS NOT NULL
AND RECORDERID > 0  -- 确保是有效的ID值
GROUP BY BARGEOWNER 
HAVING COUNT(DISTINCT RECORDERID) > 1
ORDER BY recorder_count DESC, BARGEOWNER;

-- 调试查询：先查看具体数据
SELECT 
    BARGEOWNER,
    RECORDERID,
    ID,
    BARGENAME,
    RECORDDATE
FROM PB6_BARGEINFO 
WHERE BARGEOWNER = '林丽霞'  -- 使用您提供的具体例子
ORDER BY RECORDERID, ID;

-- 进一步调试：检查数据类型和值
SELECT 
    BARGEOWNER,
    RECORDERID,
    TO_CHAR(RECORDERID) as recorderid_str,
    TYPEOF(RECORDERID) as recorderid_type,
    ID,
    BARGENAME
FROM PB6_BARGEINFO 
WHERE BARGEOWNER IS NOT NULL
AND TRIM(BARGEOWNER) != ''
AND RECORDERID IS NOT NULL
ORDER BY BARGEOWNER, RECORDERID;

-- 最简化的测试查询
SELECT DISTINCT 
    BARGEOWNER,
    RECORDERID
FROM PB6_BARGEINFO 
WHERE BARGEOWNER = '林丽霞'
ORDER BY RECORDERID;

package com.ruoyi.common.enums;

public enum UserType {
    //用户类型（00系统用户 01注册用户 10货主 11托运单联系人 12船公司管理员 13船公司业务员 14驳船管理员 15驳船业务员 16物流公司管理员）
    CONSIGNOR("10","货主"),
    WAYBILLCONTACT("11","托运单联系人"),
    CARRIERADMIN("12","船公司管理员"),
    CARRIEUSER("13","船公司业务员"),
    BARGEADMIN("14","驳船管理员"),
    BARGEUSER("15","驳船业务员"),
    PUBUSER("16", "生产系统仓库员");

    private final String code;
    private final String codeName;
    UserType(String code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public String getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

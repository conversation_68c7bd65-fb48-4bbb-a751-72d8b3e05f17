// pages/minepage/crewlist/crewlist.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //用户类型userType：00系统用户， 01-注册用户，10-货主，11-托运单联系人，12-船公司管理员，13-船公司业务员，14-驳船管理员，15-驳船业务员
    userInfos:"",//用户信息
    //搜素关键字
    searchValue:"",
    //船员列表
    crewList: []
  },

  /**
   * 
   * 方法
   */
    /**
   * 判断是否存在，不存在返回空字符串或其他
   * @param {传进来的变量} empty 
   * @param {为空时返回的值} replace 
   */
  emptyReturn(empty,replace){
    if(!replace || replace==null){
      replace = ""
    }
    if(empty == null || !empty){
      return replace
    }else{
      return empty
    }
  },

  //清空关键字
  deleteSearchValue(){
    this.setData({
      "searchValue":""
    })
    console.log(this.data.searchValue)
  },

  //搜索input
  searchHandle(e){
    let that = this
    let searchValue = e.detail.value 
  
    that.setData({
      searchValue,
    })
    setTimeout(()=>{
      that.getCrewListDatas()
    },1500)
    
  },

  //获取船员列表
  getCrewListDatas(){
    let that = this 
    let searchValue = that.data.searchValue 
    //驳船id
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息
    if(!userInfos.bargeId){
      app.$message("驳船id参数缺失")
      return
    }
    let param = {
      searchValue,
      bargeId:userInfos.bargeId,// 驳船id主键
      userId:userInfos.userId,//用户id
    }
    app.$post(app.$url.mine.getCrewList,param).then(res=>{
      if(res.code == 200){
        res.data.map(item=>{
          item.nickName = that.emptyReturn(item.nickName)
          item.phonenumber = that.emptyReturn(item.phonenumber)
          item.checked = false // 用于选中复选框
        })
        that.setData({
          crewList:res.data
        })
      }else {
      app.$message(res.msg)
      }
    })
  },

  //确定
  // submibtEvent(){
  //   wx.navigateBack({
  //     delta: 1
  //   });
  // },

  // 删除船员
  handleDeleteCrew() {
    //驳船id
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息
    let selectedArr = []
    this.data.crewList.map(item=>{
      if(item.checked) {
        let obj = {
          userId: item.userId,
          bargeId: JSON.parse(JSON.stringify(userInfos.bargeId)) //驳船id
        }
        selectedArr.push(obj)
      }
    }) //选中的数组
    if(selectedArr && selectedArr.length == 0) {
      app.$message("请选择删除的成员")
      return
    }
    let param = {
      userBargeList: selectedArr
    }
  
    app.$post(app.$url.mine.delCrew,param).then(res=>{
      if(res.code == 200){
        app.$message(res.msg)
        this.getCrewListDatas()
        
      }else {
        app.$message(res.msg)
      }
    })
  },

  // 复选框change
  checkboxChange(e) {
    console.log('checkbox发生change事件，携带value值为：', e.detail.value)
    const items = this.data.crewList
    const values = e.detail.value
    for (let i = 0, lenI = items.length; i < lenI; ++i) {
      items[i].checked = false
      for (let j = 0, lenJ = values.length; j < lenJ; ++j) {
        if (items[i].userId == values[j]) {
          items[i].checked = true
          break
        }
      }
    }
   
    this.setData({
      crewList:items
    })
  },
  
  // 点击添加船员
  handleAddCrew() {
    wx.navigateTo({
      url: '/pages/minepage/addcrew/addcrew',
      success: (result) => {
        
      },
      fail: () => {},
      complete: () => {}
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息
    this.setData({
      userInfos,
    })
    this.getCrewListDatas()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
 
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 出库单实体
 * <AUTHOR>
 * @Date 2020/8/4 14:51
 */
@Setter
@Getter
@ToString
@TableName(value = "PB3_COUTFORM")
public class Coutform {

    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    @TableField("COUTFORMID")
    private String coutformId;

    @TableField("CUSTOMERID")
    private String customerId;

    @TableField("CUSTOMERNAME")
    private String customerName;

    @TableField("CUSTOMERTEL")
    private String customerTel;

    @TableField("CONSIGNEE")
    private String consignee;

    @TableField("CARGOID")
    private String cargoId;

    @TableField("CARGONAME")
    private String cargoName;

    @TableField("UNBLID")
    private String unblId;

    @TableField("CONTRACTID")
    private String contractId;

    @TableField("RECONTRACTID")
    private String reContractId;

    @TableField("PREBALANCEID")
    private String prebalanceId;

    @TableField("UNIQECODE")
    private String uniqeCode;

    @TableField("FORMLOADOMETERID")
    private String formLoadometerId;

    @TableField("STACKNAME")
    private String stackName;

    @TableField("CARGOFLOW")
    private String cargoFlow;

    @TableField("TRANSPORTTOOL")
    private String transportTool;

    @TableField("MARK")
    private String mark;

    @TableField("PACKAGETYPE")
    private String packageType;

    @TableField("AMT")
    private String amt;

    @TableField("PIECEWEIGHT")
    private String pieceWeight;

    @TableField("WEIGHTVALUE")
    private String weightValue;

    @TableField("CARGOVOLUME")
    private String cargoVolume;

    @TableField("ISPLEDGE")
    private String isPledge;

    @TableField("PLEDGETRANDATE")
    private String pledgeTranDate;

    @TableField("FORMMAN")
    private String formMan;

    @TableField("FORMDATE")
    private String formDate;

    @TableField("REMAINAMT")
    private String remainamt;

    @TableField("REMAINWEIGHT")
    private String remainWeight;

    @TableField("REMAINVOLUME")
    private String remainVolume;

    @TableField("RECKONMNY")
    private String reckonMny;

    @TableField("RECKONMAN")
    private String reckonMan;

    @TableField("CINDATE")
    private String cinDate;

    @TableField("INVALIDDATE")
    private String invalidDate;

    @TableField("FREEDAYS")
    private String freedays;

    @TableField("ISSUEMAN")
    private String issueMan;

    @TableField("ISSUEDATE")
    private String issueDate;

    @TableField("DADCOFORMID")
    private String dadcoFormId;

    @TableField("CHANGECFORMID")
    private String changecFormId;

    @TableField("CHANGEDATE")
    private String changeDate;

    @TableField("RECEIPTMAN")
    private String receiptMan;

    @TableField("RECEIPTDATE")
    private String receiptDate;

    @TableField("FORMCLOSEMAN")
    private String formCloseMan;

    @TableField("FORMCLOSEDATE")
    private String formCloseDate;

    @TableField("REMARK")
    private String remark;

    @TableField("FREMAINAMT")
    private String fremainAmt;

    @TableField("FREMAINWEIGHT")
    private String fremainWeight;

    @TableField("FREMAINVOLUME")
    private String fremainVolume;

    @TableField("DYNAMICWEIGHT")
    private String dynamicWeight;

    @TableField("DYNAMICAMT")
    private String dynamicAmt;

    @TableField("DYNAMICVOLUME")
    private String dynamicVolume;

    @TableField("REALWEIGHT")
    private String realWeight;

    @TableField("REALAMT")
    private String realAmt;

    @TableField("REALVOLUME")
    private String realVolume;

    @TableField("REALPWEIGHT")
    private String realpWeight;

    @TableField("REMAINPWEIGHT")
    private String remainpWeight;

    @TableField("DADRELATION")
    private String dadRelation;

    @TableField("WEIGHTUNIT")
    private String weightUnit;

    @TableField("SHIPNAME")
    private String shipName;

    @TableField("DANGEROUSCARGO")
    private String dangerousCargo;

    @TableField("PLEDGECLASS")
    private String pledgeclass;

    @TableField("CHECKMAN")
    private String checkMan;

    @TableField("CHECKDATE")
    private String checkDate;

    @TableField("BONDEDCARGO")
    private String bondedCargo;

    @TableField("WORKSTYLE")
    private Object workStyle;

    @TableField("CARGOLENGTH")
    private String cargoLength;

    @TableField("CARGOHEIGHT")
    private String cargoHeight;

    @TableField("CARGOWIDTH")
    private String cargoWidth;

    @TableField("SETTLEMENTTYPE")
    private String settlementType;

    @TableField("CUSTOMERBUSINESSMAN")
    private String customerBusinessMan;

    @TableField("PACKAGETYPEID")
    private Long packageTypeId;

    @TableField("TRANSPORTTOOLID")
    private Long transportToolId;

    @TableField("CARGOFLOWID")
    private Long cargoFlowId;

    @TableField("SETTLEMENTTYPEID")
    private Long settlementTypeId;

    @TableField("WORKSTYLEID")
    private Long workStyleId;

    @TableField("CORPORATIONID")
    private Long corporationId;

    @TableField("DEPTID")
    private Long deptId;

    @TableField("CARGOSERIALNUMBER")
    private Long cargoSerialNumber;

    @TableField("IMPAWN")
    private String impawn;

    @TableField("IMPAWNNUMBER")
    private String impawnNumber;

    @TableField("FORMMANID")
    private Long formManId;

    @TableField("RECKONMANID")
    private Long reckonManId;

    @TableField("ISSUREMANID")
    private Long issureManId;

    @TableField("RECEIPTMANID")
    private Long receiptManId;

    @TableField("FORMCLOSEMANID")
    private Long formCloseManId;

    @TableField("ISRATING")
    private String isRating;

    @TableField("CUSTOMERSERIALNUMBER")
    private Long customerSerialNumber;

    @TableField("CONSIGNEEID")
    private Long consigneeId;

    @TableField("STACKNAMESERIALID")
    private Long stackNameSerialId;

    @TableField("UNBLIDSERIALID")
    private Long unBlIdSerialId;

    @TableField("CONTRACTSERIALID")
    private Long contractSerialId;

    @TableField("PREBALANCEIDSERIALID")
    private Long preBalanceIdSerialId;

    @TableField("UNIQECODESERIALID")
    private Long uniqeCodeSerialId;

    @TableField("LOADOMETERSERIALID")
    private Long loadometerSerialId;

    @TableField("DADSERIALID")
    private Long dadSerialId;

    @TableField("CHANGESERIALID")
    private Long changeSerialId;

    @TableField("DETAIN")
    private String detain;

    @TableField("MENDERID")
    private Long menderId;

    @TableField("MENDER")
    private String mender;

    @TableField("MENDERTIME")
    private String menderTime;

    @TableField("COMPLETEFORM")
    private String completeForm;

    @TableField("COUFROMCOST")
    private String coufromcost;

    @TableField("CLOSEFORM")
    private String closeForm;

    @TableField("WORKAREAID")
    private Long workAreaId;

    @TableField("ISIMPORT")
    private String isImport;

    @TableField("DELIVERYRATE")
    private String deliveryRate;

    @TableField("ISTRAN")
    private String isTran;

    @TableField("PRECARGONAME")
    private String preCargoName;

    @TableField("TRANCONTRACTID")
    private String tranContractId;

    @TableField("IMPAWNAMT")
    private String impawnAmt;

    @TableField("DECLARATIONNUMBER")
    private String declarationNumber;

    @TableField("ISAGENT")
    private String isAgent;

    @TableField("COMBINEDADFORMID")
    private String combinedadformId;

    @TableField("CARDTID")
    private String cardtId;

    @TableField("CARDNUM")
    private String cardNum;

    @TableField("CHANGEPART")
    private String changePart;

    @TableField("ORDERNUM")
    private String orderNum;

    @TableField("REASON")
    private String reason;

    @TableField("IMPAWNREASON")
    private String impawnReason;

    @TableField("REALDELIVERYRATE")
    private String realdeliveryrate;

    @TableField("ISCANCEL")
    private String isCancel;

    @TableField("PUBESTIMATIONWEIGHT")
    private String pubestimationWeight;

    @TableField("DIRECTION")
    private String direction;

    @TableField("STACKRENT")
    private String stackRent;

    @TableField("SHIPVOPXY")
    private String shipvopxy;

    @TableField("APPLYNO")
    private String applyNo;

    @TableField("ISXSCANCEL")
    private String isXsCancel;

    @TableField("OVERCARGO")
    private String overCargo;

    @TableField("TELEPHONE")
    private String telephone;

    @TableField("ONLINERESOURCE")
    private String onlineResource;

    @TableField("RECEIVED")
    private String received;

    @TableField("USERID")
    private Long userId;

    @TableField("ISCHECK")
    private String isCheck;

    @TableField("NOPASSREASON")
    private String noPassReason;

    @TableField("ISFIXED")
    private String isFixed;

    @TableField("SYWEIGHT")
    private String syWeight;

    @TableField("IDCONTRACTID")
    private Long idcontractId;

    @TableField("USERNAME")
    private String userName;

    @TableField("WQQT")
    private String wqqt;

    @TableField("PAYCHECK")
    private String paycheck;

    @TableField("ONLINEREMARK")
    private String onlineRemark;

    /*@TableField("VERSION")
    private Long version;*/

    @TableField("ISELECTRIC")
    private String isElectric;

    @TableField("FEEBELONG")
    private String feebelong;

}
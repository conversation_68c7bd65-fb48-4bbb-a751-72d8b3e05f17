package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.basic.domain.vo.ShipPersonVo;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.common.domain.vo.WaterwayCargoVO;
import com.ruoyi.databarge.domain.Pb6Bargeinfo;
import com.ruoyi.databarge.domain.Pb6WaterCargoMobile;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.domain.dto.Pb6WaterCargoSearchDTO;
import com.ruoyi.databarge.domain.dto.XGPdfDTO;
import com.ruoyi.databarge.domain.vo.Pb6WaterCargoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface Pb6WaterwaycargoMapper extends BaseMapper<Pb6Waterwaycargo> {
    Pb6Waterwaycargo searchWaterwaycargoByWaterwaycargoid(@Param("Waterwaycargoid") String Waterwaycargoid);

    IPage<Pb6WaterCargoVO> searchPagePb6WaterCargo(Page<Pb6Bargeinfo> page, @Param("pb6WaterCargoSearchDTO") Pb6WaterCargoSearchDTO pb6WaterCargoSearchDTO);

    List<Pb6WaterCargoVO> searchPagePb6WaterCargo(@Param("pb6WaterCargoSearchDTO") Pb6WaterCargoSearchDTO pb6WaterCargoSearchDTO);

    List<Pb6WaterCargoMobile> searchPb6WaterCargoForMobile(@Param("status") int status);

    List<Pb6WaterCargoMobile> searchPb6WaterCargoForMobileWorking(@Param("keyWord") String keyWord);

    Long searchLoadMeterId(@Param("loadometerid") String loadometerid);
    List<String> waterwayCargoIdStamp(@Param("applytime") String applytime);
    List<String> waterwayCargoIdStampaaa();
    List<XGPdfDTO> searchXGPdf(@Param("Waterwaycargoid") String Waterwaycargoid);

    List<Pb6WaterCargoVO> selectNLConfirmAuto(@Param("startTime")String start,@Param("endTime") String end);

    boolean updateAuto(WaterwayCargo waterwayCargo);

    //新增方法

    // 水路运单List
    List<Pb6Waterwaycargo> selectPb6WaterwaycargoList(Pb6Waterwaycargo pb6Waterwaycargo);

    // 根据船名航次列表查询水路运单
    List<Pb6Waterwaycargo> selectPb6WaterwaycargoListByShipAndVoyage(@Param("shipPersonVos") List<ShipPersonVo> shipPersonVos, @Param("pb6Waterwaycargo") Pb6Waterwaycargo pb6Waterwaycargo);




}

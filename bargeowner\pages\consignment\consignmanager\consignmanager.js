// pages/consignment/consignmanager/consignmanager.js
const app = getApp()

// 日期格式化函数
function formatDate(date, format) {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  if (format === 'yyyy-MM-dd HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } else if (format === 'yyyy年MM月dd日') {
    return `${year}年${month}月${day}日`;
  }
  return `${year}-${month}-${day}`;
}

Page({

  /**
   * 页面的初始数据
   */
  data: {
    //用户类型userType：00系统用户， 01-注册用户，10-货主，11-托运单联系人，12-船公司管理员，13-船公司业务员，14-驳船管理员，15-驳船业务员
    userInfos:"",//用户信息
    rolePermission:{},//用户权限
    activeTab: 1,
    // 查询条件参数
    form: {
      //三个为空:全部的状态值
      searchValue:"",//搜索关键字
      wxOperateState:"0",// 状态(0:待驳船主确认 1:待支付/待预约 5:驳船主已预约（待报道）)
      flagBargeState:"",//状态（5：已完成）
      beginTime:'',
      endTime:''
    },
    //托运列表
    listData: [],
    //确认弹窗表单-提交参数
    confirmForm:{
      id:"",
      rationWeight:"", // 配载吨数
      chargeBalanceType:0,//支付方式（0-月结，1-现结）
      state:"",// 1-提交审核，2-确认无误
      endPort:"",//到达港
    },
    //是否显示确认弹窗
    showConfirmDialog:false,
    //是否显示取消确认弹窗
    showCancelConfirmDialog:false,
    //确认弹窗按钮
    confirmButtons: [{text: '取消'}, {text: '确认'}],
    //流程状态数组 状态(0:待驳船主确认(待确认) 1:待支付  2:待船公司审批(月结)，3船公司审批不通过(月结) 4:待驳船主预约（待预约） 5:驳船主已预约（待报道） 6:取消预约 7:待审核(退单改单) 8:审核通过(退单改单) 9:审核不通过(退单改单))
    processStateArr:[
      {
        value:"0",
        label:"待确认"
      },{
        value:1,
        label:"待支付"
      },{
        value:2,
        label:"待船公司审批(月结)"
      },{
        value:3,
        label:"船公司审批不通过(月结)"
      },{
        value:4,
        label:"待预约"
      },{
        value:5,
        label:"待报到"
      },{
        value:6,
        label:"取消预约"
      },{
        value:7,
        label:"待审核(退单改单)"
      },{
        value:8,
        label:"审核通过(退单改单)"
      },{
        value:9,
        label:"审核不通过(退单改单)"
      }
    ],
    // applyModify 0为申请退单中，1为已退单（申请退单通过），2为申请退单失败,3为申请改单中，4为申请改单成功，5为申请改单失败
    applyModifyArray: [
      {
        value:"0",
        label:"申请退单中"
      },{
        value:1,
        label:"已退单"
      },{
        value:2,
        label:"申请退单失败"
      },{
        value:3,
        label:"申请改单中"
      },{
        value:4,
        label:"申请改单成功"
      },{
        value:5,
        label:"申请改单失败"
      }
    ],
    //退单弹窗是否显示
    chargeBackDialog:false,
    //退单弹窗按钮
    chargeBackButtons: [{text: '确定'}, {text: '取消'}],
    //退单弹窗表单
    chargeBackForm:{
      modifyReason:"",//退单原因
    },
    //当前运力对象
    currentConsignObj:{},
    //是否显示取消预约弹窗
    showCancelReservationDialog:false,
    //取消预约弹窗的按钮
    cancelReservationButtons: [{text: '确定'}, {text: '取消'}],
    //是否出现差额退款弹窗
    showbalanceRefundDialog:false,
    //差额退款弹窗的按钮
    balanceRefundButtons: [{text: '确定'}, {text: '取消'}],
    showNoticeDialog: false, // 控制告知书弹窗显示
    currentPage: 1, // 当前页码
    totalPages: 5, // 总页数，根据内容调整
    hasRead: false, // 是否已阅读完所有页面
        noticeButtons: [
      {text: '关闭'}, 
      {text: '完成', disabled: true} // 初始状态下禁用完成按钮
    ], // 弹窗按钮
    noticeContent: [
      {
        title: "广州港船务有限公司来港小型船舶(驳船)告知书",
        content: "尊敬的船长先生:\n  欢迎贵船到本港装卸作业，所有船方人员(以下统称船员）须自觉遵守国家的法律法规和我司的各项安全规章制度，为做好安全管控工作，特将主要事项告知如下："
      },
      {
        title: "一、上下浮吊及作业期间的安全要求",
        content: "一、上下浮吊及作业期间的安全要求\n\n1.船员上下浮吊应须使用安全有效的上下防护设施、防护网络或者船用梯板上下，不得经无任何防护处直接越，防止跌落水，否则责任自负。\n\n2.船员上下浮吊及在驳船生活区外活动时必须穿戴好安全有效的救生衣、安全帽等劳保用品，严禁穿拖鞋。\n\n3.船方必须栓好船上的狗等宠物，防止狗对上船人员的安全威胁，否则责任自负。\n\n4.船员上下浮吊时，在未经许可的情况下，不得擅自进入浮吊的驾驶台、机舱、船员宿舍等重要场所；船舶作业期间，禁止任何无关人员上下船，严禁搭乘未成年人。\n\n5.船员在装卸作业过程中，应站在安全位置，不得站死角位，不得站在装卸机械所吊货物行经路线或货物一旦散落所覆盖位置及平整船舱，不得站在装卸机械行走轨道范围。\n\n6.装卸机械所吊货物、物品未停稳、解钩，严禁靠近。\n\n7.在生产过程中，船方应避免与港方有交叉作业的行为。\n\n8.及时整改港方对船舶设备和作业环境提出的安全隐患。"
      },
      {
        title: "二、来港作业驳船要求",
        content: "二、来港作业驳船要求\n\n（一）装货前\n1.驳船舱内干净适装，无残留货物。\n2.驳船舱内如有铲车等设备，需用船上吊机吊起或放至驳船甲板；如无吊机，需自备帆布将铲车等设备覆盖包裹好以装货时损坏设备。\n3.船确保不私自改装导致船体结构受力变化，不使用假牌簿通过检验。\n4.驳船需配合指导员、浮吊及货主进行验舱，拍摄船舱内照片或视频发送至相关方核实，严禁使用假照片视频。\n5.候泊驳船在作业锚地附近水域抛锚候泊的驳船不要抛的太近太密集，等候时间较长的驳船可以在天后宫、32LD或者32LD附近水域抛锚。\n6.驳船靠泊后应及时带牌簿到浮吊交给指导员核验，装载A类货物时，驳船船长/大副签署船务公司版本《安全提醒告知书》、《船长声明》以及沙角海事处版本《安全提醒告示书》、《船长声明》、《散货船装卸船/岸安全检查项目表》后再开工；驳船完工后需签署《安全装货确认书》。\n\n（二）装货时\n1.装载煤炭类货物，驳船快装满舱时（货堆头未超出舱口围时），驳船方再将帆布架移至拉帆布的位置，以避免货堆头过高帆布架拉不到位。装载铁矿等其他类货物，帆布架应始终保持在船头船尾处，直到装完再拉帆布架盖帆布。\n2.驳船指挥装载时要配合浮吊平铺装货，不起山头，不能有大坑。\n\n（三）完装后\n1.驳船装好之后，离开浮吊在作业锚地附近抛锚拉好帆布，3小时内拉好帆布封舱的照片和视频发到作业群；\n2.驳船拉好帆布时，在驾驶台处拍摄一段包括船上AIS实时位置（AIS界面停留至少5秒，能清晰显示船位、日期和时间）和帆布覆盖情况的连贯视频发送至作业群。\n3.驳船确保全程开启AIS。\n4.若没有驳船接位，可以在浮吊边拉好帆布，方便监控。\n5.经过指导员核实以上工作没有问题之后，允许驳船开航。"
      },
      {
        title: "三、其它",
        content: "三、其它\n\n船方必须严格执行港方的有关规定，否则港方有权延迟24小时甚至拒绝提供装卸服务并列入黑名单，所产生的一切责任由船方负责。\n\n广州港船务有限公司\n散杂货营运中心调度操作部"
      },
      {
        title: "四、签收回执",
        content: "签收回执\n\n我船确认收到并已知悉此告知书，并严格按告知书内容执行。\n\n大船名：{{shipName}}"
      }
    ],
    showSafetyNoticeDialog: false, // 控制安全提醒告示书弹窗显示
    
    safetyNoticeButtons: [
      {text: '关闭'}, 
      {text: '完成', disabled: true} // 初始状态下禁用完成按钮
    ],
    currentSafetyPage: 1,
    totalSafetyPages: 1,
    hasReadSafety: false,
    showCaptainStatementDialog: false, // 控制船长声明弹窗显示
    captainStatementButtons: [
      { text: '关闭' },
      { text: '确认', disabled: true } // 初始状态下禁用确认按钮
    ],
      billNo: '', // 提单号
      totalTonnage: '', // 货量
      plannedDepartureDate: '', // 计划日期
    showSafetyCheckDialog: false,
    safetyCheckButtons: [
      {text: '关闭'}, 
      {text: '确认', disabled: false}
    ],
    safetyCheckForm: {
      anchorage: '',
      anchorPosition: '',
      anchorageDepth: '',
      arrivalDraft: '',
      arrivalHeight: '',
      departureDraft: '',
      departureHeight: '',
      shipSignature: '',
      portSignature: '',
      operatorNamePosition: '' // 添加姓名与职务字段
    },
    // 定义固定的检查项模板
    defaultCheckItems: [
      {
        serial: '1',
        item: '锚地/浮筒水深是否适合货物装卸？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '2',
        item: '系泊设备是否适合当地所有潮汐、海流、天气、通航及船舶离靠港的情况？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '3',
        item: '紧急情况下船舶是否可以随时离开锚地？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '4',
        item: '船上及浮同意的通信系统是否有效？',
        shipCheck: false,
        portCheck: false,
        remarks: '',
        extraFields: {
          communicationMethod: '', // 通信方式
          language: '', // 语言
          radioChannel: '' // 无线电话频道/电话号码
        }
      },
      {
        serial: '5',
        item: '操作时通信联络人员是否可以识别？',
        shipCheck: false,
        portCheck: false,
        remarks: '',
        extraFields: {
          shipContact: '', // 船舶联络人员
          floatingContact: '', // 浮吊联络人员
          position: '' // 位置
        }
      },
      {
        serial: '6',
        item: '船上及浮吊是否配备足够的处理紧急情况的人员？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '7',
        item: '是否准备或计划进行加油操作？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '8',
        item: '船舶靠港期间是否准备或计划对浮吊或船舶进行修理？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '9',
        item: '是否接受由于货物装卸操作所造成损坏的报告和记录程序',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '10',
        item: '船上是否备有港口和码头规定(包括安全和防污染要求及应急措施)的副本？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '11',
        item: '托运人是否向船长提供 SOLAS 第Ⅵ章要求所述的货物性质？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '12',
        item: '对于可能需要进入的货舱和围闭处所，其空气是否安全？熏蒸货物是否标明？船舶和港方对需要进行大气监控是否达成一致?',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '13',
        item: '货物装卸能力和每台装卸机械运行限制是否已通知船舶/港方？',
        shipCheck: false,
        portCheck: false,
        remarks: '',
        extraFields: {
          floatingCrane1: '', // 浮吊1
          floatingCrane2: '' // 浮吊2
        }
      },
      {
        serial: '14',
        item: '对于装货/卸压载或卸货/压载在各个阶段的装卸操作计划是否已经计算？',
        shipCheck: false,
        portCheck: false,
        remarks: '',
        extraFields: {
          planHolder: '' // 计划副本持有人
        }
      },
      {
        serial: '15',
        item: '装卸货计划中是否已经清楚地说明作业货舱，是否标明作业次序及每次作业货舱转移的货物等级和吨数？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '16',
        item: '是否已经讨论过货物需要平舱？其方法和范围是否已经取得一致？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '17',
        item: '船舶和港方是否理解并接受如果压载和货物作业失调，货物装卸将暂停直到压载操作调整正常？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '18',
        item: '船舶是否已经知道并同意卸货时去除遗留货物的预定程序？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '19',
        item: '船舶最终纵平衡程序是否已确立并取得一致意见？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      },
      {
        serial: '20',
        item: '货物装卸完成后船舶准备开航所需的时间是否得到双方一致确认？',
        shipCheck: false,
        portCheck: false,
        remarks: ''
      }
    ],
    safetyCheckItems: [], // 实际显示的检查项
    showWaterWayFileDialog: false,
    waterWayFiles: [],
    waterWayFileButtons: [{text: '关闭'}],
    showLoadingConfirmDialog: false,
    loadingConfirmForm: {
      port: '',
      leaveTime: '',
      shipConfirm: '',
      confirmDate: ''
    },
    loadingConfirmButtons: [{text: '取消'}, {text: '确认'}],
  },

  /**
   * 方法
   */

   /**
   * 判断是否存在，不存在返回空字符串或其他
   * @param {传进来的变量} empty 
   * @param {为空时返回的值} replace 
   */
  emptyReturn(empty,replace){
    if(!replace || replace==null){
      replace = ""
    }
    if(empty == null || !empty){
      return replace
    }else{
      return empty
    }
  },

  //空关键字
  deleteSearchValue(){
    this.setData({
      "form.searchValue":""
    })
    console.log(this.data.form)
  },


  //点击退款弹窗按钮
  tapBalanceRefundDialog(e){
    // 确认按钮
    if(e.detail.index === 0) {
      let currentConsignObj = this.data.currentConsignObj
      //参数
      let param = {
        id: currentConsignObj.consignDetailId, //运单详情id
      }
      app.$post(app.$url.consign.bargeRefund,param).then(res=>{
        if(res.code == 200){
          app.$message("退款申请成功")
          this.getSupplyData()
        }else{
          app.$message(res.msg)
        }
      })
      

    }else if(e.detail.index == 1){
    //取消按钮
    
    }
    this.setData({
      showbalanceRefundDialog:false
    })
  },


  //显示差额退款弹窗
  balanceRefundOrder(e){
    let currentConsignObj = e.currentTarget.dataset.item //当前托运对象 
    this.setData({
      showbalanceRefundDialog:true,
      currentConsignObj,
    })
  },

  //取消预约按钮
  tapCancelReservationDialog(e){
    // 确认按钮
    if(e.detail.index === 0) {
      let currentConsignObj = this.data.currentConsignObj
      //参数
      let param = {
        wxAppOintmentTime:"",/// 预时间，取消预约不用传
        id:currentConsignObj.consignDetailId, //运单详情id
        wxOperateState:"6" // 5-预约，6-取消预约
      }
      app.$post(app.$url.consign.reservation,param).then(res=>{
        if(res.code == 200){
          app.$message("取消预约成功")
          this.getSupplyData()
        }else{
          app.$message(res.msg)
        }
      })
      

    }else if(e.detail.index == 1){
    //取消按钮
    
    }
    this.setData({
      showCancelReservationDialog:false
    })
  },

  //退单弹窗的原因input
  modifyReasonInput(e){
    this.setData({
      "chargeBackForm.modifyReason":e.detail.value
    })
  },

  //退单弹窗按钮
  tapChargeDialog(e){
    let chargeBackForm = this.data.chargeBackForm
    let {consignDetailId,wxRationContactNumber,rationWeight,bargeTel,wxOperateState,chargeBalanceType,wxMonthChargeById, wxMonthChargeByName,wxAppOintmentTime} = this.data.currentConsignObj

    //参数
    let param = {
      id:consignDetailId, //运单详情id 必填
      modifyReason:chargeBackForm.modifyReason, // 退单原因 必填
      wxRationContactNumber,//托运单联系人 (托运单联系人就是一个电话号码) 改单必填
      rationWeight,// 托运单重量 改单必填
      state:"1", // 1-退单，2-单
      bargeTel, // 驳船主联系电话 改单必填
      wxOperateState,// 状态 改单必填
      chargeBalanceType,// 支付方式 0-月结，1-现结 改单必填
      companyId:wxMonthChargeById, // 船公司id(月结公司id)
      companyName: wxMonthChargeByName, // 船公司名称(月结公司名称)
      wxAppOintmentTime,// 预约时间
      updateRationContactNumber:false// 是否只修改托运单联系人 true-是 false-不是
    }

    // 确认按钮
    if(e.detail.index === 0) {
      if(!param.id){
        app.$message("运单详情id参数不能为空")
        return
      }
      if(!param.modifyReason){
        app.$message("请输入退单原因")
        return
      }
      app.$post(app.$url.consign.chargeback,param).then(res=>{
        if(res.code == 200){
          app.$message(res.msg)
          this.getSupplyData()
        }else{
          app.$message(res.msg)
        }
      })
      

    }else if(e.detail.index == 1){
    //取消按钮
    
    }
    this.setData({
      chargeBackDialog:false,
      "chargeBackForm.modifyReason":""

    })
  },

  // 水路运单文件按钮
  tapWaterWayFile(e) {
    let item = e.currentTarget.dataset.item

    if(!item.waterwaycargoid) {
      app.$message("未找到水路运单文件")
      return
    }

    app.$post(app.$url.consign.searchPb6WatercargoFileApp, item.waterwaycargoid).then(res=>{
      if(res.code == 200) {
        if(res.data.length == 0) {
          app.$message("未找到相关文件")
          return
        }
        // 保存文件列表并显示弹窗
        this.setData({
          waterWayFiles: res.data.map(file => ({
            ...file,
            // 优先使用 dataName，如果没有则使用 fileName 或默认文本
            displayName: file.dataName || file.fileName || '水路运单文件'
          })),
          showWaterWayFileDialog: true
        })
      } else {
        app.$message(res.msg)
      }
    })
  },

  // 打开具体文件
  openWaterWayFile(e) {
    const url = e.currentTarget.dataset.url
    wx.downloadFile({
      url: url,
      success: function (res) {
        const filePath = res.tempFilePath
        wx.openDocument({
          filePath: filePath,
          showMenu: true,
          success: function (res) {
            console.log('打开文档成功')
          }
        })
      }
    })
  },

  // 关闭文件列表弹窗
  closeWaterWayFileDialog() {
    this.setData({
      showWaterWayFileDialog: false
    })
  },

  // 点击确认实装
  tapConfirmLoading(e) {
    const item = e.currentTarget.dataset.item;
    this.setData({
      currentConsignObj: item,
      showLoadingConfirmDialog: true
    });

    // 获取已有的安全装货确认书数据
    app.$get(app.$url.notice.getLoadingConfirm + '/' + item.consignDetailId).then(res => {
      if (res.code === 200 && res.data) {
        // 如果有数据,填充到表单
        this.setData({
          loadingConfirmForm: {
            port: res.data.port || '',
            leaveTime: res.data.leaveTime ? res.data.leaveTime.substring(0, 5) : '', // 只保留时:分
            shipConfirm: res.data.shipConfirm || '',
            confirmDate: res.data.confirmDate ? formatDate(res.data.confirmDate, 'yyyy-MM-dd') : ''
          }
        });
      }
    });
  },

  // 处理码头输入
  handlePortInput(e) {
    this.setData({
      'loadingConfirmForm.port': e.detail.value
    });
  },

  // 修改离港时间选择的处理方法
  handleLeaveTimeChange(e) {
    // 将时间格式化为字符串，如 "14:30"
    this.setData({
      'loadingConfirmForm.leaveTime': e.detail.value
    });
  },

  // 处理船方确认输入
  handleShipConfirmInput(e) {
    this.setData({
      'loadingConfirmForm.shipConfirm': e.detail.value
    });
  },

  // 处理确认日期选择
  handleConfirmDateChange(e) {
    this.setData({
      'loadingConfirmForm.confirmDate': formatDate(e.detail.value, 'yyyy-MM-dd')
    });
  },

  // 处理安全装货确认
  handleLoadingConfirm(e) {
    if (e.detail.index === 1) { // 点击确认按钮
      // 表单验证
      const form = this.data.loadingConfirmForm;
      if (!form.port) {
        wx.showToast({
          title: '请输入码头',
          icon: 'none'
        });
        return;
      }
      if (!form.leaveTime) {
        wx.showToast({
          title: '请输入离港时间',
          icon: 'none'
        });
        return;
      }
      if (!form.shipConfirm) {
        wx.showToast({
          title: '请输入确认人',
          icon: 'none'
        });
        return;
      }
      if (!form.confirmDate) {
        wx.showToast({
          title: '请选择日期',
          icon: 'none'
        });
        return;
      }

      const data = {
        ...this.data.loadingConfirmForm,
        cargoconsignmentdetailId: this.data.currentConsignObj.consignDetailId
      };
      
      // 先保存安全装货确认书
      app.$post(app.$url.notice.saveLoadingConfirm, data).then(res => {
        if (res.code === 200) {
          // 保存成功后，显示确认实装弹窗
          wx.showModal({
            title: '提示',
            content: '是否确认实装？',
            success: (modalRes) => {
              if (modalRes.confirm) {
                // 用户点击确定，执行确认实装逻辑
                const waterCargoId = this.data.currentConsignObj.waterwaycargoid;
                if (!waterCargoId) {
                  wx.showToast({
                    title: '水路运单ID不能为空',
                    icon: 'none'
                  });
                  return;
                }
                
                app.$post(app.$url.mine.confirmLoadingOverForApp, {
                  id: waterCargoId,
                  confirmloadingover: "Y"
                }).then(confirmRes => {
                  if (confirmRes.code == 200) {
                    wx.showToast({
                      title: '确认成功',
                      icon: 'success'
                    });
                    this.getSupplyData();
                  } else {
                    wx.showToast({
                      title: confirmRes.msg,
                      icon: 'none'
                    });
                  }
                });
              }
            }
          });
          
          // 关闭安全装货确认书弹窗
          this.setData({
            showLoadingConfirmDialog: false,
            loadingConfirmForm: {
              port: '',
              leaveTime: '',
              shipConfirm: '',
              confirmDate: ''
            }
          });
        } else {
          wx.showToast({
            title: res.msg,
            icon: 'none'
          });
        }
      });
    } else {
      this.setData({
        showLoadingConfirmDialog: false
      });
    }
  },

  reGenerateWaterWayCargoApp(e) {
    let item = e.currentTarget.dataset.item
    
    // 显示确认弹窗
    wx.showModal({
      title: '提示',
      content: '是否确认重新生成水路运单？',
      success: (res) => {
        if (res.confirm) {
          // 用户点击确定，继续执行生成逻辑
          let waterCargoId = item.waterwaycargoid 
          if(!waterCargoId) {
            return
          }

          app.$post(app.$url.consign.searchPb6WatercargoFileApp, item.waterwaycargoid).then(res => {
            
            if(res.code == 200) {
              if(res.data.length == 0) {
                app.$message("未找到水路运单文件")
                app.$post(app.$url.mine.reGenerateWaterWayCargoApp, {
                  id: waterCargoId,
                  confirmloadingover: "Y"
                }).then(res => {
                  if(res.code == 200) {
                    app.$message("生成成功")
                    this.getSupplyData()
                  } else {
                    app.$message(res.msg)
                  }
                })
              } else {
                // 检查是否存在 dataType 为 40 的数据
                const hasLinkType40 = res.data.some(item => item.dataType === 43)
                if (hasLinkType40) {
                  app.$message("水路运单文件已存在")
                  return
                } else {
                  // 如果不存在 dataType 为 43 的数据，则重新生成
                  app.$post(app.$url.mine.reGenerateWaterWayCargoApp, {
                    id: waterCargoId,
                    confirmloadingover: "Y"
                  }).then(res => {
                    if(res.code == 200) {
                      app.$message("生成成功")
                      this.getSupplyData()
                    } else {
                      app.$message(res.msg)
                    }
                  })
                }
              }
            } else {
              app.$message(res.msg)
            }
          })
        }
        // 用户点击取消，不执行任何操作
      }
    })
  },

  //确认弹窗按钮
  tapConfirmdDialog(e){
    //参数
    let param = this.data.confirmForm

    console.log('确认按钮')
    console.log(param)
    // 取消按钮
    if(e.detail.index == 0) {
      param.state = ""
    }else if(e.detail.index == 1){
    //确认按钮
      param.state = "2"
      if(!param.id){
        app.$message("id参数不能为空！")
        return
      }
      if(!param.rationWeight){
        app.$message("请输入配载吨数！")
        return
      }
  
      app.$post(app.$url.consign.confirmConsign,param).then(res=>{
        if(res.code == 200){
          app.$message(res.msg)
          this.getSupplyData()
        }else{
          app.$message(res.msg)
        }
      })
    }
    
    this.setData({
      showConfirmDialog:false
    })

  },

  // 取消确认按钮
  tapCancelConfirmDialog(e){
     //参数
     let param = this.data.confirmForm
     // 取消按钮
     if(e.detail.index == 0) {
       param.state = ""
     }else if(e.detail.index == 1){
     //确认按钮
       param.state = "2"
       if(!param.id){
         app.$message("id参数不能为空！")
         return
       }
   
       app.$post(app.$url.consign.cancelConfirmConsign,param).then(res=>{
         if(res.code == 200){
           app.$message(res.msg)
           this.getSupplyData()
         }else{
           app.$message(res.msg)
         }
       })
     }
     
     this.setData({
      showCancelConfirmDialog:false
     })
  },

  //选择确认弹窗的支付方式
  identityChange(e){
    this.setData({
      "confirmForm.chargeBalanceType":e.detail.value
    })
  },

  //到达港input
  endPortInput(e){
    this.setData({
      "confirmForm.endPort":e.detail.value
    })
  },

  //配载吨数Input
  rationWeightInput(e){
    this.setData({
      "confirmForm.rationWeight":e.detail.value
    })
  },

  //获取托运列表
  getSupplyData() {
    let that = this 
    wx.stopPullDownRefresh()
    let param = {
      ...that.data.form
    }
    app.$post(app.$url.consign.getConsignmentList,param).then(res=>{
      if(res.code == 200){
        //返回两个状态的变量和请求对应变量不一致，需要区分
        // wxOperateState // 状态(0:待驳船主确认 1:待支付  2:待船公司审批(月结)，3船公司审批不通过(月结) 4:待船主预约 5:驳船主已预约 6:取消预约 7:待审核(退单改单) 8:审核通过(退单改单) 9:审核不通过(退单改单))
        // flagBargeState// 状态（5：已完成, 4:已报到）
        //校验null值
        res.data.map(item=>{
          for(let key in item){
            if(key !=  "wxOperateState"){
              item[key] = that.emptyReturn(item[key])
            }
          }  
          //重要：流转状态加工：前端显示流转状态时，直接放到一个变量wxOperateStateText,便于渲染
          that.data.processStateArr.map(item1=>{
            if(item1.value == item.wxOperateState){
              item.wxOperateStateText = item1.label
            }
          })
          item.wxOperateState = that.emptyReturn(item.wxOperateState)
          if(item.flagBargeState == 5){
            item.wxOperateStateText = "已完成"
          }else if(item.flagBargeState == 4){
            item.wxOperateStateText = "已报到"
          }else if(item.flagBargeState == 6 && item.wxOperateState == 5){
            item.wxOperateStateText = "报到完毕"
          }
          // rationWeight 后端返回的托运单重量单位为吨,前端直接显示吨

          // applyModify 0为申请退单中，1为已退单（申请退单通过），2为申请退单失败,3为申请改单中，4为申请改单成功，5为申请改单失败
          //只有applyModify为1为申请退单通过，才在前端显示该退单状态
          if(item.applyModify == 1) {
            that.data.applyModifyArray.map(item2=>{
              if(item2.value == item.applyModify){
                item.wxOperateStateText = item2.label
              }
            })
          }
        })
        this.setData({
          listData:res.data
        })
      }else {
      app.$message(res.msg)
      }
     
    })

  },
  // 跳转到查看发票
  checkFapiao(e) {
    let item = e.currentTarget.dataset.item;
    if(!item.consignDetailId) {
      app.$message('托运单详情参数为空');
      return
    }
    app.$post(app.$url.consign.getConsignDetail,{id: item.consignDetailId}).then(res=>{
      if(res.code == 200){
        if(res.data) {
          if(res.data.waterWayCargoId) {
            wx.navigateTo({
              url: '/pages/checkfapiao/checkfapiao?waterWayCargoId='+res.data.waterWayCargoId + '&chargeBalanceType=' + res.data.chargeBalanceType,
              success() {

              },
              fail() {

              },
            })
          }else {
            app.$message('该托运单暂无水路运单，还未生成发票文件！');
          }
        }
      }else {
        app.$message('获取托运单详情数据失败')
      }
    })
  },
  //关键字input 实时搜索
  searchValueInput(e){
    this.setData({
      "form.searchValue":e.detail.value
    })
    setTimeout(()=>{
      this.getSupplyData()
    },1500)
  },

  // 切换tab
  handleChangeTab(e) {
    //dataset自动转小写
    // wxOperateState:"",// 状态(0:待驳船主确认 1:待支付/待预约 5:驳船主已预约（待报道）)
    // flagBargeState:"",//状态（5：已完成）
    let wxOperateState = e.currentTarget.dataset.wxoperatestate
    let flagBargeState = e.currentTarget.dataset.flagbargestate
    this.setData({
      "form.wxOperateState":wxOperateState,
      "form.flagBargeState":flagBargeState,
    })
    
    if(e.target.dataset.tabid == this.data.activeTab) {
      return
    }else {
      this.setData({
        'activeTab': e.target.dataset.tabid
      })
    }
    wx.setStorageSync("activeTab",JSON.stringify(this.data.activeTab)) //设置tab
    this.getSupplyData()
  },
  //清空开始时间
  deleteStartTime(){
    this.setData({
      "form.beginTime":""
    })
  },

  //清空截止时间
  deleteEndTime(){
    this.setData({
      "form.endTime":""
    })
  },
  // 开始时间回调
  handleBeginDateChange(e) {
    //时撮比较日期大小
    let beginTime = new Date(e.detail.value).getTime() //开始时间
    let endTime =  new Date(this.data.form.endTime).getTime()//结束时间
    if(beginTime && endTime && beginTime > endTime){
      app.$message("开始时间需小于或等于截止时间")
      this.setData({
        'form.beginTime':""
      })
      return
    }
    this.setData({
      'form.beginTime': e.detail.value
    })
  },

  // 结束时间回调
  handleEndDateChange(e) {
    //时间撮比较日期大小
    let endTime = new Date(e.detail.value).getTime()//结束时间
    let beginTime =  new Date(this.data.form.startTime).getTime()//开始时间
    if(beginTime && endTime && endTime < beginTime){
      app.$message("截止时间需大于或等于开始时间")
      this.setData({
        'form.endTime':""
      })
      return
    }
    this.setData({
      'form.endTime': e.detail.value
    })
  },
  handleSearch(){
    this.getSupplyData()
  },

  // 点击跳转到详情页
  handleConsignDetail(e) {
    let currentConsignObj = JSON.stringify(e.currentTarget.dataset.item) //当前托运对象
    wx.navigateTo({
      url: '/pages/consignment/consigndetail/consigndetail?currentConsignObj='+currentConsignObj,
      success: (result)=>{
        
      },
      fail: ()=>{},
      complete: ()=>{}
    });
  },

  

  // 取消确认
  cancleConfirmOrder(e){
    let item = e.currentTarget.dataset.item 
    let {id,rationWeight,chargeBalanceType,state,endPort} = item

    this.setData({
      showCancelConfirmDialog:true,
      confirmForm:{
        id:item.consignDetailId,//运单详情id
        endPort,
        rationWeight, 
        chargeBalanceType,
        state,
      },
    })
  },

  //预约
  reservationOrder(e){
    let currentConsignObj = JSON.stringify(e.currentTarget.dataset.item) //当前托运对象
    if("新沙" === e.currentTarget.dataset.item.beginPort){
        wx.navigateTo({
            url:"/pages/consignment/notice/notice?currentConsignObj="+currentConsignObj,
          })
    }
    else{
        wx.navigateTo({
            url:"/pages/consignment/reservation/reservation?currentConsignObj="+currentConsignObj,
          })
    }
  },

  //取消预约
  cancelReservationOrder(e){
    let currentConsignObj = e.currentTarget.dataset.item //当前托运对象 
    this.setData({
      showCancelReservationDialog:true,
      currentConsignObj,
    })
  },

  //退单
  chargeBackOrder(e){ 
    let currentConsignObj = e.currentTarget.dataset.item //当前托运对象
    this.setData({
      chargeBackDialog:true,
      currentConsignObj,

    })
  },

  //改单
  modifyOrder(e){
    let currentConsignObj = JSON.stringify(e.currentTarget.dataset.item) //当前托运对象
    wx.navigateTo({
      url:"/pages/consignment/consignmodify/consignmodify?currentConsignObj="+currentConsignObj,
    })
  },

  //取消订阅
  cancelSubscribeOrder(e){
    let currentConsignObj = e.currentTarget.dataset.item //当前托运对象
    let param = {
      consignId:currentConsignObj.consignId,//运单id
      consignDetailId:currentConsignObj.consignDetailId //运单详情id
    }
    app.$post(app.$url.mine.cancelSubscription,param).then(res=>{
      if(res.code == 200){
        app.$message(res.msg)
        this.getSupplyData()
      }else{
        app.$message(res.msg)
      }
    })
    
  },

  //订阅
  subscribeOrder(e){
    let currentConsignObj = e.currentTarget.dataset.item //当前托运对象
    let param = {
      consignId:currentConsignObj.consignId,//运单id
      consignDetailId:currentConsignObj.consignDetailId //运单详情id
    }
    app.$post(app.$url.mine.subscription,param).then(res=>{
      if(res.code == 200){
        app.$message(res.msg)
        this.getSupplyData()
      }else{
        app.$message(res.msg)
      }
    })
      
    
  },

  // 跳转到支付页面
  payOrder(e) {
    let item = e.currentTarget.dataset.item
    //点击支付需要判断当前是否已经配载，如果没有配载则提示：已配载：flagBargeState - 3  
    if(item.flagBargeState != 3){
      app.$message("当前托运没有配载，请联系管理员进行配载")
      return
    }
    let currentConsignObj = JSON.stringify(e.currentTarget.dataset.item) //当前托运对象
    wx.navigateTo({
      url: "/pages/consignment/payment/payment?currentConsignObj="+currentConsignObj,
      success: (result)=>{
       
      },
      fail: ()=>{},
      complete: ()=>{}
    });
  },

  // 跳转到查询代理发货页面
  handleAgentdeliveryPage() {
    wx.navigateTo({
      url: '/pages/agentdelivery/query/query',
      success: (result) => {
        
      },
      fail: () => {},
      complete: () => {}
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息
    let activeTab = wx.getStorageSync("activeTab")?Number(JSON.parse(wx.getStorageSync("activeTab"))):1//托运单列表tab控制
    let rolePermission = wx.getStorageSync("permissions")?JSON.parse(wx.getStorageSync("permissions")):{} //权限表单

    // 当tab索引改变时，传的参数需要改变
    let wxOperateState = ""
    let flagBargeState = ""

    switch(activeTab) {
      // 待确认
      case 1:
        wxOperateState = 0
        flagBargeState = ""
        break;
      // 待支付/待预约
      case 2:
        wxOperateState = 1
        flagBargeState = ""
        break;
      // 带报道
      case 3:
        wxOperateState = 5
        flagBargeState = ""
        break;
      // 已完成
      case 4:
        wxOperateState = ""
        flagBargeState = 5
        break;
      // 
      case 5:
        wxOperateState = ""
        flagBargeState = ""
        break;
      // 完工
      case 6:
        wxOperateState = ""
        flagBargeState = "4"
        break;  
    }
    this.setData({
      "form.wxOperateState":wxOperateState,
      "form.flagBargeState":flagBargeState,
    })
    this.setData({
      userInfos,
      activeTab,
      rolePermission,
    }) 
    this.getSupplyData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.getSupplyData()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  
  // 托运单确认按钮点击
  confirmOrder(e) {
    this.showNotice(e);
    this.prepareCaptainStatement(e); // 准备船长声明数据，但不显示弹窗
  },

  // 显示来港小型船舶(驳船)告知书弹窗
  showNotice(e) {
    // 保存当前操作的数据项
    let item = e.currentTarget.dataset.item 
    let {id,rationWeight,chargeBalanceType,state,endPort,bargeName} = item
    this.setData({
      showNoticeDialog: true,
      currentPage: 1,
      hasRead: false,
      'noticeButtons[1].disabled': true,//初始状态下禁用完成按钮
      // 保存确认表单数据
      confirmForm: {
        id: item.consignDetailId,//运单详情id
        endPort,
        rationWeight,
        chargeBalanceType,
        state,
      },
      bargeName, // 保存船名
    });
  },

  // 确认来港小型船舶(驳船)告知书
  confirmNotice(e) {
    // 点击完成按钮
    if(e.detail.index === 1) {
      if (!this.data.hasRead) {
        app.$message('请阅读完所有告知书内容');
        return;
      }
      
      // 检查驳船船长签字/船章是否已填写
      if (!this.data.currentConsignObj.bargeCaptainSign) {
        app.$message('请填写驳船船长签字/船章');
        return;
      }
      
      // 保存驳船船长签字/船章
      app.$post(app.$url.notice.saveCaptainStatement, {
        bargeCaptainSign: this.data.currentConsignObj.bargeCaptainSign,
        cargoconsignmentdetailId: parseInt(this.data.currentConsignObj.consignDetailId)
      }).then(res => {
        if (res.code === 200) {
          // 关闭告知书弹窗
          this.setData({
            showNoticeDialog: false,
          });
          
          // 根据货物类型决定下一步操作
          if (this.data.currentConsignObj.cargoType === 'A' || this.data.currentConsignObj.cargoType === 'AB') {
            // A类货物显示安全提醒告示书
            this.showSafetyNotice(this.data.bargeName);
          } else {
            // 非A类货物直接显示确认弹窗
            this.setData({
              showConfirmDialog: true,
              confirmForm: {
                id: this.data.currentConsignObj.consignDetailId,
                rationWeight: this.data.currentConsignObj.rationWeight,
                chargeBalanceType: 1,
                state: "1",
                endPort: this.data.currentConsignObj.endPort
              }
            });
          }
        }
      });
    } else {
      // 点击关闭按钮,直接关闭
      this.setData({
        showNoticeDialog: false,
        currentPage: 1,  // 重置页码
        hasRead: false,  // 重置阅读状态
        'noticeButtons[1].disabled': true  // 重置完成按钮状态
      });
    }
  },

  // 处理驳船船长签字/船章输入
  handleBargeCaptainSignInput(e) {
    this.setData({
      'currentConsignObj.bargeCaptainSign': e.detail.value
    });
  },

  // 切换到下一页
  nextPage() {
    if (this.data.currentPage < this.data.totalPages) {
      this.setData({
        currentPage: this.data.currentPage + 1
      });
      // 如果到达最后一页,标记为已读并启用完成按钮
      if (this.data.currentPage === this.data.totalPages) {
        this.setData({
          hasRead: true,
          'noticeButtons[1].disabled': false // 启用完成按钮
        });
      }
    }
  },

  // 切换到上一页
  prevPage() {
    if (this.data.currentPage > 1) {
      this.setData({
        currentPage: this.data.currentPage - 1
      });
    }
  },

  // 显示安全提醒告示书弹窗
  showSafetyNotice(bargeName) {
    this.setData({
      showSafetyNoticeDialog: true,
      bargeName // 保存船名
    });
  },

  // 确认安全提醒告示书
  confirmSafetyNotice(e) {


    if (e.detail.index === 1) {

      // 检查船舶签收栏是否已填写
      if (!this.data.currentConsignObj.shipSign) {
        app.$message('请填写船舶签收栏');
        return;
      }

        this.setData({
            showSafetyNoticeDialog: false
        });
        this.showCaptainStatement(); // 在确认安全告知书后显示船长声明弹窗
    } else {
        this.setData({
            showSafetyNoticeDialog: false
        });
    }
  },

  // 格式化日期的辅助函数
  formatDate(date, format) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    
    return format
        .replace('yyyy', year)
        .replace('MM', month)
        .replace('dd', day);
  },

  // 准备船长声明数据
  prepareCaptainStatement(e) {
    let item = e.currentTarget.dataset.item;
    if (!item || !item.consignDetailId) {
        app.$message("获取托运单明细ID失败");
        return;
    }

    // 获取船长明数据其他弹窗内容
    app.$get(app.$url.notice.getCaptainStatement + '/' + item.consignDetailId).then(res => {
        if (res.code === 200 && res.data) {
            // 获取createTime,如果不存在则使用当前时间
            const createTimeDate = res.data.createTime ? new Date(res.data.createTime) : new Date();
            const createTime = formatDate(createTimeDate, 'yyyy年MM月dd日');
            
            // 计算计划时间(createTime+1天)
            const nextDay = new Date(createTimeDate);
            nextDay.setDate(nextDay.getDate() + 1);
            const plannedTime = formatDate(nextDay, 'yyyy-MM-dd');
            
            this.setData({
                currentConsignObj: {
                    ...item,
                    maritimeOffice: res.data.maritimeOffice || '沙角',
                    berth: res.data.berth || '',
                    createTime: createTime,
                    plannedDepartureDate: plannedTime,
                    shipSign: res.data.shipSign || '',
                    bargeCaptainSign: res.data.bargeCaptainSign || '',
                    captainSign: res.data.captainSign || '',
                    maritimeBureau: res.data.maritimeBureau || '广州'
                },
                // 更新所有需要显示日期的内容
                'noticeContent[3].content': this.data.noticeContent[3].content.replace('{{createTime}}', createTime),
                'noticeContent[4].content': this.data.noticeContent[4].content
                    .replace('{{createTime}}', createTime)
                    .replace('{{shipName}}', item.shipName || '') // 替换大船名
            });
        } else {
            const now = new Date();
            const formattedDate = formatDate(now, 'yyyy年MM月dd日');
            
            // 计算计划时间(当前时间+1天)
            const nextDay = new Date(now);
            nextDay.setDate(nextDay.getDate() + 1);
            const plannedTime = formatDate(nextDay, 'yyyy-MM-dd');
            
            this.setData({
                currentConsignObj: {
                    ...item,
                    maritimeOffice: '沙角',
                    berth: '',
                    createTime: formattedDate,
                    plannedDepartureDate: plannedTime,
                    shipSign: '',
                    bargeCaptainSign: '',
                    captainSign: '',
                    maritimeBureau: '广州'
                },
                // 更新所有需要显示日期的内容
                // 'noticeContent[3].content': this.data.noticeContent[3].content.replace('{{createTime}}', formattedDate),
                'noticeContent[4].content': this.data.noticeContent[4].content
                    .replace('{{createTime}}', formattedDate)
                    .replace('{{shipName}}', item.shipName || '') // 替换大船名
            });
        }
    }).catch(err => {
        app.$message("获取船长声明数据失败");
        console.error(err);
    });
  },

  // 显示船长声明弹窗
  showCaptainStatement() {
    const { consignDetailId } = this.data.currentConsignObj;
    
    // 先查询是否存在船长声明
    app.$get(app.$url.notice.getCaptainStatement + '/' + consignDetailId).then(res => {
        if (res.code === 200 && res.data) {
            // 如果存在,则填充数据
            this.setData({
                'currentConsignObj.maritimeOffice': res.data.maritimeOffice,
                
                'currentConsignObj.berth': res.data.berth
            });
        }
        // 显示弹窗
        this.setData({
            showCaptainStatementDialog: true
        });
    });
  },

  // 确认船长声明
  confirmCaptainStatement(e) {
    if (e.detail.index === 1) {

      // 检查船长签名是否已填写
      if (!this.data.currentConsignObj.captainSign) {
        app.$message('请填写船长（签名）');
        return;
      }

        let { maritimeOffice, berth, consignDetailId, shipSign, bargeCaptainSign, captainSign, createTime, maritimeBureau } = this.data.currentConsignObj;
        
        // 设置海事处默认值为"沙角"
        maritimeOffice = maritimeOffice || '沙角';
        
        // 检查用户是否填写了必要信息
        if (!maritimeOffice || !berth) {
            app.$message('请填写完整的海事处和泊位信息');
            return;
        }

        // 将中文格式的日期转换为标准格式 (2024年12月20日 -> 2024-12-20)
        const dateMatch = createTime.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
        const formattedDate = dateMatch ? 
            `${dateMatch[1]}-${dateMatch[2].padStart(2, '0')}-${dateMatch[3].padStart(2, '0')}` : 
            new Date().toISOString().split('T')[0];
        
        // 确保转换为整数
        app.$post(app.$url.notice.saveCaptainStatement, {
            maritimeOffice,
            berth,
            cargoconsignmentdetailId: parseInt(consignDetailId),
            shipSign,
            bargeCaptainSign,
            captainSign,
            maritimeBureau,
            createTime: formattedDate
        }).then(res => {
            if (res.code === 200) {
                app.$message('船长声明已保存');
                this.setData({
                    showCaptainStatementDialog: false,
                });
                this.showSafetyCheck();
            } else {
                app.$message(res.msg);
            }
        });
    } else {
        this.setData({
            showCaptainStatementDialog: false
        });
    }
  },

  // 处理海事处输入
  handleMaritimeOfficeInput(e) {
    const value = e.detail.value ; // 如果没有输使用默认值
    this.setData({
        'currentConsignObj.maritimeOffice': value
    });
  },
  // 处理海事局输入
  handleMaritimeBureauInput(e) {
    const value = e.detail.value ; // 如果没有输使用默认值
    this.setData({
        'currentConsignObj.maritimeBureau': value
    });
  },

  // 处理泊位输入
  handleBerthInput(e) {
    this.setData({
        'currentConsignObj.berth': e.detail.value
    });
  },
  // 处理船长签名栏输入
  handleCaptainSignInput(e) {
    this.setData({
        'currentConsignObj.captainSign': e.detail.value
    });
  },
  // 处理船舶签收栏输入
  handleShipSignInput(e) {
    this.setData({
        'currentConsignObj.shipSign': e.detail.value
    });
  },

  // 显示安全检查表
  showSafetyCheck() {
    // 获取当前时间，格式为 HH:mm
    const now = new Date();
    const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

    // 先获取已有的安全检查表数据
    app.$get(app.$url.notice.getAnchorageCheck + '/' + this.data.currentConsignObj.consignDetailId).then(res => {
      if (res.code === 200 && res.data) {
        //如果有数据，填充到表中
        const checkId = res.data.checkId;
        this.setData({
          'currentConsignObj.checkId': checkId, // 存储 checkId
          'safetyCheckForm': {
            anchorage: res.data.anchorageName || '',
            anchorPosition: res.data.anchorPosition || '',
            anchorageDepth: res.data.waterDepth || '',
            arrivalDraft: res.data.draftOnArrival || '',
            arrivalHeight: res.data.freeboardOnArrival || '',
            departureDraft: res.data.draftOnDeparture || '',
            departureHeight: res.data.freeboardOnDeparture || '',
            portSignature: res.data.portOperator || '',
            operatorNamePosition: res.data.operatorNamePosition || '',
            checkDate: formattedTime // 设置当前时间，只显示时分
          }
        });

        // 获取表格项数据
        if (checkId) {
          app.$get(app.$url.notice.getAnchorageCheckItems + '/' + checkId).then(itemRes => {
            if (itemRes.code === 200) {
              const { items, subQuestions } = itemRes.data;
              console.log(res.data);
              // 合并模板和后端数据
              const mergedItems = this.data.defaultCheckItems.map(template => {
                // 查找对应的后端数据
                const backendItem = items.find(item => 
                  item.itemId.toString() === template.serial
                ) || {};

                // 合并基础数据
                const mergedItem = {
                  ...template,
                  itemRecordId: backendItem.itemRecordId || null,
                  // 处理 shipResponse 为 null 的情况
                  shipResponse: backendItem.shipResponse === '-' ? 'dash' : 
                              (backendItem.shipResponse || 
                               (template.serial === '7' || template.serial === '8' ? 'dash' : 'Y')),
                  portCheck: backendItem.portResponse === 'Y',
                  remarks: backendItem.remarks || '',
                  extraFields: template.extraFields || {} // 保留模板中的extraFields结构
                };

                return mergedItem;
              });

              // 如果有子问题答案，填充到对应的extraFields中
              if (subQuestions) {
                // 找到需要填充子问题的检查项
                const item4 = mergedItems.find(item => item.serial === '4');
                if (item4) {
                  item4.extraFields = {
                    communicationMethod: subQuestions.communicationMethod || '',
                    language: subQuestions.language || '',
                    radioChannel: subQuestions.radioChannel || ''
                  };
                }

                const item5 = mergedItems.find(item => item.serial === '5');
                if (item5) {
                  item5.extraFields = {
                    shipContact: subQuestions.shipContact || '',
                    floatingContact: subQuestions.floatingCraneContact || '',
                    position: subQuestions.position || ''
                  };
                }

                const item13 = mergedItems.find(item => item.serial === '13');
                if (item13) {
                  item13.extraFields = {
                    floatingCrane1: subQuestions.floatingCrane1 || '',
                    floatingCrane2: subQuestions.floatingCrane2 || ''
                  };
                }

                const item14 = mergedItems.find(item => item.serial === '14');
                if (item14) {
                  item14.extraFields = {
                    planHolder: subQuestions.planHolder || ''
                  };
                }
              }

              this.setData({
                safetyCheckItems: mergedItems
              });
            }
          });
        } else {
          // 如果没有checkId，使用默认模板
          const defaultItems = this.data.defaultCheckItems.map(item => ({
            ...item,
            // 序号7和序号8默认填"-"，其他默认"√"
            shipResponse: item.serial === '7' || item.serial === '8' ? 'dash' : 'Y',
            portCheck: false,
            remarks: ''
          }));

          this.setData({
            safetyCheckItems: defaultItems,
            'safetyCheckForm.checkDate': formattedTime // 设置当前时间，只显示时分
          });
        }
      } else {
        // 如果没有数据，使用默认模板
        const defaultItems = this.data.defaultCheckItems.map(item => ({
          ...item,
          // 序号7和序号8默认填"-"，其他默认"√"
          shipResponse: item.serial === '7' || item.serial === '8' ? 'dash' : 'Y',
          portCheck: false,
          remarks: ''
        }));
        this.setData({
          safetyCheckItems: defaultItems,
          'safetyCheckForm.checkDate': formattedTime // 设置当前时间，只显示时分
        });
      }
      // 显示弹窗
      this.setData({
        showSafetyCheckDialog: true,
        'safetyCheckForm.checkDate': formattedTime // 设置当前时间，只显示时分
      });
    }).catch(err => {
      console.error('获取安全检查表数据失败:', err);
      app.$message('获取安全检查表数据失败');
      // 发生错误时也使用默认模板
      const defaultItems = this.data.defaultCheckItems.map(item => ({
        ...item,
        // 序号7和序号8默认填"-"，其他默认"√"
        shipResponse: item.serial === '7' || item.serial === '8' ? 'dash' : 'Y',
        portCheck: false,
        remarks: ''
      }));
      this.setData({
        safetyCheckItems: defaultItems,
        showSafetyCheckDialog: true,
        'safetyCheckForm.checkDate': formattedTime // 设置当前时间，只显示时分
      });
    });
  },

  // 确认安全检查表
  confirmSafetyCheck(e) {
    if (e.detail.index === 1) {
      // 检查姓名及职务是否已填写
      if (!this.data.safetyCheckForm.operatorNamePosition) {
        app.$message('请填写姓名及职务');
        return;
      }
      
      // 检查序号4和5的子问题是否已填写
      const item4 = this.data.safetyCheckItems[3];
      const item5 = this.data.safetyCheckItems[4];
      
      // 检查序号4的必填字段
      if (!item4.extraFields.communicationMethod || 
          !item4.extraFields.language || 
          !item4.extraFields.radioChannel) {
        app.$message('请填写序号4的子问题(通信方式、语言、无线电话频道)');
        return;
      }
      
      // 检查序号5的必填字段
      if (!item5.extraFields.shipContact || 
          !item5.extraFields.floatingContact || 
          !item5.extraFields.position) {
        app.$message('请填写序号5的子问题(船舶联络人员、浮吊联络人员、位置)');
        return;
      }

      // 获取当前时间并格式化为 yyyy-MM-dd HH:mm:ss 格式
      const now = new Date();
      const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
      
      // 处理表格项数据
      const checkItems = this.data.safetyCheckItems.map(item => ({
        itemRecordId: item.itemRecordId,
        itemId: parseInt(item.serial),
        shipResponse: item.shipResponse === 'dash' ? '-' : item.shipResponse,  // 转换回 - 符号
        portResponse: parseInt(item.serial) === 7 || parseInt(item.serial) === 8 ? '-' : 'Y',
        remarks: item.remarks || null
      }));

      // 准备主表数据
      const mainData = {
        bargeName: this.data.currentConsignObj.bargeName,
        checkDate: formattedDate, // 使用 yyyy-MM-dd HH:mm:ss 格式的时间
        cargoconsignmentdetailId: this.data.currentConsignObj.consignDetailId,
        anchorageName: this.data.safetyCheckForm.anchorage || '',
        anchorPosition: this.data.safetyCheckForm.anchorPosition || '',
        waterDepth: this.data.safetyCheckForm.anchorageDepth ? parseFloat(this.data.safetyCheckForm.anchorageDepth) : 0,
        draftOnArrival: this.data.safetyCheckForm.arrivalDraft ? parseFloat(this.data.safetyCheckForm.arrivalDraft) : 0,
        freeboardOnArrival: this.data.safetyCheckForm.arrivalHeight ? parseFloat(this.data.safetyCheckForm.arrivalHeight) : 0,
        draftOnDeparture: this.data.safetyCheckForm.departureDraft ? parseFloat(this.data.safetyCheckForm.departureDraft) : 0,
        freeboardOnDeparture: this.data.safetyCheckForm.departureHeight ? parseFloat(this.data.safetyCheckForm.departureHeight) : 0,
        portOperator: this.data.safetyCheckForm.portSignature || '',
        operatorNamePosition: this.data.safetyCheckForm.operatorNamePosition || ''
      };

      // 获取 checkId
      const checkId = this.data.currentConsignObj.checkId;
      console.log(checkId);
      // 准备子问题数据
      const subQuestions = {
        checkId: checkId,  // 使用获取的 checkId
        communicationMethod: this.data.safetyCheckItems[3].extraFields.communicationMethod,
        language: this.data.safetyCheckItems[3].extraFields.language,
        radioChannel: this.data.safetyCheckItems[3].extraFields.radioChannel,
        shipContact: this.data.safetyCheckItems[4].extraFields.shipContact,
        floatingCraneContact: this.data.safetyCheckItems[4].extraFields.floatingContact,
        position: this.data.safetyCheckItems[4].extraFields.position,
        floatingCrane1: this.data.safetyCheckItems[12].extraFields.floatingCrane1,
        floatingCrane2: this.data.safetyCheckItems[12].extraFields.floatingCrane2,
        planHolder: this.data.safetyCheckItems[13].extraFields.planHolder
      };

      // 1. 先保存主表和检查项
      app.$post(app.$url.notice.saveAnchorageCheck, {
        mainData,
        checkItems
      }).then(res => {
        if (res.code !== 200) {
          return Promise.reject(res.msg || '保存主表失败');
        }
        
        // 2. 保存子问题
        return app.$post(app.$url.notice.saveSubQuestions, {
          mainData,
          subQuestions
        });
      }).then(res => {
        if (res.code !== 200) {
          return Promise.reject(res.msg || '保存子问题失败');
        }
        
        // 全部保存成功
        app.$message('安全检查表已保存');
        this.setData({
          showSafetyCheckDialog: false,
          showConfirmDialog: true,
        });
      }).catch(err => {
        app.$message(err || '保存失败');
      });
    } else {
      this.setData({
        showSafetyCheckDialog: false
      });
    }
  },

  // 处理各种输入
  handleAnchorageInput(e) {
    this.setData({
      'safetyCheckForm.anchorage': e.detail.value
    });
  },
  
  // 处理地位置输入
  handleAnchorPositionInput(e) {
    this.setData({
      'safetyCheckForm.anchorPosition': e.detail.value
    });
  },
  
  // 处理水深输入
  handleWaterDepthInput(e) {
    this.setData({
      'safetyCheckForm.anchorageDepth': e.detail.value
    });
  },
  
  // 处理到达时吃水输入
  handleArrivalDraftInput(e) {
    this.setData({
      'safetyCheckForm.arrivalDraft': e.detail.value
    });
  },
  
  // 处理到达时干舷输入
  handleArrivalHeightInput(e) {
    this.setData({
      'safetyCheckForm.arrivalHeight': e.detail.value
    });
  },
  
  // 处理离开时吃水输入
  handleDepartureDraftInput(e) {
    this.setData({
      'safetyCheckForm.departureDraft': e.detail.value
    });
  },
  
  // 处理离开时干舷输入
  handleDepartureHeightInput(e) {
    this.setData({
      'safetyCheckForm.departureHeight': e.detail.value
    });
  },
  
  // 处理港操作员输入
  handlePortOperatorInput(e) {
    this.setData({
      'safetyCheckForm.portSignature': e.detail.value
    });
  },

  // ... 添加其他输入处理方法

  handleShipCheck(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      [`safetyCheckItems[${index}].shipCheck`]: !this.data.safetyCheckItems[index].shipCheck
    });
  },

  handlePortCheck(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      [`safetyCheckItems[${index}].portCheck`]: !this.data.safetyCheckItems[index].portCheck
    });
  },

  // 处理备注输入
  handleRemarksInput(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      [`safetyCheckItems[${index}].remarks`]: e.detail.value
    });
  },

  // 处理额外字段输入
  handleExtraFieldInput(e) {
    const { index, field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    // 只更新本地数据
    this.setData({
      [`safetyCheckItems[${index}].extraFields.${field}`]: value
    });
  },

  // 处理操作员姓名与职务输入
  handleOperatorNamePositionInput(e) {
    this.setData({
      'safetyCheckForm.operatorNamePosition': e.detail.value
    });
  },

  // 切换船方检查状态
  toggleShipCheck(e) {
    const index = e.currentTarget.dataset.index;
    const currentResponse = this.data.safetyCheckItems[index].shipResponse || '';
    let newResponse;
    
    // 循环切换状态: '-' -> 'Y' -> 'N' -> '-' 
    switch(currentResponse) {
      case 'dash':
        newResponse = 'Y';
        break;
      case 'Y':
        newResponse = 'N';
        break;
      case 'N':
        newResponse = 'dash';  // 改用 dash 而不是 -
        break;
      
    }
    
    this.setData({
      [`safetyCheckItems[${index}].shipResponse`]: newResponse
    });
  }
})
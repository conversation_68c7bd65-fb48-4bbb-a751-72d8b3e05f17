package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 驳船单证退单改单审核流程表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PB6_BARGE_CHECK_NODE")
@KeySequence("SEQ_PB6_BARGE_CHECK_NODE")
public class Pb6BargeCheckNode implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 当前流程节点： 0物流公司直接审核，1码头审核， 2物流公司间接审核
     */
    @TableField("WXNODE")
    private Integer wxnode;

    /**
     * 提交人
     */
    @TableField("WXAPPLYMAN")
    private String wxapplyman;

    /**
     * 提交时间
     */
    @TableField("WXAPPLYTIME")
    private String wxapplytime;

    /**
     * 当前状态审核人
     */
    @TableField("WXAPPLYAUDITMAN")
    private String wxapplyauditman;

    /**
     * 当前状态审核时间
     */
    @TableField("WXAPPLYAUDITTIME")
    private String wxapplyaudittime;

    /**
     * 审核状态： 0未审核，1审核通过，2审核不通过
     */
    @TableField("AUDITSTATES")
    private Integer auditstates;

    /**
     * 审核不通过原因
     */
    @TableField("FAILUREREASONS")
    private String failurereasons;

    /**
     * 关联表id： 审核节点类型为 0、1 时关联的是PB6_CARGOCONSIGNMENTDETAIL表ID
     */
    @TableField("LINKID")
    private Long linkid;

    /**
     * 审核节点类型： 0、退单， 1、改单， 2、取消申请
     */
    @TableField("WXNODEFLAG")
    private Integer wxnodeflag;


}

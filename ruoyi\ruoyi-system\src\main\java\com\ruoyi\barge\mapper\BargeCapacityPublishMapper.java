package com.ruoyi.barge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.barge.domain.BargeCapacityPublish;
import com.ruoyi.barge.domain.bo.BargeCapacityPublishBO;
import com.ruoyi.barge.domain.vo.BargeCapacityPublishVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/11 11:08
 */
public interface BargeCapacityPublishMapper extends BaseMapper<BargeCapacityPublish> {

    int deleteByPrimaryKey(Long id);

    int insertSelective(BargeCapacityPublish record);

    BargeCapacityPublish selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BargeCapacityPublish record);

    int updateByPrimaryKey(BargeCapacityPublish record);


    /**
     * 获取运力信息列表
     * @param bargeCapacityPublishBO
     * @return
     */
    List<BargeCapacityPublishVO> getCapacityList(BargeCapacityPublishBO bargeCapacityPublishBO);
}
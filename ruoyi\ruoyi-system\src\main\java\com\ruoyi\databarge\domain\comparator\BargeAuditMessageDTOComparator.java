package com.ruoyi.databarge.domain.comparator;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.databarge.domain.dto.BargeAuditMessageDTO;

import java.util.Comparator;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/9/9.
 * @Date: 2020/9/9 16:39
 */
public class BargeAuditMessageDTOComparator implements Comparator<BargeAuditMessageDTO> {

    @Override
    public int compare(BargeAuditMessageDTO o1, BargeAuditMessageDTO o2) {
        return DateUtils.compareDate(o1.getTime(), o2.getTime());
    }
}

package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 客户基本资料
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PB1_CUSTOMER")
@KeySequence("SEQ_PB1_CUSTOMER")
public class Pb1Customer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 客户编号
     */
    @TableField("CUSTOMERID")
    private String customerid;

    /**
     * 客户简称
     */
    @TableField("CSHORTNAME")
    private String cshortname;

    /**
     * 客户全称
     */
    @TableField("CFULLNAME")
    private String cfullname;

    /**
     * 所属地区
     */
    @TableField("CAREA")
    private String carea;

    /**
     * 地址
     */
    @TableField("CADDRESS")
    private String caddress;

    /**
     * 邮编
     */
    @TableField("CPOSTCODE")
    private String cpostcode;

    /**
     * 固定电话
     */
    @TableField("CTELEPHONE")
    private String ctelephone;

    /**
     * 主要联系人
     */
    @TableField("CMAINCONTACTS")
    private String cmaincontacts;

    /**
     * Email
     */
    @TableField("CEMAIL")
    private String cemail;

    /**
     * 手机
     */
    @TableField("CMOBILEPHONE")
    private String cmobilephone;

    /**
     * 传真
     */
    @TableField("CFAX")
    private String cfax;

    /**
     * 客户等级
     */
    @TableField("CRATE")
    private String crate;

    /**
     * 客户类型
     */
    @TableField("CTYPE")
    private String ctype;

    /**
     * 隶属关系
     */
    @TableField("CRELATION")
    private String crelation;

    /**
     * 公司性质
     */
    @TableField("CNATURE")
    private String cnature;

    /**
     * 营业证编码
     */
    @TableField("CCODING")
    private String ccoding;

    /**
     * 经营额
     */
    @TableField("CTURNOVER")
    private String cturnover;

    /**
     * 开户行
     */
    @TableField("CBANK")
    private String cbank;

    /**
     * 银行账号
     */
    @TableField("CACCOUNT")
    private String caccount;

    /**
     * 职务
     */
    @TableField("CPOSITION")
    private String cposition;

    /**
     * 创建人编号
     */
    @TableField("CREATEPEOPLEID")
    private Long createpeopleid;

    /**
     * 创建人
     */
    @TableField("CREATEPEOPLE")
    private String createpeople;

    /**
     * 创建时间
     */
    @TableField("CREATEDATA")
    private String createdata;

    /**
     * 修改人编号
     */
    @TableField("MODIFYPEOPLEID")
    private Long modifypeopleid;

    /**
     * 修改人
     */
    @TableField("MODIFYPEOPLE")
    private String modifypeople;

    /**
     * 修改时间
     */
    @TableField("MODIFYDATA")
    private String modifydata;

    /**
     * 客户类型
     */
    @TableField("CUSTOMERTYPE")
    private String customertype;

    /**
     * 委托人拼音头
     */
    @TableField("SPELLPRE")
    private String spellpre;

    /**
     * 财务系统中客户代码
     */
    @TableField("FINANCECUSTOMERID")
    private String financecustomerid;

    /**
     * 财务系统中客户名称
     */
    @TableField("FINANCECUSTOMERNAME")
    private String financecustomername;

    /**
     * xhad
     */
    @TableField("CORPORATIONID")
    private Long corporationid;

    /**
     * 是否导入数据
     */
    @TableField("ISIMPORT")
    private String isimport;

    @TableField("DD_ID")
    private Long ddId;

    /**
     * 曾用名
     */
    @TableField("CUSEDNAME")
    private String cusedname;

    /**
     * 付款方证件类型编码
     */
    @TableField("CREDTYPECODE")
    private String credtypecode;

    /**
     * 付款方证件类型名称
     */
    @TableField("CREDTYPENAME")
    private String credtypename;

    /**
     * 付款方类型编码
     */
    @TableField("PTYPECODE")
    private String ptypecode;

    /**
     * 付款方类型名称
     */
    @TableField("PTYPENAME")
    private String ptypename;

    /**
     * 机构代码/身份证号
     */
    @TableField("PAYCODE")
    private String paycode;

    /**
     * 发票种类 填“专用”或“普通”
     */
    @TableField("INV_TYPE")
    private String invType;

    /**
     * 客户税号
     */
    @TableField("CLIENT_TAXNO")
    private String clientTaxno;

    /**
     * 银行和帐号
     */
    @TableField("CLIENT_BANK_ACCOUNT")
    private String clientBankAccount;

    /**
     * 使用次数
     */
    @TableField("FREQUENCY")
    private Long frequency;

    /**
     * 特殊使用  长航0
     */
    @TableField("SPECIALUSE")
    private String specialuse;

    /**
     * 是否为网上营业厅注册的公司
     */
    @TableField("ISREGISTER")
    private String isregister;


    //是否有效
    @TableField("CISACTIVE")
    private String cisactive;


}

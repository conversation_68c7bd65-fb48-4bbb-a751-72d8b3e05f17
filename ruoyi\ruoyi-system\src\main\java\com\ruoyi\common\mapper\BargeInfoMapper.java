package com.ruoyi.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.vo.BargeInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BargeInfoMapper extends BaseMapper<BargeInfo> {

    /**
     * 获取驳船列表
     * @param bargeInfoBO
     * @return
     */
    List<BargeInfoVO> getBargeList(BargeInfoBO bargeInfoBO);

    List<BargeInfoVO> getBargeListByCompanyId(BargeInfoBO bargeInfoBO);

    List<BargeInfoVO> getBargeListByCompanyUser(BargeInfoBO bargeInfoBO);


    List<BargeInfoVO> getBargeListByNoCompany(BargeInfoBO bargeInfoBO);




    /**
     *  搜索驳船
     * @param searchValue
     */
    List<BargeInfoVO> searchBarge(String searchValue);

    /**
     * 获取驳船主用户驳船备案信息
     * @param bargeInfoBO
     * @return
     */
    BargeInfoVO getUserBargeDetail(BargeInfoBO bargeInfoBO);

    List<BargeInfoVO> getUserBargeAuditDetail(BargeInfoBO bargeInfoBO);

    BargeInfoVO searchBargeByName(String searchValue);
}

package com.ruoyi.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.domain.Coutform;
import com.ruoyi.common.domain.bo.CoutformBO;
import com.ruoyi.common.domain.vo.CoutformVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 出库单持久层接口
 * <AUTHOR>
 * @Date 2020/8/10  17:02
 */
public interface CoutformMapper extends BaseMapper<Coutform> {

    public List<CoutformVO> getCoutformList(CoutformBO coutformBO);

    // 查询出库单信息
    List<CoutformVO> findCoutform(CoutformBO coutformBO);

    List<String> getFormloadometerIdList(@Param("coutformId") String coutformId);

    String getUnblId(@Param("unblId") String unblId);

    String checkAmt(String outOrInFormId);
}

/* pages/login/login.wxss */
page {
  height: 100%;
  width: 100%;
}
.login-page{
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
.login-logo{
  height: 700rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.login-warp{
  height: 300rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.login-protocol{
  height: 200rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}
.login-page .login-btn{
  width: 80%;
  height: 90rpx;
  padding: 0rpx;
  margin: 0rpx;
  border-radius: 10rpx;
  border: none;
  line-height: 90rpx;
  color: #fff;
  background: rgb(6, 143, 13);
}
.logo-image{
  width: 200rpx;
  height: 200rpx;
}
.login-title{
  height: 60rpx;
  width: 100%;
  text-align: center;
  color: #333;
  font-size: 30rpx;
  line-height: 60rpx;
}
.protocol-server,.protocol-privacy{
  color: #FF0000;
  text-decoration: underline;
  font-size: 34rpx;
}
.protocol-download{
  color: #0066ff;
  font-size: 34rpx;
}
.iden-radio{
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}
.iden-radio-item{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.iden-radio-text{
  color: #333;
  font-size: 30rpx;
  padding-left: 10rpx;
}
.normal-dialog{
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
}
.normal-dialog-wrap .weui-dialog .weui-dialog__bd{
  margin-bottom: 0rpx;
}
.normal-dialog .weui-dialog .weui-dialog__ft{
  line-height: 90rpx;
  height: 90rpx;
  font-size:30rpx;
  padding: 0 24rpx;
}
.normal-dialog-info{
  flex-shrink: 0;
  width: 100%;
  height: 200rpx;
  display: flex;
  justify-items: center;
  align-items: center;
  font-size: 34rpx;
  color:#333;
}
.normal-dialog-btnwrap{
  flex-shrink: 0;
  width: 100%;
  height: 70rpx;
  display: flex;
}
.normal-dialog-btnwrap .normal-btn1{
  width:50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color:#333;
  border:1rpx solid #ddd;
}
.normal-dialog-wrap .normalbtn{
  padding:0rpx 50rpx;
  text-align: right;
  height: 90rpx;
  font-size: 30rpx;
}
.normal-dialog-btnwrap .active-normal-btn1{
  background:#00416f;
  color:#fff;
}
.review-dialog-info{
  flex-shrink: 0;
  width: 100%;
  height: auto;
  color: #333;
}
.normal-dialog-wrap  .weui-dialog__ft{
  width: 50%;
  margin-left: auto;
}
.normal-dialog-wrap1 .weui-dialog__ft .weui-dialog__ft{
  width: 100%;
  margin-left: auto;
}
.version{
  position: absolute;
  left: 45%;
  top: 95%;
}
.versionId{
  color: gray;
  font-size: x-small;
}
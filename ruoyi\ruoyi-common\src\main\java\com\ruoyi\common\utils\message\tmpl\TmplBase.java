package com.ruoyi.common.utils.message.tmpl;

import com.ruoyi.common.utils.message.request.mpTem.DataValue;
import com.ruoyi.common.utils.message.request.mpTem.Miniprogram;

import java.util.Map;

/**
 * 公众号模版类
 * <AUTHOR>
 *
 */
public abstract class TmplBase {
	
	/**
	 * 当前模式
	 */
	public String profile;
	/**
	 * 模版id
	 */
	public String tmplID;
	/**
	 * 模版参数
	 */
	public Map<String, DataValue> data;
	
	/**
	 * 公总号跳转小程序
	 */
	public Miniprogram miniprogram;
	
	
	public TmplBase(){
	}
	
	public Map<String, DataValue> getData() {
		return data;
	}

	public void setTmplID(String tmplID) {
		this.tmplID = tmplID;
	}


}

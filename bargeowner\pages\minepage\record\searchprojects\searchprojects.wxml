<!--pages/scancollect/chooseprojects/searchprojects/searchprojects.wxml-->
<view class="search-dialog" hover-class="none" hover-stop-propagation="false">
  <!-- <view class="search-header" hover-class="none" hover-stop-propagation="false" bindtap="closeDialog">
    <text class="iconfont icon-xiala1 pull-icon" selectable="false" space="false" decode="false"></text>
  </view> -->
  <view class="search-content" hover-class="none" hover-stop-propagation="false">
    <view class="search-ipt-wrap" hover-class="none" hover-stop-propagation="false">
      <input placeholder-style="font-weight:500;color:#999;"  adjust-position="false" type="text" value="{{searchKey}}" bindinput="searchInput1" class="head-input" placeholder-class="input-holder" placeholder="项目名称"/>
      <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-icon" wx:if="{{searchKey}}" bindtap="deleteSearchKey"></mp-icon>
    </view>
    <button bindtap="submitHandle" class="submit-btn1">确定</button>
  </view>
  <scroll-view class="search-page"  scroll-y="true">
    <view class="project-list" hover-class="none" hover-stop-propagation="false">
      <view class="{{item.checked ?'each-project active-each-project':'each-project'}}" hover-class="none" hover-stop-propagation="false" wx:for="{{projectsArray1}}"  data-index="{{index}}" data-item="{{item}}" wx:key="index" bindtap="currentCheck">
        <text class="iconfont icon-jiance search-icon1" selectable="false" space="false" decode="false"></text>
        <view class="pro-title" hover-class="none" hover-stop-propagation="false">
          {{item.cfullName}}
        </view>
      </view>
    </view>
  </scroll-view>
</view>


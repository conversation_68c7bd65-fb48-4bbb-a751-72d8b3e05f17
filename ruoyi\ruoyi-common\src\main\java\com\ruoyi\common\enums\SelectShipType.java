package com.ruoyi.common.enums;

/**
 * @Description 派船类型枚举
 * <AUTHOR>
 * @Date 2020/8/12 14:59
 */
public enum SelectShipType {
    //分两种情况：如果是货主--选择船公司派船、自己派船
              //如果船公司--选择其他船公司派船、自己公司派船
    SELECTCOMPANY(1,"船公司派船"),
    SELECTSELF(0,"自己派船"),

    //派船状态
    DONESELECT(1,"已派船"),
    TODOSELECT(0,"待派船");
    private final Integer code;
    private final String codeName;
    SelectShipType(Integer code,String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

package com.ruoyi.common.enums;

/**
 * 驳船审核标识
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/7 9:53
 */
public enum CheckFlagEnum {

    /**
     * 1.备案审核，2.备案修改审核,0-挂靠修改审核，1-挂靠审核
     */
    BARGE_AUDIT(1, "备案审核"),
    UPDATE_BARGE_AUDIT(2, "修改审核"),
    CANCEL_BINDING_AUDIT(2, "取消挂靠审核"),
    BINDING_AUDIT(1, "挂靠审核"),
    UPDATE_BINDING_AUDIT(0, "挂靠修改审核");

    private final Integer code;

    private final String codeName;

    CheckFlagEnum(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

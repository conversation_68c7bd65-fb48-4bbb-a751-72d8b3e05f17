/* pages/minepage/crewlist/crewlist.wxss */
.search-warp{
  position: fixed;
  top: 0rpx;
  left: 0rpx;
  right: 0rpx;
  height: 100rpx;
  background: #ffffff;
  z-index: 100;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #ddd;
}
.search-bottom{
  position: fixed;
  left: 0rpx;
  bottom: 0rpx;
  right: 0rpx;
  height: 150rpx;
  background: #ffffff;
  z-index: 100;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  border-top: 1px solid #ddd;
}
.search-area{
  height: 70rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 710rpx;
}
.search-ipt{
  height: 70rpx;
  line-height: 70rpx;
  flex: 1;
  width: 100%;
  padding: 0 20rpx;
}
.search-icon{
  flex-shrink: 0;
}
.crew-page{
  padding-top: 100rpx;
}
.crew-list{
  padding-bottom: 200rpx;
}
.crew-item{
  min-height: 160rpx;
  border-bottom: 8rpx solid #f5f5f5;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
}
.crew-item-message{
  width:80%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  padding-right: 20rpx;
}
.search-bottom .confirm-btn{
  height: 70rpx;
  line-height: 70rpx;
  background: #00426B;
  color: #fff;
  padding: 0rpx;
  margin: 0rpx;
  width: 260rpx;
  font-weight: normal;
  border-radius: 20rpx;
}
.search-bottom .add-btn{
  height: 70rpx;
  line-height: 66rpx;
  border: 1px solid #00426B;
  padding: 0rpx;
  margin: 0rpx;
  width: 260rpx;
  color: #00426B;
  font-weight: normal;
  border-radius: 20rpx;
}
.crew-icon{
  flex-shrink: 0;
  font-size: 50rpx;
  color: #00426B;
  padding: 0 4rpx;
}
.crew-item-text{
  flex-shrink: 0;
  width:37%;
  font-size: 30rpx;
  color: #00426B;
  padding: 0 10rpx;
  word-wrap:break-word;
}
.crew-page .crew-list .auth-btn {
  flex-shrink: 0;
  width: 100rpx;
  height: 60rpx;
  line-height: 56rpx;
  border: 1px solid #00426B;
  padding: 0rpx;
  margin: 0rpx;
  width: 140rpx;
  color: #00426B;
  background: #fff;
  font-weight: normal;
  border-radius: 20rpx;
}
.close-icon{
  flex-shrink: 0;
  width: 40rpx;
  text-align: center;
  height: 70rpx;
  line-height: 70rpx;
  color: #ddd;
  margin-right: 10rpx;
}
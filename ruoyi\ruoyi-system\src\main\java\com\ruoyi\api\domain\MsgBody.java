package com.ruoyi.api.domain;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/11/1 9:02
 */
@Data
public class MsgBody {


    private String orderNum;

    // 通知类型 01-新分派订单、02-订单信息变更、03-集装箱清单更新、09-订单取消
    private String mqType;

    // 订单状态 021：待接单、023：待指定航次、022：待预配确认、024：待起运、02401：起始港到港、02402：起始港靠泊、02403：起始港离港、
    // 02404：目的港到港、02405：目的港靠泊、025：已完成、026：已取消
    private String orderStatus;

    // 订单类型
    private String orderType;

    // 起始港编码
    private String startPortCode;

    // 目的港编码
    private String endPortCode;

    // 驳船名称
    private String shipName;

    // 驳船航次号
    private String voyageNum;

    private List<AtBox> boxList;

}

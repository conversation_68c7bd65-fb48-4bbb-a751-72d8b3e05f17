// pages/checkfapiao/checkfapiao.js
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    fapiaoMsg: {
      invoicetype: '', // 发票类型 0 普票 1专票
      type: '', // 发票抬头类型 0个人 1企业
      buyername: '',
      taxnum: '', 
      phone: '',
      state: '',
    },
    // 发票附件信息
    fileList: [], 
    waterWayCargoId: '', // 水路运单号
    //批量下载按钮
    batchButtons:[{text: '取消'}, {text: '确定'}],
    batchDialog: false,
    userEmail: '', // 用户邮箱
    chargeBalanceType: '', // 结算方式
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.waterWayCargoId) {
      this.getFapiaoMsg(options.waterWayCargoId);
      this.setData({
        'waterWayCargoId': options.waterWayCargoId
      })
    }else {
      app.$message('水路运单号缺失，无法查询发票信息');
    }
    if(options.chargeBalanceType) {
      this.setData({
        'chargeBalanceType' : options.chargeBalanceType
      });
    }
  },
  getFapiaoMsg(value) {
    let param = {
      waterwayCargoId: value
    }
    app.$post(app.$url.getInvoice, param).then((res) => {
      if(res.code == 200) {
        if(res.data && res.data.invoicetype) {
          this.setData({
            ['fapiaoMsg.invoicetype']: res.data.invoicetype,
            ['fapiaoMsg.type']: res.data.type,
            ['fapiaoMsg.buyername']: res.data.buyername,
            ['fapiaoMsg.taxnum']: res.data.taxnum,
            ['fapiaoMsg.phone']: res.data.phone,
            ['fapiaoMsg.address']: res.data.address,
            ['fapiaoMsg.account']: res.data.account,
            ['fapiaoMsg.email']: res.data.email,
            ['fapiaoMsg.state']: res.data.state,
            'userEmail': res.data.email || '',
          });
          // 如果是普通发票 那么去加载附件
          if(res.data.invoicetype == 0) {
            let param = {
              fpqqlsh: res.data.fpqqlsh,
              orderno: res.data.orderno
            };
            this.getFileList(param);
          }
        }else {
          app.$message('该水路运单还未生成发票');
          setTimeout(() => {
            wx.navigateBack();
          }, 3000);
        }
      }else {
        app.$message('获取发票信息失败，请重试');
      }
    })
  },
  // 获取附件列表信息
  getFileList(param) {
    if(this.data.fapiaoMsg.state != '1') {
      return;
    }
    app.$post(app.$url.getInvoiceAnnex, param).then((res) => {
      if(res.code == 200) {
        if(res.data.list && res.data.list.length >= 1) {
          this.setData({
            'fileList': res.data.list
          });
        }
      }else {
        app.$message(`获取发票附件失败，${res.msg}`);
      }
    })
  },
  // 点击查看pdf发票
  handleOpenPdf(e) {
    let item = e.currentTarget.dataset.item;
    if(!item.c_url) {
      app.$messag('发票文件不存在，请联系管理员查看原因');
      return
    }
    wx.downloadFile({
      // 示例 url，并非真实存在
      url: item.c_url,
      success: function (res) {
        const filePath = res.tempFilePath
        wx.openDocument({
          filePath: filePath,
          success: function (res) {
            console.log('打开文档成功')
          },
          fail() {
            app.$message('打开文件失败，请联系管理员！');
          },
        })
      },
      fail:function (err) {
        app.$message('下载失败',err)
      }
    })
  },
  // 打开邮箱弹窗
  handleOpenEmailDialog() {
    if(!(this.data.fileList instanceof Array && this.data.fileList.length >= 1)) {
      app.$message('该发票还未生成附件或者附件为空，无须发送到邮箱。');
      return
    }
    this.setData({
      'batchDialog': true
    })
  },
  //邮箱input
  emailInput(e){
    this.setData({
      userEmail:e.detail.value
    })
  },
  // 点击邮箱弹窗按钮
  tapBatchDialogButton(e) {
    // 点击取消按钮
    if(e.detail.index == 0) {
      this.setData({
        batchDialog: false
      })
      return 
    }
    // 点击确定按钮-发送邮件 //参数
    let param = {
      toEmail:this.data.userEmail,
      waterwayCargoId: this.data.waterWayCargoId,
      fileList: [],
    }
    for(let i = 0; i < this.data.fileList.length; i++) {
      param.fileList.push(this.data.fileList[i].c_url);
    }
    // 邮箱校验格式
    let emailReg = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    let email = param.toEmail // 邮箱
    if(!email){
      app.$message("请输入邮箱")
      return
    }
    if(email && !emailReg.test(email)) {
      app.$message("你输入的邮箱格式不正确!")
      return;
    } 
    app.$post(app.$url.sendInvoiceAnnexToEmail, param).then(res=>{
      if(res.code == 200){
        app.$message(res.msg)
      }else {
        app.$message(res.msg)
      }
    })
    this.setData({
      batchDialog:false
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
package com.ruoyi.common.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.SysMessage;
import com.ruoyi.common.domain.SysMessageModel;
import com.ruoyi.common.domain.SysUserMessage;
import com.ruoyi.common.domain.SysUserSubscription;
import com.ruoyi.common.domain.bo.UserMessageBO;
import com.ruoyi.common.domain.vo.UserMessageVO;
import com.ruoyi.common.enums.MessageIsRead;
import com.ruoyi.common.enums.SendMessageEnum;
import com.ruoyi.common.mapper.NoticeMapper;
import com.ruoyi.common.mapper.SysMessageMapper;
import com.ruoyi.common.service.AppNoticeService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.message.WechatMessageUtil;
import com.ruoyi.common.utils.message.request.mpTem.DataValue;
import com.ruoyi.common.utils.message.request.weappTem.WechatTemplate;
import com.ruoyi.common.utils.message.responce.RespPo;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.net.URISyntaxException;
import java.util.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/5 10:52
 */
@Slf4j
@Service
public class AppNoticeServiceImpl extends ServiceImpl<NoticeMapper,SysUserMessage> implements AppNoticeService {

    @Autowired
    private NoticeMapper noticeMapper;
    @Autowired
    private WechatMessageUtil wechatMessageUtil;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysMessageMapper sysMessageMapper;

    /**
     * 订阅
     * @param userMessageBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult subscription(UserMessageBO userMessageBO) {
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userId = loginUser.getUserId();

        SysUserSubscription subscription = new SysUserSubscription();
        subscription.setUserId(userId);
        subscription.setConsignId(userMessageBO.getConsignId());
        subscription.setConsignDetailId(userMessageBO.getConsignDetailId());

        List<SysUserSubscription> list = noticeMapper.selectSubscription(subscription);

        if (list.size() > 0) {
            return AjaxResult.error("此托运单已订阅");
        }

        noticeMapper.addSubscription(subscription);

        return AjaxResult.success("订阅成功");
    }

    /**
     * 取消订阅
     * @param userMessageBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult cancelSubscription(UserMessageBO userMessageBO) {
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userId = loginUser.getUserId();

        SysUserSubscription subscription = new SysUserSubscription();
        subscription.setUserId(userId);
        subscription.setConsignId(userMessageBO.getConsignId());
        subscription.setConsignDetailId(userMessageBO.getConsignDetailId());

        noticeMapper.deleteSubscription(subscription);

        return AjaxResult.success("取消订阅成功");
    }

    /**
     * 发送消息
     * @param userMessageBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult sendMessage(UserMessageBO userMessageBO) {

        /**
         * 0.根据状态区分使用的信息模板
         * 1.获取模板
         * 2.根据map内容填充模板
         * 3.查找订阅的用户
         * 4.发送消息
         */
        Map<String, Object> map;
        String newTitle;
        String newContent;
        Long messageId;
        switch (Objects.requireNonNull(SendMessageEnum.getTypeByValue(userMessageBO.getSendMessageType()))) {
            case BARGE_RECORD:
                map = getMsgTemp(SendMessageEnum.BARGE_RECORD.getCode());

                newTitle = ((String) map.get("title"));
                newContent = ((String) map.get("content"));
                newContent = StrUtil.format(newContent,
                        userMessageBO.getParams().get("first"),
                        userMessageBO.getParams().get("keyword1"),
                        userMessageBO.getParams().get("keyword2"),
                        userMessageBO.getParams().get("remark"));

                messageId = addMessage(newTitle, newContent, (Long) map.get("modelId"), null);
                // 发送小程序消息 驳船主或船公司备案申请人
                this.sendUserMsg(userMessageBO.getUserIds(), messageId);
                break;
            case LOGISTICS_STOWAGE:
                map = getMsgTemp(SendMessageEnum.LOGISTICS_STOWAGE.getCode());

                newTitle = ((String) map.get("title"));
                newContent = ((String) map.get("content"));
                newContent = StrUtil.format(newContent,
                        userMessageBO.getParams().get("first"),
                        userMessageBO.getParams().get("keyword1"),
                        userMessageBO.getParams().get("keyword2"),
                        userMessageBO.getParams().get("keyword3"),
                        userMessageBO.getParams().get("remark"));

                messageId = addMessage(newTitle, newContent, (Long) map.get("modelId"), null);
                // 发送小程序消息 驳船主(发给所有订阅的人)
                List<Long> userIdList = this.sendSubscriptionMsg(userMessageBO, messageId);
                // 发送公众号
                Map<String, DataValue> dataSub = new LinkedHashMap<>();
                dataSub.put("first", new DataValue((String) userMessageBO.getParams().get("first")));
                dataSub.put("keyword1", new DataValue((String) userMessageBO.getParams().get("keyword1")));
                dataSub.put("keyword2", new DataValue((String) userMessageBO.getParams().get("keyword2")));
                dataSub.put("keyword3", new DataValue((String) userMessageBO.getParams().get("keyword3")));
                dataSub.put("remark", new DataValue((String) userMessageBO.getParams().get("remark")));
                this.sendPublic(userIdList, SendMessageEnum.LOGISTICS_STOWAGE.getCode(), dataSub);
                break;
            case PIER_CONFIRM:
                map = getMsgTemp(SendMessageEnum.PIER_CONFIRM.getCode());

                newTitle = ((String) map.get("title"));
                newContent = ((String) map.get("content"));
                newContent = StrUtil.format(newContent,
                        userMessageBO.getParams().get("first"),
                        userMessageBO.getParams().get("keyword1"),
                        userMessageBO.getParams().get("keyword2"),
                        userMessageBO.getParams().get("keyword3"),
                        userMessageBO.getParams().get("remark"));

                //jinn 2021-11-22 发送实装数消息时，先判断之前是否有发送记录，有的话需删掉（只做删除标识，不显示在驳船主消息中）
                List<SysMessage> sysMessageList = sysMessageMapper.selectList(new LambdaQueryWrapper<SysMessage>().eq(SysMessage::getWaterCargoId, userMessageBO.getWaterCargoId()));
                for(SysMessage sysMessage: sysMessageList){
                    this.update(new LambdaUpdateWrapper<SysUserMessage>().set(SysUserMessage::getDelFlag, 1).eq(SysUserMessage::getMessageId, sysMessage.getId()));
                }

                messageId = addMessage(newTitle, newContent, (Long) map.get("modelId"), userMessageBO.getWaterCargoId());
                // 发送小程序消息 预约人
                this.sendUserMsg(userMessageBO.getUserIds(), messageId);
                return new AjaxResult(200,"发送成功",messageId);
            case BARGE_MONTHLY_CHECK:
                // 驳船主月结审批消息

                map = getMsgTemp(SendMessageEnum.BARGE_MONTHLY_CHECK.getCode());

                newTitle = ((String) map.get("title"));
                newContent = ((String) map.get("content"));
                newContent = StrUtil.format(newContent, userMessageBO.getParams().get("consignFlag"), DateUtils.getTime(), userMessageBO.getParams().get("bargeName") + "，" + userMessageBO.getParams().get("name"));

                messageId = addMessage(newTitle, newContent, (Long) map.get("modelId"), null);

                List<Long> userIds = userMessageBO.getUserIds();
                // 发送船公司用户
                this.sendUserMsg(userIds, messageId);
                // 发送公众号
                Map<String, DataValue> data = new LinkedHashMap<>();
                data.put("first", new DataValue("您有一份月结订单需要审核！"));
                data.put("keyword1", new DataValue((String) userMessageBO.getParams().get("consignFlag")));
                data.put("keyword2", new DataValue(DateUtils.getTime()));
                data.put("keyword3", new DataValue(userMessageBO.getParams().get("bargeName") + "，" + userMessageBO.getParams().get("name")));
                data.put("remark", new DataValue("请点击查看详情"));
                this.sendPublic(userIds, SendMessageEnum.BARGE_MONTHLY_CHECK.getCode(), data);
                break;
            case BARGE_CONSIGN_CONFIRM:

                map = getMsgTemp(SendMessageEnum.BARGE_CONSIGN_CONFIRM.getCode());

                newTitle = ((String) map.get("title"));
                newContent = ((String) map.get("content"));
                newContent = StrUtil.format(newContent, userMessageBO.getParams().get("consignFlag"), userMessageBO.getParams().get("name"));

                addMessage(newTitle, newContent, (Long) map.get("modelId"), null);
                return AjaxResult.success();
            case CHARGE_BACK:
                map = getMsgTemp(SendMessageEnum.CHARGE_BACK.getCode());

                newTitle = ((String) map.get("title"));
                newContent = ((String) map.get("content"));
                newContent = StrUtil.format(newContent,
                        userMessageBO.getParams().get("first"),
                        userMessageBO.getParams().get("keyword1"),
                        userMessageBO.getParams().get("keyword2"),
                        userMessageBO.getParams().get("remark"));

                messageId = addMessage(newTitle, newContent, (Long) map.get("modelId"), null);
                // 发送小程序消息 预约人
                this.sendUserMsg(userMessageBO.getUserIds(), messageId);
                break;
            default:
                return AjaxResult.error("未知状态");
        }

        log.info("NoticeService - sendMessage - 发送消息成功");

        return AjaxResult.success("发送消息成功");
    }

    /**
     * 获取模板信息
     * @param code
     * @return
     */
    private Map<String, Object> getMsgTemp(String code) {
        Map<String, Object> map = new LinkedHashMap<>();
        SysMessageModel model = noticeMapper.selectMegModelByCode(code);
        map.put("title", model.getTitle());
        map.put("content", model.getContent());
        map.put("modelId", model.getId());
        return map;
    }

    /**
     * 插入消息表
     * @param newTitle
     * @param newContent
     * @param modelId
     * @return
     */
    private Long addMessage(String newTitle, String newContent, Long modelId, String waterCargoId) {
        // 插入消息表
        SysMessage msg = new SysMessage();
        msg.setTitle(newTitle);
        msg.setContent(newContent);
        msg.setMessageModelId(modelId);
        msg.setCreateTime(DateUtils.getNowDate());

        //2021.08.27 jinn 添加水路运单
        msg.setWaterCargoId(waterCargoId);

        noticeMapper.addMessage(msg);

        return msg.getId();
    }

    /**
     * 获取消息通知列表
     * @param userMessageBO
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public AjaxResult getNoticeList(UserMessageBO userMessageBO) {
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userId = loginUser.getUserId();

        userMessageBO.setUserId(userId);

        List<UserMessageVO> list = noticeMapper.getUserMessageList(userMessageBO);

        return AjaxResult.success("获取消息通知列表成功", list);
    }


    /**
     * 更新消息读取状态为已读
     * @param userMessageBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult updateIsRead(UserMessageBO userMessageBO) {

        userMessageBO.setIsRead(MessageIsRead.READ.getCode());
        noticeMapper.updateIsReadById(userMessageBO);
        log.info("AppNoticeService - updateIsRead - 更新消息读取状态成功");

        return AjaxResult.success("更新消息读取状态成功");
    }


    public List<String> getPublicOpenid(){
            return null;
    }

    /**
     * 给非订阅用户发消息
     * @param userIdList 用户id集合
     * @param messageId 消息id
     */
    private void sendUserMsg(List<Long> userIdList, Long messageId) {
        List<SysUserMessage> sysUserMessages=new ArrayList<>();
        // 发送消息
        userIdList.forEach(userId -> {
            SysUserMessage message = new SysUserMessage();
            message.setUserId(userId);
            message.setMessageId(messageId);
            sysUserMessages.add(message);
        });
        // 插入Sys_User_Message用户消息表
        this.saveBatch(sysUserMessages);
    }

    /**
     * 给订阅用户发送消息
     * @param userMessageBO consignId - 运单主表id, consignDetailId - 运单明细表id
     * @param messageId 消息id
     * @return 订阅用户id集合
     */
    private List<Long> sendSubscriptionMsg(UserMessageBO userMessageBO, Long messageId) {
        // 查找订阅的用户
        SysUserSubscription subscription = new SysUserSubscription();
        subscription.setConsignId(userMessageBO.getConsignId());
        subscription.setConsignDetailId(userMessageBO.getConsignDetailId());
        List<SysUserSubscription> subscriptionList = noticeMapper.selectSubscription(subscription);

        List<SysUserMessage> sysUserMessages=new ArrayList<>();
        List<Long> userIds = new ArrayList<>();
        // 4.发送消息
        subscriptionList.forEach(item -> {
            SysUserMessage message = new SysUserMessage();
            message.setUserId(item.getUserId());
            message.setMessageId(messageId);
            sysUserMessages.add(message);
            userIds.add(item.getUserId());
        });
        // 插入Sys_User_Message用户消息表
        this.saveBatch(sysUserMessages);
        return userIds;
    }


    /**
     * 发送公众号
     * @param userIds 要发送的用户id
     * @param templateId 模板id
     * @param data 消息数据
     */
    private void sendPublic(List<Long> userIds, String templateId, Map<String, DataValue> data) {

        /**
         * 1.根据订阅用户的unionid查询用户关注公众号的openid
         * 2.组装、发送消息
         */
        List<String> openIds = new LinkedList<>();
        //List<Map<String, String>> userList = new LinkedList<>();
        for (Long userId : userIds) {
            //SysUser sysUser = sysUserMapper.selectUserById(userId);
            String openId = sysUserMapper.selectPublicUser(userId);
            if (StringUtils.isNotEmpty(openId)) {
                openIds.add(openId);
            } else {
                log.error("用户id"+userId+"未关注公众号");
            }
        }

        // 推送公众号
        if(openIds.size()>0){
            try {
                String publicAccessToken = wechatMessageUtil.getPublicAccessToken();

                for (String openId : openIds) {
                    WechatTemplate wechatTemplate = new WechatTemplate();//需要区分模板类型及策略
                    wechatTemplate.setTouser(openId);
                    wechatTemplate.setTemplate_id(templateId);
                    wechatTemplate.setData(data);
                    RespPo respPo = wechatMessageUtil.templateMsgSend(wechatTemplate, publicAccessToken);
                    if (respPo.getErrcode() != 0) {//表示发送失败
                        //记录日志
                        log.error("公众号发送消息失败-> 模->" + wechatTemplate.getTemplate_id() + ";用户->" + openId + ";错误代码：" + respPo.getErrcode() + ";错误信息：" + respPo.getErrmsg());
                    } else {
                        log.info("用户：" + openId + "推送公众号成功" + ";代码：" + respPo.getErrcode() + ";信息：" + respPo.getErrmsg());
                    }
                }
            } catch (URISyntaxException e) {
                e.printStackTrace();
                log.info("公众号发送消息获取AccessToken失败->"+e.getMessage());
            }

        }
    }
}

package com.ruoyi.app.controller.carrier;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.Customer;
import com.ruoyi.common.domain.bo.CargocmentBO;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.bo.CoutformBO;
import com.ruoyi.common.domain.dto.CargocmentDTO;
import com.ruoyi.common.domain.vo.CargocmentVO;
import com.ruoyi.common.domain.vo.CargocmentdetailVO;
import com.ruoyi.common.domain.vo.CoutformVO;
import com.ruoyi.common.domain.vo.PubPortVo;
import com.ruoyi.common.enums.PayWayEnum;
import com.ruoyi.common.enums.WxOperateStatus;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.CargocmentService;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.service.CoutformService;
import com.ruoyi.common.service.CustomerService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.consignor.domain.vo.ConsignorConsignVO;
import com.ruoyi.consignor.service.ConsignorConsignService;
import com.ruoyi.databarge.domain.vo.PortLoadingMsgVO;
import com.ruoyi.databarge.service.Pb6BargeCheckMessageService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 船公司用户端
 * <AUTHOR>
 * @Date 2020/7/24 15:07
 */
@RestController
@RequestMapping("/carrierapp")
public class CarrierController {

    @Autowired
    private CargocmentService cargocmentService;

    @Autowired
    private CargocmentdetailService cargocmentdetailService;
    @Autowired
    private CoutformService coutformService;

    @Autowired
    private CustomerService customerService;
    @Autowired
    private ConsignorConsignService consignorConsignService;
    @Autowired
    private Pb6BargeCheckMessageService pb6BargeCheckMessageService;

    // ---------------------运单管理----------------------//
    /**
     * 查询运单列表(待审核tab)
     * @return
     */
    @RequestMapping("/waybills/billslist")
    public AjaxResult billslist(@RequestBody CargocmentdetailBO cargocmentdetailBO){
        if(cargocmentdetailBO.getTabState()==null){
            throw new CustomException("请求参数异常");
        }
        //根据不同的tab栏获取对应的数据
        if(cargocmentdetailBO.getTabState().intValue()==0){//待审核列表
            List<ConsignorConsignVO> list = cargocmentService.billslist(cargocmentdetailBO);
            return AjaxResult.success(list);
        }else if(cargocmentdetailBO.getTabState().intValue()==1){//1.待支付/待预约列表
            List<CargocmentVO> list = cargocmentdetailService.bargebillsList(cargocmentdetailBO);
            return AjaxResult.success(list);
        }else if(cargocmentdetailBO.getTabState().intValue()==2){//2.进行中
            List<CargocmentVO> list = cargocmentdetailService.bargebillsList(cargocmentdetailBO);
            return AjaxResult.success(list);
        }else if(cargocmentdetailBO.getTabState().intValue()==3){//3.已完成
            List<CargocmentVO> list = cargocmentdetailService.bargebillsList(cargocmentdetailBO);
            return AjaxResult.success(list);
        }else if(cargocmentdetailBO.getTabState().intValue()==4){//4.全部
            List<CargocmentVO> list = cargocmentdetailService.bargebillsList(cargocmentdetailBO);
            return AjaxResult.success(list);
        }else if (cargocmentdetailBO.getTabState().intValue()==5) { // 待确认
            List<CargocmentVO> list = cargocmentdetailService.bargebillsList(cargocmentdetailBO);
            return AjaxResult.success(list);
        } else {
            throw new CustomException("请求异常");
        }

    }
    //查询托运单各个状态
    @RequestMapping("/waybills/details")
    public AjaxResult details(@RequestBody CargocmentdetailBO cargocmentdetailBO){
        CargocmentBO cargocmentBO = consignorConsignService.selectConsignById(cargocmentdetailBO.getConsignId());
        return AjaxResult.success(cargocmentBO);
    }


    /**
     * 查询驳船的运单明细
     * @param cargocmentdetailBO
     * @return
     */
    @RequestMapping("/waybills/barge/details")
    public AjaxResult bargeDetails(@RequestBody CargocmentdetailBO cargocmentdetailBO){
//        List<Cargoconsignmentdetail> list = cargocmentdetailService.list(
//                new QueryWrapper<Cargoconsignmentdetail>()
//                        .eq("onineresourse","3")
//                        .eq(cargocmentdetailBO.getConsignId() != null,"consignid", cargocmentdetailBO.getConsignId())
//                        .eq(cargocmentdetailBO.getConsignFlag() != null,"consignflag", cargocmentdetailBO.getConsignFlag())
//        );
        Cargoconsignmentdetail byId = cargocmentdetailService.getById(cargocmentdetailBO.getId());
        PortLoadingMsgVO portLoadingMsgVO = pb6BargeCheckMessageService.searchPortLoadingInfo(byId.getWaterwayCargoId());
        if (portLoadingMsgVO != null) {
            byId.setTscargoweightValue(portLoadingMsgVO.getTscargoweightValue());
        }
        return AjaxResult.success(byId);
    }
    /**
     * 货主-船公司派船操作
     * @param cargocmentBO
     * @return
     */
    @RequestMapping("/waybills/selectShips")
    public AjaxResult waybillsSelectShips(@RequestBody CargocmentBO cargocmentBO){

        cargocmentService.waybillsSelectShips(cargocmentBO);

        return AjaxResult.success();
    }
//-----------------------------------托运单办理-----------------------------//
    /**
     * 出库单查询
     * @param coutformBO
     * @return
     */
    @RequestMapping("/waybills/coutformList")
    public AjaxResult waybillsCoutformList(@RequestBody CoutformBO coutformBO){

        List<CoutformVO> coutformList = coutformService.getCoutformList(coutformBO);

        return AjaxResult.success(coutformList);
    }

    /**
     * 获取目的港列表
     * @param coutformBO
     * @return
     */
    @RequestMapping("/waybills/searchEndPort")
    public AjaxResult searchEndPort(@RequestBody CoutformBO coutformBO){

        List<PubPortVo> vo = consignorConsignService.searchEndPort(coutformBO.getSearchValue());

        return AjaxResult.success(vo);
    }

    /**
     * 船公司查询
     * @param customer
     * @return
     */
    @RequestMapping("/waybills/customerList")
    public AjaxResult waybillsCustomerList(@RequestBody Customer customer){

        List<Customer> customerList = customerService.getCustomerList(customer);

        return AjaxResult.success(customerList);
    }

    /**
     * 船公司新增运单
     * @param cargocmentBO
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ship:consign:make')")
    @RequestMapping("/waybills/add")
    public AjaxResult addWaybills(@RequestBody CargocmentBO cargocmentBO){
        cargocmentService.addWaybills(cargocmentBO);

        return AjaxResult.success();
    }


    /**
     * 船公司修改运单
     * @param cargocmentBO
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ship:consign:update')")
    @RequestMapping("/waybills/update")
    public AjaxResult updateWaybills(@RequestBody CargocmentBO cargocmentBO){

        ///接其他接口
        consignorConsignService.updateBookingNote(cargocmentBO);

        return AjaxResult.success();
    }
    /**
     * 托运单-改单/退单
     *
     * @param cargocmentDTO
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ship:consign:chargeback')")
    @ApiOperation("托运单-(改单/退单)")
    @RequestMapping("/waybills/modify")
    public AjaxResult modify(@RequestBody CargocmentDTO cargocmentDTO){

        ///接其他接口
        consignorConsignService.modificationBookingNote(cargocmentDTO);

        return AjaxResult.success();
    }

    /**
     * 查询待审核的运单列表（1-退单，2-改单 3-月结,4-差额退款）
     */

    @RequestMapping("/waybills/auditlist")
    public AjaxResult auditList(@RequestBody CargocmentdetailBO cargocmentdetailBO){
        return AjaxResult.success(cargocmentdetailService.auditList(cargocmentdetailBO));
    }



    /**
     * 运单月结审批（批量）、退单、改单
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ship:approve:startAudit')")
    @RequestMapping("/waybills/startAudit")
    public AjaxResult startAudit(@RequestBody CargocmentdetailBO cargocmentdetailBO){
        cargocmentdetailService.startAudit(cargocmentdetailBO);
        return AjaxResult.success(cargocmentdetailBO);
    }

    /**
     * 运单支付
     * @return
     */
    @RequestMapping("/waybills/chargePay")
    public AjaxResult chargePay(@RequestBody CargocmentdetailBO cargocmentdetailBO){

        SysUser user = SecurityUtils.getLoginUser().getUser();

        String payWay = cargocmentdetailBO.getChargeBalanceType();
        Cargoconsignmentdetail cargoconsignmentdetail = new Cargoconsignmentdetail();
        cargoconsignmentdetail.setId(cargocmentdetailBO.getId());
        if(PayWayEnum.MONTHLY_PAY.getCodeName().equals(payWay)){//月结
            cargoconsignmentdetail.setChargeBalanceType("月结");
            cargoconsignmentdetail.setWxOperateState(WxOperateStatus.WAIT_COMPANY_CHECK.getCode());//待审核
            cargoconsignmentdetail.setWxMonthChargeById(user.getCompanyId());
            cargoconsignmentdetail.setWxMonthChargeByName(user.getCompanyName());

        }else if(PayWayEnum.WX_PAY.getCodeName().equals(payWay)){//现结
            //调用支付接口后回调更新
            cargoconsignmentdetail.setChargeBalanceType("现结");
            cargoconsignmentdetail.setWxOperateState(WxOperateStatus.WAIT_BARGE_RESERVE.getCode());//待预约
        }else{
            throw new CustomException("参数有误");
        }
        cargoconsignmentdetail.setWxUpdateById(user.getUserId());
        cargoconsignmentdetail.setWxUpdateTime(DateUtils.getTime());
        cargocmentdetailService.updateById(cargoconsignmentdetail);
        return AjaxResult.success();
    }

    /**
     * 运单驳船预约
     * @return
     */
    @RequestMapping("/waybills/bargeAppointment")
    public AjaxResult bargeAppointment(@RequestBody CargocmentdetailBO cargocmentdetailBO){
        SysUser user = SecurityUtils.getLoginUser().getUser();

        Cargoconsignmentdetail cargoconsignmentdetail = new Cargoconsignmentdetail();

        cargoconsignmentdetail.setId(cargocmentdetailBO.getId());
        cargoconsignmentdetail.setWxOperateState(WxOperateStatus.PASS_BARGE_RESERVE.getCode());
        cargoconsignmentdetail.setWxAppointmentTime(cargocmentdetailBO.getWxAppOintmentTime());
        cargoconsignmentdetail.setWxUpdateById(user.getUserId());
        cargoconsignmentdetail.setWxUpdateTime(DateUtils.getTime());
        cargocmentdetailService.updateById(cargoconsignmentdetail);

        return AjaxResult.success();
    }

    @ApiOperation("删除托运单")
    @PreAuthorize("@ss.hasPermi('ship:consign:delete')")
    @RequestMapping("/waybills/delete")
    public AjaxResult delete(@RequestBody CargocmentBO cargocmentBO) {
        consignorConsignService.deleteBookingNote(cargocmentBO);
        return AjaxResult.success("删除成功");
    }

    //模糊查询船公司的客户
    @PostMapping("/waybills/searchCustomerByName")
    public AjaxResult searchCustomerByName(){
        return AjaxResult.success(consignorConsignService.consignee());
    }

    /**
     * 取消改单/退单操作
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ship:consign:chargeback')")
    @ApiOperation("取消改单/退单操作")
    @PostMapping("/waybills/cancel")
    public AjaxResult cancel(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        consignorConsignService.cancelOperation(cargocmentdetailBO);
        return AjaxResult.success();
    }

    /**
     * 水路运单详情
     *
     * @param id 托运单明细id
     * @param status 1 改单需要数据 2水路详情
     * @return
     */
    @ApiOperation("水路运单详情")
    @GetMapping("/waybills/waybillsDetail")
    public AjaxResult waybillsDetail(@RequestParam("id") Long id , @RequestParam("status") Integer status) {
        return AjaxResult.success(consignorConsignService.waybillDetail(id, status));
    }
}

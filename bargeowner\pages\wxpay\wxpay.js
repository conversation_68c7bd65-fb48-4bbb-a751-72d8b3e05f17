// pages/wxpay/wxpay.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let that = this;
    console.log("options.appId:" + options.appId);
    console.log("options.package:" + options.package);
    console.log("options.nonceStr:" + options.nonceStr);
    console.log("options.timeStamp:" + options.timeStamp);
    console.log("options.paySign:" + options.paySign);
    console.log("options.signType:" + options.signType);
    console.log("options.resulturl:" + options.resulturl);

    var packageString = decodeURIComponent(options.package)
    var appId = decodeURIComponent(options.appId)
    var nonceStr = decodeURIComponent(options.nonceStr)
    var timeStamp = decodeURIComponent(options.timeStamp)
    var paySign = decodeURIComponent(options.paySign)
    var signType = decodeURIComponent(options.signType)
    var resulturl = decodeURIComponent(options.resulturl)

    console.log("appId:" + appId);
    console.log("nonceStr:" + nonceStr);
    console.log("timeStamp:" + timeStamp);
    console.log("paySign:" + paySign);
    console.log("signType:" + signType);
    console.log("package:" + packageString);
    console.log("resulturl:" + resulturl);
    console.log(wx.getStorageSync('payCurrentConsignObj'));
    wx.requestPayment({
        "appId": appId,
        "timeStamp": timeStamp,
        "nonceStr": nonceStr,
        "package": packageString,
        "signType": signType,
        "paySign": paySign,
        "success": function (res) {
          wx.showToast({
            title: '支付成功',
            icon: 'none',
            duration: 1000
          });
          that.payCallBack(resulturl);
        },
        "fail": function (res) {
          wx.navigateBack({
            delta: 2
          })
          
        },
        "complete": function (res) {
         
        }
    })
  },
  // 支付成功需要调用的接口
  payCallBack(resulturl) {
    let currentConsignObj = JSON.parse(wx.getStorageSync('payCurrentConsignObj'))//当前托运对象
    app.$post(app.$url.consign.getConsignDetail, {id:currentConsignObj.consignDetailId}).then(res=>{
      if(res.code == 200){
        console.log(res);
        let param = {
          id: currentConsignObj.consignDetailId,//运单详情id
          chargeBalanceType: 1,// 支付方式 0-月结 1-现结
          bargeId: res.data.bargeId,
          waterwayCargoId: res.data.waterWayCargoId,//水路运单编号
        }
        return  app.$post(app.$url.consign.defray,param)
      }else {
        app.$message(res.msg);
      }
    }).then((res) => {
      console.log(resulturl);
      console.log(res);
      if(res.code == 200){
        if (resulturl && resulturl.length >= 5) {
          wx.navigateTo({
              url: resulturl
          });
        }else {
          wx.showToast({
            title: '回调地址不存在，请联系您的管理员',
            icon: 'none',
            duration: 3000
          });
        } 
      }else {
        wx.showToast({
          title: '水路运单文件盖章失败，请联系你的管理员',
          icon: 'none',
          duration: 3000
        });
      }
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
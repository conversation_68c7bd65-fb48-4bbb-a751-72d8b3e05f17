{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "lazyloadPlaceholderEnable": false, "disableUseStrict": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true, "useStaticServer": true, "minifyWXML": true}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"plugin": {"list": []}, "game": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": [{"id": -1, "name": "pages/mainpage/main/main", "pathName": "pages/mainpage/main/main", "query": "", "scene": null}, {"id": -1, "name": "pages/mainpage/supplydetail/supplydetail", "pathName": "pages/mainpage/supplydetail/supplydetail", "query": "", "scene": null}, {"id": -1, "name": "pages/mainpage/transportpublish/transportpublish", "pathName": "pages/mainpage/transportpublish/transportpublish", "query": "", "scene": null}, {"id": 7, "name": "pages/minepage/mine/mine", "pathName": "pages/minepage/mine/mine", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/message/message", "pathName": "pages/minepage/message/message", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/crewlist/crewlist", "pathName": "pages/minepage/crewlist/crewlist", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/addcrew/addcrew", "pathName": "pages/minepage/addcrew/addcrew", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/record/record", "pathName": "pages/minepage/record/record", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/choosebarge/choosebarge", "pathName": "pages/minepage/choosebarge/choosebarge", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/addbarge/addbarge", "pathName": "pages/minepage/addbarge/addbarge", "query": "", "scene": null}, {"id": -1, "name": "pages/login/login", "pathName": "pages/login/login", "query": "", "scene": null}, {"id": 17, "name": "pages/login/login", "pathName": "pages/login/login", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/record/record", "pathName": "pages/minepage/record/record", "query": "", "scene": null}, {"id": 18, "name": "pages/minepage/mine/mine", "pathName": "pages/minepage/mine/mine", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/choosebarge/choosebarge", "pathName": "pages/minepage/choosebarge/choosebarge", "query": "", "scene": null}, {"id": -1, "name": "pages/mainpage/main/main", "pathName": "pages/mainpage/main/main", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/message/message", "pathName": "pages/minepage/message/message", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/crewlist/crewlist", "pathName": "pages/minepage/crewlist/crewlist", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/crewlist/crewlist", "pathName": "pages/minepage/crewlist/crewlist", "query": "", "scene": null}, {"id": 21, "name": "托运单列表", "pathName": "pages/consignment/consignmanager/consignmanager", "query": "", "scene": null}, {"id": 27, "name": "pages/consignment/consigndetail/bargeOutboundInquiry/bargeOutboundInquiry", "pathName": "pages/consignment/consigndetail/bargeOutboundInquiry/bargeOutboundInquiry", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/message/message", "pathName": "pages/minepage/message/message", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/choosebarge/choosebarge", "pathName": "pages/minepage/choosebarge/choosebarge", "query": "", "scene": null}, {"id": -1, "name": "pages/minepage/record/record", "pathName": "pages/minepage/record/record", "query": "", "scene": null}, {"id": -1, "name": "pages/login/login", "pathName": "pages/login/login", "query": "", "scene": null}, {"name": "托运单详情", "pathName": "pages/consignment/consigndetail/consigndetail", "query": "currentConsignObj={\"consignDetailId\":162675,\"bargeInfoId\":786,\"consignFlag\":\"TD0121010690\",\"cargeName\":\"煤炭（块煤除外）\",\"beginPort\":\"新沙\",\"endPort\":\"江门港\",\"rationWeight\":\"10\",\"wxRationContactNumber\":\"15816165040\",\"consignee\":\"测试\",\"flagBargeState\":\"1\",\"wxOperateState\":\"\",\"bargeName\":\"测试驳船050\",\"chargeBalanceType\":\"1\",\"isSubscription\":\"\",\"consignId\":159392,\"wxConfirmCheck\":\"\",\"bargeTel\":\"15816165040\",\"wxMonthChargeById\":\"\",\"wxMonthChargeByName\":\"\",\"customerUserId\":\"\",\"payUserId\":\"\",\"applyModify\":\"\",\"monthlyCode\":\"\",\"wxOperateStateText\":\"待确认\"}", "scene": null}, {"name": "预约", "pathName": "pages/consignment/reservation/reservation", "query": "currentConsignObj={\"consignDetailId\":162675,\"bargeInfoId\":786,\"consignFlag\":\"TD0121010690\",\"cargeName\":\"煤炭（块煤除外）\",\"beginPort\":\"新沙\",\"endPort\":\"江门港\",\"rationWeight\":\"10\",\"wxRationContactNumber\":\"15816165040\",\"consignee\":\"测试\",\"flagBargeState\":\"1\",\"wxOperateState\":\"\",\"bargeName\":\"测试驳船050\",\"chargeBalanceType\":\"1\",\"isSubscription\":\"\",\"consignId\":159392,\"wxConfirmCheck\":\"\",\"bargeTel\":\"15816165040\",\"wxMonthChargeById\":\"\",\"wxMonthChargeByName\":\"\",\"customerUserId\":\"\",\"payUserId\":\"\",\"applyModify\":\"\",\"monthlyCode\":\"\",\"wxOperateStateText\":\"待确认\"}", "scene": null}, {"name": "支付", "pathName": "pages/consignment/payment/payment", "query": "currentConsignObj={\"consignDetailId\":162675,\"bargeInfoId\":786,\"consignFlag\":\"TD0121010690\",\"cargeName\":\"煤炭（块煤除外）\",\"beginPort\":\"新沙\",\"endPort\":\"江门港\",\"rationWeight\":\"10\",\"wxRationContactNumber\":\"15816165040\",\"consignee\":\"测试\",\"flagBargeState\":\"1\",\"wxOperateState\":\"\",\"bargeName\":\"测试驳船050\",\"chargeBalanceType\":\"1\",\"isSubscription\":\"\",\"consignId\":159392,\"wxConfirmCheck\":\"\",\"bargeTel\":\"15816165040\",\"wxMonthChargeById\":\"\",\"wxMonthChargeByName\":\"\",\"customerUserId\":\"\",\"payUserId\":\"\",\"applyModify\":\"\",\"monthlyCode\":\"\",\"wxOperateStateText\":\"待确认\"}", "scene": null}, {"name": "改单", "pathName": "pages/consignment/consignmodify/consignmodify", "query": "currentConsignObj={\"consignDetailId\":162675,\"bargeInfoId\":786,\"consignFlag\":\"TD0121010690\",\"cargeName\":\"煤炭（块煤除外）\",\"beginPort\":\"新沙\",\"endPort\":\"江门港\",\"rationWeight\":\"10\",\"wxRationContactNumber\":\"15816165040\",\"consignee\":\"测试\",\"flagBargeState\":\"1\",\"wxOperateState\":\"\",\"bargeName\":\"测试驳船050\",\"chargeBalanceType\":\"1\",\"isSubscription\":\"\",\"consignId\":159392,\"wxConfirmCheck\":\"\",\"bargeTel\":\"15816165040\",\"wxMonthChargeById\":\"\",\"wxMonthChargeByName\":\"\",\"customerUserId\":\"\",\"payUserId\":\"\",\"applyModify\":\"\",\"monthlyCode\":\"\",\"wxOperateStateText\":\"待确认\"}", "scene": null}, {"name": "pages/minepage/record/record", "pathName": "pages/minepage/record/record", "query": "", "scene": null}, {"name": "pages/protocol/protocol", "pathName": "pages/protocol/protocol", "query": "", "scene": null}, {"name": "pages/minepage/record/record", "pathName": "pages/minepage/record/record", "query": "", "scene": null}, {"name": "pages/minepage/record/record", "pathName": "pages/minepage/record/record", "query": "", "scene": null}, {"name": "pages/consignment/consigndetail/consigndetail", "pathName": "pages/consignment/consigndetail/consigndetail", "query": "currentConsignObj={\"consignDetailId\":162675,\"bargeInfoId\":786,\"consignFlag\":\"TD0121010690\",\"cargeName\":\"煤炭（块煤除外）\",\"beginPort\":\"新沙\",\"endPort\":\"江门港\",\"rationWeight\":\"10\",\"wxRationContactNumber\":\"15816165040\",\"consignee\":\"测试\",\"flagBargeState\":\"1\",\"wxOperateState\":\"\",\"bargeName\":\"测试驳船050\",\"chargeBalanceType\":\"1\",\"isSubscription\":\"\",\"consignId\":159392,\"wxConfirmCheck\":\"\",\"bargeTel\":\"15816165040\",\"wxMonthChargeById\":\"\",\"wxMonthChargeByName\":\"\",\"customerUserId\":\"\",\"payUserId\":\"\",\"applyModify\":\"\",\"monthlyCode\":\"\",\"wxOperateStateText\":\"待确认\"}", "scene": null}, {"name": "pages/fapiao/fapiao", "pathName": "pages/fapiao/fapiao", "query": "", "scene": null}, {"name": "pages/checkfapiao/checkfapiao", "pathName": "pages/checkfapiao/checkfapiao", "query": "waterWayCargoId=BBZ042103151009", "scene": null}]}}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "libVersion": "3.6.6", "packOptions": {"ignore": [], "include": []}, "appid": "wx3f62ce7e9e20f483"}
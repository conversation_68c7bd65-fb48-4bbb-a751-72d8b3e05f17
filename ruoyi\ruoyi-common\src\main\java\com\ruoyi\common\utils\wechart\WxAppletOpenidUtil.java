package com.ruoyi.common.utils.wechart;

import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.utils.JacksonUtil;
import org.apache.commons.lang3.StringUtils;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;



import java.io.IOException;

/**
 * 微信小程序openid工作类
 * <AUTHOR>
 *
 */
@SuppressWarnings("deprecation")
@Component
public class WxAppletOpenidUtil {
	private Logger LOGGER = LoggerFactory.getLogger(this.getClass());

	final String openidUrl = "https://api.weixin.qq.com/sns/jscode2session";

	@Value("${wx-barge.app-id}")
    String bargeAppid;
	@Value("${wx-barge.app-secret}")
    String bargeSecret;
	@Value("${wx-barge.grant_type}")
    String bargeGrantType;

	@Value("${wx-consignor.app-id}")
	String consignorAppid;
	@Value("${wx-consignor.app-secret}")
	String consignorSecret;
	@Value("${wx-consignor.grant_type}")
	String consignorGrantType;

	/**
	 * 驳船主端小程序
	 * @param wxcode
	 * @return
	 */
	public WxOpenIdPo GetWxOpenIdBarge(String wxcode) {

		WxOpenIdPo info = null;
		// 微信API接口
		String url = openidUrl + "?appid=" + bargeAppid + "&secret=" + bargeSecret + "&js_code=" + wxcode + "&grant_type="
				+ bargeGrantType + "";

		HttpGet request = new HttpGet(url);
		HttpResponse response = null;
		try {
			@SuppressWarnings("resource")
			HttpClient client = new DefaultHttpClient();
			response = client.execute(request);
			if (response.getStatusLine().getStatusCode() == HttpStatus.SUCCESS) {
				String strResult = EntityUtils.toString(response.getEntity());
				if (!StringUtils.isEmpty(strResult)) {
					info = JacksonUtil.defaultInstance().json2pojo(strResult, WxOpenIdPo.class);
				}
			}
		} catch (IOException e) {
			e.printStackTrace();
			LOGGER.error("根据wxcode获取微信小程序openid失败(customer): wxcode(" + wxcode + ")");
		}

		return info;
	}
	/**
	 * 货主/船公司/托运单联系人端小程序
	 * @param wxcode
	 * @return
	 */
	public WxOpenIdPo GetWxOpenIdConsignor(String wxcode) {
		
		WxOpenIdPo info = null;
		// 微信API接口
		String url = openidUrl + "?appid=" + consignorAppid + "&secret=" + consignorSecret + "&js_code=" + wxcode + "&grant_type="
				+ consignorGrantType + "";
		
		HttpGet request = new HttpGet(url);
		HttpResponse response = null;
		try {
			@SuppressWarnings("resource")
			HttpClient client = new DefaultHttpClient();
			response = client.execute(request);
			if (response.getStatusLine().getStatusCode() == HttpStatus.SUCCESS) {
				String strResult = EntityUtils.toString(response.getEntity());
				if (!StringUtils.isEmpty(strResult)) {
					info = JacksonUtil.defaultInstance().json2pojo(strResult, WxOpenIdPo.class);
				}
			}
		} catch (IOException e) {
			e.printStackTrace();
			LOGGER.error("根据wxcode获取微信小程序openid失败(deliveryman): wxcode(" + wxcode + ")");
		}
		
		return info;
	}

}

package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.databarge.domain.ShipInvoiceHistory;
import com.ruoyi.databarge.domain.dto.ShipInvoiceHistorySearchDTO;
import com.ruoyi.databarge.domain.vo.ShipInvoiceHistoryVO;
import com.ruoyi.databarge.mapper.ShipInvoiceHistoryMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.databarge.service.ShipInvoiceHistoryService;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * 发票开票记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
@Service
public class ShipInvoiceHistoryServiceImpl extends ServiceImpl<ShipInvoiceHistoryMapper, ShipInvoiceHistory> implements ShipInvoiceHistoryService {

    public IPage<ShipInvoiceHistoryVO> searchPage(ShipInvoiceHistorySearchDTO shipInvoiceHistorySearchDTO) {
        Long pageNum= Optional.ofNullable(shipInvoiceHistorySearchDTO.getPageNum()).orElse(0L);
        Long pageSize= Optional.ofNullable(shipInvoiceHistorySearchDTO.getPageSize()).orElse(-1L);

        IPage<ShipInvoiceHistoryVO> shipInvoiceHistoryIPage=super.baseMapper.searchPage(new Page<>(pageNum,pageSize),shipInvoiceHistorySearchDTO);
        return shipInvoiceHistoryIPage;
    }

}

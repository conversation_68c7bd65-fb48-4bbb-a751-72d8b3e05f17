package com.ruoyi.consignor.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * @Description 货源类
 * <AUTHOR>
 * @Date 2020/8/14  16:29
 */
@Data
public class CargoSourceBO {

    /**
     * id
     */
    private Long id;

    /**
     * 货主id
     */
    private Long userId;

    /**
     * 货源号
     */
    private String cargoSourceId;

    /**
     * 货物名称
     */
    private String cargoName;

    /**
     * 出发地
     */
    private String placeOfDeparture;

    /**
     * 目的地
     */
    private String destination;

    /**
     * 货物数量
     */
    private Integer cargoNumber;

    /**
     * 货物重量
     */
    private String cargoWeight;

    /**
     * 货物体积
     */
    private String cargoBulk;

    /**
     * 货主名称
     */
    private String userName;


    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^(((13[0-9]{1})|(15[0-9]{1})|(18[0-9]{1}))+\\d{8})$", message = "手机号格式不正确")
    private String phone;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 搜索条件
     */
    private String search;

    /**
     * 创建人
     */
    private String createUser;


    /** 备注 */
    private String remark;



}

package com.ruoyi.databarge.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.databarge.domain.dto.BargeCheckInSearchDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeCheckInVO;
import com.ruoyi.databarge.domain.vo.Pb6CargoconsignmentVO;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <p>
 * 驳船托运单明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface Pb6CargoconsignmentdetailService extends IService<Pb6Cargoconsignmentdetail> {

    List<Pb6CargoconsignmentVO> searchPb6Cargoconsignment(Pb6CargoconsignmentSearchDTO pb6CargoconsignmentSearchDTO);

    Pb6CargoconsignmentDTO searchApplyDateByConsignFlag(String consignflag);

    void searchPortByEndPortName(String endPortName);

    List<Pb6Cargoconsignmentdetail> searchBargeByWxappointmenttime();

    IPage<BargeCheckInVO> searchBargeCheckINVO(BargeCheckInSearchDTO bargeCheckInSearchDTO);

    boolean updateInfoByID(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail);
//    List<BargeCheckInVO> searchBargeCheckINVO (BargeCheckInSearchDTO bargeCheckInSearchDTO);

    /**
     * 查询新沙当前预约报到的船的信息
     * @return mmsi集合
     */
    List<String> searchBargeOrder(Long comid) throws ExecutionException, InterruptedException;

    //新港西基驳船预报管理水路运单和出入库单完全模糊查询 2022/03/07
    IPage<BargeCheckInVO> xgXjSearch(BargeCheckInSearchDTO bargeCheckInSearchDTO);


    // 查询托运单下的明细列表
    List<Pb6Cargoconsignmentdetail> searchByConsignId(Long consignid);

    // 参数驳船信息对象，在驳船信息表中查询所有驳船名+航次相同、并且提单号不同的驳船信息
    List<Pb6Cargoconsignmentdetail> searchRepeatByBargeInfo(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail);

    // 参数驳船信息对象，在驳船信息表中查询所有驳船名+航次相同、并且大船计划不同的驳船信息
    List<Pb6Cargoconsignmentdetail> searchRepeatBarge(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail);

    // 参数驳船信息对象，在驳船信息表中查询所有驳船名+航次相同、并且驳船id不同的驳船信息
    List<Pb6Cargoconsignmentdetail> searchRepeatBargeExceptNow(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail);

    // 根据大船ID查询驳船信息
    List<Pb6Cargoconsignmentdetail> searchByShipInfoId(Long shipInfoId);

}

/* pages/consigncontact/fapiao/fapiao.wxss */
page{
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.fapiao-page{
  padding: 20rpx 10rpx 0rpx 10rpx;
  flex: 1;
  overflow-y: auto;
}
.fapiao-item{
  height: 90rpx;
  border-bottom: 1px solid #ddd;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 730rpx;
}
.fapiao-item-label{
  flex: 1;
  padding: 0 20rpx;
}
.fapiao-type{
  display: flex;
  flex-direction: row;
}
.fapiao-type-item{
  width: 200rpx;
}
.fapiao-title{
  flex-shrink: 0;
  padding: 0rpx 10rpx;
}
.fapiao-header{
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 730rpx;
}
.picker-title{
  flex: 1;
}
.fapiao-content{
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-left: 20rpx;
}
.fapiao-item-ipt{
  flex: 1;
  width: 100%;
  height: 70rpx;
  padding: 0 20rpx;
}
.require{
  color: red;
}
.login-wrap{
  flex-shrink: 0;
  height: 150rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.login-wrap .login-btn{
  width: 80%;
  height: 90rpx;
  padding: 0rpx;
  border-radius: 10rpx;
  border: none;
  line-height: 90rpx;
  color: #fff;
  background: rgb(0, 66, 107);
}
.fapiaotips {
  text-align: center;
}
package com.ruoyi.api.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.api.domain.AcceptOrderUser;
import com.ruoyi.api.domain.AtMessageParam;
import com.ruoyi.api.domain.MsgBody;
import com.ruoyi.api.service.AtService;
import com.ruoyi.basic.mapper.SlaveMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.bo.UserMessageBO;
import com.ruoyi.common.enums.BargeCheckMessageType;
import com.ruoyi.common.enums.CheckEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.databarge.domain.WechatMpUser;
import com.ruoyi.databarge.service.WechatMpUserService;
import com.ruoyi.databarge.wechat.MpMessageDTO;
import com.ruoyi.databarge.wechat.MpMessageResult;
import com.ruoyi.databarge.wechat.MpUtil;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.wechat.service.WechatMpAccessTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/11/1 9:17
 */
@Service
@Slf4j
public class AtServiceImpl implements AtService {

    @Autowired
    private SlaveMapper slaveMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private WechatMpUserService wechatMpUserService;

    @Autowired
    private WechatMpAccessTokenService wechatMpAccessTokenService;

    @Autowired
    private MpUtil mpUtil;


    @Override
    public AjaxResult atMessage(AtMessageParam atMessageParam) {
        System.out.println("接收安通消息");
        System.out.println(atMessageParam); // atMessageParam

        MsgBody msgBody = atMessageParam.getMsgBody();
        String orderNum = msgBody.getOrderNum();
        String mqType = msgBody.getMqType();
        String startPortCode = msgBody.getStartPortCode();
        String endPortCode = msgBody.getEndPortCode();

        String endPortName = startPortCode;
        String startPortName = endPortCode;

        // 如果起运港或者目的港编码不为空，查询港口名称
        if (startPortCode != null && !"".equals(startPortCode)) {
            String atPortName = slaveMapper.selectPortameByAtPortCode(startPortCode);
            if(atPortName != null && !"".equals(atPortName)) {
                startPortName = atPortName;
            }
        }
        if (endPortCode != null && !"".equals(endPortCode)) {
            String atPortName = slaveMapper.selectPortameByAtPortCode(endPortCode);
            if(atPortName != null && !"".equals(atPortName)) {
                endPortName = atPortName;
            }
        }

        String shipName = msgBody.getShipName();
        String voyage = msgBody.getVoyageNum();

        // 查询穿巴库所有的单证接单员
        List<AcceptOrderUser> acceptOrderUsers = slaveMapper.selectAcceptOrderUserList();
        // 遍历，根据手机号匹配开单库的用户
        for (AcceptOrderUser acceptOrderUser : acceptOrderUsers) {
            // 根据手机号查询用户
            String phonenumber = acceptOrderUser.getPhonenumber();
            List<SysUser> sysUsers = sysUserMapper.selectUserByPhoneNumber(phonenumber);
            if (sysUsers.size() > 0) {
                SysUser sysUser = sysUsers.get(0);
                // unionId不为空
                if (sysUser.getUnionId() != null && !"".equals(sysUser.getUnionId())) {
                    // 发送消息
                    System.out.println("发送消息给：" + sysUser.getUserName());

                    // 查询前，先根据openId去查询用户信息
                    wechatMpUserService.getUserInfo(sysUser.getUnionId());

                    List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                    if (wechatMpUserList.size() == 0) {
                    } else if (wechatMpUserList.size() == 1) {

                        Map<String, Object> params = new HashMap<>();
                        params.put("orderNum", orderNum);
                        params.put("cusName", "泉州安通物流有限公司");
                        // 获取当前时间
                        Date date = new Date();
                        // 格式化时间
                        String time = DateUtils.parseDateToStr("yyyy年MM月dd日 HH:mm:ss", date);
                        params.put("time",time);
                        // 船名/航次
                        params.put("shipVoyage", shipName + "/" + voyage);
                        // 起运港/目的港
                        params.put("startEndPort", startPortName + "/" + endPortName);

                        String accessToken = wechatMpAccessTokenService.getAccessToken();

                        // 根据当前消息类型，发送不同的消息
                        if(mqType.equals("09")){
                            // 发送订单取消消息

                            MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                                    .template_id("WXW7FLN5ugdkEXwv0_J7Mo6h2npndGKUML6X3pncDqc")
                                    .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("cusName").toString()).color("#000000").build())
                                    .data("character_string2", MpMessageDTO.MpMessageDataField.builder().value(params.get("orderNum").toString()).color("#000000").build())
                                    .data("thing3", MpMessageDTO.MpMessageDataField.builder().value(params.get("shipVoyage").toString()).color("#000000").build())
                                    .data("time4", MpMessageDTO.MpMessageDataField.builder().value(params.get("time").toString()).color("#000000").build())
                                    .build();
                            System.out.println("accessToken: " + accessToken);
                            MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);

                            log.info("发送订单取消消息：{}",mpMessageResult);
                        }else if(mqType.equals("01")){
                            // 新订单消息
                            MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                                    .template_id("epuV8xv_whLJLJ3XV2RCEHHc7nhA9UDzOT5dI7-coLg")
                                    .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("cusName").toString()).color("#000000").build())
                                    .data("character_string2", MpMessageDTO.MpMessageDataField.builder().value(params.get("orderNum").toString()).color("#000000").build())
                                    .data("thing3", MpMessageDTO.MpMessageDataField.builder().value(params.get("startEndPort").toString()).color("#000000").build())
                                    .data("time4", MpMessageDTO.MpMessageDataField.builder().value(params.get("time").toString()).color("#000000").build())
                                    .build();
                            System.out.println("accessToken: " + accessToken);
                            MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);

                            log.info("发送新订单消息：{}",mpMessageResult);
                        }else if(mqType.equals("02") || mqType.equals("03")){

                            String type = "";
                            if(mqType.equals("02")){
                                type = "订单信息变更";
                            }else{
                                type = "集装箱清单更新";
                            }

                            // 订单信息更新
                            MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                                    .template_id("aGRjOLf9mwTkW4qCWKrHmmocjKbgCPM4nYyn5RDvPlg")
                                    .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("cusName").toString()).color("#000000").build())
                                    .data("character_string2", MpMessageDTO.MpMessageDataField.builder().value(params.get("orderNum").toString()).color("#000000").build())
                                    .data("thing3", MpMessageDTO.MpMessageDataField.builder().value(params.get("startEndPort").toString()).color("#000000").build())
                                    .data("thing4", MpMessageDTO.MpMessageDataField.builder().value(type).color("#000000").build())
                                    .data("time5", MpMessageDTO.MpMessageDataField.builder().value(params.get("time").toString()).color("#000000").build())
                                    .build();
                            System.out.println("accessToken: " + accessToken);
                            MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);

                            log.info("发送订单信息更新消息：{}",mpMessageResult);
                        }

                    } else {
                        throw new CustomException("数据库数据错误!");
                    }



                }
            }
        }

        return AjaxResult.success();

    }
}

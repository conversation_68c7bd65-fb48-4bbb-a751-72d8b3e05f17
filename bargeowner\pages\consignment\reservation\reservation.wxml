<!--pages/consignment/reservation/reservation.wxml-->
<view class="payment-page" hover-class="none" hover-stop-propagation="false">
  <view class="payment-step" hover-class="none" hover-stop-propagation="false">
    <text class="payment-text" selectable="false" space="false" decode="false">第一步</text>
    <mp-icon class="arrow-icon" type="outline" icon="arrow" color="#00426B" size="{{12}}"></mp-icon>
    <text class="payment-tex-active" selectable="false" space="false" decode="false">第二步</text>
  </view>

  <view class="order-list" hover-class="none" hover-stop-propagation="false">
    <picker mode="date" value="{{appointmentDate}}"  bindchange="bindDateChange">
      <view class="order-item" hover-class="none" hover-stop-propagation="false">
        <view class="order-item-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>预约日期：</view>
        <view class="order-item-date" hover-class="none" hover-stop-propagation="false">{{appointmentDate}}</view>
        <mp-icon class="arrow-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{18}}"></mp-icon>
      </view>
    </picker>
    <picker mode="time" value="{{appointmentTime}}"  bindchange="bindTimeChange">
      <view class="order-item" hover-class="none" hover-stop-propagation="false">
        <view class="order-item-title" hover-class="none" hover-stop-propagation="false"><text class="star">*</text>预约时间：</view>
        <view class="order-item-date" hover-class="none" hover-stop-propagation="false">{{appointmentTime}}</view>
        <mp-icon class="arrow-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{18}}"></mp-icon>
      </view>
    </picker>
  </view>
  
  <view class="consign-top" hover-class="none" hover-stop-propagation="false">
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">驳船名称：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.bargeName}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运单号：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.consignFlag}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">水路运单：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.waterWayCargoId}}</view>
    </view>
    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">创建时间：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.applyDate}}</view>
    </view> -->
  </view>

  <view class="consign-top" hover-class="none" hover-stop-propagation="false">
    <view class="consign-title" hover-class="none" hover-stop-propagation="false">
      运单信息
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">申请日期：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.applyDate}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">出库单号：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.outOrInformId}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">货物名称：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.cargeName}}</view>
    </view>

    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运人公司：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.consigner}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">起运港：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.beginPort}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">目的港：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.endPort}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">中转港：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.midPort}}</view>
    </view>
    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">发货人：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.consigner}}</view>
    </view> -->
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">收货人：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.consignee}}</view>
    </view>
    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">装货地点：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.shipmentPlace}}</view>
    </view>

    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">卸货地点：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.shipmentunPlace}}</view>
    </view> -->
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">地磅单号：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.loadometerId}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">发货符号：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.consignmentFlag}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">包装方式：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.packageType}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">承运船舶公司：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.shippingCoName}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运单配载重量（吨）：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.rationWeight}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运单配载件数(件)：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.rationPiece == null ? '' : form.rationPiece}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运单联系人：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.wxRationContactNumber}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">驳船主联系电话：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.bargeTel}}</view>
    </view>

    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">费用结算方式：</view>
      <radio-group bindchange="radioChange" class="radio-list">
        <label class="radio-item">
          <radio class="" value="0"  color="#00426B" checked="{{form.chargeBalanceType==='0'}}" disabled="{{form.chargeBalanceType==1}}"></radio>
          <view class="" hover-class="none" hover-stop-propagation="false">公司月结</view>
        </label>
        <label class="radio-item">
          <radio class="" value="1"   color="#00426B" checked="{{form.chargeBalanceType==1}}" disabled="{{form.chargeBalanceType==0}}"></radio>
          <view class="" hover-class="none" hover-stop-propagation="false">现结</view>
        </label>
      </radio-group>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">月结单位：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.wxMonthChargeByName}}</view>
    </view>
    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运单实际装货量：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.workWeight}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">是否加急：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{!form.isHarry ? "" :(form.isHarry == 'Y' ? '是' : '否') }}</view>
    </view> -->
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">特约事项：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.specialProceeding}}</view>
    </view>
    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">预约时间：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.wxAppointmentTime}}</view>
    </view> -->
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">实装数：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.workWeight}}</view>
    </view>
  </view>

  <view class="consign-top" hover-class="none" hover-stop-propagation="false">
    <view class="consign-title" hover-class="none" hover-stop-propagation="false">
      费用信息
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">货物港物费：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.cargoPortCharge}}</view>
    </view>

    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">围油栏费：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.serviceAgentCharge}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">代理费：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.businessAgentCharge}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">停泊费：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.berthCharge}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">总费用：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.totalCharge}}</view>
    </view>
  </view>
  <view class="consign-top" hover-class="none" hover-stop-propagation="false" wx:if="{{payRecordList.length >= 1}}">
    <view class="consign-title" hover-class="none" hover-stop-propagation="false">
      支付记录
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="consign-half-item" hover-class="none" hover-stop-propagation="false">支付方式：<text class="value-fontcolor">{{payType}}</text></view>
       <view class="consign-half-item" hover-class="none" hover-stop-propagation="false">支付状态：<text class="value-fontcolor">{{payStatusObj['status'+payStatus]}}</text></view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="consign-col3-item" hover-class="none" hover-stop-propagation="false">付款时间</view>
      <view class="consign-col2-item" hover-class="none" hover-stop-propagation="false">实付金额</view>
      <view class="consign-col2-item" hover-class="none" hover-stop-propagation="false">订单状态</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false" wx:for="{{payRecordList}}" wx:key="index">
      <view class="consign-col3-item" style="color: #333;" hover-class="none" hover-stop-propagation="false">{{item.paytime || ''}}</view>
      <view class="consign-col2-item" style="color: #333;" hover-class="none" hover-stop-propagation="false">{{item.actualmount}}元</view>
      <view class="consign-col2-item" style="color: #333;" hover-class="none" hover-stop-propagation="false">{{payStatusObj['status'+item.status]}}</view>
    </view>
  </view>
  <view class="consign-middle" hover-class="none" hover-stop-propagation="false"></view>
  <view class="consign-bottom-title" hover-class="none" hover-stop-propagation="false"  wx:if="{{form.dataList && form.dataList.length>0}}">
    <text class="annex-text" selectable="false" space="false" decode="false">附件列表</text>
    <button class="download-btn" bindtap="batchDown">发送邮箱</button>
  </view>
  <view class="order-list" hover-class="none" hover-stop-propagation="false" wx:if="{{form.dataList && form.dataList.length>0}}" >
    <view class="order-item" hover-class="none" hover-stop-propagation="false" wx:for="{{form.dataList}}" wx:key="index" data-item="{{item}}" data-index="{{index}}">
      <view class="order-item-title" hover-class="none" hover-stop-propagation="false">{{item.dataName}}</view>
      <view class="checktxt" data-item="{{item}}" data-index="{{index}}" bindtap = "getSeaWayBill">查看</view>
    </view>
  </view>


  <!-- <view class="deliver-item" hover-class="none" hover-stop-propagation="false" bindtap="outBoundJump">
    驳船出库记录查询
  </view>
  <view class="deliver-item" hover-class="none" hover-stop-propagation="false">
    查询轨迹
  </view> -->
  
  <view class="login-warp" hover-class="none" hover-stop-propagation="false">
    <button class="login-btn" bindtap="handleReview">提交预约</button>
  </view>

  <!-- <view class="overlay-bg" wx:if="{{showMessage}}">
    <scroll-view class="message" scroll-y="true" style="height: 400px" wx:if="{{showMessage}}">
      <text class="title">关于进一步加强驳船疫情防控工作的通知 \n</text>
      <text decode="true">
      各部门、各承包公司：
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;当前，国内本土疫情呈多点散发和局部暴发的态势，香港疫情持续发展，我司“外防输入”防线面临严峻考验。为进一步加强驳船疫情防控工作，现通知如下：
      1、所有驳船（含外转内驳船、内贸驳船）靠港前，船方必须向公司相关业务部门申报是否外转内不足14天及14天以内驳船是否靠泊香港或国内中高风险地区、船员体温（37.2度以下）、健康码。生产业务部、集装箱操作部、淡水河操作部要通过船讯网或海事服务APP核对船舶轨迹。
      2、如果14内曾经靠泊香港或国内中高风险地区，按“关于进一步加强港澳驳船疫情防控工作的通知（穗港集团防疫专班【2022】8号”通知要求做好防控工作。
      3、严禁船员私自上岸，一经发现要立即制止；杜绝码头作业人员与船员接触，码头作业人员严格落实非必要不登轮。
      4、驳船系解缆，一般采用无接触方式，若需港方协助系解缆的，作业人员应做好个人防护，并在作业前对缆绳进行消杀，作业后立即做好手部消毒。
      5、对驳船未如实申报14内航行轨迹、船员身体健康信息或其它违反疫情防控要求的船舶，报疫情防控专班处理。\n
      海事服务APP下载地址(复制后浏览器打开)：<text class="download">http://121.8.249.13:8081/msa-apk/publicApp.apk \n\n</text>
      </text>
      <text class="company">广州港新沙港务有限公司疫情防控工作专班
      </text>
      <text class="messageDate">2022年3月12日
      </text>
      <button class="handleOk" bindtap="handleOk">确定</button>
    </scroll-view>
  </view> -->

  <modal title="提醒" hidden="{{modalHidden}}" bindconfirm="modalConfirm" no-cancel="true">
      <view>
          <image class="image" src="../../../assets/image/modal.jpg" mode="aspectFill"></image>
      </view>
      <view class="imageText">预约成功，但需申报绿色驳船</view>
      <view class="imageText">请截图扫码申请！</view>
  </modal>

  <!-- 批量下载弹窗 -->
  <mp-dialog title="{{userEmail ? '您的邮箱如下':'账号尚未绑定邮箱，请填写邮箱'}}" show="{{batchDialog}}" mask="true" mask-closable="false" bindbuttontap="tapBatchDialogButton" buttons="{{batchButtons}}">
    <input type="text" class="add-message-emailipt" placeholder="请输入邮箱" value="{{userEmail}}"  bindinput="emailInput"/>
  </mp-dialog>
</view>

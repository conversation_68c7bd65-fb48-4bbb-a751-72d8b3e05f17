package com.ruoyi.common.domain.bo;

import lombok.*;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/13 16:13
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class SendEmailBO {

    /**
     * 目标邮箱
     */
    private String toEmail;

    /**
     * 标题
     */
    private String subject;

    /**
     * 内容
     */
    private String content;

    /**
     * 文件地址集合
     */
    private List<String> urlList;

    /**
     * 文件
     */
    private HashMap<String, String> base64File;

    private String waterwayCargoId;
}

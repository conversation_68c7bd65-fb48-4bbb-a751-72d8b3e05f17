package com.ruoyi.databarge.domain.dto;

import com.ruoyi.common.core.domain.BaseParamEntity;
import lombok.Getter;
import lombok.Setter;

import java.sql.Date;
import java.sql.Timestamp;
/**
 * Description:
 *
 * @Author: ChenJin on 2020/10/20.
 * @Date: 2020/10/20 9:02
 */
@Getter
@Setter
public class Pb6BargeCheckMessageSearchDTO extends BaseParamEntity {

    private Integer mtype;

    private Date begintime;

    private Date endtime;

    private Integer auditflag;

    private String bargename;

    private Long comid;

    private String waterwaycargoid;

    private Long pageNum;

    private Long pageSize;

    private String applyman;

    private String bargeId;
}

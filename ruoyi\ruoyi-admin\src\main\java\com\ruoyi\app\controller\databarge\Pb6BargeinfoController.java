package com.ruoyi.app.controller.databarge;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.app.controller.support.fdd.FddCompanyService;
import com.ruoyi.app.controller.support.fdd.FddShipOwnerService;
import com.ruoyi.app.controller.support.fdd.Sha256Util;
import com.ruoyi.app.controller.support.ftp.FtpTemplate;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.barge.domain.SysUserBargeBak;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.SysUserBargeBakService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.UploadAddress;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.UploadAddressService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.dto.BargeCompanyAuditDTO;
import com.ruoyi.databarge.domain.dto.BargeInfoDTO;
import com.ruoyi.databarge.domain.dto.Pb6BargeinfoAuditSearchDTO;
import com.ruoyi.databarge.domain.dto.SysUserBargeDTO;
import com.ruoyi.databarge.domain.vo.BargeCompanyResultVO;
import com.ruoyi.databarge.service.*;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/8/27.
 * @Date: 2020/8/27 16:21
 */
@Slf4j
@RestController
@RequestMapping("/barge/bargeinfo")
public class Pb6BargeinfoController {

    @Autowired
    private Pb6BargeinfoService pb6BargeinfoService;

    @Autowired
    private Pb6BargeinfoAuditService pb6BargeinfoAuditService;

    @Autowired
    private Pb6BargeCompanyService pb6BargeCompanyService;

    @Autowired
    private Pb6BargeCheckMessageService pb6BargeCheckMessageService;

    @Autowired
    private ShipFddUserRelService shipFddUserRelService;

    @Autowired
    private FddShipOwnerService fddShipOwnerService;
    @Autowired
    private FddCommonService fddCommonService;
    @Autowired
    private FtpTemplate ftpTemplate;
    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    @Autowired
    private SysUserInfoService sysUserInfoService;

    @Autowired
    private FddCompanyService fddCompanyService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysUserBargeBakService sysUserBargeBakService;

    @Autowired
    private UploadAddressService uploadAddressService;

    //驳船基本信息管理，查询驳船信息列表
    @PostMapping("/list")
    public AjaxResult list(@RequestBody Pb6BargeinfoAuditSearchDTO pb6BargeinfoAuditSearchDTO) {
        return AjaxResult.success(pb6BargeinfoService.searchPagePb6Bargeinfo(pb6BargeinfoAuditSearchDTO));
    }

    //导出方法
    @PostMapping("/export")
    public AjaxResult export(Pb6BargeinfoAuditSearchDTO pb6BargeinfoAuditSearchDTO) {
        List<BargeCompanyResultVO> list = pb6BargeinfoService.searchPagePb6Bargeinfo(pb6BargeinfoAuditSearchDTO).getRecords();
        ExcelUtil<BargeCompanyResultVO> util = new ExcelUtil<BargeCompanyResultVO>(BargeCompanyResultVO.class);
        return util.exportExcel(list, "驳船信息");
    }

    /**
     * 判断驳船标识是否唯一
     * 当新增数据时，若没有找出使用中的驳船对应数据则可以新增；
     * 当修改数据时，需要当前查出数据只有一条，并且 ID 值与前端传来的相等，即说明数据唯一可以修改
     *
     * @param bargeInfoDTO 驳船信息，只需要其中的 驳船标识 以及当前 ID 就行
     * @param sign         标识： 1、新增； 2、修改
     * @return 返回true，可继续操作， 返回false，弹窗提示不能操作
     */
    @PostMapping("/updateCheck/{sign}")
    public AjaxResult updateCheck(@RequestBody BargeInfoDTO bargeInfoDTO, @PathVariable("sign") Integer sign) {
        List<Pb6Bargeinfo> pb6BargeinfoList = pb6BargeinfoService.searchForUpdateCheck(bargeInfoDTO.getBargeid());
        if (sign == 1) {
            if (pb6BargeinfoList.size() >= 1) {
                return AjaxResult.success(false);
            } else {
                //当没有找到当前驳船标识的数据时，才可以新增
                return AjaxResult.success(true);
            }
        } else {
            if (pb6BargeinfoList.size() == 1 && pb6BargeinfoList.get(0).getId().equals(bargeInfoDTO.getId())) {
                //返回true，说明只有当前一条数据，可以保存，不用提示
                return AjaxResult.success(true);
            } else if (pb6BargeinfoList.size() == 0) {
                return AjaxResult.success(true);
            } else {
                return AjaxResult.success(false);
            }
        }
    }

    //更新驳船信息
    @Log(title = "驳船基本信息管理", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult update(@RequestBody BargeInfoDTO bargeInfoDTO) {
        return AjaxResult.success(pb6BargeinfoService.updateBargeInfoAndCompanyBind(bargeInfoDTO));
    }

    @Log(title = "驳船基本信息管理", businessType = BusinessType.INSERT)
    @PutMapping("/add")
    public AjaxResult add(@RequestBody BargeInfoDTO bargeInfoDTO) {
        Pb6Bargeinfo pb6Bargeinfo = new Pb6Bargeinfo();
        pb6Bargeinfo.setBargeid(bargeInfoDTO.getBargeid());
        pb6Bargeinfo.setBargename(bargeInfoDTO.getBargename());
        pb6Bargeinfo.setBargeoperator(bargeInfoDTO.getBargeoperator());
        pb6Bargeinfo.setMmsi(bargeInfoDTO.getMmsi());
        pb6Bargeinfo.setBargeweight(bargeInfoDTO.getBargeweight());
        pb6Bargeinfo.setBargeloada(bargeInfoDTO.getBargeloada());
        pb6Bargeinfo.setBargeloadb(bargeInfoDTO.getBargeloadb());
        pb6Bargeinfo.setValidsaildate(bargeInfoDTO.getValidsaildate());
        pb6Bargeinfo.setBelongarea(bargeInfoDTO.getBelongarea());
        pb6Bargeinfo.setBargelinkman(bargeInfoDTO.getBargelinkman());
        pb6Bargeinfo.setContactphone(bargeInfoDTO.getContactphone());
        pb6Bargeinfo.setIsuse(bargeInfoDTO.getIsuse());
        return AjaxResult.success(pb6BargeinfoService.save(pb6Bargeinfo));
    }

    //通过驳船基本信息ID号查找驳船信息
    @PostMapping("/searchbyid")
    public AjaxResult searchById(@RequestBody Long id) {
        BargeCompanyResultVO bargeCompanyResultVO = pb6BargeinfoService.searchPb6BargeinfoById(id);
        if (bargeCompanyResultVO != null) {
            return AjaxResult.success(bargeCompanyResultVO);
        } else {
            return AjaxResult.error("请仔细核对驳船信息！");
        }
    }


    @PostMapping("/searchByBargeName")
    public AjaxResult searchByBargeName(@RequestBody String bargeName) {
        return AjaxResult.success(pb6BargeinfoService.searchByBargeName(bargeName));
    }

    /**
     * 通过用户输入的驳船名模糊查询数据库的驳船信息
     *
     * @param bargeName 驳船名
     * @return 返回 id倒序排列的10条数据
     */
    @PostMapping("/searchListByBargeName")
    public AjaxResult searchListByBargeName(@RequestBody String bargeName) {
        return AjaxResult.success(pb6BargeinfoService.searchListByBargeName(bargeName));
    }

    // 驳船审核
    @PostMapping("/bargeCompanyAuditcheck")
    @Transactional
    public AjaxResult check(@RequestBody BargeCompanyAuditDTO bargeCompanyAuditDTO) throws URISyntaxException {

        // 审核失败原因不能超过20个字符
        if (bargeCompanyAuditDTO.getFailureReasons() != null && bargeCompanyAuditDTO.getFailureReasons().length() > 20) {
            return AjaxResult.error("审核失败原因不能超过20个字符");
        }

        Pb6Bargeinfo pb6Bargeinfo1 = null;// 旧船的信息
        if (bargeCompanyAuditDTO.getBargeCompanyResultVO() != null && bargeCompanyAuditDTO.getBargeCompanyResultVO().getId() != null) {
            Pb6Bargeinfo pb6Bargeinfo = new Pb6Bargeinfo();
            BeanUtils.copyProperties(bargeCompanyAuditDTO.getBargeCompanyResultVO(), pb6Bargeinfo);
            pb6Bargeinfo.setId(bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId());

            //取数显示前 把数据除了 1000 ，这里乘回去
            pb6Bargeinfo.setBargeloada(new BigDecimal(pb6Bargeinfo.getBargeloada()).multiply(new BigDecimal("1000")).toString());
            pb6Bargeinfo.setBargeloadb(new BigDecimal(pb6Bargeinfo.getBargeloadb()).multiply(new BigDecimal("1000")).toString());
            if (bargeCompanyAuditDTO.getCheckStatus() == 1) {
                pb6Bargeinfo1 = pb6BargeinfoService.getById(bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId());

                pb6Bargeinfo.setAudittime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
                if (pb6Bargeinfo1 == null) {
                    pb6BargeinfoService.insertById(pb6Bargeinfo);
                } else {
                    pb6BargeinfoService.updateById(pb6Bargeinfo);
                }

                if(pb6Bargeinfo1 != null && bargeCompanyAuditDTO.getBargeCompanyResultVO().getBargename()!=null && !bargeCompanyAuditDTO.getBargeCompanyResultVO().getBargename().equals(pb6Bargeinfo1.getBargename())){
                    throw new CustomException("驳船名不一致不能审核通过");
                }

                //备案人、备案人手机号、身份证号
                //审核的时候，判断是否有驳船主关联，假如没有，则往 SysUserInfo 表中存入临时数据
                SysUser sysUserBargeOwner = sysUserInfoService.searchUserByBargeId(pb6Bargeinfo.getId());
                if (sysUserBargeOwner == null) {
                    SysUserInfo sysUserInfo = new SysUserInfo();
                    sysUserInfo.setBargeid(pb6Bargeinfo.getId());
                    sysUserInfo.setIdentity(bargeCompanyAuditDTO.getBargeCompanyResultVO().getIdentityId());
                    sysUserInfo.setPhone(bargeCompanyAuditDTO.getBargeCompanyResultVO().getPhonenumber());
                    sysUserInfo.setUsername(bargeCompanyAuditDTO.getBargeCompanyResultVO().getNickName());
                    sysUserInfoService.save(sysUserInfo);
                } else {

                    // 2021.08.20 洪 叫添加。判断手机号和驳船主不一致时，删掉业务员的绑定
                    if(!sysUserBargeOwner.getPhonenumber().equals(bargeCompanyAuditDTO.getBargeCompanyResultVO().getPhonenumber())){
                        List<Long> longList = sysUserInfoService.searchSalesman(pb6Bargeinfo.getId());
                        for(Long id: longList){
                            List<SysUserBarge> sysUserBargeList = sysUserBargeMapper.selectList(new LambdaQueryWrapper<SysUserBarge>().eq(SysUserBarge::getBargeId, pb6Bargeinfo.getId())
                                    .eq(SysUserBarge::getUserId, id));
                            sysUserBargeList.forEach(i -> sysUserBargeMapper.delete(new LambdaQueryWrapper<SysUserBarge>().eq(SysUserBarge::getBargeId, i.getBargeId())
                                    .eq(SysUserBarge::getUserId, i.getUserId())));
                        }
                    }

                    sysUserBargeOwner.setIdentityId(bargeCompanyAuditDTO.getBargeCompanyResultVO().getIdentityId());
                    sysUserBargeOwner.setPhonenumber(bargeCompanyAuditDTO.getBargeCompanyResultVO().getPhonenumber());
                    sysUserBargeOwner.setNickName(bargeCompanyAuditDTO.getBargeCompanyResultVO().getNickName());
                    sysUserService.updateUserProfile(sysUserBargeOwner);
                }
            }

            Pb6BargeinfoAudit pb6BargeinfoAudit = new Pb6BargeinfoAudit();
            BeanUtils.copyProperties(bargeCompanyAuditDTO.getBargeCompanyResultVO(), pb6BargeinfoAudit);
            if (pb6BargeinfoAudit.getCheckFlag() == 1) {
                pb6BargeinfoAudit.setRecordCheck(bargeCompanyAuditDTO.getCheckStatus());
            } else if (pb6BargeinfoAudit.getCheckFlag() == 2) {
                pb6BargeinfoAudit.setUpdateCheck(bargeCompanyAuditDTO.getCheckStatus());
            }
            SysUser sysUser = SecurityUtils.getLoginUser().getUser();
            pb6BargeinfoAudit.setBargeAuditByName(sysUser.getUserName());
            pb6BargeinfoAudit.setBargeAuditById(sysUser.getUserId());
            pb6BargeinfoAudit.setBargeAuditTime(DateUtils.getTime());
            pb6BargeinfoAudit.setIsDelete(0);
            pb6BargeinfoAudit.setBargeloada(pb6Bargeinfo.getBargeloada());
            pb6BargeinfoAudit.setBargeloadb(pb6Bargeinfo.getBargeloadb());
            pb6BargeinfoAuditService.updateById(pb6BargeinfoAudit);
        }
        if (bargeCompanyAuditDTO.getPb6BargeCompanyVO() != null && bargeCompanyAuditDTO.getPb6BargeCompanyVO().getId() != null) {
            pb6BargeCompanyService.check(bargeCompanyAuditDTO.getPb6BargeCompanyVO(), bargeCompanyAuditDTO.getCheckStatus());
        }

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        Pb6BargeCheckMessage pb6BargeCheckMessage = pb6BargeCheckMessageService.getById(bargeCompanyAuditDTO.getPb6BargeCheckMessageId());
        pb6BargeCheckMessage.setAuditmanid(sysUser.getUserId());
        pb6BargeCheckMessage.setAuditman(sysUser.getUserName());
        pb6BargeCheckMessage.setAudittime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        pb6BargeCheckMessage.setAuditflag(bargeCompanyAuditDTO.getCheckStatus());
        pb6BargeCheckMessage.setFailurereasons(bargeCompanyAuditDTO.getFailureReasons());
        boolean flag = pb6BargeCheckMessageService.updateById(pb6BargeCheckMessage);

        // ---------王伦辉---------
        Long shipId = null;
        List<ShipFddUserRel> shipFddUserRelList = null;
        List<ShipFddUserRel> shipFddUserRelListAll = null;
        List<ShipFddUserRel> shipFddUserRelListBarge = null;
        ShipFddUserRel shipFddUserRel = null;
        ShipFddUserRel shipFddUserRelAll = null;
        ShipFddUserRel shipFddUserRelBarge = null;

//        System.out.println("传入用户类型："+sysUserService.selectUserById(pb6BargeCheckMessage.getApplymanid()).getUserType());
//        System.out.println("传入用户id："+pb6BargeCheckMessage.getApplymanid());
//        System.out.println("传入类型："+pb6BargeCheckMessage.getMtype());
//        System.out.println(pb6BargeCheckMessage.getShipfdduserrelid());
        if (pb6BargeCheckMessage.getMtype() != 3) {
            if (StringUtils.isNotBlank(pb6BargeCheckMessage.getShipfdduserrelid())) {
                log.info(pb6BargeCheckMessage.getShipfdduserrelid()+"开始审核");
                breakSwitch:
                switch (bargeCompanyAuditDTO.getCheckStatus()) {
                    case 1:// 1审核通过
                        Pb6Bargeinfo pb6Bargeinfo2 = pb6BargeinfoService.getOne(new LambdaQueryWrapper<Pb6Bargeinfo>().eq(Pb6Bargeinfo::getId,bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId()));


                        shipFddUserRel = shipFddUserRelService.getById(pb6BargeCheckMessage.getShipfdduserrelid());
                        //审核通过将sys_user_barge_bak的数据插入sys_user_barge中，同时删除bak中的数据
                        SysUserBargeBak sysUserBargeBak = sysUserBargeBakService.getOne(new LambdaQueryWrapper<SysUserBargeBak>().eq(SysUserBargeBak::getBargeId,bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId()));
                        SysUserBarge  sysUserBarge = new SysUserBarge();
                        BeanUtils.copyProperties(sysUserBargeBak,sysUserBarge);
                        sysUserBargeMapper.delete(new LambdaQueryWrapper<SysUserBarge>().eq(SysUserBarge::getUserId,sysUserBargeBak.getUserId()));
                        sysUserBargeMapper.insert(sysUserBarge);
                        sysUserBargeBakService.remove(new LambdaQueryWrapper<SysUserBargeBak>().eq(SysUserBargeBak::getBargeId,bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId()));
                        if (shipFddUserRel.getType().equals("3") || shipFddUserRel.getType().equals("2")) {
                            //船公司
                            switch (pb6BargeCheckMessage.getMtype()) {
                                case 0:
                                case 1:
                                case 2:// 只管消息类型是0、1、2，其他不管
                                    shipId = bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId();
                                    //查询需审核的Fdd数据
                                    shipFddUserRel = shipFddUserRelService.getById(pb6BargeCheckMessage.getShipfdduserrelid());

                                    //查看是否有驳船主上传印章
                                    shipFddUserRelListAll = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                                            .eq(ShipFddUserRel::getType, "1")
                                            .eq(ShipFddUserRel::getReviewStatus, "1")
                                            .eq(ShipFddUserRel::getShipId, shipId));

                                    if (shipFddUserRelListAll.size() == 1) {
                                        shipFddUserRelAll = shipFddUserRelListAll.get(0);


                                        Pb6Bargeinfo pb6Bargeinfo = pb6BargeinfoService.getById(shipId);
                                        String sealName = pb6Bargeinfo.getBargename();// 公章名称直接为船名
                                        String transactionNo = IdUtils.fastSimpleUUID();

                                        File file = null;
                                        try {
                                            file = File.createTempFile("ship-", ".png");
                                            file.deleteOnExit();
                                            ftpTemplate.download(shipFddUserRel.getSealUrl(), new FileOutputStream(file));
                                        } catch (Exception e) {
                                            throw new CustomException("印章文件不存在!", e);
                                        }

                                        String accountNo = fddCompanyService.companyRegister(IdUtils.fastSimpleUUID(), "驳船主");
                                        String evidenceNo = fddShipOwnerService.shipOwnerSaveHashEvidence(
                                                accountNo,
                                                shipId + "的存证",
                                                shipId + "的存证描述",
                                                file.getName(),
                                                "" + file.lastModified() / 1000,
                                                "" + file.length(),
                                                Sha256Util.getSha256(file.getName()),
                                                transactionNo);
                                        String evidenceNumberCert = fddShipOwnerService.applyEvidenceNumberCert(accountNo, evidenceNo);
                                        fddCommonService.uploadAndSaveSeal(accountNo, sealName, file);
                                        shipFddUserRel.setFddAccountId(accountNo);
                                        shipFddUserRelListAll = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                                                .eq(ShipFddUserRel::getType, "3")
                                                .eq(ShipFddUserRel::getReviewStatus, "1")
                                                .eq(ShipFddUserRel::getShipId, shipId));
                                        shipFddUserRelListAll.forEach(i -> {
                                            i.setReviewStatus("2");
                                            shipFddUserRelService.updateById(i);
                                        });
                                        shipFddUserRel.setReviewStatus("1");
                                        shipFddUserRelService.updateById(shipFddUserRel);
                                    } else if (shipFddUserRelListAll.size() >= 2) {
                                        throw new CustomException("数据错误,一个驳船主有多个审核通过驳船");
                                    } else {
                                        //若驳船主没章，判断是否有物流公司的章
                                        List<ShipFddUserRel> shipFddUserRelListWLGS = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                                                .eq(ShipFddUserRel::getType, "4")
                                                .eq(ShipFddUserRel::getReviewStatus, "1")
                                                .eq(ShipFddUserRel::getShipId, shipId));
                                        if (shipFddUserRelListWLGS.size() == 1) {
                                            ShipFddUserRel shipFddUserRel1wlgs = shipFddUserRelListWLGS.get(0);

                                            Pb6Bargeinfo pb6Bargeinfo = pb6BargeinfoService.getById(shipId);
                                            String sealName = pb6Bargeinfo.getBargename();// 公章名称直接为船名
                                            String transactionNo = IdUtils.fastSimpleUUID();

                                            File file = null;
                                            try {
                                                file = File.createTempFile("ship-", ".png");
                                                file.deleteOnExit();
                                                ftpTemplate.download(shipFddUserRel.getSealUrl(), new FileOutputStream(file));
                                            } catch (Exception e) {
                                                throw new CustomException("印章文件不存在!", e);
                                            }

                                            String accountNo = fddCompanyService.companyRegister(IdUtils.fastSimpleUUID(), "驳船主");
                                            String evidenceNo = fddShipOwnerService.shipOwnerSaveHashEvidence(
                                                    accountNo,
                                                    shipId + "的存证",
                                                    shipId + "的存证描述",
                                                    file.getName(),
                                                    "" + file.lastModified() / 1000,
                                                    "" + file.length(),
                                                    Sha256Util.getSha256(file.getName()),

                                                    transactionNo);
                                            String evidenceNumberCert = fddShipOwnerService.applyEvidenceNumberCert(accountNo, evidenceNo);
                                            fddCommonService.uploadAndSaveSeal(accountNo, sealName, file);
                                            shipFddUserRel.setFddAccountId(accountNo);
                                            shipFddUserRelListAll = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                                                    .eq(ShipFddUserRel::getType, "3")
                                                    .eq(ShipFddUserRel::getReviewStatus, "1")
                                                    .eq(ShipFddUserRel::getShipId, shipId));
                                            shipFddUserRelListAll.forEach(i -> {
                                                i.setReviewStatus("2");
                                                shipFddUserRelService.updateById(i);
                                            });
                                            shipFddUserRel.setReviewStatus("1");
                                            shipFddUserRelService.updateById(shipFddUserRel);
                                        } else if (shipFddUserRelListAll.size() >= 2) {
                                            throw new CustomException("数据错误,该驳船存在多个审核通过数据");
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        } else {
                            //非船公司
                            switch (pb6BargeCheckMessage.getMtype()) {
                                case 0:
                                case 1:
                                    shipId = bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId();
                                    //查询需审核的Fdd数据
                                    shipFddUserRel = shipFddUserRelService.getById(pb6BargeCheckMessage.getShipfdduserrelid());

                                    Pb6Bargeinfo pb6Bargeinfo = pb6BargeinfoService.getById(shipId);
                                    String sealName = pb6Bargeinfo.getBargename();// 公章名称直接为船名
                                    String transactionNo = IdUtils.fastSimpleUUID();
                                    File file = null;

                                    //查询是否有船公司备案
                                    shipFddUserRelListBarge = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                                            .eq(ShipFddUserRel::getType, "3")
                                            .eq(ShipFddUserRel::getReviewStatus, "1")
                                            .eq(ShipFddUserRel::getShipId, shipId));
                                    //有船公司备案，下载船公司印章；没有，下载当前审核数据的印章
                                    if (shipFddUserRelListBarge.size() == 1) {
                                        shipFddUserRelBarge = shipFddUserRelListBarge.get(0);
                                        try {
                                            file = File.createTempFile("ship-", ".png");
                                            file.deleteOnExit();
                                            ftpTemplate.download(shipFddUserRelBarge.getSealUrl(), new FileOutputStream(file));
                                        } catch (Exception e) {
                                            throw new CustomException("印章文件不存在!", e);
                                        }
                                    } else if (shipFddUserRelListBarge.size() == 0) {
                                        try {
                                            file = File.createTempFile("ship-", ".png");
                                            file.deleteOnExit();
                                            ftpTemplate.download(shipFddUserRel.getSealUrl(), new FileOutputStream(file));
                                        } catch (Exception e) {
                                            throw new CustomException("印章文件不存在!", e);
                                        }
                                    }
                                    String accountNo = fddCompanyService.companyRegister(IdUtils.fastSimpleUUID(), "驳船主");
                                    String evidenceNo = fddShipOwnerService.shipOwnerSaveHashEvidence(
                                            accountNo,
                                            shipId + "的存证",
                                            shipId + "的存证描述",
                                            file.getName(),
                                            "" + file.lastModified() / 1000,
                                            "" + file.length(),
                                            Sha256Util.getSha256(file.getName()),
                                            transactionNo);
                                    String evidenceNumberCert = fddShipOwnerService.applyEvidenceNumberCert(accountNo, evidenceNo);
                                    fddCommonService.uploadAndSaveSeal(accountNo, sealName, file);
                                    //判断是否有驳船主审核通过数据，若有，将之前的改成审核不通过
                                    shipFddUserRelListAll = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                                            .eq(ShipFddUserRel::getType, "1")
                                            .eq(ShipFddUserRel::getReviewStatus, "1")
                                            .eq(ShipFddUserRel::getShipId, shipId));
                                    shipFddUserRelListAll.forEach(i -> {
                                        i.setReviewStatus("2");
                                        shipFddUserRelService.updateById(i);
                                    });

                                    shipFddUserRel.setFddAccountId(accountNo);
                                    shipFddUserRel.setReviewStatus("1");// 1审核通过
                                    shipFddUserRelService.updateById(shipFddUserRel);
                                    break;

                                case 2:// 2驳船信息修改审核

                                    shipId = bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId();


                                    //查询需审核的Fdd数据
                                    shipFddUserRel = shipFddUserRelService.getById(pb6BargeCheckMessage.getShipfdduserrelid());
                                    //先查是否有驳船主审核通过的
                                    shipFddUserRelListAll = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                                            .eq(ShipFddUserRel::getType, "1")
                                            .eq(ShipFddUserRel::getReviewStatus, "1")
                                            .eq(ShipFddUserRel::getShipId, shipId));

                                    if (shipFddUserRelListAll.size() == 0) {
                                        //驳船主初次备案，
                                        pb6Bargeinfo = pb6BargeinfoService.getById(shipFddUserRel.getShipId());
                                        sealName = pb6Bargeinfo.getBargename();// 公章名称直接为船名
                                        transactionNo = IdUtils.fastSimpleUUID();

                                        try {
                                            file = File.createTempFile("ship-", ".png");
                                            file.deleteOnExit();
                                            ftpTemplate.download(shipFddUserRel.getSealUrl(), new FileOutputStream(file));
                                        } catch (Exception e) {
                                            throw new CustomException("印章文件不存在!", e);
                                        }
                                        accountNo = fddCompanyService.companyRegister(IdUtils.fastSimpleUUID(), "驳船主");
                                        evidenceNo = fddShipOwnerService.shipOwnerSaveHashEvidence(
                                                accountNo,
                                                shipId + "的存证",
                                                shipId + "的存证描述",
                                                file.getName(),
                                                "" + file.lastModified() / 1000,
                                                "" + file.length(),
                                                Sha256Util.getSha256(file.getName()),
                                                transactionNo);
                                        evidenceNumberCert = fddShipOwnerService.applyEvidenceNumberCert(accountNo, evidenceNo);
                                        fddCommonService.uploadAndSaveSeal(accountNo, sealName, file);
                                        shipFddUserRel.setFddAccountId(accountNo);
                                        shipFddUserRel.setReviewStatus("1");
                                        shipFddUserRelService.updateById(shipFddUserRel);
                                    } else if (shipFddUserRelListAll.size() == 1) {
                                        ShipFddUserRel shipFddUserRelWithFddAcountId = shipFddUserRelListAll.get(0);

                                        try {
                                            file = File.createTempFile("ship-", ".png");
                                            file.deleteOnExit();
                                            ftpTemplate.download(shipFddUserRel.getSealUrl(), new FileOutputStream(file));// 新的印章文件
                                        } catch (Exception e) {
                                            throw new CustomException("印章文件不存在!", e);
                                        }

                                        pb6Bargeinfo = pb6BargeinfoService.getById(shipFddUserRel.getShipId());// 新船的信息
                                        sealName = pb6Bargeinfo.getBargename();// 公章名称直接为船名
                                        // 先删除公章
                                        fddCommonService.deleteAllSeal(shipFddUserRelWithFddAcountId.getFddAccountId());// 删除的是旧的所有印章
                                        // 删除数据库表
//                                        shipFddUserRelService.removeById(shipFddUserRelWithFddAcountId.getId());
                                        // 再重新上传
                                        fddCommonService.uploadAndSaveSeal(shipFddUserRelWithFddAcountId.getFddAccountId(), sealName, file);// 新上传的是新船的船名
                                        shipFddUserRel.setFddAccountId(shipFddUserRelWithFddAcountId.getFddAccountId());// 设置fdd_account_id
                                        shipFddUserRelWithFddAcountId.setReviewStatus("2");
                                        shipFddUserRel.setReviewStatus("1");// 1审核通过
                                        pb6Bargeinfo.setAuditstate("");
                                        pb6BargeinfoService.updateById(pb6Bargeinfo);
                                        shipFddUserRelService.updateById(shipFddUserRelWithFddAcountId);
                                        shipFddUserRelService.updateById(shipFddUserRel);
                                    } else {
                                        throw new CustomException("数据错误！");
                                    }
                                    break;
                                case 3:
//                                 shipId=bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId();
                                    //查询需审核的Fdd数据
                                    shipFddUserRel = shipFddUserRelService.getById(pb6BargeCheckMessage.getShipfdduserrelid());
                                    pb6Bargeinfo = pb6BargeinfoService.getById(shipFddUserRel.getShipId());
                                    sealName = pb6Bargeinfo.getBargename();// 公章名称直接为船名
                                    transactionNo = IdUtils.fastSimpleUUID();
                                    file = null;

                                    //查询是否有船公司备案
                                    shipFddUserRelListBarge = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                                            .eq(ShipFddUserRel::getType, "3")
                                            .eq(ShipFddUserRel::getReviewStatus, "1")
                                            .eq(ShipFddUserRel::getShipId, shipFddUserRel.getShipId()));
                                    //有船公司备案，下载船公司印章；没有，下载当前审核数据的印章
                                    if (shipFddUserRelListBarge.size() == 1) {
                                        shipFddUserRelBarge = shipFddUserRelListBarge.get(0);
                                        try {
                                            file = File.createTempFile("ship-", ".png");
                                            file.deleteOnExit();
                                            ftpTemplate.download(shipFddUserRelBarge.getSealUrl(), new FileOutputStream(file));
                                        } catch (Exception e) {
                                            throw new CustomException("印章文件不存在!", e);
                                        }
                                    } else if (shipFddUserRelListBarge.size() == 0) {
                                        try {
                                            file = File.createTempFile("ship-", ".png");
                                            file.deleteOnExit();
                                            ftpTemplate.download(shipFddUserRel.getSealUrl(), new FileOutputStream(file));
                                        } catch (Exception e) {
                                            throw new CustomException("印章文件不存在!", e);
                                        }
                                    }
                                    accountNo = fddCompanyService.companyRegister(IdUtils.fastSimpleUUID(), "驳船主");
                                    evidenceNo = fddShipOwnerService.shipOwnerSaveHashEvidence(
                                            accountNo,
                                            shipFddUserRel.getShipId() + "的存证",
                                            shipFddUserRel.getShipId() + "的存证描述",
                                            file.getName(),
                                            "" + file.lastModified() / 1000,
                                            "" + file.length(),
                                            Sha256Util.getSha256(file.getName()),
                                            transactionNo);
                                    evidenceNumberCert = fddShipOwnerService.applyEvidenceNumberCert(accountNo, evidenceNo);
                                    fddCommonService.uploadAndSaveSeal(accountNo, sealName, file);
                                    //判断是否有驳船主审核通过数据，若有，将之前的改成审核不通过
//                                shipFddUserRelListAll=shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
//                                        .eq(ShipFddUserRel::getType,"1")
//                                        .eq(ShipFddUserRel::getReviewStatus,"1")
//                                        .eq(ShipFddUserRel::getShipId,shipId));
//                                shipFddUserRelListAll.forEach(i->{
//                                    i.setReviewStatus("2");
//                                    shipFddUserRelService.updateById(i);
//                                });

                                    shipFddUserRel.setFddAccountId(accountNo);

                                    //修改其他船公司
                                    List<ShipFddUserRel> shipFddUserRelShipList = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                                            .eq(ShipFddUserRel::getType, "3")
                                            .eq(ShipFddUserRel::getReviewStatus, "1")
                                            .eq(ShipFddUserRel::getShipId, shipFddUserRel.getShipId()));
                                    if (shipFddUserRelShipList.size() > 0) {
                                        shipFddUserRelShipList.forEach(i -> {
                                            if (StringUtils.isNotBlank(pb6BargeCheckMessage.getPb6bargecompanyid().toString())) {

                                                if (sysUserService.selectUserById(i.getShipUserId()) != null) {
                                                    Pb6BargeCompany pb6BargeCompany = pb6BargeCompanyService.getById(pb6BargeCheckMessage.getPb6bargecompanyid());
                                                    if (pb6BargeCompany != null) {
                                                        if (sysUserService.selectUserById(i.getShipUserId()).getCompanyId().equals(pb6BargeCompany.getCompanyid()) == false) {
                                                            i.setReviewStatus("2");
                                                            shipFddUserRelService.updateById(i);
                                                        }
                                                    }
                                                }
                                            }
                                        });
                                    }
                                    shipFddUserRel.setReviewStatus("1");// 1审核通过
                                    shipFddUserRelService.updateById(shipFddUserRel);
                                    break;

                                default:
                                    break;
                            }
                        }
                        break;
                    case 2:// 2审核不通过
                        shipFddUserRel = shipFddUserRelService.getById(pb6BargeCheckMessage.getShipfdduserrelid());
                        if (shipFddUserRel.getReviewStatus().equals(0)){
                            shipFddUserRel.setReviewStatus("2");
                            shipFddUserRelService.updateById(shipFddUserRel);
                        }
                        // 身份证/委托书关联的用户id
                        Long userId = null;

                        //审核不通过删除sys_user_barge_bak表
                        sysUserBargeBakService.remove(new LambdaQueryWrapper<SysUserBargeBak>().eq(SysUserBargeBak::getBargeId,bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId()));
                        // 审核不通过处理
                        // 查出linkId为当前船舶，status为0，linkType 为10 的所有数据
                        List<UploadAddress> oldList = uploadAddressService.list(new LambdaQueryWrapper<UploadAddress>()
                                .eq(UploadAddress::getLinkId, bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId())
                                .eq(UploadAddress::getLinkType, 10)
                                .eq(UploadAddress::getStatus, 0));
                        // 遍历List列表，将status改为1，查询出所有的数据
                        List<UploadAddress> newList = new ArrayList<>();
                        for (UploadAddress uploadAddress : oldList) {
                            // 查出LinkID为当前船舶，status为1，linkType为10 的所有数据，dateType为当前数据的dateType
                            UploadAddress newUploadAddress = uploadAddressService.getOne(new LambdaQueryWrapper<UploadAddress>().eq(UploadAddress::getLinkId, bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId())
                                    .eq(UploadAddress::getStatus, 1)
                                    .eq(UploadAddress::getLinkType, 10)
                                    .eq(UploadAddress::getDataType, uploadAddress.getDataType()));
                            newList.add(newUploadAddress);
                        }
                        // 遍历newList，根据ID删除数据
                        for(UploadAddress uploadAddress : newList){
                            // 去掉uploadaddress为null的情况
                            if (uploadAddress != null) {
                                uploadAddressService.removeById(uploadAddress.getId());
                            }
                        }
                        // 遍历oldList，根据ID修改status为1
                        for(UploadAddress uploadAddress : oldList){
                            uploadAddress.setStatus(1);
                            uploadAddressService.updateById(uploadAddress);
                        }

                        // 查出linkId为当前船舶，status为1，linkType 为10 的所有数据
                        List<UploadAddress> alreadyList = uploadAddressService.list(new LambdaQueryWrapper<UploadAddress>()
                                .eq(UploadAddress::getLinkId, bargeCompanyAuditDTO.getBargeCompanyResultVO().getPb6BargeInfoId())
                                .eq(UploadAddress::getLinkType, 10)
                                .eq(UploadAddress::getStatus, 1));
                        // for (UploadAddress uploadAddress : alreadyList) {
                        //     userId = uploadAddress.getUploadUserId();
                        // }
                        // 根据当前备案手机号查询用户信息
                        String phonenumber = bargeCompanyAuditDTO.getBargeCompanyResultVO().getPhonenumber();
                        List<SysUser> sysUsers = sysUserMapper.selectUserByPhoneNumber(phonenumber);
                        for(SysUser sysUser1 : sysUsers){
                            userId = sysUser1.getUserId();
                        }

                        // 重复上述操作，区别为linkType为50，linkid为userid
                        List<UploadAddress> oldList50 = uploadAddressService.list(new LambdaQueryWrapper<UploadAddress>()
                                .eq(UploadAddress::getLinkId, userId)
                                .eq(UploadAddress::getLinkType, 50)
                                .eq(UploadAddress::getStatus, 0));
                        List<UploadAddress> newList50 = new ArrayList<>();
                        for (UploadAddress uploadAddress : oldList50) {
                            UploadAddress newUploadAddress = uploadAddressService.getOne(new LambdaQueryWrapper<UploadAddress>().eq(UploadAddress::getLinkId, userId)
                                    .eq(UploadAddress::getStatus, 1)
                                    .eq(UploadAddress::getLinkType, 50)
                                    .eq(UploadAddress::getDataType, uploadAddress.getDataType()));
                            newList50.add(newUploadAddress);
                        }
                        for(UploadAddress uploadAddress : newList50){
                            if (uploadAddress != null) {
                                uploadAddressService.removeById(uploadAddress.getId());
                            }
                        }
                        for(UploadAddress uploadAddress : oldList50){
                            uploadAddress.setStatus(1);
                            uploadAddressService.updateById(uploadAddress);
                        }
                        break;
                    default:
                        break;
                }
            } else {
                return AjaxResult.error("数据异常联系管理员");
            }

        }

        // ---------王伦辉---------

        //结束了再发消息 驳船审核消息
        pb6BargeCheckMessageService.sendMessageForAddOrUpdateBargeInfo(pb6BargeCheckMessage);
        return AjaxResult.success("审核成功！");
    }

    //通过驳船挂靠表中存的驳船基本信息表ID 查询驳船基本信息
    @PostMapping("searchPb6BargeinfoByPb6BargeCompanyId")
    public AjaxResult searchPb6BargeinfoByPb6BargeCompanyId(@RequestBody Long pb6BargeInfoAuditId) {
        return AjaxResult.success(pb6BargeinfoService.searchPb6BargeinfoByPb6BargeCompanyId(pb6BargeInfoAuditId));
    }

    //根据驳船id查询驳船主
    @PostMapping("/queryAllUserByBargeId")
    public AjaxResult queryAllUserByBargeId(@RequestBody Long bargeId) {
        return AjaxResult.success(sysUserBargeMapper.queryAllUserByBargeId(bargeId));
    }

    //去除用户驳船关联
    @Log(title = "用户驳船关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteLinkOfUserAndBarge")
    public AjaxResult deleteLinkOfUserAndBarge(@RequestBody SysUserBargeDTO sysUserBargeDTO) {
        sysUserBargeDTO.getUserIds().forEach(i -> {
            QueryWrapper<SysUserBarge> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(SysUserBarge::getUserId, i)
                    .eq(SysUserBarge::getBargeId, sysUserBargeDTO.getBargeId());
            sysUserBargeMapper.delete(queryWrapper);
            QueryWrapper<SysUser> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.lambda().eq(SysUser::getUserId,i);
            sysUserMapper.delete(queryWrapper1);
        });
        return AjaxResult.success();
    }

    @GetMapping("/alreadyToNew")
    public AjaxResult alreadyToNew(Long bargeId){
        //SysUserBargeBak sysUserBargeBak = sysUserBargeBakService.getOne(new LambdaQueryWrapper<SysUserBargeBak>().eq(SysUserBargeBak::getUserId,userId));
        // return AjaxResult.success(pb6BargeinfoAuditService.searchPb6BargeinfoAuditById(bargeId));
        // 不知道这个接口有啥用，先返回null
        return  AjaxResult.success(null);
    }
}

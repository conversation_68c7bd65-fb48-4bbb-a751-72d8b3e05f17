package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.databarge.domain.Pb6BargeinfoAudit;
import com.ruoyi.databarge.domain.vo.BargeCompanyResultVO;
import com.ruoyi.databarge.mapper.Pb6BargeinfoAuditMapper;
import com.ruoyi.databarge.service.Pb6BargeinfoAuditService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/8/27.
 * @Date: 2020/8/27 11:19
 */
@Service
public class Pb6BargeinfoAuditServiceImpl extends ServiceImpl<Pb6BargeinfoAuditMapper, Pb6BargeinfoAudit> implements Pb6BargeinfoAuditService {
    @Override
    public BargeCompanyResultVO searchPb6BargeinfoAuditById(Long pb6BargeInfoAuditId) {
        return super.baseMapper.searchPb6BargeinfoAuditById(pb6BargeInfoAuditId);
    }

    @Override
    public BargeCompanyResultVO searchPb6BargeinfoAuditByBakId(Long pb6BargeInfoAuditId) {
        return super.baseMapper.searchPb6BargeinfoAuditByBakId(pb6BargeInfoAuditId);
    }

    @Override
    public List<Pb6BargeinfoAudit> selectPb6BargeinfoAuditListByBargeName(String bargename) {
        return baseMapper.selectPb6BargeinfoAuditListByBargeName(bargename);
    }
}

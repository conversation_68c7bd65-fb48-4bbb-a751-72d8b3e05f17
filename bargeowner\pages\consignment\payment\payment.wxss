/* pages/consignment/payment/payment.wxss */
.payment-page{
  width: 100%;
  height: auto;
  background: #f8f8f8;
}
.consign-message{
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 70rpx;
  border-bottom: 1px solid #ddd;
  padding: 0 30rpx;
  width: 100%;
}
.consign-message2{
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 70rpx;
  border-bottom: 1px solid #ddd;
  padding: 0 30rpx;
  width: 100%;
  margin-bottom: 20rpx;
  background:#fff;
}
.message-title{
  color: #999;
  font-size: 28rpx;
}
.message-value{
  color: #333;
  font-size: 28rpx;
  flex:1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.message-value1{
  color: #999;
}
.order-item{
  border-bottom: 1px solid #ddd;
  padding: 0 30rpx;
}
.order-item-title{
  font-size: 28rpx;
  color: #333;
  line-height: 60rpx;
  width: auto;
}
.order-item-content{
  font-size: 28rpx;
  color: #0099ff;
  padding-left: 40rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.order-item-url{
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.arrow-icon{
  flex-shrink: 0;
}
.pay-type{
  width: 100%;
  height: auto;
  background: #ffff;
  margin: 20rpx 0;
}
.pay-type-title{
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  color: #999999;
  padding: 0 20rpx;
}
.radio-list{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 40rpx;
  height: 70rpx;
}
.radio-item{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-right: 40rpx;
  font-size: 28rpx;
}
.login-warp{
  height: 200rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.login-warp .login-btn{
  position: fixed;
  left: 50rpx;
  right: 50rpx;
  bottom: 50rpx;
  width: 80%;
  height: 90rpx;
  padding: 0rpx;
  border-radius: 10rpx;
  border: none;
  line-height: 90rpx;
  color: #fff;
  background: rgb(0, 66, 107);
}
.payment-step{
  height: 80rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  background: #fff;
  margin-bottom: 20rpx;
}
.payment-text{
  font-size: 28rpx;
  padding:0 10rpx;
  color: #00426B;
}
.payment-tex-active{
  font-size: 28rpx;
  padding:0 10rpx;
  color: red;
}
.uncross-step{
  font-size: 28rpx;
  padding:0 10rpx;
  color: #999999;
}
.consign-bottom-title{
  height: 76rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  width: 100%;
  border-bottom: 1px solid #ddd;
  background:#fff;
}
.annex-text{
  color: #333;
  font-size: 32rpx;
}
.consign-bottom-title .download-btn{
  height: 60rpx;
  border:1px solid #00426B;
  border-radius: 33rpx;
  line-height: 56rpx;
  color: #00426B;
  background: #ffffff;
  width: 220rpx;
  padding: 0rpx;
  margin: 0rpx;
  font-size: 28rpx;
  font-weight: normal;
}
.consign-title{
  width:100%;
  height: 60rpx;
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  padding: 0 30rpx;
  border-bottom: 1px solid #ddd;
}
.order-list{
  background-color: #fff;
  margin-bottom: 20rpx;
}
.consign-top{
  height: auto;
  width: 100%;
  margin-bottom: 20rpx;
  background: #fff;
}
.star{
  width: 8rpx;
  flex-shrink: 0;
  margin:0 5rpx;
  display: inline-block;
  color:red;
}
.deliver-item{
  width:100%;
  height: 60rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 32rpx;
  color: #00639d;
  padding: 0 30rpx;
  border-bottom: 1px solid #ddd;
  background-color: #fff;
}
.consign-message1{
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 70rpx;
  border-bottom: 1px solid #ddd;
  padding: 0 30rpx;
  width: 100%;
  background: #fff;
  margin-bottom: 20rpx;
}
.add-message-ipt{
  flex: 1;
  width: 100%;
  height: 100%;
  color: #333;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.mask1{
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  background:rgba(103, 103, 103,.5);
}
.search-dialog{
  position: fixed;
  left: 0;
  top: 150rpx;
  right: 0;
  bottom: 0;
  z-index: 5000;
  width: 100%;
  height: auto;
  background:#FFF;
}
.add-message-emailipt{
  width: 100%;
  height:55rpx;
  color: #333;
  padding: 0 20rpx;
  font-size: 30rpx;
  border: 1rpx solid #ddd;
  text-align: left ;
}
.iconquestion{
  font-size: 40rpx;
}

package com.ruoyi.consignor.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @Description  货源返回类
 * <AUTHOR>
 * @Date 2020/8/14  17:09
 */
@Data
public class CargoSourceVO {

    /**
     * id
     */
    private Long id;

    /**
     * 货主id
     */
    private Long userId;

    /**
     * 货主名称
     */
    private String userName;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 货源号
     */
    private String cargoSourceId;

    /**
     * 货物名称
     */
    private String cargoName;

    /**
     * 出发地
     */
    private String placeOfDeparture;

    /**
     * 目的地
     */
    private String destination;

    /**
     * 货物数量
     */
    private Integer cargoNumber;

    /**
     * 货物重量
     */
    private String cargoWeight;

    /**
     * 货物体积
     */
    private String cargoBulk;

    /**
     * 货主
     */
    private String consignor;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Timestamp startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Timestamp endTime;

    /**
     * 备注
     */
    private String remark;


}

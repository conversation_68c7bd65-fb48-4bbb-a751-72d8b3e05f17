package com.ruoyi.barge.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 船运力信息发布表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/11 11:08
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("BARGE_CAPACITY_PUBLISH")
@KeySequence("SEQ_BARGE_CAPACITY_PUBLISH")
public class BargeCapacityPublish {
    /**
    * 主键
    */
    @TableId(value = "id",type = IdType.INPUT)
    private Long id;

    /**
    * 驳船id
    */
    @TableField("barge_id")
    private Long bargeId;

    /**
    * 联系电话
    */
    @TableField("phone")
    private String phone;

    /**
    * 运力数据-承载吨数-单位：吨
    */
    @TableField("capacity")
    private String capacity;

    /**
    * 开始时间
    */
    @TableField("start_time")
    private Date startTime;

    /**
    * 结束时间
    */
    @TableField("end_time")
    private Date endTime;

    /**
    * 创建人
    */
    @TableField("create_user")
    private String createUser;

    /**
    * 创建时间
    */
    @TableField("create_time")
    private Date createTime;

    /**
    * 更新人
    */
    @TableField("update_user")
    private String updateUser;

    /**
    * 更新时间
    */
    @TableField("update_time")
    private Date updateTime;

    /**
    * remark
    */
    @TableField("remark")
    private String remark;

    /**
     * 驳船名称
     */
    @TableField("BARGENAME")
    private String bargeName;

    /**
     * 驳船识别号
     */
    @TableField("BARGEIDENTIFIER")
    private String bargeIdentifier;
}
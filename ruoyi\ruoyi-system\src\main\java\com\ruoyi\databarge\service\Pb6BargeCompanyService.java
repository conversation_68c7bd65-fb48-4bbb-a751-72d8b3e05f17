package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.Pb6BargeCompany;
import com.ruoyi.databarge.domain.dto.Pb6BargeinfoAuditSearchDTO;
import com.ruoyi.databarge.domain.vo.Pb6BargeCompanyVO;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/9/1.
 * @Date: 2020/9/1 11:39
 */
public interface Pb6BargeCompanyService  extends IService<Pb6BargeCompany> {

    Pb6BargeCompanyVO searchByBargeId(Long bargeid);

    Pb6BargeCompany searchPb6BargeCompanyByBargeId(Long bargeid);

    void check(Pb6BargeCompanyVO pb6BargeCompanyVO, Integer checkStatus);

    Pb6BargeCompanyVO searchById(Long id);

    /**
     * 通过船公司ID，查找所有挂靠在该公司下的所有船信息
     * @param companyid 船公司ID
     * @return 挂靠列表
     */
    List<Pb6BargeCompanyVO> searchByCompanyId(Long companyid);

    boolean updateCompanyName(Long bargeId,Long updatebyid,String updatetime,String updateByName,String companyName,Long id);

}

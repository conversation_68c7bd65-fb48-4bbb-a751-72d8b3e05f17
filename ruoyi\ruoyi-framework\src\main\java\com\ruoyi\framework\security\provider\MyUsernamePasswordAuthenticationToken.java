package com.ruoyi.framework.security.provider;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/7/28 15:26
 */
public class MyUsernamePasswordAuthenticationToken extends UsernamePasswordAuthenticationToken {

    private String userType; //用户类型enum UserType
    private String loginType;//登陆类型pc/app
    private String wxCode; //用户微信code
    private String phoneNumber;//用户手机号
    private Integer index;

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }


    public String getWxCode() {
        return wxCode;
    }

    public void setWxCode(String wxCode) {
        this.wxCode = wxCode;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        userType = userType;
    }

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }

    public MyUsernamePasswordAuthenticationToken(String loginType, String UserType, String wxCode, String phoneNumber, Object principal, Object credentials,Integer index) {
        super(principal, credentials);
        this.userType=UserType;
        this.loginType=loginType;
        this.wxCode=wxCode;
        this.phoneNumber=phoneNumber;
        this.index = index;
    }

    public MyUsernamePasswordAuthenticationToken(Object principal, Object credentials) {
        super(principal, credentials);
    }

    public MyUsernamePasswordAuthenticationToken(Object principal, Object credentials, Collection<? extends GrantedAuthority> authorities) {
        super(principal, credentials, authorities);
    }
}

package com.ruoyi.framework.web.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.UserType;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户权限处理
 * 
 * <AUTHOR>
 */
@Component
public class SysPermissionService
{
    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysMenuService menuService;

    /**
     * 获取角色数据权限
     * 
     * @param user 用户信息
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(SysUser user)
    {
        Set<String> roles = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.isAdmin())
        {
            roles.add("admin");
        }
        else
        {
            roles.addAll(roleService.selectRolePermissionByUserId(user.getUserId()));
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     * 
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(SysUser user)
    {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.isAdmin())
        {
            perms.add("*:*:*");
        }
        else
        {

//            CONSIGNOR("10","货主"),
//            WAYBILLCONTACT("11","托运单联系人"),
//            CARRIERADMIN("12","船公司管理员"),
//            CARRIEUSER("13","船公司业务员"),
//            BARGEADMIN("14","驳船管理员"),
//            BARGEUSER("15","驳船业务员");
            //根据用户类型获取权限
            if(user.getUserType()==null){
                return perms;
            }else if(user.getUserType().equalsIgnoreCase(UserType.CARRIEUSER.getCode())
                    || user.getUserType().equalsIgnoreCase(UserType.BARGEUSER.getCode())
                    || user.getUserType().equalsIgnoreCase(UserType.CARRIERADMIN.getCode())
                    || user.getUserType().equalsIgnoreCase(UserType.CONSIGNOR.getCode())){
                perms.addAll(menuService.selectMenuPermsByAppUserId(user.getUserId()));
            } else if(user.getUserType().equalsIgnoreCase(UserType.PUBUSER.getCode())){
                // 201 为生产系统账号仓库员的通用角色
                perms.addAll(menuService.selectMenuPermsByRoleId(201L));
            }
            else{
                perms.addAll(menuService.selectMenuPermsByUserId(user.getUserId()));
            }

        }
        return perms;
    }
}

package com.ruoyi.basic.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/1/11 16:58
 */
@KeySequence(value = "SEQ_BASIC_GOODS_CATEGORY")
@TableName(value ="BASIC_GOODS_CATEGORY")
@Data
public class BasicGoodsCategory extends BaseEntity implements Serializable {
    /**
     * 货类ID
     */
    @TableId(value = "GOODS_ID",type = IdType.INPUT)
    private Long goodsId;

    /**
     * 货类
     */
    private String goodsCategory;

    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}

package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.enums.DataType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ftp.BargeFtpUtils;
import com.ruoyi.databarge.domain.UploadAddressDomain;
import com.ruoyi.databarge.domain.dto.BargePictureSearchDTO;
import com.ruoyi.databarge.mapper.UploadAddressDomainMapper;
import com.ruoyi.databarge.service.UploadAddressDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/10/9.
 * @Date: 2020/10/9 14:34
 */
@Service
public class UploadAddressDomainServiceImpl extends ServiceImpl<UploadAddressDomainMapper, UploadAddressDomain> implements UploadAddressDomainService {

    @Autowired
    private BargeFtpUtils bargeFtpUtils;

    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    /**
     * sftp服务器路径
     */
    @Value("${barge.ftp.path.ftpBasePath}")
    private volatile String ftpBasePath;

    //数据公司 -->托运单界面查询托运单对应图片
    @Override
    public List<UploadAddressDomain> searchCargoconsignmentPictureByMainId(Long id) {
        QueryWrapper<UploadAddressDomain> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UploadAddressDomain::getLinkId, id)
                .eq(UploadAddressDomain::getLinkType, 40)
                .eq(UploadAddressDomain::getStatus, 1);
        return super.list(queryWrapper);
    }

    //数据公司 -->托运单审核改单查询文件关联
    @Override
    public List<UploadAddressDomain> searchLinkFileForUpdateByLinkId(Long linkId) {
        QueryWrapper<UploadAddressDomain> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UploadAddressDomain::getLinkId, linkId)
                .eq(UploadAddressDomain::getLinkType, "40")
                .eq(UploadAddressDomain::getStatus, 1)
                .eq(UploadAddressDomain::getBakFlag, 2);
        return super.list(queryWrapper);
    }

    //数据公司 -->批量更新数据
    @Override
    public void updateUploadAddressList(List<UploadAddressDomain> uploadAddressList) {
        super.updateBatchById(uploadAddressList);
    }

    //数据公司 -->查询驳船对应图片
    @Override
    public List<UploadAddressDomain> searchBargePicturesByIdAndStatus(BargePictureSearchDTO bargePictureSearchDTO) {
        QueryWrapper<UploadAddressDomain> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UploadAddressDomain::getLinkId, bargePictureSearchDTO.getId())
                .in(UploadAddressDomain::getLinkType, 10,50)
                .in(UploadAddressDomain::getDataType,11,12,13,14,53)
                .eq(UploadAddressDomain::getStatus, bargePictureSearchDTO.getStatus());
        if(bargePictureSearchDTO.getApplyId() != null){
            queryWrapper.lambda().eq(UploadAddressDomain::getUploadUserId, bargePictureSearchDTO.getApplyId());
        }
        return super.list(queryWrapper);
    }

    // 查询驳船附件照片
    @Override
    public List<UploadAddressDomain> searchBargeAttachmentPic(BargePictureSearchDTO bargePictureSearchDTO) {
        QueryWrapper<UploadAddressDomain> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UploadAddressDomain::getLinkId, bargePictureSearchDTO.getId())
                .in(UploadAddressDomain::getLinkType, 10)
                .in(UploadAddressDomain::getDataType,71,72,73)
                .eq(UploadAddressDomain::getStatus, bargePictureSearchDTO.getStatus());
        if(bargePictureSearchDTO.getApplyId() != null){
            queryWrapper.lambda().eq(UploadAddressDomain::getUploadUserId, bargePictureSearchDTO.getApplyId());
        }
        return super.list(queryWrapper);
    }

    // 查询驳船需备案下载文件，包括水路运单、驳船通知书等
    @Override
    public List<UploadAddressDomain> searchBargeNeedRecordDownloadFile(Long id) {

        // 查询水路运单 linkType为40，dataType为43,linkId为bargeId, status为1
        QueryWrapper<UploadAddressDomain> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UploadAddressDomain::getLinkId, id)
                .eq(UploadAddressDomain::getLinkType, 40)
                .eq(UploadAddressDomain::getDataType, 43)
                .eq(UploadAddressDomain::getStatus, 1);
        List<UploadAddressDomain> list = super.list(queryWrapper);

        QueryWrapper<UploadAddressDomain> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.lambda().eq(UploadAddressDomain::getLinkId, id)
                .in(UploadAddressDomain::getLinkType, 60)
                .in(UploadAddressDomain::getDataType,62,63,64,65,66)
                .eq(UploadAddressDomain::getStatus, 1);
        List<UploadAddressDomain> list1 = super.list(queryWrapper1);

        // 合并查询结果
        list.addAll(list1);

        return list;
    }

    //数据公司 -->查询驳船所有人的图片
    @Override
    public List<UploadAddressDomain> searchBargeOwnerIdentityPic(BargePictureSearchDTO bargePictureSearchDTO) {
        return super.baseMapper.searchBargeOwnerIdentityPic(bargePictureSearchDTO);
    }

    //数据公司 -->驳船基本信息界面更改驳船图片
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePictureForShipInfo(MultipartFile file, Long bargeId, Integer picType) throws SftpException, JSchException, IOException {
        List<UploadAddressDomain> list;

        boolean isIdentity = false;
        //驳船主身份证查找方式和驳船不同，单独判断
        if(picType.equals(DataType.IDENTITY_POSITIVE.getCode()) || picType.equals(DataType.IDENTITY_NEGATIVE.getCode())
            || picType.equals(DataType.ELECTRONIC_SIGNATURE_ATTORNEY.getCode())){
            BargePictureSearchDTO bargePictureSearchDTO = new BargePictureSearchDTO();
            bargePictureSearchDTO.setId(bargeId);
            bargePictureSearchDTO.setStatus(1);
            bargePictureSearchDTO.setDataType(picType);
            list = this.searchBargeOwnerIdentityPic(bargePictureSearchDTO);

            if(list == null || list.size() == 0){
                list = this.searchBargeOwnerIdentityPicByUploadUserId(bargePictureSearchDTO);
            }

            isIdentity = true;
        } else {
            QueryWrapper<UploadAddressDomain> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(UploadAddressDomain::getLinkId, bargeId)
                    .eq(UploadAddressDomain::getLinkType, 10)
                    .eq(UploadAddressDomain::getStatus, 1)
                    .eq(UploadAddressDomain::getDataType, picType);
            list = super.list(queryWrapper);
        }
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        if(list.size() != 1){
            if(list.size() > 1){
                //有多张图片需要先删除
                for(UploadAddressDomain domain: list){
                    // String[] urls = domain.getUrl().split("/");
                    // String path = ftpBasePath + domain.getUploadUserId() + "/";
                    // bargeFtpUtils.deleteFile(path, urls[urls.length - 1]);
                    super.removeById(domain.getId());
                }
            }

        } else{
            //找到了一张图片，需先删除原来的图片，再传新的图片上去
            UploadAddressDomain uploadAddressDomain = list.get(0);
            // String[] urls = uploadAddressDomain.getUrl().split("/");
            // String path = ftpBasePath + uploadAddressDomain.getUploadUserId() + "/";
            // bargeFtpUtils.deleteFile(path, urls[urls.length - 1]);
            super.removeById(uploadAddressDomain.getId());
        }
        //没有找到该类型的驳船图片，需新建
        String url = bargeFtpUtils.uploadFile(file, sysUser.getUserId());
        UploadAddressDomain uploadAddressDomain = new UploadAddressDomain();
        if(isIdentity){
            List<SysUserBarge> sysUserBarge = sysUserBargeMapper.selectList(new QueryWrapper<SysUserBarge>().lambda().eq(SysUserBarge::getBargeId, bargeId));
            if(sysUserBarge.size() == 0){
                throw new CustomException("未找到驳船主绑定该驳船");
            } else if(sysUserBarge.size() > 1){
                throw new CustomException("发现多个驳船主绑定该驳船");
            } else {
                uploadAddressDomain.setLinkType("50"); //50为用户关联图片
                uploadAddressDomain.setLinkId(sysUserBarge.get(0).getUserId());
            }
        } else {
            uploadAddressDomain.setLinkId(bargeId);
            uploadAddressDomain.setLinkType("10"); // 10为驳船备案资料
        }
        uploadAddressDomain.setUrl(url);
        uploadAddressDomain.setUploadTime(new Date());
        uploadAddressDomain.setUploadUserId(uploadAddressDomain.getUploadUserId() == null ? sysUser.getUserId() : uploadAddressDomain.getUploadUserId());
        uploadAddressDomain.setUploadUserName(StringUtils.isBlank(uploadAddressDomain.getUploadUserName()) ? sysUser.getUserName() : uploadAddressDomain.getUploadUserName());
        uploadAddressDomain.setDataName(DataType.codeToCodeName(picType));
        uploadAddressDomain.setStatus(1);
        uploadAddressDomain.setDataType(picType);
        return super.save(uploadAddressDomain);
    }

    @Override
    public boolean uploadAgentDeliveryImage(MultipartFile file, Long agentDeliveryId, Integer isAdd) throws SftpException, JSchException, IOException {
        // 1、查询数据库中是否已存在图
        List<UploadAddressDomain> uploadAddressDomainList = this.list(new LambdaQueryWrapper<UploadAddressDomain>().eq(UploadAddressDomain::getLinkId, agentDeliveryId)
                .eq(UploadAddressDomain::getLinkType, 60));
        UploadAddressDomain uploadAddressDomain;

        //添加判断逻辑
        if(isAdd == 0){
            if(uploadAddressDomainList.size() <= 1){
                uploadAddressDomain = new UploadAddressDomain();
            } else {
                if(uploadAddressDomainList.get(0).getUploadTime().before(uploadAddressDomainList.get(1).getUploadTime())){
                    uploadAddressDomain = uploadAddressDomainList.get(0);
                } else {
                    uploadAddressDomain = uploadAddressDomainList.get(1);
                }

                //有两张图的，需要删除较先上传的那一张
                String[] urls = uploadAddressDomain.getUrl().split("/");
                String path = ftpBasePath + uploadAddressDomain.getUploadUserId() + "/";
                bargeFtpUtils.deleteFile(path, urls[urls.length - 1]);
            }
        } else {
            //修改需要先删除以前的
            for(UploadAddressDomain domain: uploadAddressDomainList){
                String[] urls = domain.getUrl().split("/");
                String path = ftpBasePath + domain.getUploadUserId() + "/";
                bargeFtpUtils.deleteFile(path, urls[urls.length - 1]);
                removeById(domain.getId());
            }
            uploadAddressDomain = new UploadAddressDomain();
        }

        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        String url = bargeFtpUtils.uploadFile(file, sysUser.getUserId());
        if(uploadAddressDomain.getId() == null){
            //添加
            uploadAddressDomain.setLinkId(agentDeliveryId);
            //60-代理发货资料
            uploadAddressDomain.setLinkType("60");
            uploadAddressDomain.setUploadTime(DateUtils.getNowDate());
            uploadAddressDomain.setUploadUserId(sysUser.getUserId());
            uploadAddressDomain.setDataName(DataType.AGENT_DELIVERY.getCodeName());
            uploadAddressDomain.setStatus(1);
            uploadAddressDomain.setDataType(DataType.AGENT_DELIVERY.getCode());
            uploadAddressDomain.setUrl(url);
            return save(uploadAddressDomain);
        } else {
            //修改
            //60-代理发货资料
            uploadAddressDomain.setUploadTime(DateUtils.getNowDate());
            uploadAddressDomain.setUploadUserId(sysUser.getUserId());
            uploadAddressDomain.setStatus(1);
            uploadAddressDomain.setUrl(url);
            return updateById(uploadAddressDomain);
        }
    }

    @Override
    public List<UploadAddressDomain> searchBargeOwnerIdentityPicByUploadUserId(BargePictureSearchDTO bargePictureSearchDTO) {
        return super.baseMapper.searchBargeOwnerIdentityPicByUploadUserId(bargePictureSearchDTO);
    }

    @Override
    public List<UploadAddressDomain> searchPb6WatercargoFile(String waterwaycargoid) {
        return super.baseMapper.searchPb6WatercargoFileByWaterwaycargoId(waterwaycargoid);
    }

    @Override
    public List<UploadAddressDomain> searchPb6WatercargoFileApp(String id) {
        List<UploadAddressDomain> uploadAddressDomains = baseMapper.searchPb6WatercargoFile(id);
        // 处理uploadAddressDomains 对其中的url进行拼接，为https://scbs.gzport.com + url.subString(17) +"?timestamp=" +new Date().getTime();
        for(UploadAddressDomain uploadAddressDomain:uploadAddressDomains){
            String url = uploadAddressDomain.getUrl();
            url = "https://scbs.gzport.com" + url.substring(17) + "?timestamp=" + new Date().getTime();
            uploadAddressDomain.setUrl(url);
        }
        return uploadAddressDomains;
    }
}

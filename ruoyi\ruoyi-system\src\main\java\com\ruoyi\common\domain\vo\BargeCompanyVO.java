package com.ruoyi.common.domain.vo;

import lombok.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/15 13:05
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class BargeCompanyVO {

    private Long id;

    private String customerId;

    /**
     * 简称
     */
    private String cShortName;

    /**
     * 公司名称 全称
     */
    private String cFullName;

    /**
     *
     */
    private String isRegiester;

    /**
     * 挂靠审核（0-待审核，1-审核通过，2-审核不通过）
     */
    private Integer auditStatus;

    /**
     * 绑定类型（1-自有、2-临时挂靠、3-长期挂靠，4-其他）
     */
    private Integer bindingType;

    /**
     * 挂靠审核（0-修改挂靠审核，1-挂靠审核， 2-取消挂靠审核）
     */
    private Integer isAudit;

    /**
     * 船公司id
     */
    private Long companyId;
}

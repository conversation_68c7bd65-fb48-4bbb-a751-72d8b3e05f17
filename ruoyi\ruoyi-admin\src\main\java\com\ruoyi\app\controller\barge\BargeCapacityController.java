package com.ruoyi.app.controller.barge;

import com.ruoyi.barge.domain.bo.BargeCapacityPublishBO;
import com.ruoyi.barge.service.BargeCapacityService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.consignor.domain.bo.CargoSourceBO;
import com.ruoyi.consignor.service.ConsignorCargoSourceService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Description 驳船运力主页
 * <AUTHOR>
 * @Date 2020/7/24 15:06
 */
@Api(value = "驳船主-主页-api")
@Slf4j
@RestController
@RequestMapping("/barge/capacity")
public class BargeCapacityController {

    @Autowired
    private BargeCapacityService bargeCapacityService;
    @Autowired
    private ConsignorCargoSourceService consignorCargoSourceService;

    /**
     * 获取货源信息列表
     * @return
     */
    @PostMapping("/getCargoSourceList")
    public AjaxResult getCargoSourceList(@RequestBody CargoSourceBO cargoSourceBO) {
        return AjaxResult.success("获取货源信息成功", consignorCargoSourceService.selectCargoSource(cargoSourceBO));
    }

    /**
     * 获取运力信息列表
     * @return
     */
    @PostMapping("/getCapacityList")
    public AjaxResult getCapacityList(@RequestBody BargeCapacityPublishBO bargeCapacityPublishBO) {
        return AjaxResult.success("获取运力成功", bargeCapacityService.getCapacityList(bargeCapacityPublishBO));
    }

    /**
     * 运力发布
     * @return
     */
    @PostMapping("/addCapacity")
    public AjaxResult addCapacity(@RequestBody @Valid BargeCapacityPublishBO bargeCapacityPublishBO, BindingResult result) {

        // 判断BindingResult是否保存错误的验证信息，如果有，则直接return
        if (result.hasErrors()) {
            return AjaxResult.getErrors(result);
        }
        log.error(bargeCapacityPublishBO.getBeginTime());
        log.error(bargeCapacityPublishBO.getEndTime());

        if (StringUtils.isBlank(bargeCapacityPublishBO.getBeginTime())
                || StringUtils.isBlank(bargeCapacityPublishBO.getEndTime())) {
            log.error("开始时间与结束时间不能为空");
            return AjaxResult.error("开始时间与结束时间不能为空");
        }

        return bargeCapacityService.addCapacity(bargeCapacityPublishBO);
    }

    /**
     * 运力修改
     * @return
     */
    @PostMapping("/updateCapacity")
    public AjaxResult updateCapacity(@RequestBody @Valid BargeCapacityPublishBO bargeCapacityPublishBO, BindingResult result) {

        if (null == bargeCapacityPublishBO.getId()) {
            log.error("运力id不能为空");
            return AjaxResult.error("运力id不能为空");
        }

        // 判断BindingResult是否保存错误的验证信息，如果有，则直接return
        if (result.hasErrors()) {
            return AjaxResult.getErrors(result);
        }

        if (StringUtils.isBlank(bargeCapacityPublishBO.getBeginTime())
                || StringUtils.isBlank(bargeCapacityPublishBO.getEndTime())) {
            log.error("开始时间与结束时间不能为空");
            return AjaxResult.error("开始时间与结束时间不能为空");
        }

        return bargeCapacityService.updateCapacity(bargeCapacityPublishBO);
    }

    /**
     * 运力删除
     * @return
     */
    @PostMapping("/deleteCapacity")
    public AjaxResult deleteCapacity(@RequestBody BargeCapacityPublishBO bargeCapacityPublishBO) {
        if (null == bargeCapacityPublishBO.getId()) {
            log.error("运力id不能为空");
            return AjaxResult.error("运力id不能为空");
        }
        return bargeCapacityService.deleteCapacity(bargeCapacityPublishBO.getId());
    }
}

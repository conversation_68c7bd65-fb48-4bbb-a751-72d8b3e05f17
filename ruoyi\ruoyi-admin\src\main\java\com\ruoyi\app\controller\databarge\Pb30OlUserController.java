package com.ruoyi.app.controller.databarge;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.app.controller.support.fdd.FddCompanyService;
import com.ruoyi.app.controller.support.ftp.FtpTemplate;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.databarge.domain.Pb1Customer;
import com.ruoyi.databarge.domain.Pb30OlUser;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.domain.dto.Pb30OlUserDTO;
import com.ruoyi.databarge.domain.dto.Pb30OlUserSearchDTO;
import com.ruoyi.databarge.service.Pb1CustomerService;
import com.ruoyi.databarge.service.Pb30OlUserService;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import com.ruoyi.system.service.ISysUserService;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/11/17.
 * @Date: 2020/11/17 9:59
 */
@RestController
@RequestMapping("/barge/pb30OlUser")
public class Pb30OlUserController {

    @Autowired
    private Pb30OlUserService pb30OlUserService;

    @Autowired
    private FddCompanyService fddCompanyService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ShipFddUserRelService shipFddUserRelService;

    @Autowired
    private FddCommonService fddCommonService;

    @Autowired
    private FtpTemplate ftpTemplate;

    @Autowired
    private Pb1CustomerService pb1CustomerService;

    @PostMapping("/searchCheckedBargeCompany")
    public AjaxResult searchCheckedBargeCompany(@RequestBody Pb30OlUserSearchDTO pb30OlUserSearchDTO){
        return AjaxResult.success(pb30OlUserService.searchCheckedBargeCompany(pb30OlUserSearchDTO));
    }

    @PostMapping("/searchNotCheckBargeCompany")
    public AjaxResult searchNotCheckBargeCompany(@RequestBody Pb30OlUserSearchDTO pb30OlUserSearchDTO){
        return AjaxResult.success(pb30OlUserService.searchNotCheckBargeCompany(pb30OlUserSearchDTO));
    }

    @Log(title = "船公司审核", businessType = BusinessType.CHECK)
    @PostMapping("/checkBargeCompany")
    public AjaxResult checkBargeCompany(@RequestBody Long id){
        Pb30OlUser pb30OlUser = pb30OlUserService.getById(id);
        Pb1Customer pb1Customer = pb1CustomerService.getOne(new LambdaQueryWrapper<Pb1Customer>().eq(Pb1Customer::getId,pb30OlUser.getCompanyId()));
        if(StringUtils.isNotBlank(pb30OlUser.getPassport())){
            String []comids = pb30OlUser.getPassport().split(",");
            int i = 0; //记录当前passport里有多少个2
            for(String comid: comids){
                if(comid.equals("2")){
                    i++;
                }
            }
            if(i == 0){
                //如果 没有找到2，说明需要审核通过
                pb30OlUser.setPassport(pb30OlUser.getPassport() + ",2");
                //20220908洪叫添加
                pb30OlUser.setIsAudit("1");
                pb1Customer.setCtype("船公司");
                pb1Customer.setSpecialuse("0");
                pb1Customer.setIsregister("Y");
                pb1Customer.setCisactive("1");
                //
                pb1CustomerService.updateById(pb1Customer);
            } else {
                //否则是需要去掉审核通过
                String passport = new String("");
                for(String comid: comids){
                    if(!comid.equals("2")){
                        if(StringUtils.isNotBlank(passport)) {
                            passport = passport + "," + comid;
                        } else {
                            if(StringUtils.isNotBlank(comid)){
                                passport = passport + comid;
                            }
                        }
                    }
                }
                pb30OlUser.setPassport(passport);
            }
        } else {
            pb30OlUser.setPassport("2");
        }
        pb30OlUser.setAuditDate(DateUtils.getTime());
        pb30OlUser.setAuditPerson(SecurityUtils.getLoginUser().getUser().getNickName());
        return AjaxResult.success(pb30OlUserService.updateById(pb30OlUser));
    }

    @PostMapping("searchAllClerksByCompanyId")
    public AjaxResult searchAllClerksByCompanyId(@RequestBody Long companyId){
        List<Pb30OlUser> pb30OlUserList = pb30OlUserService.searchAllClerksByCompanyId(companyId);
        List<Pb30OlUserDTO> pb30OlUserDTOList = new ArrayList<>();
        for(Pb30OlUser pb30OlUser: pb30OlUserList){
            Pb30OlUserDTO pb30OlUserDTO = new Pb30OlUserDTO();
            BeanUtils.copyProperties(pb30OlUser, pb30OlUserDTO);
            if(StringUtils.isNotBlank(pb30OlUser.getPassport())) {
                String[] comids = pb30OlUserDTO.getPassport().split(",");

                int i = 0; //记录当前passport里有多少个2
                for (String comid : comids) {
                    if (comid.equals("2")) {
                        i++;
                    }
                }
                if (i == 0) {
                    pb30OlUserDTO.setAuditStatus("未审核");
                } else {
                    pb30OlUserDTO.setAuditStatus("已审核");
                }
            } else {
                pb30OlUserDTO.setAuditStatus("未审核");
            }
            pb30OlUserDTOList.add(pb30OlUserDTO);
        }
        return AjaxResult.success(pb30OlUserDTOList);
    }

    @PostMapping("/searchAuditStatusOfFdd")
    public AjaxResult searchAuditStatusOfFdd(@RequestBody Long pb30OlUserId){
        SysUser sysUser = sysUserService.selectUserByGmUserId(pb30OlUserId);
        if(sysUser == null){
            throw new CustomException("该船公司管理员没有补充船公司资料，无法审核通过！");
        }
        List<ShipFddUserRel> shipFddUserRelList = shipFddUserRelService.list(new QueryWrapper<ShipFddUserRel>().lambda().eq(ShipFddUserRel::getShipUserId, sysUser.getUserId()).eq(ShipFddUserRel::getType,"2"));
        if(shipFddUserRelList.size() > 1){
            throw new CustomException("发现多个法大大审核数据，该船公司无法审核通过！");
        } else if(shipFddUserRelList.size() == 0){
            throw new CustomException("未找到法大大审核数据，该船公司无法审核通过！");
        }
        return AjaxResult.success(fddCompanyService.companyCertificationStatus(shipFddUserRelList.get(0).getFddAccountId()));
    }

    // ---------王伦辉---------
    @PostMapping("/checkBargeCompanySealChange")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult checkBargeCompanySealChange(@RequestParam("shipFddUserRelId") Long shipFddUserRelId,@RequestParam("reviewStatus") String reviewStatus){
        ShipFddUserRel shipFddUserRel=shipFddUserRelService.getById(shipFddUserRelId);
        switch (reviewStatus){
            case "0":// 审核未通过
                // 重置新印章图片url为null，删除新印章图片url
                shipFddUserRelService.update(Wrappers.<ShipFddUserRel>lambdaUpdate().set(ShipFddUserRel::getNewSealUrl,null).eq(ShipFddUserRel::getId,shipFddUserRel.getId()));
                try {
                    ftpTemplate.rmFile(shipFddUserRel.getNewSealUrl());
                }catch (IOException e){
                    throw new CustomException("服务器写文件失败!",e);
                }

                return AjaxResult.success("审核成功!");
            case "1":// 审核通过
                // 重置印章图片url为新印章图片url，新印章图片url为null，并上传到法大大，最后删除旧印章图片
                shipFddUserRelService.update(Wrappers.<ShipFddUserRel>lambdaUpdate().set(ShipFddUserRel::getSealUrl,shipFddUserRel.getNewSealUrl()).set(ShipFddUserRel::getNewSealUrl,null).eq(ShipFddUserRel::getId,shipFddUserRel.getId()));
                try {
                    File file = File.createTempFile("ship-", ".png");
                    file.deleteOnExit();

                    System.out.println(shipFddUserRel);
                    FileOutputStream fileOutputStream=new FileOutputStream(file);
                    ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
                    ftpTemplate.download(shipFddUserRel.getNewSealUrl(),byteArrayOutputStream);
                    String fileBase64=Base64.encode(byteArrayOutputStream.toByteArray());
                    shipFddUserRel.setFileBase64(fileBase64);

                    InputStream inputStream=new ByteArrayInputStream(Base64.decode(shipFddUserRel.getFileBase64()));
                    Thumbnails
                            .of(inputStream)
                            .size(166,166)
                            .outputQuality(1f)
                            .outputFormat("png")
                            .toOutputStream(fileOutputStream);
                    //fddCommonService.uploadAndUpdateSeal(shipFddUserRel.getFddAccountId(),shipFddUserRel.getShipName(),file);

//                    ftpTemplate.download(shipFddUserRel.getNewSealUrl(),fileOutputStream);

                    fddCommonService.uploadAndUpdateSeal(shipFddUserRel.getFddAccountId(),sysUserService.selectUserById(shipFddUserRel.getShipUserId()).getCompanyName(),file);// 船公司印章名为船公司名

                    ftpTemplate.rmFile(shipFddUserRel.getSealUrl());
                    inputStream.close();

                    fileOutputStream.close();

                    return AjaxResult.success("审核成功!");
                }catch (IOException e){
                    throw new CustomException("服务器写文件失败!",e);
                }
            default:
                throw new CustomException("审核状态不正确!");
        }
    }
    // ---------王伦辉---------
}

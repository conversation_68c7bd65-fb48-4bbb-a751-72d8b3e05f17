/* pages/protocol/protocol.wxss */
.protocol-head{
  height: 60rpx;
  line-height: 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}
.normal-text{
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 5rpx 0rpx;
}
.protocol-content{
  padding: 20rpx;
}
.normal-indent{
  text-indent: 2em;
}
page{
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.page-content{
  flex: 1;
  overflow-y: scroll;
}
.page-back{
  height: 150rpx;
  line-height: 150rpx;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.page-back .page-backbtn{
  width: 60%;
  background: #00426B;
  color: #ffffff;
  font-size: 30rpx;
  margin: 0;
}
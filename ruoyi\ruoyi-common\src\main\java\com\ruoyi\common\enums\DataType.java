package com.ruoyi.common.enums;

import java.util.Objects;

/**
 * 资料细分类型
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/21 11:27
 */
public enum DataType {

    BARGE_INSPECTION_CERTIFICATE(11, "船舶检验证书簿封面"),
    BARGE_PROJECT_CONTENT(12, "船舶主要项目内容"),
    BARGE_AIRWORTHINESS_CERTIFICATE(13, "船舶适航证书"),
    BARGE_ASI_CERTIFICATE(14, "船舶自动识别系统AIS证书"),
    BARGE_OWNER_ID_CARD_FRONT_SIDE(15, "驳船主身份证正面"),
    BARGE_OWNER_ID_CARD_REVERSE_SIDE(16, "驳船主身份证反面"),
    ELECTRONIC_SIGNATURE_ATTORNEY(17, "电子签章授权委托书"),

    CONSIGN_ORDER_IMG(41, "出库单图片"),
    //CONSIGN_COMMISSION_IMG(42, "委托书图片"),
    CONSIGN_WATER_ORDER(43, "水路货物运单"),
    CONSIGN_HANDOVER(44, "货物交接清单"),
    CONSIGN_CERTIFICATE(45, "驳船装货交接凭证"),

    IDENTITY_POSITIVE(51, "身份证正面"),
    IDENTITY_NEGATIVE(52, "身份证反面"),
    CONSIGN_COMMISSION_IMG(53, "委托书图片"),

    BARGE_NOTICE_FILE(62, "驳船告知书"),

    SAFETY_NOTICE_FILE(63, "安全告知书"),

    CAPTAIN_STATEMENT_FILE(64, "船长声明"),

    SAFETY_CHECKLIST_FILE(65, "安全检查表"),

    SAFETY_LOAD_FILE(66, "安全装货确认书"),

    AGENT_DELIVERY(61, "代理发货图片"),

    ATTACHMENT_FILE_ONE(71, "附件1"),

    ATTACHMENT_FILE_TWO(72, "附件2"),

    ATTACHMENT_FILE_THREE(73, "附件3");



    private final Integer code;

    private final String codeName;

    DataType(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }

    /**
     * 枚举值获取描述值
     * @param code
     * @return
     */
    public static String codeToCodeName(Integer code){
        if(Objects.nonNull(code)){
            for (DataType status : DataType.values()) {
                if (status.code.equals(code)){
                    return status.codeName;
                }
            }
        }
        return null;
    }

    /**
     * 描述值获取枚举值
     * @param codeName
     * @return
     */
    public static Integer codeNameToCode(String codeName){
        if(Objects.nonNull(codeName)){
            for (DataType status : DataType.values()) {
                if (status.codeName.equals(codeName)){
                    return status.code;
                }
            }
        }
        return null;
    }
}

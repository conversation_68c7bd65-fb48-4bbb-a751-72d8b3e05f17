package com.ruoyi.api.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/6/13 16:06
 */
@Data
public class CountResult {

    private String workPoint;//作业点

    private Long workPointUuid;//作业点ID

    private String shipCompanyName;//船公司名称

    private Long shipCompanyUuid;//船公司ID

    private int totalSC;//艘次

    private String bglx;

    private String bglxName;//报关类型

    private int importSC;//进口艘次

    private int importF20;//进口F2O

    private int importF40;//进口F4O

    private int importE20;//进口E2O

    private int importE40;//进口E4O

    private BigDecimal importTdCount;//进口提单数

    private int exportSC;//出口艘次

    private int exportF20;//出口F2O

    private int exportF40;//出口F4O

    private int exportE20;//出口E2O

    private int exportE40;//出口E4O

    private BigDecimal exportTdCount;//出口提单数

    private BigDecimal inCome;

    private double expense;

    private BigDecimal summation;

}

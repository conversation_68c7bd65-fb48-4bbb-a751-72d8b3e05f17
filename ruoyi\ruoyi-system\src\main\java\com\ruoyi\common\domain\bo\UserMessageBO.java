package com.ruoyi.common.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/5 14:56
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class UserMessageBO extends BaseEntity {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 运单id
     */
    private Long consignId;

    /**
     * 运单明细id
     */
    private Long consignDetailId;

    /**
     * 运单状态
     */
    private String status;

    /**
     * 用户消息主键id
     */
    private Long userMessageId;

    /**
     * 读取状态
     */
    private String isRead;

    private Map<String, Object> params;

    private String sendMessageType;

    /**
     * 发送消息的用户id
     */
    private List<Long> userIds;

    /**
     * 水路运单号
     */
    private String waterCargoId;
}

// pages/consignment/payment/payment.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //当前托运对象
    currentConsignObj:{},
    //表单
    form:{
      chargeBalanceType:"0",//支付方式（0-月结，1-现结）
      monthlyCode:"",//月结码验证
    },
    // isMonthlyCheck: false, //是否勾选月结码验证
    showSearchDialog:false,//是否显示查找月结单位的弹窗
    //选中的月结单位数组
    selectedProjectsArray:[],
    //是否显示批量下载弹窗
    batchDialog:false,
    //用户邮箱 
    userEmail:"",
    //批量下载按钮
    batchButtons:[{text: '取消'}, {text: '确定'}],
},

  /**
   * 
   * 方法
   */
  /**
   * 判断是否存在，不存在返回空字符串或其他
   * @param {传进来的变量} empty 
   * @param {为空时返回的值} replace 
   */
  emptyReturn(empty,replace){
    if(!replace || replace==null){
      replace = ""
    }
    if(empty == null || !empty){
      return replace
    }else{
      return empty
    }
  },

  // 提示
  tipSomething(){
    wx.showModal({
      title: '提示',
      content: '输入正确的船公司月结校验码，即可月结成功',
      showCancel: false,
      success (res) {
        // if (res.confirm) {
        //   console.log('用户点击确定')
        // } else if (res.cancel) {
        //   console.log('用户点击取消')
        // }
      }
    })
  },
  // 是否选中月结码
  // isMonthlyCheckChange(e) {
  //   this.setData({
  //     isMonthlyCheck:!this.data.isMonthlyCheck
  //   })
  // },

  //月结码验证输入框事件
  monthlyCodeInput(e) {
    this.setData({
      "form.monthlyCode": e.detail.value,
    })
  },

  /**
   *  获取水路运单资料
   * 参数：
   * waterwayCargoId 水路运单编号
     type 文件类型 1-广州港新沙港务有限公司驳船装货交接凭证.docx 2-货物交接清单.docx 3-水路货物运单.pdf
   */
  getSeaWayBill(e) {
    let item = e.currentTarget.dataset.item 
    let param = {
      type: item.type,
      waterwayCargoId: this.data.form.waterWayCargoId,
    }
    if(!param.waterwayCargoId) {
      app.$message('水路运单未生成，暂无可预览文件');
    }
    app.$downLoad(app.$url.consign.getPdfOrWord, param).then((res) => {
      if(res.byteLength <= 100) {
        let uint8_msg = new Uint8Array(res);
        let decodedString = JSON.parse(String.fromCharCode.apply(null, uint8_msg));
        app.$message(`预览文件失败，原因${decodedString.msg}`);
        return
      }
      const fs = wx.getFileSystemManager(); // 获取全局唯一的文件管理器
      // 写文件
      fs.writeFile({
        filePath: wx.env.USER_DATA_PATH + `/${item.dataName}`, //写入的文件路径 (本地路径)
        data: res, // 写入的文本或二进制数据
        encoding: "binary", // 文件的字符编码
        success(res) {
          wx.openDocument({
            filePath: wx.env.USER_DATA_PATH + `/${item.dataName}`,  //拿上面存入的文件路径
            success: function (res) {
              console.log(res);
            }
          })
        },
      })
    })

  
  
  },

  //出库记录查询
  outBoundJump(){
    wx.navigateTo({
      url: "/pages/consignment/bargeOutboundInquiry/bargeOutboundInquiry",
    })
  },

  //邮箱input
  emailInput(e){
    this.setData({
      userEmail:e.detail.value
    })
  },

  //点击批量下载弹窗按钮
  tapBatchDialogButton(e){
    // 点击取消按钮
    if(e.detail.index == 0) {
    }else {
      // 点击确定按钮-发送邮件 //参数
      let param = {
        toEmail:this.data.userEmail,
        waterwayCargoId: this.data.form.waterWayCargoId,
      }
      //邮箱校验格式
      let emailReg = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
      let email = param.toEmail //邮箱
      if(!email){
        app.$message("请输入邮箱")
        return
      }
      if(email && !emailReg.test(email)) {
        app.$message("你输入的邮箱格式不正确!")
        return;
      } 
      app.$post(app.$url.consign.sendEmail,param).then(res=>{
        if(res.code == 200){
          app.$message(res.msg)
        }else {
          app.$message(res.msg)
        }
      })
    }
    this.setData({
      batchDialog:false
    })
  },

  //批量下载
  batchDown(){
    this.setData({
      batchDialog:true
    })
  },


  //关闭搜索月结单位弹窗,获取子组件选中的值
  closeSearchDialog(e){
    let that = this
    let selectedArr = e.detail.selectedArr //获取子组件选中月结单位的数组
    if(selectedArr && selectedArr.length>0){
      that.setData({
        "form.companyId":selectedArr[0].id, // 公司id
        "form.companyName":selectedArr[0].cfullName, // 公司名称
        "form.wxMonthChargeByName":selectedArr[0].cfullName,//月结单位
      })
    }else{
      that.setData({
        "form.companyId":"", // 公司id
        "form.companyName":"", // 公司名称
        "form.wxMonthChargeByName":"",//月结单位
      })
    }
    that.setData({
      showSearchDialog : !that.data.showSearchDialog,
      selectedProjectsArray:selectedArr,
    })
  },

  //获取详情
  getDetailData(){
    let that = this 
    if(!this.data.currentConsignObj.consignDetailId){
      app.$message("详情参数id为空")
      return
    }
    //运单详情id
    app.$post(app.$url.consign.getConsignDetail,{id:this.data.currentConsignObj.consignDetailId}).then(res=>{
      if(res.code == 200){
        if(res.data){
          //校验null值
          for(let key in res.data){
            if(key !=  "wxOperateState"){
              res.data[key] = this.emptyReturn(res.data[key])
            }
          }
          

          /**
           *  1、已支付才给展示水路运单文件，“待确认、待支付”状态下不展示文件。
              2、“待预约、待报道、已报到”这三个状态就展示一个文件：“水路运单”
              3、“已完成”状态下，展示3个文件：“水路运单、驳船交接凭证、驳船交接清单”
           */
         
          // 固定写死文件列表
          let dataList = [{
            type: 1,
            dataName: "广州港新沙港务有限公司驳船装货交接凭证.pdf",
          },{
            type: 2,
            dataName: "货物交接清单.pdf",
          },{
            type: 3,
            dataName: "水路货物运单.pdf",
          },]
          let resultDataList = [] //过滤后的文件列表
 
          let wxOperateStateText = this.data.currentConsignObj.wxOperateStateText //订单状态
          dataList.map(item=>{
            if(item.type == 3 && (wxOperateStateText == "待预约" || wxOperateStateText == "待报到" || wxOperateStateText == "已报到")) {
              resultDataList.push(item)
            }else if(wxOperateStateText == "已完成") {
              resultDataList.push(item)
            }
          })
          res.data.dataList = resultDataList


          /**
           * 支付、改单参数companyId和companyName：
           *  如果没用重新选择月结单位，则获取的是托运单
              详情接口的wxMonthChargeById和wxMonthChargeByName的值，
              如果重新选择月结单位，则获取选中的id和cfullName的值
           */
  
          //初始化判断wxMonthChargeById是否为空,有值放进月结单位选中数组selectedProjectsArray； wxMonthChargeById-月结公司id ,wxMonthChargeByName-月结公司名称  companyId,companyName
          this.setData({
            form:{
              ...res.data,
              companyId:res.data.wxMonthChargeById,
              companyName:res.data.wxMonthChargeByName,
            }
          })
          if(res.data.wxMonthChargeById){
            let selectedProjectsArray = [{id:res.data.wxMonthChargeById,cfullName:res.data.wxMonthChargeByName}]
            this.setData({
              selectedProjectsArray,
            })
          }
        }
      }else {
      app.$message(res.msg)
      }
    })
  },

  // 结算方式切换
  radioChange(e){
    this.setData({
      "form.chargeBalanceType":e.detail.value
    })
  },

  // 提交审核
  handleReview() {
    //月结直接返回托运单列表;现结，跳转支付页面，支付成功后说跳转到预约页面。如果不预约，点击返回，那就是回到托运单列表，状态为待预约
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息
    let form = this.data.form
    let currentConsignObj = JSON.stringify(this.data.currentConsignObj) //当前托运对象

    let param = {
      id:this.data.currentConsignObj.consignDetailId,//运单详情id
      chargeBalanceType:form.chargeBalanceType,// 支付方式 0-月结 1-现结
      companyId:form.companyId,// 公司id 
      companyName:form.companyName,// 公司名称 
      monthlyCode:form.monthlyCode,//月结码验证
      waterwayCargoId:form.waterWayCargoId,//水路运单编号

    }

    if(!param.id){
      app.$message("详情参数id为空")
      return
    }
    if(!param.chargeBalanceType){
      app.$message("请选择结算方式")
      return
    }
    
  
    //结算方式切换-选中公司月结，月结单位输入框才会显示，点击现结会消失
    //现结
    if(param.chargeBalanceType == "1"){
      param.companyId = ""
      param.companyName = ""
    }else if(param.chargeBalanceType === "0"){
    //月结-必须选择月结单位
      param.bargeId = form.bargeId
      if(this.data.selectedProjectsArray && this.data.selectedProjectsArray.length == 0){
        app.$message("请选择月结单位")
        return
      }
      // if(this.data.isMonthlyCheck && !param.monthlyCode) {
      //   app.$message("请输入月结码验证")
      //   return
      // }
      
    }
    console.log("param",param)

    app.$post(app.$url.consign.defray,param).then(res=>{
      if(res.code == 200){
        app.$message(res.msg)
        //月结
        if(param.chargeBalanceType === "0"){
          wx.setStorageSync("activeTab","2") //跳转待支付/待预约tab
          wx.navigateBack({
            delta: 1
          });
        }else{
          //暂时跳转预约界面
          //现结，跳到微信扫码页面;支付成功后说跳转到预约页面。如果不预约，点击返回，那就是回到托运单列表，状态为待预约
          wx.navigateTo({
            url: '/pages/consignment/reservation/reservation?currentConsignObj='+currentConsignObj,
            success: (result) => {
              
            },
            fail: () => {},
            complete: () => {}
          });
        }
        
      }else {
      app.$message(res.msg)
      }
    })

    
  },

    // jinn 2021-01-28 支付
    handlePay(){
      if(this.data.form.waterWayCargoId != null && this.data.form.waterWayCargoId != ""){
        // 保存当前托运单信息
        let currentConsignObj = JSON.stringify(this.data.currentConsignObj);
        wx.setStorageSync('payCurrentConsignObj', currentConsignObj);
        // 如果支付方式是现结 那么需要填写发票信息
        if(this.data.form.chargeBalanceType == 1) {
          wx.navigateTo({
            url: '/pages/fapiao/fapiao'
          })
          return;
        }
        app.$post(app.$url.pay.applyBargePay, this.data.form.waterWayCargoId).then(res => {
          if(res.code == 200){
            if(res.data.resmsg.reshead.procd == "0000"){
              let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):"";//获取用户信息
              if(userInfos != ""){
                wx.navigateTo({
                  url: '/pages/pay/pay?gatewayurl=' + res.data.resmsg.gatewayurl + "&orderid=" + res.data.resmsg.orderid + "&openid=" + userInfos.openId,
                })
              } else {
                wx.showToast({
                  title: "没有登录用户信息！",
                  icon: 'none',
                  duration: 2000
                })
              }
            } else {
              wx.showToast({
                title: res.data.resmsg.reshead.proinfo,
                icon: 'none',
                duration: 2000
              })
            }
          }
        })
      } else {
        //没有水路运单号，不能创建订单
        wx.showToast({
          title: '没有水路运单号',
          icon: 'none',
          duration: 2000
        })
      }
    },
    // jinn 2021-01-28 支付


  //打开搜索项目弹窗
  wxMonthChargeByNameInput(e){
    this.setData({
      showSearchDialog : !this.data.showSearchDialog,
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if(options.currentConsignObj != "{}"){
      let currentConsignObj = JSON.parse(options.currentConsignObj)
      this.setData({
        currentConsignObj,
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getDetailData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
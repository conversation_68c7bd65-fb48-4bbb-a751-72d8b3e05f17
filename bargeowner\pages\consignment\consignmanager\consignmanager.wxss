/* pages/consignment/consignmanager/consignmanager.wxss */
.consign-page{
  width: 100%;
  padding-top: 180rpx;
}
.consign-search{
  position: fixed;
  top: 0rpx;
  left: 0rpx;
  right: 0rpx;
  height: 270rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #ddd;
  background: #ffffff;
  z-index: 100;
}
.search-area{
  height: 70rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  display: flex;
  flex-direction: row;  
  align-items: center;
  justify-content: center;
  width: 710rpx;
  margin: 10rpx 0;
}
.barge-search-area{
  height: 70rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  display: flex;
  flex-direction: row;  
  align-items: center;
  justify-content: center;
  width: 710rpx;
  margin: 10rpx 0;
}
.search-area .search-btn {
  width: 215rpx;
  background: #00426B;
  color: #fff;
  font-size: 28rpx;
  padding: 0;
  margin-left: 20rpx;
  font-weight: normal;
}
.search-ipt{
  height: 70rpx;
  line-height: 70rpx;
  flex: 1;
  width: 100%;
  padding: 0 20rpx;
}
.search-icon{
  flex-shrink: 0;
}
.consign-tablist{
  height: 70rpx;
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: space-around;
  align-items: center;
}
.consign-tab{
  padding: 0 15rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 30rpx;
  color: #999999;
  position: relative;
}
.consign-tab.active-tab {
  color: #00426B;
  position: relative;
}
.consign-tab.active-tab::after{
  transform: translateX(-50%) scale(1);
}
.consign-tab::after{
  position: absolute;
  bottom: 0rpx;
  content: '';
  height: 4rpx;
  width: 100%;
  background: #00426B;
  left: 50%;
  transform: translateX(-50%) scale(0);
  transition: all 1s;
}
.consign-content{
  padding: 80rpx 20rpx 0;
}
.data-item{
  padding: 10rpx 0rpx;
  margin: 30rpx 0rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
  box-shadow: 0rpx -3rpx 3rpx 0rpx rgba(51,51,51,0.04);
}
.data-item-title{
  display: flex;
  height: 70rpx;
  padding: 0 20rpx;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;
}
.item-title-left{
  flex: 1;
  color: #333333;
  font-size: 28rpx;
}
.data-item-right{
  padding-left: 20rpx;
  color: #333333;
  font-size: 28rpx;
}
.data-content{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 0 20rpx;
}
.data-content-message{
  min-width: 50%;
  display: flex;
  flex-direction: row;
  height: 70rpx;
  font-size: 28rpx;
  align-items: center;
}
.message-title{
  color: #999999;
}
.message-value{
  color: #333333;
}
.data-operate{
  max-height: 100rpx;
  display: flex;
  flex-direction: row;
  border-top: 1px solid #ddd;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
}
.data-operate .oper-btn{
  height: 50rpx;
  width: 140rpx;
  border-radius: 30rpx;
  border: 1px solid #00426B;
  color: #00426B;
  font-size: 28rpx;
  padding: 0rpx;
  margin: 10rpx;
  line-height: 46rpx;
  font-weight: normal;
}
.confirm-dialog-wrap .confirm-dialog-content{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.confirm-dialog-wrap  .confirm-dialog-each{
  flex-shrink: 0;
  width: 100%;
  height: 90rpx;
  display: flex;
  align-items: center;
  font-size:30rpx;
  color:#333;
}
.confirm-dialog-wrap  .confirm-dialog-each1{
  flex-shrink: 0;
  width: 100%;
  height: 90rpx;
  font-size:28rpx;
  color:#333;
}

.confirm-dialog-input{
  border:1rpx solid #ddd;
  padding:0 10rpx;
  text-align:left;
}
.confirm-dialog-wrap  .confirm-dialog-left{
  flex-shrink: 0;
  width: 190rpx;
  height: 90rpx;
  line-height: 90rpx;
  padding-right: 8rpx;
}
.confirm-dialog-wrap  .confirm-dialog-right{
  flex:1;
  display: flex;
  align-items: center;
}
.iden-radio{
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: center;
  
}
.iden-radio-item{
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right:20rpx;
}
.iden-radio-text{
  color: #333;
  font-size: 30rpx;
  padding-left: 10rpx;
}
.audit-status-wrap{
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  border-top: 1px solid #ddd;
  padding: 0 20rpx;
}
.audit-statis-text{
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 30rpx;
  color:#333;
}
.charge-dialog-wrap{
  width: 100%;
  height: 100%;
}
.charge-dialog-wrap .weui-dialog__bd{
  display: flex;
  justify-content: center;
}
.charge-dialog-wrap .charge-textarea{
  width: 100%;
  height: 200rpx;
  border:1rpx solid #ddd;
  padding: 20rpx;
  color: #333;
  font-size: 30rpx;
  text-align: left;
}
.cancel-reser-dialog-wrap .cancel-reser-dialog-content{
  width: 100%;
  height: auto;
  padding: 0 10rpx;
  color: #333;
  font-size: 30rpx;
}
.holder-class{
  color:#ccc;
  font-size: 26rpx;
  font-weight: 500;
  line-height: 50rpx;
}
.close-icon{
  flex-shrink: 0;
  width: 40rpx;
  text-align: center;
  height: 70rpx;
  line-height: 70rpx;
  color: #ddd;
  margin-right: 10rpx;
}
.datesearch {
  border: none;
}
.data-item {
  border: 1px solid #ddd;
  margin-top: 30rpx;
  background: #ffffff;
  border-radius: 10rpx;
  padding: 10rpx 0rpx;
}
.data-list{
  padding: 10rpx 20rpx;
  margin-top: 170rpx;
  background: #f5f5f5;
}
.data-item-title{
  height: 80rpx;
  border-bottom: 1px solid #ddd;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx;
}
.detail-icon{
  flex-shrink: 0;
  padding: 0 5rpx;
}
.data-item-text{
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.data-item-message{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding-right:10rpx;
  width:100%;
}
.message-wrap{
  min-width: 50%;
  display: flex;
  flex-direction: row;
  height: 60rpx;
  padding:0 10rpx;
}
.message-wrap-title{
  color: #999;
  font-size: 28rpx;
}
.message-wrap-value{
  color: #00426B;
  font-size: 28rpx;
}
 .datesearch .publish-btn{
  width: 150rpx;
  border: none;
  border-radius: 10rpx;
  height: 60rpx;
  line-height: 60rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
  padding: 0rpx;
  background: #00426B;
  color: #fff;
  font-weight: normal;

}
.search-right{
  margin-left: 10rpx;
}
 .publish-btn.search-btn {
  width: 120rpx;
}
.search-data-box{
  position: relative;
  height: 70rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  flex-shrink: 0;
  flex: 1;
  display: flex;
  align-items: center;
}
.padding-text{
  padding: 0 5rpx;
  flex-shrink: 0;
}
.search-date-text{
  height: 60rpx;
  width: 85%;
  position: absolute;
  left: 0;
  top: 15rpx;
  padding: 0 20rpx;
  color: #808080;
}
.search-date-selected{
  height: 60rpx;
  width: 85%;
  position: absolute;
  left: 0;
  top: 15rpx;
  padding: 0 20rpx;
  color: #333;
}
.search-ipt-wrap{
  flex-shrink: 0;
  width: 100%;
  height: 70rpx;
  padding: 0 15rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
}
.close-icon{
  flex-shrink: 0;
  width: 40rpx;
  text-align: center;
  height: 70rpx;
  line-height: 70rpx;
  color: #ddd;
}
.close-iconp{
  flex-shrink: 0;
  position: absolute;
  right: 6rpx;
  top: 15rpx;
  width: 40rpx;
  height:auto;
  color: #ddd;
}
.notice-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.page-control {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-top: 1px solid #eee;
}

.page-number {
  color: #666;
}

.button-disabled {
  opacity: 0.5 !important;
  background: #999 !important;
}

/* 覆盖van-dialog的默认样式 */
.van-dialog__button--disabled {
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.safety-notice-dialog-content {
  padding: 16px;
}

.safety-notice-content {
  margin-bottom: 16px;
}

.page-control {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-number {
  font-size: 14px;
  color: #666;
}

.view-contain-ti {
  display: flex;
  height: 40px;
  margin: 20px;
  border: 3rpx solid #faca82;
  border-radius: 10rpx;
}

.text-ti {
  position: relative;
  font-size: 12px;
  background: white;
  margin: -10px 0 0 10px;
  padding: 0 5px;
  color: rgb(144, 147, 167);
}

.input1 {
  margin: auto 10px;
  width: 100%; /* 确保输入框填满容器 */
  font-size: 15px; /* 设置字体大小为16px */
}

.inline-container {
  display: flex;
  align-items: center;
}

.view-contain-ti {
  display: flex;
  height: 40px;
  margin: 10px;
  border: 3rpx solid #faca82;
  border-radius: 10rpx;
}

.view-contain-ti.small-input {
  width: 100px; /* 调整宽度以适应行内布局 */
}

.input1 {
  margin: auto 10px;
  width: 100%; /* 确保输入框填满容器 */
}

.statement-date,
.notice-date {
  margin-top: 20rpx;
  text-align: right;
  font-size: 28rpx;
  color: #333;
}

.signature-section {
  margin-top: 30rpx;
  padding: 20rpx;
  border-top: 1px solid #eee;
}

.input-item {
  display: flex;
  align-items: center;
  margin: 10rpx 0;
}

.input-item text {
  width: 200rpx;
  font-size: 28rpx;
  color: #333;
}

.input-item input {
  flex: 1;
  height: 60rpx;
  border: 1px solid #ddd;
  border-radius: 4rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.date {
  display: block;
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.centered-date {
  justify-content: center;
  text-align: center;
}

.right-aligned {
  text-align: center;
  justify-content: flex-end;
}

.underline {
  border-bottom: 1px solid #000;
}

.short-underline {
  width: 5ch;
  border-bottom: 1px solid #000;
}

.longer-underline {
  width: 6ch;
  border-bottom: 1px solid #000;
}

.date-text {
  text-align: right;
  display: block;
  margin-top: 10rpx;
}

/* 安全检查表样式 */
.safety-check-dialog-content {
  padding: 20rpx;
  height: 100%;
  overflow-y: auto;
  font-size: 24rpx;
}

.safety-check-header {
  margin-bottom: 20rpx;
}

.header-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

.input-field {
  border: 1px solid #ddd;
  padding: 4rpx 8rpx;
  margin: 0 10rpx;
  flex: 1;
  font-size: 24rpx;
}

.safety-check-table {
  width: 100%;
  border: 1px solid #ddd;
  margin: 20rpx 0;
}

.table-header {
  display: flex;
  background-color: #f8f8f8;
  border-bottom: 1px solid #ddd;
  font-weight: bold;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #eee;
}

.col-serial {
  width: 60rpx;
  padding: 10rpx;
  text-align: center;
  border-right: 1px solid #ddd;
  font-size: 24rpx;
}

.col-item {
  flex: 2;
  padding: 10rpx;
  border-right: 1px solid #ddd;
  font-size: 24rpx;
}

.col-ship, .col-port {
  width: 80rpx;
  padding: 10rpx;
  text-align: center;
  border-right: 1px solid #ddd;
  font-size: 24rpx;
}

.col-remarks {
  flex: 1;
  padding: 10rpx;
  font-size: 24rpx;
}

.extra-fields {
  margin-top: 10rpx;
}

.sub-item {
  margin: 10rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sub-item-label {
  font-size: 24rpx;
  padding: 10rpx;
  color: inherit;
  text-align: center;
}

.input-field {
  width: 100%;
  height: 48rpx;
  border: 1px solid #dcdfe6;
  border-radius: 4rpx;
  padding: 0 10rpx;
  font-size: 24rpx;
  color: inherit;
  background-color: #fff;
  text-align: center;
}

.input-field:focus {
  border-color: #409eff;
  outline: none;
}

.header-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

/* 水路运单文件列表弹窗样式 */
.waterway-file-dialog-content {
  padding: 20rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.waterway-file-list {
  width: 100%;
}

.waterway-file-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.waterway-file-item:hover {
  background-color: #f5f5f5;
}

.file-name {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.no-files {
  text-align: center;
  color: #999;
  padding: 40rpx 0;
  font-size: 28rpx;
}

.loading-confirm-content {
  padding: 20rpx;
}

.confirm-text {
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

.inline-input {
  display: inline-block;
  border-bottom: 1px solid #ddd;
  min-width: 120rpx;
  text-align: center;
  margin: 0 10rpx;
}

.signature-row {
  margin-top: 30rpx;
  display: flex;
  align-items: center;
}

.signature-input {
  flex: 1;
  border-bottom: 1px solid #ddd;
  margin-left: 20rpx;
}

.date-row {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.picker {
  padding: 10rpx 0;
  border-bottom: 1px solid #ddd;
  min-width: 200rpx;
  display: inline-block;
  text-align: center;
}

.date-label {
  margin-right: 10rpx;
  white-space: nowrap;
}

.custom-checkbox {
  width: 44rpx;
  height: 44rpx;
  border: 2rpx solid #999;
  border-radius: 4rpx;
  position: relative;
  display: inline-block;
}

/* 勾选状态 (Y) */
.custom-checkbox.Y::before {
  content: '√';
  color: #07c160;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32rpx;
}

/* 叉号状态 (N) */
.custom-checkbox.N::before {
  content: '×';
  color: #ff4d4f;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32rpx;
}

/* 横线状态 (-) */
.custom-checkbox[class*="dash"]::before {
  content: '-';
  color: #999999;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 50rpx;
  font-weight: bold;
}

/* 添加时间选择器的样式 */
.time-picker {
  display: inline-block;
  min-width: 180rpx;
  height: 60rpx;
  vertical-align: middle;
}

.picker-text {
  display: inline-block;
  border-bottom: 1px solid #ddd;
  padding: 0 20rpx;
  min-width: 180rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  color: #333;
  font-size: 28rpx;
}

.picker-text:empty::before {
  content: '请选择时间';
  color: #999;
}

/* 调整确认书内容的样式 */
.confirm-text {
  line-height: 60rpx;
}

.inline-input {
  height: 60rpx;
  line-height: 60rpx;
}

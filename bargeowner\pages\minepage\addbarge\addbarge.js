// pages/minepage/addbarge/addbarge.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //表单
    form:{
      bargeName:"",//驳船名称
      bargeId:"CN",//驳船标识
    }
  },

  /**
   * 方法
   */
  //驳船名称input
  bargeNameInput(e){
    this.setData({
      "form.bargeName":e.detail.value.trim()
    })
  },

  //驳船标识input
  bargeIdInput(e){
    let value = e.detail.value.trim()
    if(value.indexOf("CN") == -1){
      value = "CN"+value
    }
    this.setData({
      "form.bargeId": value
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  // 点击确定新增-目前这个接口只校验唯一性，并不是新增
  handleConfirm() {
    let param = {
      ...this.data.form
    }
    if(!param.bargeName){
      app.$message("请输入驳船名称")
      return
    }
    if(!param.bargeId){
      app.$message("请输入驳船标识")
      return
    }
    let bargeObj = param //传给跳转页面的值
    app.$post(app.$url.mine.addBarge,param).then(res=>{
      if(res.code == 200){
        app.$message(res.msg)
            
        //将校验成功的驳船名称和驳船识别号，传到备案页面，备案页面默认选中该新增驳船
        let pages = getCurrentPages();
        let currPage = pages[pages.length - 1];   //当前页面
        let prevPage = pages[pages.length - 3];  //上两个页面
       
        //获取选中船舶的值
        let {bargeName,bargeId} = bargeObj
        prevPage.setData({
          selectedBargeArr:[{bargeName,bargeId}] ,//选中的驳船
            // 驳船备案数据-除了新增返回的两个字段赋值，其他数据初始化
            "bargeParam.bargeId": bargeId, // 驳船识别号
            "bargeParam.bargeName": bargeName, // 驳船名称
            "bargeParam.id": "", // 驳船id主键
            "bargeParam.barge": '', // 已选驳船(驳船名称和驳船标识Id)
            "bargeParam.mmsi": '',  // MMSI标识
            "bargeParam.bargeLoadA": '',  // 船舶载货量（A级）
            "bargeParam.bargeLoadB": '',  // 船舶载货量（B级）
            "bargeParam.validSailDate": '',  //证书有效期
            "bargeParam.belongArea": '',  //船籍港
            "bargeParam.bargeOwner": '', //驳船所有人
            "bargeParam.contactPhone": '', //联系电话
            "bargeParam.bargeOperator": '', //驳船经营人
            "bargeParam.auditStatus": '', //租用状态
            "bargeParam.bargeType": '', //船支类型
            "bargeParam.bingingType": '', //租用方式
            "bargeParam.companyId": '', //租用公司id
            "bargeParam.companyName": '', //租用公司名称
            "bargeParam.flag" : false,
            
            isApplyBind: '', // 是否申请租用
            bargeTypeIndex: '', // 船支类型索引
            bingingTypeIndex: '', // 租用方式索引

            sealImg1: "",
            coverBargeImgArr: [],
            bargeMainItemImgArr: [],
            seaworthinessCertificateImgArr: [],
            automaticAisImgArr: [],
            powerOfAttorneyImgArr: [],
        })

        //跳到备案页面
        wx.navigateBack({
          delta: 2
        });

       
      }else {
      app.$message(res.msg)
      }
    })


    
   
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
package com.ruoyi.carrier.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.carrier.domain.CarrierUser;
import com.ruoyi.carrier.domain.bo.CarrierBO;
import com.ruoyi.carrier.service.CarrierInfoService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.BargeCheckMessage;
import com.ruoyi.common.domain.OlUser;
import com.ruoyi.common.enums.UserType;
import com.ruoyi.common.enums.WebCarrierRoleType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.BargeCheckMessageMapper;
import com.ruoyi.common.mapper.OlUserMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/7/24 15:31
 */
@Slf4j
@Service
public class CarrierInfoServiceImpl implements CarrierInfoService {

    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private OlUserMapper olUserMapper;
    @Autowired
    private BargeCheckMessageMapper bargeCheckMessageMapper;


    /**
     * 注册
     * @param carrierBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult webRegister(CarrierBO carrierBO) {
        QueryWrapper<OlUser> wrapper = new QueryWrapper<>();
        wrapper.eq("userid", carrierBO.getAccount());
        OlUser user = olUserMapper.selectOne(wrapper);

        String userType = WebCarrierRoleType.ADMIN.getCode()
                .equals(carrierBO.getRoleType())
                ?UserType.CARRIERADMIN.getCode()
                :UserType.CARRIEUSER.getCode();

        if (StringUtils.isNotNull(user)) {
            // 如果是货主管理员以外其他人 pb30_ol_user.roleid不为3的时候
            SysUser su = new SysUser();
            su.setGmUserId(user.getId());
            su.setUserType(userType);
            List<SysUser> gmSysUser = sysUserMapper.selectUser(su);
            if (user.getRoleId() != 3) {
                // 查询用户表
                if (gmSysUser.size() <= 0) {
                    List<OlUser> olUserList = olUserMapper.searchAdminByCompanyId(user.getCompanyId());
                    if(olUserList.size() == 0){
                        throw new CustomException("您所在的公司尚未有管理员注册或管理员未审核通过，请先让管理员注册");
                    }
                }
            }
            if (UserType.CARRIERADMIN.getCode().equals(userType)) {
                // 船公司校验账号唯一性
                /*SysUser su = new SysUser();
                su.setGmUserId(user.getId());
                su.setUserType(userType);
                List<SysUser> gmSysUser = sysUserMapper.selectUser(su);*/
                if (gmSysUser.size()>=1) {
                    throw new CustomException("该船公司已有管理员");
                }
            }
        }



        OlUser olUser = new OlUser();
        if (user == null) {
            // 插入ol_user表
            olUser.setUserId(carrierBO.getAccount());
            olUser.setPassword(carrierBO.getPassword());
            olUser.setCid(carrierBO.getCardId());
            olUser.setCustomerTypeId(5L);
            olUser.setContactPerson(carrierBO.getRealName());
            olUser.setContactCellPhone(carrierBO.getPhone());
            olUser.setRoleId(carrierBO.getRoleType());
            olUser.setRegDate(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
            olUser.setComId(carrierBO.getComId());
            olUser.setCompanyId(carrierBO.getCompanyId());
            olUser.setCompany(carrierBO.getCompanyName());
            olUser.setIsAudit("0");
            olUser.setComId("2");

            olUserMapper.insert(olUser);
        } else {
            olUser.setId(user.getId());
            String comId = user.getComId();
            if (StringUtils.isNotEmpty(comId)) {
                String[] str = comId.split(",");
                for (int i = 0; i < str.length; i++) {
                    if ("2".equals(str[i])) {
                        break;
                    }
                    if (i == (str.length - 1)) {
                        comId = comId + ",2";
                    }
                }
                olUser.setComId(comId);
            } else {
                olUser.setComId("2");
            }
            olUserMapper.updateById(olUser);
        }



        // 插入sysUser表
        SysUser sysUser = new SysUser();
        sysUser.setUserName(carrierBO.getAccount());
        sysUser.setNickName(carrierBO.getRealName());
        sysUser.setUserType(userType);
        sysUser.setEmail(carrierBO.getEmail());
        sysUser.setPhonenumber(carrierBO.getPhone());
        sysUser.setCompanyId(carrierBO.getCompanyId());
        sysUser.setCompanyName(carrierBO.getCompanyName());
        sysUser.setGmUserId(user==null?olUser.getId():user.getId());
        sysUserMapper.insertUser(sysUser);

        // 插入PB6_BARGE_CHECK_MESSAGE
        BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();
        bargeCheckMessage.setMType((short) 12);
        bargeCheckMessage.setApplyManId(sysUser.getUserId());
        bargeCheckMessage.setApplyMan(sysUser.getUserName());
        bargeCheckMessage.setApplyTime(new Date());
        bargeCheckMessage.setAuditFlag((short) 0);
        bargeCheckMessageMapper.insert(bargeCheckMessage);
        return AjaxResult.success("注册成功", sysUser.getUserId());
    }

    /**
     * 获取船公司普通用户列表
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public List<CarrierUser> getCarrierUserList() {
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userCompanyId = loginUser.getCompanyId();

        return sysUserMapper.getCarrierUserList(userCompanyId);
    }

    @Override
    public List<CarrierUser> getCarrierUsers(SysUser sysUser) {
        return sysUserMapper.getCarrierUsers(sysUser);
    }
}

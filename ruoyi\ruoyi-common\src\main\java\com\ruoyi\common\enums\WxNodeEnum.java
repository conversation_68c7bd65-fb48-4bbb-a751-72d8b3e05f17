package com.ruoyi.common.enums;

/**
 * 当前流程节点枚举
 *
 */
public enum WxNodeEnum {

    /**
     * 当前流程节点： 0提交审核，1物流公司直接审核，2码头审核， 3物流公司间接审核
     */
    SUBMIT_AUDIT(0,"提交审核"),
    DIRECT_AUDIT_BY_LOGISTICS_COMPANY(1, "物流公司直接审核"),
    DOCK_AUDIT(2, "码头审核"),
    LOGISTICS_COMPANY_INDIRECT_AUDIT(3, "物流公司间接审核");

    private final Integer code;

    private final String codeName;

    WxNodeEnum(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

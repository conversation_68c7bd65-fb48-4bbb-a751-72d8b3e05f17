package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.enums.UploadDataType;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/28 10:41
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
@TableName("upload_address")
@KeySequence(value = "seq_upload_address")
public class UploadAddressDomain implements Comparable<UploadAddressDomain>{

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 关联id
     */
    @TableField("link_id")
    private Long linkId;

    /**
     * 关联表名称
     */
    @TableField("link_table")
    private String linkTable;

    /**
     * 资料类型
     */
    @TableField("link_type")
    private String linkType;

    /**
     * 资料url
     */
    @TableField("url")
    private String url;

    /**
     * 上传时间
     */
    @TableField("upload_time")
    private Date uploadTime;

    /**
     * 上传用户id
     */
    @TableField("upload_user_id")
    private Long uploadUserId;

    /**
     * 公司id
     */
    @TableField("link_company_id")
    private Long linkCompanyId;

    /**
     * 资料状态（0-失效，1-生效）
     */
    @TableField("status")
    private Integer status;

    @TableField("upload_user_name")
    private String uploadUserName;

    @TableField("data_name")
    private String dataName;

    /**
     * 资料细分类型
     * 11 船舶检验证书簿封面
     * 12 船舶主要项目内容
     * 13 船舶适航证书
     * 14 船舶自动识别系统AIS证书
     * 15 驳船主身份证正面
     * 16 驳船主身份证反面
     * 17 电子签章授权委托书电子签章授权委托书
     * 41-出库单图片
     * 42-委托书图片
     */
    @TableField("data_type")
    private Integer dataType;


    /**
     *  是否改单备份文件标识，默认为0 （1-非备份，2-备份）
     */
    @TableField("BAK_FLAG")
    private Integer bakFlag;

    @TableField(exist = false)
    private String fileBase64;

    @Override
    public int compareTo(UploadAddressDomain uploadAddressDomain){
        int result = this.getUploadTime().compareTo(uploadAddressDomain.getUploadTime());
        return result;
    }
}

package com.ruoyi.barge.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.barge.domain.SysUserBargeBak;
import com.ruoyi.barge.domain.bo.BargeUserBO;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.barge.service.SysUserBargeBakService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.*;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.vo.BargeCompanyVO;
import com.ruoyi.common.domain.vo.BargeInfoVO;
import com.ruoyi.common.enums.*;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.*;
import com.ruoyi.common.service.BargeCompanyLinkService;
import com.ruoyi.common.service.BargeInfoAuditService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.databarge.domain.Pb6Bargework;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.domain.WechatMpUser;
import com.ruoyi.databarge.mapper.Pb6BargeworkMapper;
import com.ruoyi.databarge.mapper.ShipFddUserRelMapper;
import com.ruoyi.databarge.service.WechatMpUserService;
import com.ruoyi.databarge.wechat.MpMessageDTO;
import com.ruoyi.databarge.wechat.MpMessageResult;
import com.ruoyi.databarge.wechat.MpUtil;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.wechat.service.WechatMpAccessTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/29 22:09
 */
@Slf4j
@Service
public class BargeCenterServiceImpl implements BargeCenterService {

    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;
    @Autowired
    private BargeInfoMapper bargeInfoMapper;
    @Autowired
    private BargeInfoAuditService bargeInfoAuditService;
    @Autowired
    private BargeInfoAuditMapper bargeInfoAuditMapper;
    @Autowired
    private UploadAddressMapper uploadAddressMapper;
    @Autowired
    private BargeCompanyMapper bargeCompanyMapper;
    @Autowired
    private BargeCompanyLinkService bargeCompanyLinkService;
    @Autowired
    private BargeCheckMessageMapper bargeCheckMessageMapper;
    @Autowired
    private ShipFddUserRelMapper shipFddUserRelMapper;
    @Autowired
    private WaterwayCargoMapper waterwayCargoMapper;
    @Autowired
    private CargocmentdetailMapper cargocmentdetailMapper;
    @Autowired
    private WechatMpUserService wechatMpUserService;
    @Autowired
    private Pb6BargeworkMapper pb6BargeworkMapper;
    @Value("${mkdirPath}")
    private String mkdirPath;

    @Value("${dataLocal.barge}")
    private String dataLocal;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    private String gsDataLocal = "https://scbs.gzport.com";

    @Autowired
    private SysUserBargeBakService sysUserBargeBakService;

    @Autowired
    private WechatMpAccessTokenService wechatMpAccessTokenService;

    @Autowired
    private MpUtil mpUtil;

    /**
     * 个人备案
     * @param bargeUserBO
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult personRecord(BargeUserBO bargeUserBO) {

        SysUser sysUser = new SysUser();
        BeanUtils.copyBeanProp(sysUser, bargeUserBO);

        // 将个人备案信息更新到用户表
        int result = sysUserMapper.updateUser(sysUser);

        if (result <= 0) {
            log.error("个人备案失败");
            return AjaxResult.error("保存失败");
        }

        // 身份证正反面
        List<UploadAddress> dataList = bargeUserBO.getDataList();
        if (dataList != null) {
            dataList.forEach(data -> {
                // 删除旧的身份证资料
                QueryWrapper<UploadAddress> uploadAddressQueryWrapper = new QueryWrapper<>();
                uploadAddressQueryWrapper.eq("LINK_TYPE", UploadDataType.IDENTITY.getType())
                        .eq("LINK_ID", sysUser.getUserId())
                        .eq("STATUS", 0);
                uploadAddressMapper.delete(uploadAddressQueryWrapper);

                // 把新的改为旧的
                uploadAddressQueryWrapper = new QueryWrapper<>();
                uploadAddressQueryWrapper.eq("LINK_TYPE", UploadDataType.IDENTITY.getType())
                        .eq("LINK_ID", sysUser.getUserId())
                        .eq("STATUS", 1);
                UploadAddress ua = new UploadAddress();
                ua.setStatus(0);
                if (DataType.IDENTITY_POSITIVE.getCode().equals(data.getDataType())) {
                    uploadAddressQueryWrapper.eq("DATA_TYPE", DataType.IDENTITY_POSITIVE.getCode());
                    uploadAddressMapper.update(ua,uploadAddressQueryWrapper);
                } else if (DataType.IDENTITY_NEGATIVE.getCode().equals(data.getDataType())) {
                    uploadAddressQueryWrapper.eq("DATA_TYPE", DataType.IDENTITY_NEGATIVE.getCode());
                    uploadAddressMapper.update(ua,uploadAddressQueryWrapper);
                }

                // 插入新的
                UploadAddress upload = new UploadAddress();
                upload.setId(data.getId());
                upload.setLinkId(sysUser.getUserId());
                uploadAddressMapper.updateById(upload);
            });
        }

        // 身份证正面
        /*if (StringUtils.isNotBlank(bargeUserBO.getIdentityPositive())) {
            uploadIdentity(bargeUserBO.getIdentityPositive(), sysUser.getUserId(), 0);
        }
        if (StringUtils.isNotBlank(bargeUserBO.getIdentityNegative())) {
            uploadIdentity(bargeUserBO.getIdentityNegative(), sysUser.getUserId(), 1);
        }*/

        return AjaxResult.success("保存成功");
    }

    /**
     * 备案详情
     * @param bargeInfoBO
     * @return
     */
    @Override
    public AjaxResult recordDetail(BargeInfoBO bargeInfoBO) {
        Map<String, Object> map = new HashMap<>();

        BargeInfoVO bargeInfoVO = new BargeInfoVO();

        SysUser personRecord = sysUserMapper.selectUserById(bargeInfoBO.getUserId());
        // 获取身份证资料
        QueryWrapper<UploadAddress> personWrapper = new QueryWrapper<>();
        personWrapper.eq("LINK_ID", personRecord.getUserId())
                .eq("LINK_TYPE", UploadDataType.IDENTITY.getType())
                //.eq("UPLOAD_USER_ID", personRecord.getUserId())
                .eq("STATUS", 1)
                .eq("BAK_FLAG", 1)
                .ne("upload_user_id", 0);
        List<UploadAddress> personList = uploadAddressMapper.selectList(personWrapper);
        personList.forEach(item -> {
            String[] str = item.getUrl().split("/uploadFiles");
            if (DataType.IDENTITY_POSITIVE.getCode().equals(item.getDataType())) {
                // 暂时不用，将本地url返回给前端
                personRecord.setIdentityPositiveUrl(gsDataLocal + str[1]);
                // personRecord.setIdentityPositiveUrl(item.getUrl());
            } else if (DataType.IDENTITY_NEGATIVE.getCode().equals(item.getDataType())) {
                personRecord.setIdentityNegativeUrl(gsDataLocal + str[1]);
                // personRecord.setIdentityNegativeUrl(item.getUrl());
            } else if (DataType.CONSIGN_COMMISSION_IMG.getCode().equals(item.getDataType())) {
                // item.setUrl(dataLocal + str[1]);
                item.setUrl(item.getUrl());
            }
        });
        map.put("personRecordDetail",personRecord);
        AjaxResult result = this.checkUserBarge(bargeInfoBO.getUserId());
        boolean flag = (boolean) result.get("isRecord");
        BargeInfoAudit bargeInfoAudit = (BargeInfoAudit) result.get("barge");
        if (bargeInfoAudit != null) {
            // 判断是否审核通过，审核通过则获取驳船备案表里的驳船信息
            if (flag) {
                BargeInfo bargeInfo = bargeInfoMapper.selectById(bargeInfoAudit.getPb6bargeInfoId());
                BeanUtils.copyBeanProp(bargeInfoVO, bargeInfo);
            } else {
                BeanUtils.copyBeanProp(bargeInfoVO, bargeInfoAudit);
                bargeInfoVO.setId(bargeInfoAudit.getPb6bargeInfoId());
            }

            // 获取驳船挂靠公司
            BargeCompanyVO bargeCompanyVO = bargeCompanyMapper.queryCompanyByBargeId(bargeInfoAudit.getPb6bargeInfoId(), bargeInfoBO.getUserId());
            if (bargeCompanyVO != null) {
                bargeInfoVO.setCompanyId(bargeCompanyVO.getCompanyId());
                bargeInfoVO.setCompanyName(bargeCompanyVO.getCFullName());
                bargeInfoVO.setAuditStatus(bargeCompanyVO.getAuditStatus());
                bargeInfoVO.setBindingType(bargeCompanyVO.getBindingType());
                bargeInfoVO.setIsAudit(bargeCompanyVO.getIsAudit());
            }

            // 获取资料
            QueryWrapper<UploadAddress> wrapper = new QueryWrapper<>();
            wrapper.eq("LINK_ID", bargeInfoAudit.getPb6bargeInfoId())
                    .eq("LINK_TYPE", UploadDataType.BARGE_RECORD_DATA.getType())
                    //.eq("UPLOAD_USER_ID", personRecord.getUserId())
                    .eq("STATUS", 1)
                    .eq("BAK_FLAG", 1);
            List<UploadAddress> list = uploadAddressMapper.selectList(wrapper);

            list.addAll(personList.stream()
                    .filter(item->DataType.CONSIGN_COMMISSION_IMG.getCode().equals(item.getDataType()))
                    .collect(Collectors.toList()));
            list.forEach(item -> {
                String url = item.getUrl();
                String[] u;
                if (url.contains("/uploadFiles")) {
                    u = url.split("/uploadFiles");
                } else {
                    u = url.split("/olorder");
                }
                item.setUrl(gsDataLocal + u[1]);

            });
            bargeInfoVO.setDataList(list);

            // 获取电子签章
            QueryWrapper<ShipFddUserRel> fddRel = new QueryWrapper<>();
            fddRel.eq("type", FddUserType.BARGE_USER.getCode())
                    .eq("SHIP_ID", bargeInfoAudit.getPb6bargeInfoId())
                    .eq("SHIP_USER_ID", bargeInfoBO.getUserId());
            ShipFddUserRel userRel = shipFddUserRelMapper.selectOne(fddRel);
            bargeInfoVO.setFdd(userRel);

        }

        map.put("bargeRecordDetail",bargeInfoVO);
        return AjaxResult.success("获取备案详情成功",map);
    }

    /**
     * 切换身份
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult switchIdentity() {
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userId = loginUser.getUserId();

        // 只有驳船业务人员可以切换为管理员，管理员不能切换身份
        // 判断是否可以切换身份
        if (!UserType.BARGEUSER.getCode().equals(loginUser.getUserType())) {
            return AjaxResult.error("不能切换身份");
        }

        SysUser user = new SysUser();
        user.setUserId(userId);
        user.setUserType(UserType.BARGEADMIN.getCode());

        sysUserMapper.updateUser(user);

        return AjaxResult.success("切换身份成功");
    }

    /**
     * 获取驳船列表
     * @param bargeInfoBO
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public AjaxResult getBargeList(BargeInfoBO bargeInfoBO) {
        List<BargeInfoVO> bargeInfoVOS = bargeInfoMapper.getBargeList(bargeInfoBO);
        bargeInfoVOS.forEach(bargeInfoVO -> {
            // 获取资料
            QueryWrapper<UploadAddress> wrapper = new QueryWrapper<>();
            wrapper.eq("LINK_ID", bargeInfoVO.getId())
                    .eq("LINK_TYPE", UploadDataType.BARGE_RECORD_DATA.getType())
                    //.eq("UPLOAD_USER_ID", personRecord.getUserId())
                    .eq("STATUS", 1)
                    .eq("BAK_FLAG", 1);
            List<UploadAddress> list = uploadAddressMapper.selectList(wrapper);
            list.forEach(item -> {
                String url = item.getUrl();
                String[] u;
                if (url.contains("/uploadFiles")) {
                    u = url.split("/uploadFiles");
                } else {
                    u = url.split("/olorder");
                }
                if (u.length>1) {
                    item.setUrl(gsDataLocal + u[1]);
                }
            });
            bargeInfoVO.setDataList(list);
        });
        return AjaxResult.success(bargeInfoVOS);
    }

    /**
     * 新增驳船
     * @param bargeInfoBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult addBarge(BargeInfoBO bargeInfoBO) {

        // 唯一性校验
        AjaxResult result = this.checkOnlyBarge(bargeInfoBO);
        if (500 == (int) result.get("code")) {
            return result;
        }
        //多次备案校验
        AjaxResult result1 = this.addBargeCheck(bargeInfoBO);
        if (500 == (int) result1.get("code")) {
            return result1;
        }

        log.info("新增驳船成功");

        Map<String, String> map = new HashMap<>();
        map.put("bargeId", bargeInfoBO.getBargeId());
        map.put("bargeName", bargeInfoBO.getBargeName());

        return AjaxResult.success("新增驳船成功", map);
    }

    private AjaxResult addBargeCheck(BargeInfoBO bargeInfoBO) {
            Long companyId = SecurityUtils.getLoginUser().getUser().getCompanyId();
            List<Long> userList = sysUserMapper.selectList(new QueryWrapper<SysUser>()
                            .eq("COMPANY_ID", companyId)
                            .in("USER_TYPE", UserType.CONSIGNOR.getCode(),
                                    UserType.CARRIERADMIN.getCode(),
                                    UserType.CARRIEUSER.getCode()))
                    .stream()
                    .map(SysUser::getUserId)
                    .collect(Collectors.toList());
            QueryWrapper<BargeInfoAudit> wrapper = new QueryWrapper<>();
            wrapper.eq("BARGEID", bargeInfoBO.getBargeId())
                    .and(i -> i.eq("RECORDCHECK", 0).or().eq("UPDATECHECK", 0));
            if(userList.size() != 0){
                wrapper.in("RECORDERID", userList);
            }
            List<BargeInfoAudit> list = bargeInfoAuditMapper.selectList(wrapper);

            if (list != null && list.size() > 0) {
                return AjaxResult.error("该驳船您已新增备案");
            }

        return AjaxResult.success();
    }

    // 一个人已经备案的情况下，多次备案校验
    private AjaxResult checkDoubleRecord(BargeInfoBO bargeInfoBO) {
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userId = loginUser.getUserId();
        // 判断该驳船是否被绑定
        SysUserBarge su = sysUserBargeMapper.queryUserBargeByUserId(userId);



        if (su != null) {
            BargeInfo bargeInfo = bargeInfoMapper.selectById(su.getBargeId());
            // 不等于空，判断是否是同一个驳船
            if (!bargeInfo.getBargeId().equals(bargeInfoBO.getBargeId())) {
                log.error("你已备案过驳船" + bargeInfo.getBargeName() + "，请勿重复备案");
                return AjaxResult.error("你已备案过驳船"+ bargeInfo.getBargeName() +"，请勿重复备案");
            }
        }
        // 判断该驳船是否被备案
        SysUserBargeBak sysUserBargeBak = sysUserBargeBakService.getOne(new LambdaQueryWrapper<SysUserBargeBak>().eq(SysUserBargeBak::getUserId,userId));
        if (sysUserBargeBak != null) {
            // 查询bargeInfoAudit表中pb6bargeinfoid等于sysUserBargeBak中的bargeId的数据
            BargeInfoAudit bargeInfoAudit = bargeInfoAuditMapper.selectOne(new LambdaQueryWrapper<BargeInfoAudit>().eq(BargeInfoAudit::getPb6bargeInfoId,sysUserBargeBak.getBargeId()));
            if (bargeInfoAudit != null) {
                log.error("你已备案过驳船" + bargeInfoAudit.getBargeName() + "，请勿重复备案");
                return AjaxResult.error("你已备案过驳船"+ bargeInfoAudit.getBargeName() +"，请勿重复备案");
            }
        }
        return AjaxResult.success();
    }

    // 驳船备案，给系统业务员发送模板消息
    public void sendBargeRecordMsg(BargeInfoBO bargeInfoBO) {
        // 获取系统业务员
        // 先查所有角色id为41，即驳船备案审核员的用户
        List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectUserRoleByRoleId(41L);
        // 获取用户id,对于每个用户id，查询用户手机号为该用户手机号，并且USER_TYPE为15的用户
        for(SysUserRole sysUserRole : sysUserRoles){
            SysUser sysUser = sysUserMapper.selectById(sysUserRole.getUserId());
            List<SysUser> sysUsers = sysUserMapper.selectUserByPhoneNumber(sysUser.getPhonenumber());
            // 发送模板消息
            for(SysUser user : sysUsers){
                // 发送模板消息
                // 3、发送微信公众号消息
                Map<String, Object> params = new HashMap<>();
                // 备案船舶 穗港2001 备案手机号 13123456789 备案时间 2024年9月12日 14:51
                params.put("thing1", bargeInfoBO.getBargeName());
                params.put("phone_number2", bargeInfoBO.getContactPhone());
                // 作废时间
                Date date = new Date();
                String time = DateUtils.parseDateToStr("yyyy年MM月dd日 HH:mm", date);
                params.put("time3", time);

                // 查询前，先根据openId去查询用户信息
                wechatMpUserService.getUserInfo(user.getUnionId());

                // 2、发公众号消息
                List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", user.getUnionId()));
                if (wechatMpUserList.size() == 0) {
                } else if (wechatMpUserList.size() == 1) {
                    String accessToken = wechatMpAccessTokenService.getAccessToken();
                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                            .template_id("J0uUv-s7w7vIXD2b4VInL6o0QojdfqPnga6kUqvnr70")
                            .data("thing1", MpMessageDTO.MpMessageDataField.builder().value(params.get("thing1").toString()).color("#000000").build())
                            .data("phone_number2", MpMessageDTO.MpMessageDataField.builder().value(params.get("phone_number2").toString()).color("#000000").build())
                            .data("time3", MpMessageDTO.MpMessageDataField.builder().value(params.get("time3").toString()).color("#000000").build())
                            .build();
                    System.out.println("W H Y");
                    System.out.println(mpMessageDTO);
                    MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);
                    System.out.println("收到新船舶备案完成通知");
                    System.out.println(mpMessageResult);
                }
            }
        }
    }


    /**
     * 驳船备案
     * @param bargeInfoBO 驳船备案基本信息
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult bargeRecord(BargeInfoBO bargeInfoBO) {

        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userId = loginUser.getUserId();
        Long finalBargeInfoId = bargeInfoBO.getId();

        // 是否关注船务公司公众号
        String unionId = loginUser.getUnionId();

        // 查询前，先根据openId去查询用户信息
        wechatMpUserService.getUserInfo(unionId);


        List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", unionId));
        if (wechatMpUserList.size() == 0) {
            return AjaxResult.error("请先关注广州港船务有限公司公众号，谢谢！");
        }

        // 1.判断该驳船是否被绑定
        if (bargeInfoBO.getId() != null) {
            SysUser su = sysUserBargeMapper.queryBargeUserByBargeId(userId, bargeInfoBO.getId());
            if (su != null) {
                log.error("该驳船已被绑定，请重新选择");
                return AjaxResult.error("该驳船已被绑定，请重新选择");
            }
            SysUserBargeBak sysUserBargeBak = sysUserBargeBakService.getOne(new LambdaQueryWrapper<SysUserBargeBak>().eq(SysUserBargeBak::getBargeId,bargeInfoBO.getId()));
            if(sysUserBargeBak != null){
                log.error("该驳船正在被通过审核，请重新选择");
                return AjaxResult.error("该驳船正在被通过审核，请重新选择");
            }
        }

        // 唯一性校验
        AjaxResult result = this.checkOnlyBarge(bargeInfoBO);
        if (500 == (int) result.get("code")) {
            return result;
        }

        //多次备案校验
        AjaxResult result1 = this.addBargeCheck(bargeInfoBO);
        if (500 == (int) result1.get("code")) {
            return result1;
        }

        // 已经备案的情况下，多次备案校验
        AjaxResult result2 = this.checkDoubleRecord(bargeInfoBO);
        if (500 == (int) result2.get("code")) {
            return result2;
        }

        // 判断驳船净吨、最小散货密度，最大散货密度 是否为数字，如果不是则返回错误
        // 判断有效期是否大于当前时间
        AjaxResult ajaxResult = this.checkBargeInfoBo(bargeInfoBO);
        if (500 == (int) ajaxResult.get("code")) {
            return ajaxResult;
        }


        // 2.驳船备案信息补充到驳船表
        BargeInfoAudit bargeInfoAudit = new BargeInfoAudit();

        if (bargeInfoBO.getId() != null) {
            BargeInfo barge = bargeInfoMapper.selectById(bargeInfoBO.getId());
            BeanUtils.copyBeanProp(bargeInfoAudit, barge);
        }

        BeanUtils.copyBeanProp(bargeInfoAudit, bargeInfoBO);
        bargeInfoAudit.setId(null);
        bargeInfoAudit.setPb6bargeInfoId(bargeInfoBO.getId());
        bargeInfoAudit.setRecorderId(userId);
        bargeInfoAudit.setMmsi(bargeInfoBO.getMmsi());
        if (bargeInfoBO.getId() == null) {
            // 新增驳船
            bargeInfoAudit.setCheckFlag(CheckFlagEnum.BARGE_AUDIT.getCode());
            bargeInfoAudit.setRecordCheck(CheckEnum.WAIT_CHECK.getCode());
        } else {
            // 判断是否存在改驳船提交记录，有
            QueryWrapper<BargeInfoAudit> bia = new QueryWrapper<>();
            bia.eq("RECORDERID", userId)
                    .eq("PB6BARGEINFOID", bargeInfoBO.getId())
                    .eq("CHECKFLAG", CheckFlagEnum.UPDATE_BARGE_AUDIT.getCode())
                    .eq("UPDATECHECK", CheckEnum.WAIT_CHECK.getCode())
                    .eq("ISDELETE", 1);
            bargeInfoAuditMapper.delete(bia);
            // 修改驳船
            bargeInfoAudit.setCheckFlag(CheckFlagEnum.UPDATE_BARGE_AUDIT.getCode());
            bargeInfoAudit.setUpdateCheck(CheckEnum.WAIT_CHECK.getCode());
        }
        bargeInfoAudit.setIsDelete(1);
        bargeInfoAudit.setBargeType(bargeInfoBO.getBargeType());

        int bargeInfoRows = bargeInfoAuditService.addCarrierBarge(bargeInfoAudit);

        if (bargeInfoRows <= 0) {
            log.info("BargeCenterService - bargeRecord - 驳船备案 - 提交审核失败");
            throw new CustomException("提交审核失败");
        }

        /*QueryWrapper<SysUserBarge> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id",userId);
        sysUserBargeMapper.delete(wrapper);*/

        // 插入用户驳船中间表
        /*SysUserBarge sysUserBarge = new SysUserBarge();
        sysUserBarge.setUserId(userId);
        sysUserBarge.setBargeId(bargeInfoAudit.getPb6bargeInfoId());
        sysUserBarge.setBargeAuditId(bargeInfoAudit.getId());
        int userBargeRows = sysUserBargeMapper.insert(sysUserBarge);*/

        SysUserBargeBak sysUserBargeBak = new SysUserBargeBak();
        sysUserBargeBak.setUserId(userId);
        sysUserBargeBak.setBargeId(bargeInfoAudit.getPb6bargeInfoId());
        sysUserBargeBak.setBargeAuditId(bargeInfoAudit.getId());
        boolean userBargeRows = sysUserBargeBakService.save(sysUserBargeBak);

        if (!userBargeRows) {
            log.info("BargeCenterService - bargeRecord - 驳船备案 - 提交审核失败");
            throw new CustomException("提交审核失败，绑定驳船失败");
        }


        // 删除旧资料
        List<UploadAddress> delList = bargeInfoBO.getDelDataList();
        System.out.println(delList.size());
        if (delList != null && delList.size() > 0) {
            // 清除以前旧的
            QueryWrapper<UploadAddress> uaWrapper = new QueryWrapper<>();
            uaWrapper.eq("status", "0")
                    .eq("link_id", bargeInfoAudit.getPb6bargeInfoId());
            uploadAddressMapper.delete(uaWrapper);

            // 清除以前旧的用户关联文件
            uaWrapper = new QueryWrapper<>();
            uaWrapper.eq("status", "0")
                    .eq("link_type", UploadDataType.IDENTITY.getType())
                    .eq("link_id", userId);
            uploadAddressMapper.delete(uaWrapper);
            //删除原来的委托书对应
            //uploadAddressMapper.delete(new LambdaQueryWrapper<UploadAddress>().eq(UploadAddress::getLinkId,userId).eq(UploadAddress::getDataType,53));
            List<UploadAddress> uaList = uploadAddressMapper.selectList(uaWrapper);
            if (StringUtils.isNotNull(uaList) && uaList.size() > 0) {
                List<Long> list = uaList.stream().map(UploadAddress::getId).collect(Collectors.toList());
                uploadAddressMapper.deleteBatchIds(list);
            }
            delList.stream().filter(item -> StringUtils.isNotNull(item) && StringUtils.isNotNull(item.getId()))
                    .forEach(item -> {
                log.info("把以前的图片资料改为旧的，UploadAddress：{}", item);
                UploadAddress address = uploadAddressMapper.selectById(item.getId());

                UploadAddress ua = new UploadAddress();
                ua.setStatus(0);
                uploadAddressMapper.update(ua, new UpdateWrapper<UploadAddress>()
                        .eq("data_type", address.getDataType())
                        .eq("status", "1")
                        .in("link_id", userId, bargeInfoAudit.getPb6bargeInfoId()));
            });
        }
        // 删除该驳船其他资料
        /*QueryWrapper<UploadAddress> uploadAddressQueryWrapper = new QueryWrapper<>();
        uploadAddressQueryWrapper.eq("LINK_TYPE", UploadDataType.BARGE_RECORD_DATA.getType())
                .eq("LINK_ID", bargeInfoAudit.getPb6bargeInfoId())
                .eq("BAK_FLAG", 1);
        UploadAddress ua = new UploadAddress();
        ua.setStatus(0);
        uploadAddressMapper.update(ua,uploadAddressQueryWrapper);*/

        // 资料
        List<UploadAddress> dataList = bargeInfoBO.getDataList();
        if (dataList != null) {
            dataList.forEach(data -> {
                UploadAddress upload = new UploadAddress();
                upload.setId(data.getId());
                if (UploadDataType.IDENTITY.getType().equals(data.getLinkType())) {
                    // 用户关联资料
                    upload.setLinkId(userId);
                } else {
                    upload.setLinkId(bargeInfoAudit.getPb6bargeInfoId());
                }
                uploadAddressMapper.updateById(upload);
            });
            uploadAddressMapper.delete(new LambdaQueryWrapper<UploadAddress>().eq(UploadAddress::getLinkId,userId).eq(UploadAddress::getLinkType,53));
            for (UploadAddress uploadAddress : dataList){
                if(uploadAddress.getLinkType().equals(53)){
                    uploadAddressMapper.insert(uploadAddress);
                }
            }
        }

        // 上传印章
        Long shipFddUserUrlId = null;
        String sealImg = bargeInfoBO.getSealImg();
        if (StringUtils.isNotBlank(sealImg)) {
            //String sealUrl = ftpService.selaUpload(sealImg);
            String sealUrl = bargeInfoBO.getSealPath();

            ShipFddUserRel shipFddUserRel = new ShipFddUserRel();
            shipFddUserRel.setType(FddUserType.BARGE_USER.getCode());
            shipFddUserRel.setShipUserId(userId);
            //shipFddUserRel.setShipId(bargeInfoAudit.getPb6bargeInfoId());

            // 查询原有的数据
            QueryWrapper<ShipFddUserRel> qw = new QueryWrapper<>(shipFddUserRel);
            ShipFddUserRel rel = shipFddUserRelMapper.selectOne(qw);
            if (rel == null) {
                shipFddUserRel.setSealUrl(sealUrl);
                shipFddUserRel.setReviewStatus("0");
                //shipFddUserRel.setShipUserId(userId);
                shipFddUserRel.setShipId(bargeInfoAudit.getPb6bargeInfoId());
                shipFddUserRelMapper.insert(shipFddUserRel);
                shipFddUserUrlId = shipFddUserRel.getId();
            } else {
                // 没有accountId则修改数据
                rel.setSealUrl(sealUrl);
                rel.setReviewStatus("0");
                //shipFddUserRel.setShipUserId(userId);
                rel.setShipId(bargeInfoAudit.getPb6bargeInfoId());
                shipFddUserRelMapper.updateById(rel);
                shipFddUserUrlId = rel.getId();
            }
        }

        BargeCompanyVO bargeCompanyVO = bargeCompanyMapper.queryCompanyByBargeId(bargeInfoAudit.getPb6bargeInfoId(), userId);
        // 删除挂靠
        if (StringUtils.isNotNull(bargeCompanyVO)) {
            BargeCompany bargeCompany = new BargeCompany();
            bargeCompany.setId(bargeCompanyVO.getId());
            bargeCompany.setStatus((short) 0);
            bargeCompany.setIsDelete((short) 0);
            bargeCompanyMapper.updateById(bargeCompany);
        }
        // 是否申请挂靠
        Integer isAudit = bargeInfoBO.getIsAudit();
        Long bargeCompanyId = null;

        short mType = BargeCheckMessageType.BARGE_FILING_AUDIT.getCode().shortValue();
        if (StringUtils.isNotNull(finalBargeInfoId)) {
            // 驳船备案修改
            mType = BargeCheckMessageType.BARGE_INFORMATION_MODIFICATION_REVIEW.getCode().shortValue();
        }
        if (StringUtils.isNull(finalBargeInfoId) && isAudit == 1) {
            // 备案加挂靠
            log.info("新增挂靠");
            bargeCompanyId = this.anchor(bargeInfoAudit, bargeInfoBO, loginUser, null, 1);
            mType = BargeCheckMessageType.BARGE_WAS_REGISTERED_AND_CHECK.getCode().shortValue();
        } else if (StringUtils.isNotNull(finalBargeInfoId) && isAudit == 1) {
            bargeCompanyId = this.anchor(bargeInfoAudit, bargeInfoBO, loginUser, null, 1);
            this.bargeCheckMsg(BargeCheckMessageType.ADD_BARGE_ADDITIONAL.getCode().shortValue(), loginUser, bargeInfoAudit, bargeCompanyId, shipFddUserUrlId);
        }

        // 插入表PB6_BARGE_CHECK_MESSAGE
        this.bargeCheckMsg(mType, loginUser, bargeInfoAudit, bargeCompanyId, shipFddUserUrlId);
        log.info("BargeCenterService - bargeRecord - 驳船备案 - 提交审核成功");

        // 驳船备案，给系统业务员发送模板消息
        this.sendBargeRecordMsg(bargeInfoBO);

        return AjaxResult.success("提交审核成功");
    }

    /**
     * @param
     * @return
     * @description 判断驳船信息字段是否符合要求，净吨、最小散货密度、最大散货密度是否是数字
     * <AUTHOR>
     * @date 2024/4/17 8:58
     */
    public AjaxResult checkBargeInfoBo(BargeInfoBO bargeInfoBO){
        // 判断驳船净吨是否为数字，如果不是则返回错误
        if (!NumberUtils.isCreatable(bargeInfoBO.getBargeWeight())) {
            log.error("驳船净吨必须为数字");
            return AjaxResult.error("驳船净吨必须为数字");
        }
        if(bargeInfoBO.getLoadingWeightMin() !=null && !"".equals(bargeInfoBO.getLoadingWeightMin()) && !NumberUtils.isCreatable(bargeInfoBO.getLoadingWeightMin())){
            log.error("最小散货密度（容量）必须为数字");
            return AjaxResult.error("最小散货密度（容量）必须为数字");
        }
        if(bargeInfoBO.getLoadingWeightMax() !=null && !"".equals(bargeInfoBO.getLoadingWeightMin()) && !NumberUtils.isCreatable(bargeInfoBO.getLoadingWeightMax())){
            log.error("最大散货密度（容量）必须为数字");
            return AjaxResult.error("最大散货密度（容量）必须为数字");
        }
        // 判断有效期是否大于当前时间
        if (DateUtils.getNowDate().after(DateUtils.parseDate(bargeInfoBO.getValidSailDate()))) {
            log.error("有效期必须大于当前时间");
            return AjaxResult.error("有效期必须大于当前时间");
        }
        return AjaxResult.success();
    }


    /**
     * 是否备案成功
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public AjaxResult checkRecordIsSuccess() {
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userId = loginUser.getUserId();

        return this.checkUserBarge(userId);
    }

    /**
     * 检查是否备案
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public BargeInfoVO checkRecord(Long userId) {
        BargeInfoVO bargeVO = sysUserBargeMapper.queryBargeInfoByUserId(userId);

        return bargeVO;
    }

    /**
     * 获取船员列表
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public AjaxResult getCrewList(BargeUserBO bargeUserBO) {
        if (null == bargeUserBO.getBargeId()) {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            bargeUserBO.setUserId(user.getUserId());
        }
        List<SysUser> crewList = sysUserMapper.getCrewList(bargeUserBO);
        return AjaxResult.success(crewList);
    }


    /**
     * 添加船员
     * @param bargeUserBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult addCrew(BargeUserBO bargeUserBO) {

        Long bargeUserId = bargeUserBO.getUserId();
        if (bargeUserId == null) {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            bargeUserId = user.getUserId();
        }


        // 查找驳船管理员用户信息
        BargeInfoVO bargeVO = sysUserBargeMapper.queryBargeInfoByUserId(bargeUserId);

        // 判断该用户身份
        SysUser crewUser = sysUserBargeMapper.queryUserByPhone(bargeUserBO.getPhonenumber());

        // 没有该用户则注册，有则验证身份
        if (crewUser == null) {

            SysUser sysUser = new SysUser();
            BeanUtils.copyBeanProp(sysUser, bargeUserBO);

            sysUser.setUserId(null);
            sysUser.setUserName(bargeUserBO.getPhonenumber());
            sysUser.setStatus(UserStatus.OK.getCode());
            sysUser.setUserType(UserType.BARGEUSER.getCode());
            sysUser.setDelFlag(UserStatus.OK.getCode());
            sysUser.setCreateBy(bargeVO.getUserName());
            sysUser.setCreateTime(new Date());
            sysUser.setUpdateBy(bargeVO.getUserName());
            sysUser.setUpdateTime(new Date());

            // 插入用户表
            sysUserMapper.insertUser(sysUser);
            crewUser = new SysUser();
            crewUser.setUserId(sysUser.getUserId());
        } else {
            // 查找该船员是否已绑定该驳船
            QueryWrapper<SysUserBarge> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", crewUser.getUserId());
            SysUserBarge userBarge = sysUserBargeMapper.selectOne(queryWrapper);
            if (userBarge != null) {
                // 提示该用户已经绑定xx驳船
                BargeInfo bargeInfo = bargeInfoMapper.selectById(userBarge.getBargeId());
                return AjaxResult.error("该用户已绑定驳船：" + bargeInfo.getBargeName());
            }

            // 更新用户信息
            SysUser sysUser = new SysUser();
            sysUser.setUserId(crewUser.getUserId());
            sysUser.setNickName(bargeUserBO.getNickName());
            sysUserMapper.updateUser(sysUser);
        }

        SysUserBarge sysUserBarge = new SysUserBarge();
        sysUserBarge.setUserId(crewUser.getUserId());
        sysUserBarge.setBargeId(bargeVO.getId());

        int result = sysUserBargeMapper.insert(sysUserBarge);
        log.info(String.valueOf(result));

        Map<String, Long> map = new HashMap<>();
        map.put("userId",crewUser.getUserId());
        map.put("bargeId",bargeVO.getId());

        return AjaxResult.success("添加成功",map);
    }

    /**
     * 删除船员
     * @param bargeUserBO
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public AjaxResult delCrew(BargeUserBO bargeUserBO) {

        List<SysUserBarge> list = bargeUserBO.getUserBargeList();

        // 删除船员与驳船之间的关系
        AtomicInteger result = new AtomicInteger();
        list.forEach(ub -> {
            QueryWrapper<SysUserBarge> wrapper = new QueryWrapper<>();
            wrapper.eq("USER_ID", ub.getUserId())
                    .eq("BARGE_ID", ub.getBargeId());
            result.addAndGet(sysUserBargeMapper.delete(wrapper));
        });

        return result.get() >0?AjaxResult.success("删除成功"):AjaxResult.error("删除失败");
    }

    /**
     * 获取水路运单
     * @param waterwayCargoId 水路运单编号
     * @return
     */
    @Override
    public AjaxResult getLoading(String waterwayCargoId) {
        QueryWrapper<WaterwayCargo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("waterwayCargoId", waterwayCargoId);
        return AjaxResult.success(waterwayCargoMapper.selectOne(queryWrapper));
    }

    /**
     * 确认实装数
     * @param waterwayCargo 确认是否实装： Y 确认实装， N是没有确认实装
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult confirmLoadingOver(WaterwayCargo waterwayCargo) {
        // 修改水路运单表驳船状态
        WaterwayCargo cargo = new WaterwayCargo();

        // 2021.08.24 jinn 洪工要求加
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        if(sysUser != null){
            cargo.setCONFIRMBARGEEID(sysUser.getUserId());
        }
        cargo.setConfirmtime(DateUtils.getTime());
        // 2021.08.24 jinn 洪工要求加

        cargo.setConfirmloadingover(waterwayCargo.getConfirmloadingover());
        cargo.setFLAGBARGESTATE("2");
        UpdateWrapper<WaterwayCargo> cargoUpdateWrapper = new UpdateWrapper<>();
        cargoUpdateWrapper.eq("WATERWAYCARGOID", waterwayCargo.getWaterwayCargoId());
        int result = waterwayCargoMapper.update(cargo, cargoUpdateWrapper);

        // 修改托运单明细表驳船状态
        Cargoconsignmentdetail detail = new Cargoconsignmentdetail();
        detail.setFlagBargeState(FlagBargeState.LEAVING.getCode());
        UpdateWrapper<Cargoconsignmentdetail> detailUpdateWrapper = new UpdateWrapper<>();
        detailUpdateWrapper.eq("waterwaycargoid", waterwayCargo.getWaterwayCargoId());
        int detailResult = cargocmentdetailMapper.update(detail, detailUpdateWrapper);

        // 修改pb6_bargework表leaveporttime离港时间
        Pb6Bargework bargework = new Pb6Bargework();
        bargework.setLeaveporttime(DateUtil.formatDateTime(new DateTime()));
        UpdateWrapper<Pb6Bargework> bargeworkUpdateWrapper = new UpdateWrapper<>();
        bargeworkUpdateWrapper.eq("waterwaycargoid", waterwayCargo.getWaterwayCargoId());
        int bargeworkResult = pb6BargeworkMapper.update(bargework, bargeworkUpdateWrapper);
        if (result <= 0 || detailResult <= 0 || bargeworkResult <= 0) {
            log.error("确认实装数失败");
            throw new CustomException("确认实装数失败");
        }

        // 开发票

        return AjaxResult.success("确认实装数成功", cargocmentdetailMapper.selectOne(detailUpdateWrapper));
    }

    @Override
    public BargeInfo getBargeById(Long id) {
        return bargeInfoMapper.selectById(id);
    }

    /**
     * 校验唯一性 驳船名称 驳船识别号
     * @param bargeInfoBO
     * @return
     */
    private AjaxResult checkOnlyBarge(BargeInfoBO bargeInfoBO) {
        QueryWrapper<BargeInfo> wrapper = new QueryWrapper<>();

        // 2021.09.15 洪小林要求更改，说未使用的驳船的驳船标识不影响，若是这里更改出了错，jinn不负责更改
        wrapper.eq("BARGEID", bargeInfoBO.getBargeId())
                .eq("ISUSE", "Y")
                .ne(bargeInfoBO.getId()!=null,"ID", bargeInfoBO.getId());
        List<BargeInfo> list = bargeInfoMapper.selectList(wrapper);

        if (list != null && list.size() > 0) {
            for (BargeInfo item : list) {
                if (bargeInfoBO.getBargeId().equals(item.getBargeId())) {
                    log.info("该驳船标识已存在");
                    return AjaxResult.error("该驳船标识已存在");
                }
            }
        }

        return AjaxResult.success();
    }

    /**
     * 查询驳船是否备案成功
     * @param userId
     * @return
     */
    private AjaxResult checkUserBarge(Long userId) {
        AjaxResult ajaxResult = new AjaxResult();
        BargeInfoVO bargeInfo = sysUserBargeMapper.queryBargeInfoByUserId(userId);
        QueryWrapper<BargeInfoAudit> biaWrapper = new QueryWrapper<>();
        biaWrapper.eq("RECORDERID", userId);
        if (bargeInfo == null) {
            QueryWrapper<SysUserBarge> subWrapper = new QueryWrapper<>();
            subWrapper.eq("USER_ID", userId);
            SysUserBarge sub = sysUserBargeMapper.selectOne(subWrapper);
            if (sub == null) {
                log.error("您还没有进行驳船备案，请前往备案");
                ajaxResult.put("isRecord", false);
                ajaxResult.put("recordStatus", 4);
                ajaxResult.put("barge", null);
                //return AjaxResult.success("您还没有进行驳船备案，请前往备案", false);
                return ajaxResult;
            }
            biaWrapper.eq("PB6BARGEINFOID", sub.getBargeId())
                    .eq("CHECKFLAG", CheckFlagEnum.BARGE_AUDIT.getCode());
            BargeInfoAudit audit = bargeInfoAuditMapper.selectOne(biaWrapper);
            return this.bargeMsg(audit, ajaxResult);
        } else {
            biaWrapper.eq("PB6BARGEINFOID", bargeInfo.getId())
                    .eq("CHECKFLAG", CheckFlagEnum.UPDATE_BARGE_AUDIT.getCode())
                    .orderByDesc("MODIFYDATE");
            List<BargeInfoAudit> list = bargeInfoAuditMapper.selectList(biaWrapper);
            if (list.size() > 0) {
                BargeInfoAudit audit = list.get(0);
                return this.bargeMsg(audit, ajaxResult);
            } else {
                biaWrapper = new QueryWrapper<>();
                biaWrapper.eq("RECORDERID", userId)
                        .eq("PB6BARGEINFOID", bargeInfo.getId())
                        .eq("CHECKFLAG", CheckFlagEnum.BARGE_AUDIT.getCode());
                BargeInfoAudit audit = bargeInfoAuditMapper.selectOne(biaWrapper);
                return this.bargeMsg(audit, ajaxResult);
                /*log.error("您还没有进行备案，请备案");
                return AjaxResult.success("您还没有进行备案，请备案", false);*/
            }
        }
    }

    private AjaxResult bargeMsg(BargeInfoAudit audit, AjaxResult ajaxResult) {
        if (audit == null) {
            log.error("您还没有进行驳船备案，请前往备案");
            ajaxResult.put("isRecord", false);
            ajaxResult.put("recordStatus", 4);
            ajaxResult.put("barge", null);
            //return AjaxResult.success("您还没有进行驳船备案，请前往备案", false);
            return ajaxResult;
        }
        Integer check;
        if (audit.getRecordCheck() != null) {
            check = audit.getRecordCheck();
        } else {
            check = audit.getUpdateCheck();
        }
        if (CheckEnum.PASS_CHECK.getCode().equals(check)) {
            log.error("驳船备案已通过");
            ajaxResult.put("isRecord", true);
            ajaxResult.put("recordStatus", 2);
            ajaxResult.put("barge", audit);
            //return AjaxResult.success("驳船备案已通过", true);
            return ajaxResult;
        } else if (CheckEnum.FAIL_CHECK.getCode().equals(check)) {
            log.error("驳船备案审核不通过，请重新备案");
            ajaxResult.put("isRecord", false);
            ajaxResult.put("recordStatus", 3);
            ajaxResult.put("barge", audit);
            //return AjaxResult.success("驳船备案审核不通过，请重新备案", false);
            return ajaxResult;
        } else {
            log.error("驳船备案审核中");
            ajaxResult.put("isRecord", false);
            ajaxResult.put("recordStatus", 1);
            ajaxResult.put("barge", audit);
            //return AjaxResult.success("驳船备案审核中，请耐心等待", false);
            return ajaxResult;
        }
    }

    /**
     * 挂靠
     * @param bargeInfoAudit 驳船备案表内容
     * @param bargeInfoBO 驳船备案信息
     * @param loginUser 登录用户
     * @param isAudit 挂靠审核（0-修改挂靠，1-挂靠，2-取消挂靠）
     */
    private Long anchor(BargeInfoAudit bargeInfoAudit, BargeInfoBO bargeInfoBO, SysUser loginUser,Long id, Integer isAudit) {
        BargeCompanyLink bargeCompanyLink = new BargeCompanyLink();
        bargeCompanyLink.setId(id);
        bargeCompanyLink.setBargeId(bargeInfoAudit.getPb6bargeInfoId());
        bargeCompanyLink.setCompanyId(bargeInfoBO.getCompanyId());
        bargeCompanyLink.setBindingType(bargeInfoBO.getBindingType());
        bargeCompanyLink.setStatus(1);
        bargeCompanyLink.setIsDelete(1);
        bargeCompanyLink.setCreateById(loginUser.getUserId());
        bargeCompanyLink.setCreateByName(loginUser.getUserName());
        bargeCompanyLink.setCreateTime(DateUtils.getDate());
        bargeCompanyLink.setIsAudit(isAudit);
        bargeCompanyLinkService.bingdingBarge(bargeCompanyLink);
        return bargeCompanyLink.getId();
    }

    /**
     * 插入表PB6_BARGE_CHECK_MESSAGE
     * @param mType BargeCheckMessageType 0、驳船备案审核；1、驳船备案加挂靠；
     *              2、驳船信息修改审核；3、驳船添加挂靠； 4、驳船挂靠修改审核； 5、驳船取消挂靠；
     * @param loginUser 登录用户
     * @param bargeInfoAudit 驳船信息
     * @param bargeCompanyId 驳船挂靠公司id
     */
    private void bargeCheckMsg(short mType, SysUser loginUser, BargeInfoAudit bargeInfoAudit, Long bargeCompanyId, Long shipFddUserRelId) {
        BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();
        bargeCheckMessage.setMType(mType);
        bargeCheckMessage.setApplyManId(loginUser.getUserId());
        bargeCheckMessage.setApplyMan(loginUser.getUserName());
        bargeCheckMessage.setApplyTime(DateUtils.getNowDate());
        bargeCheckMessage.setAuditFlag((short) 0);
        bargeCheckMessage.setPb6BargeInfoAuditId(bargeInfoAudit.getId());
        bargeCheckMessage.setPb6BargeCompanyId(bargeCompanyId);
        bargeCheckMessage.setBargeName(bargeInfoAudit.getBargeName());
        bargeCheckMessage.setShipFddUserRelId(shipFddUserRelId);
        bargeCheckMessageMapper.insert(bargeCheckMessage);
    }

    /**
     * 上传身份证正反面
     * @param base64String 图片base64
     * @param userId 用户id
     * @param type 0-身份证正面 1-身份证反面
     */
    private void uploadIdentity(String base64String, Long userId, int type) {

        byte[] bytes = Base64.decode(base64String.split(",")[1]);

        //目标路径
        File mkdirFile = new File(mkdirPath + userId + "/");
        //如果文件目录不存在，就执行创建
        if(!mkdirFile.isDirectory()){
            mkdirFile.mkdirs();
        }

        UploadAddress uploadAddress = new UploadAddress();
        uploadAddress.setLinkId(userId);
        uploadAddress.setLinkType(UploadDataType.IDENTITY.getType());
        uploadAddress.setLinkTable(UploadDataType.IDENTITY.getTable());
        uploadAddress.setUploadTime(new Date());
        uploadAddress.setUploadUserId(userId);
        uploadAddress.setStatus(1);

        QueryWrapper<UploadAddress> uploadAddressQueryWrapper = new QueryWrapper<>();
        uploadAddressQueryWrapper.eq("LINK_TYPE", UploadDataType.IDENTITY.getType())
                .eq("LINK_ID", userId)
                .eq("BAK_FLAG", 1);
        UploadAddress ua = new UploadAddress();
        ua.setStatus(0);

        switch (type) {
            case 0:
                // 删除旧 身份证正面
                uploadAddressQueryWrapper.eq("DATA_TYPE", DataType.IDENTITY_POSITIVE.getCode());
                uploadAddressMapper.update(ua,uploadAddressQueryWrapper);

                String fileName = "身份证正面.png";
                String url = mkdirPath + userId + "/" + fileName;
                log.info("身份证正面url:{}",url);
                File file = new File(url);
                FileUtil.writeBytes(bytes, file);
                uploadAddress.setUrl(url);
                uploadAddress.setDataName(DataType.IDENTITY_POSITIVE.getCodeName());
                uploadAddress.setDataType(DataType.IDENTITY_POSITIVE.getCode());
                break;
            case 1:
                // 删除旧 身份证反面
                uploadAddressQueryWrapper.eq("DATA_TYPE", DataType.IDENTITY_NEGATIVE.getCode());
                uploadAddressMapper.update(ua,uploadAddressQueryWrapper);

                fileName = "身份证反面.png";
                url = mkdirPath + userId + "/" + fileName;
                log.info("身份证反面url:{}",url);
                file = new File(url);
                FileUtil.writeBytes(bytes, file);
                uploadAddress.setUrl(url);
                uploadAddress.setDataName(DataType.IDENTITY_NEGATIVE.getCodeName());
                uploadAddress.setDataType(DataType.IDENTITY_NEGATIVE.getCode());
                break;
            default:
                log.info("位置身份证类型");
                break;
        }

        int count = uploadAddressMapper.insert(uploadAddress);
        if (count <= 0) {
            log.info("身份证{}上传失败", type==0?"正面":"反面");
        } else {
            log.info("身份证{}上传成功", type==0?"正面":"反面");
        }

    }
}

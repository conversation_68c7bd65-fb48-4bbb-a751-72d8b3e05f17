package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ShipFddUserRelMapper extends BaseMapper<ShipFddUserRel> {

    @Select("select id,type,ship_user_id,ship_id,fdd_account_id,seal_url,new_seal_url,verified_serial_no,review_status,url from ship_fdd_user_rel where ship_id=#{shipId}")
    public List<ShipFddUserRel> listByShipId(Long shipId);

    @Select("select seal_url from ship_fdd_user_rel where ship_id=#{shipId}")
    public List<String> getSealUrlByShipId(Long shipId);

    @Select("select fdd_account_id from ship_fdd_user_rel where ship_id=#{shipId}")
    public String getFddAccountIdByShipId(Long shipId);

    @Select("select t.* from SHIP_FDD_USER_REL t where t.fdd_account_id is not null and t.ship_id is not null and t.remarks is null")
    public List<ShipFddUserRel>  getShipFddUserRel();

    /**
     * 通过驳船ID查找发票公司抬头
     * @param bargeId 驳船ID
     * @return 发票公司抬头
     */
    @Select("select t.ship_company_name from SHIP_FDD_USER_REL t where t.ship_id=#{bargeId} and t.review_status=1")
    List<String> selectTtByBargeId(@Param("bargeId") Long bargeId);
}

package com.ruoyi.app.controller.databarge;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.app.controller.barge.BargeConsignController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.databarge.domain.Pb1Customer;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.domain.dto.Pb1CustomerSearchDTO;
import com.ruoyi.databarge.service.Pb1CustomerService;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import com.ruoyi.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户基本资料 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-10
 */
@Api("客户关系管理 - api")
@RestController
@RequestMapping("/barge/customer")
public class Pb1CustomerController {

    @Autowired
    private Pb1CustomerService pb1CustomerService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ShipFddUserRelService shipFddUserRelService;

//    @GetMapping("/test")
//    public AjaxResult test(){
//        BargeConsignController bargeConsignController = new BargeConsignController();
//        bargeConsignController.againStamp("BBZ042106241023", null, "水路货物运单.pdf");
//        return AjaxResult.success();
//    }

    @ApiOperation("分页查询客户信息")
    @PostMapping("/search/page")
    public AjaxResult searchForPage(@RequestBody Pb1CustomerSearchDTO pb1CustomerSearchDTO) {
        return AjaxResult.success(pb1CustomerService.searchForPage(pb1CustomerSearchDTO));
    }

    @ApiOperation("修改客户信息")
    @Log(title = "客户关系管理", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult update(@RequestBody Pb1Customer pb1Customer) {
        return AjaxResult.success(pb1CustomerService.updateOne(pb1Customer));
    }

    @ApiOperation("添加客户信息")
    @Log(title = "客户关系管理", businessType = BusinessType.INSERT)
    @PutMapping("/add")
    public AjaxResult add(@RequestBody Pb1Customer pb1Customer) {
        boolean b = pb1CustomerService.add(pb1Customer);
        return AjaxResult.success(b);
    }

//    @ApiOperation("删除客户信息")
//    @DeleteMapping("/delete")
//    public AjaxResult delete(@RequestBody @NotNull(message = "不能为空!") List<Long> idList) {
//        return AjaxResult.success(pb1CustomerService.delete(idList));
//    }

    @ApiOperation("修改客户信息判断")
    @PostMapping("/updateJudge")
    public AjaxResult updateJudge(@RequestBody Pb1Customer pb1Customer){
        return AjaxResult.success(pb1CustomerService.updateJudge(pb1Customer));
    }

    @ApiOperation("模糊查询船公司的客户")
    @PostMapping("/searchPb1CustomerByName")
    public AjaxResult searchPb1CustomerByName(@RequestBody String cfullname){
        return AjaxResult.success(pb1CustomerService.searchPb1CustomerByName(cfullname));
    }

    @GetMapping("/get/shipFddUserRelById")
    public AjaxResult getShipFddUserRelById(@RequestParam("id") Long id){
        // 先从SysUser查
        SysUser sysUser=new SysUser();
        sysUser.setUserType("12");
        sysUser.setCompanyId(id);
        List<SysUser> sysUserList=sysUserService.getUser(sysUser);
        if(sysUserList.size()==0){
            throw new CustomException("未找到管理员!");
        }else if (sysUserList.size()>=2){
            throw new CustomException("数据错误,找到2个管理员!");
        }else{
            sysUser=sysUserList.get(0);
            // 再从ShipFddUserRel查
            List<ShipFddUserRel> shipFddUserRelList=shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                    .eq(ShipFddUserRel::getType,"2")// 2船公司
                    .eq(ShipFddUserRel::getShipUserId,sysUser.getUserId()));
            if(shipFddUserRelList.size()==0){
                throw new CustomException("未找到用户和驳船对应记录");
            }else if (shipFddUserRelList.size()>=2){
                throw new CustomException("数据错误,一个用户有多个驳船");
            }else{
                return AjaxResult.success(shipFddUserRelList.get(0));
            }
        }
    }
}
package com.ruoyi.common.exception;

/**
 * 自定义异常
 * 
 * <AUTHOR>
 */
public class FirstLoginException extends RuntimeException
{
    private static final long serialVersionUID = 1L;

    private Integer code;

    private String message;

    public FirstLoginException(String message)
    {
        this.message = message;
    }

    public FirstLoginException(String message, Integer code)
    {
        this.message = message;
        this.code = code;
    }

    public FirstLoginException(String message, Throwable e)
    {
        super(message, e);
        this.message = message;
    }

    public FirstLoginException(String message, Throwable e, Integer code)
    {
        super(message, e);
        this.message = message;
        this.code = code;
    }

    @Override
    public String getMessage()
    {
        return message;
    }

    public Integer getCode()
    {
        return code;
    }
}

package com.ruoyi.databarge.domain.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * Description:
 *
 * @Author: ChenJin on 2021/3/19.
 * @Date: 2021/3/19 10:06
 */
@Setter
@Getter
public class BargePictureSearchDTO {

    private Long id; //驳船id

    private Integer status; // 图片状态，判断查询生效还是失效的图片， 1 生效中的   0 失效的

    private Long applyId; //申请人ID，因为驳船图片只查询当前申请人的图

    //数据类型，用于驳船消息界面修改驳船主身份证图片修改使用
    private Integer dataType;
}

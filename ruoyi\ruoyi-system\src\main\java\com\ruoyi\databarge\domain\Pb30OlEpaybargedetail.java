package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/11/17 9:27
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PB30_OL_EPAYBARGEDETAIL")
@KeySequence("SEQ_PB30_OL_EPAYBARGEDETAIL")
public class Pb30OlEpaybargedetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 订单号
     */
    @TableField("ORDERID")
    private Long orderid;

    /**
     * 托运单号
     */
    @TableField("CONSIGNFLAG")
    private String consignflag;

    /**
     * 出库单号
     */
    @TableField("OUTORINFORMID")
    private String outorinformid;

    /**
     * 水路货运单号
     */
    @TableField("WATERWAYCARGOID")
    private String waterwaycargoid;

    /**
     * 付款日期
     */
    @TableField("PAYTIME")
    private String paytime;

    /**
     * 付款人
     */
    @TableField("PAYMAN")
    private String payman;

    /**
     * 付款方式(现结,月结)
     */
    @TableField("PAYTYPE")
    private String paytype;

    /**
     * 总金额
     */
    @TableField("COUNT")
    private String count;

    /**
     * 计费重量
     */
    @TableField("WEIGHTVALUE")
    private String weightvalue;

    /**
     * 运费
     */
    @TableField("TRANSPORTCHARGE")
    private String transportcharge;

    /**
     * 货港费
     */
    @TableField("CARGOPORTCHARGE")
    private String cargoportcharge;

    /**
     * 围油栏费
     */
    @TableField("SERVICEAGENTCHARGE")
    private String serviceagentcharge;

    /**
     * 代理费
     */
    @TableField("BUSINESSAGENTCHARGE")
    private String businessagentcharge;

    /**
     * 停泊费
     */
    @TableField("BERTHCHARGE")
    private String berthcharge;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 公司
     */
    @TableField("COMID")
    private String comid;

    /**
     * 订单状态：0未支付；1支付成功；2支付失败；3处理中；4待退款 ；5 退款成功； 6退款失败；7退款中；8退款待复核；9订单关闭
     */
    @TableField("STATUS")
    private String status;

    /**
     * 实付金额
     */
    @TableField("ACTUALMOUNT")
    private String actualmount;

    /**
     * 已付金额
     */
    @TableField("ALREADYMOUNT")
    private String alreadymount;

    /**
     * 付款人联系方式
     */
    @TableField("CONTACTPHONE")
    private String contactphone;

    /**
     * 支付方式：0网页端支付；1移动端支付；2出库单支付；3扫码枪支付
     */
    @TableField("RESOURSE")
    private String resourse;


}

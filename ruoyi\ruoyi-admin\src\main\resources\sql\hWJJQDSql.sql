SELECT
	pb6_waterwaycargo.id,
	pb6_waterwaycargo.waterwaycargoid,
	pb6_waterwaycargo.outorinformidid,
	pb6_waterwaycargo.outorinformid,
	pb6_waterwaycargo.loadometeridid,
	pb6_waterwaycargo.loadometerid,
	pb6_waterwaycargo.bargeidid,
	pb6_bargeInfo.bargeid,
	pb6_bargeInfo.bargename,
	pb6_waterwaycargo.shipnumber,
	pb6_waterwaycargo.consigner,
	pb6_waterwaycargo.consignee,
	pb6_waterwaycargo.shipmentplace,
	pb6_waterwaycargo.beginportid,
	pb6_waterwaycargo.shipmentplace AS beginport,
	pb6_waterwaycargo.midportid,
	( SELECT pub_port.portcname FROM pub_port WHERE pub_port.ID = pb6_waterwaycargo.midportid ) AS midport,
	pb6_waterwaycargo.endportid,
	pb6_waterwaycargo.validDate AS endport,
	--( SELECT pub_port.portcname FROM pub_port WHERE pub_port.ID = pb6_waterwaycargo.endportid ) AS endport,
	pb6_waterwaycargo.sailarea,
	pb6_waterwaycargo.consignmentflag,
	pb6_waterwaycargo.cargenameid,
	pb6_waterwaycargo.cargename AS cargename,
	pb6_waterwaycargo.rationweight,
	pb6_waterwaycargo.rationpiece,
	pb6_waterwaycargo.chargebalancetype,
	pb6_waterwaycargo.chargeweight,
	pb6_waterwaycargo.transportcharge,
	pb6_waterwaycargo.cargoportcharge,
	pb6_waterwaycargo.serviceagentcharge,
	pb6_waterwaycargo.businessagentcharge,
	pb6_waterwaycargo.totalcharge,
	pb6_waterwaycargo.specialproceeding,
	pb6_waterwaycargo.validdate,
	pb6_waterwaycargo.rationdate,
	pb6_waterwaycargo.rationprincipalid,
	pb6_waterwaycargo.rationprincipal,
	pb6_waterwaycargo.cancelrationprincipalid,
	pb6_waterwaycargo.cancelrationprincipal,
	pb6_waterwaycargo.cancelrationdate,
	pb6_waterwaycargo.modifyManid,
	pb6_waterwaycargo.modifyman,
	pb6_waterwaycargo.modifydate,
	pb6_waterwaycargo.doCharge,
	pb6_waterwaycargo.flagBargeState,
	pb6_bargeInfo.bargeLinkman,
	pb6_bargeInfo.contactPhone,
	pb6_waterwaycargo.flagLoadOrUnload,
	pb6_waterwaycargo.transportChargeRate,
	pb6_waterwaycargo.cargoPortChargeRate,
	pb6_waterwaycargo.serviceAgentChargeRate,
	pb6_waterwaycargo.businessAgentChargeRate,
	pb6_waterwaycargo.consignFlag,
	pb6_waterwaycargo.flagManyFormId,
	pb6_waterwaycargo.waterWaycargoNo,
	pb6_waterwaycargo.packagetype,
	pb6_waterwaycargo.packagetypeid,
	pb6_waterwaycargo.FLAGREG,
	pb6_waterwaycargo.DISRATIONWEIGHT,
	pb6_waterwaycargo.disrationpiece,
	pb6_waterwaycargo.isApplyUniquecode,
	pb6_waterwaycargo.isContainer,
	pb6_waterwaycargo.RECORDDATE,
	( SELECT pub_port.portcname FROM pub_port WHERE pub_port.ID = pb6_waterwaycargo.beginportid ) AS beginport2,
	pb6_waterwaycargo.comid,
	pb6_waterwaycargo.cargosize,
	pb6_waterwaycargo.flagship,
	pb6_waterwaycargo.COASTALVESSEL,
	pb6_waterwaycargo.MOORINGCHARGE,
	pb6_waterwaycargo.OCHATCHCHARGE,
	pb6_waterwaycargo.BERTHCHARGE,
	pb6_waterwaycargo.VHFCHARGE,
	pb6_waterwaycargo.MOORINGCHARGERATE,
	pb6_waterwaycargo.OCHATCHCHARGERATE,
	pb6_waterwaycargo.BERTHCHARGERATE,
	pb6_waterwaycargo.VHFCHARGERATE,
	pb6_waterwaycargo.BERTHDAYS,
	pb6_waterwaycargo.FLOW,
	pb6_waterwaycargo.shipname,
	pb3_coutform.uniqecode,
	pb6_waterwaycargo.isverificate,
	pb6_waterwaycargo.verificatereason,
	CASE

		WHEN pb6_bargework.comid = 4 THEN
		(
		SELECT
			round( sum( to_number ( xs2.workweight ) / to_number ( pb6_bargework.shipmentrate ) ) )
		FROM
			pb6_xsloadbargeorder xs1,
			pb6_xsorderdetail xs2
		WHERE
			xs1.orderid = xs2.orderid
			AND xs2.waterwaycargoid = pb6_bargework.waterwaycargoid
			) ELSE (
		SELECT
			sum( xs2.workweight )
		FROM
			pb6_xsloadbargeorder xs1,
			pb6_xsorderdetail xs2
		WHERE
			xs1.orderid = xs2.orderid
			AND xs2.waterwaycargoid = pb6_bargework.waterwaycargoid
			AND xs1.type = '4'
		)
	END AS workWeight,
		(
	SELECT
		cast( sum( cast( tscargoite1_.weightValue AS DOUBLE PRECISION ) ) AS varchar2 ( 255 CHAR ) )
	FROM
		PB11_TALLYSHEET_LOADITEM tallysheet0_,
		PB11_TSCARGOITEM tscargoite1_
	WHERE
		tallysheet0_.ciid = tscargoite1_.ciid
		AND tallysheet0_.noticeNo = pb6_bargework.waterwaycargoid
		AND tallysheet0_.outFormid = pb6_waterwaycargo.outorinformid
	) AS weightValue,
	( select sum(i.amt) from PB11_TALLYSHEET_LOADITEM t
left join pb11_tscargoitem i on t.ciid = i.ciid
where t.noticeno=pb6_waterWayCargo.waterwaycargono )as workPiece
FROM
	pb6_waterwaycargo left join pb6_bargeInfo ON pb6_bargeInfo.id = pb6_waterwaycargo.bargeidid left join pb3_coutform ON pb3_coutform.coutformid = pb6_waterwaycargo.outorinformid left join pb6_bargework ON pb6_waterwaycargo.id = pb6_bargework.waterwaycargoidid
WHERE
	1 = 1
	AND pb6_waterWayCargo.waterwaycargono = ?

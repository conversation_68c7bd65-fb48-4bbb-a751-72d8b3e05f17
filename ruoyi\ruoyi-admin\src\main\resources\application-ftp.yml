# FTP服务器配置
# 货主、托运联系人、船公司
ftp:
  host: ************ #cn-hk-dmit.sakurafrp.com #************
  port: 22 #32322 #22
  username: root
  password: BJ@jport2016
  ftpBasePath: /var/www/olorder/ConsignPicture/
  #ftpBasePath: /var/www/olorder/CargolistPicture/

#ftp:
#  host: ***************
#  port: 22
#  username: root
#  password: root
#  ftpBasePath: /var/www/olorder/images/
# 驳船主
mkdirPath: /root/gzgtest/bargeRecord/
barge:
  ftp:
    host: ************ #cn-hk-dmit.sakurafrp.com #************
    port: 22 #32322 #22
    username: root
    password: Barge@2020
    path:
      ftpBasePath: /root/gzgtest/bargeRecord/
      cargoFilePath: /root/gzgtest/cargoFile/
      uploadFileLocation: /gzcw/uploadFiles/bargeRecord/
      uploadCargoFileLocation: /gzcw/uploadFiles/cargoFile/
    # 连接池参数
    pool:
      max-total: 10
      max-idle: 10
      min-idle: 5

# 驳船文件服务器
ship:
  ftp:
    host: ************
    port: 21
    username: root
    password: Barge@2020
    controlEncoding: UTF-8
    clientMode: 2
    fileType: 2
    bufferSize: 100000
    sessionCacheSize: 20
    sessionWaitTimeout: 20000
    testSession: true
    testWhileIdle: true                 # 空闲时测试连接
    timeBetweenEvictionRunsMillis: 60000 # 每分钟运行一次空闲连接检测
    minEvictableIdleTimeMillis: 600000  # 连接空闲10分钟后可被清除

#散货帐龄盖章
receivable:
  ftp:
    host: *************
    port: 22
    username: root
    password: cloud@2021
    path: /home/<USER>/uploadPath
    # 连接池参数
    pool:
      max-total: 10
      max-idle: 10
      min-idle: 5
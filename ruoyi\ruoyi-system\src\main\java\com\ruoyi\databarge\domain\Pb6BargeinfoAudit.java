package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 驳船基本信息审核历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("PB6_BARGEINFO_AUDIT")
@KeySequence(value = "SEQ_PB6_BARGEINFO_AUDIT")
public class Pb6BargeinfoAudit  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 驳船标识
     */
    @TableField("BARGEID")
    private String bargeid;

    /**
     * 驳船名称
     */
    @TableField("BARGENAME")
    private String bargename;

    /**
     * 驳船载货量（A级）
     */
    @TableField("BARGELOADA")
    private String bargeloada;

    /**
     * 驳船载货量（B级）
     */
    @TableField("BARGELOADB")
    private String bargeloadb;

    /**
     * 证书有效期
     */
    @TableField("VALIDSAILDATE")
    private String validsaildate;

    /**
     * 所属地区
     */
    @TableField("BELONGAREA")
    private String belongarea;

    /**
     * 驳船所有人
     */
    @TableField("BARGEOWNER")
    private String bargeowner;

    /**
     * 联系电话
     */
    @TableField("CONTACTPHONE")
    private String contactphone;

    /**
     * 驳船类型
     */
    @TableField("BARGETYPE")
    private String bargetype;

    /**
     * 是否在黑名单
     */
    @TableField("FLAGBLACKLIST")
    private String flagblacklist;

    /**
     * 录入人
     */
    @TableField("RECORDER")
    private String recorder;

    /**
     * 录入时间
     */
    @TableField("RECORDDATE")
    private String recorddate;

    /**
     * 驳船联系人
     */
    @TableField("BARGELINKMAN")
    private String bargelinkman;

    /**
     * 改建日期
     */
    @TableField("REMAKEDATE")
    private String remakedate;

    /**
     * 驳船呼号
     */
    @TableField("BARGECALL")
    private String bargecall;

    /**
     * 驳船制造厂
     */
    @TableField("MAKEFACTORY")
    private String makefactory;

    /**
     * 驳船改建厂
     */
    @TableField("REMAKEFACTORY")
    private String remakefactory;

    /**
     * 驳船经营人
     */
    @TableField("BARGEOPERATOR")
    private String bargeoperator;

    /**
     * 总长
     */
    @TableField("TOTALLENGTH")
    private String totallength;

    /**
     * 船长
     */
    @TableField("BARGELENGTH")
    private String bargelength;

    /**
     * 船宽
     */
    @TableField("BARGEWIDTH")
    private String bargewidth;

    /**
     * 型深
     */
    @TableField("MODELDEEP")
    private String modeldeep;

    /**
     * 最大船宽
     */
    @TableField("MAXBARGEWIDTH")
    private String maxbargewidth;

    /**
     * 最大船高
     */
    @TableField("MAXBARGEHIGH")
    private String maxbargehigh;

    /**
     * 满载水线长
     */
    @TableField("LOADWATERLINELENGTH")
    private String loadwaterlinelength;

    /**
     * 空载吃水
     */
    @TableField("UNLOADDRAUGHT")
    private String unloaddraught;

    /**
     * 满载吃水
     */
    @TableField("LOADDRAUGHT")
    private String loaddraught;

    /**
     * 空载排水量
     */
    @TableField("UNLOADDISPLACEMENT")
    private String unloaddisplacement;

    /**
     * 满载排水量
     */
    @TableField("LOADDISPLACEMENT")
    private String loaddisplacement;

    /**
     * 结构型式
     */
    @TableField("STRUCTURE")
    private String structure;

    /**
     * 船索引
     */
    @TableField("BARGEINDEX")
    private String bargeindex;

    /**
     * 船检登记号
     */
    @TableField("BARGECHECKID")
    private String bargecheckid;

    /**
     * 安放龙骨日期
     */
    @TableField("EMPLACEKEELDATE")
    private String emplacekeeldate;

    /**
     * 录入人代理主键
     */
    @TableField("RECORDERID")
    private Long recorderid;

    /**
     * 修改人
     */
    @TableField("MODIFYMAN")
    private String modifyman;

    /**
     * 修改人代理主键
     */
    @TableField("MODIFYMANID")
    private Long modifymanid;

    /**
     * 修改人时间
     */
    @TableField("MODIFYDATE")
    private String modifydate;

    /**
     * 驳船名称首字母
     */
    @TableField("PYCAP")
    private String pycap;

    /**
     * 是否为导入数据
     */
    @TableField("ISIMPORT")
    private String isimport;

    /**
     * 特约事项
     */
    @TableField("MEMO")
    private String memo;

    /**
     * 运输证编号
     */
    @TableField("TRANSPORTNUM")
    private String transportnum;

    /**
     * 经营范围
     */
    @TableField("BUSINESSSCOPE")
    private String businessscope;

    /**
     * 年审有效期
     */
    @TableField("ANNUALVALIDSAILDATE")
    private String annualvalidsaildate;

    /**
     * 驳船净吨
     */
    @TableField("BARGEWEIGHT")
    private String bargeweight;

    /**
     * 是否为客户
     */
    @TableField("SPECIALCUSTOMER")
    private String specialcustomer;

    /**
     * 特殊客户日期
     */
    @TableField("SPECIALCUSDATE")
    private String specialcusdate;

    /**
     * 特殊用户运单号
     */
    @TableField("SPECIALID")
    private String specialid;

    /**
     * 驳船总吨（小虎）
     */
    @TableField("BARGETOTALWEIGHT")
    private String bargetotalweight;

    /**
     * MMSI标识
     */
    @TableField("MMSI")
    private String mmsi;

    /**
     * 是否是网上营业厅办理
     */
    @TableField("ISOLORDER")
    private String isolorder;

    /**
     * 最新联系电话
     */
    @TableField("NEWCONTACTNUM")
    private String newcontactnum;

    /**
     * 微信号
     */
    @TableField("OPENID")
    private String openid;

    /**
     * 驳船审核标识(1.备案审核，2.修改审核)
     */
    @TableField("CHECKFLAG")
    private Integer checkFlag;

    /**
     * 备案审核（0：待审核， 1：审核通过   2.审核不通过 ）
     */
    @TableField("RECORDCHECK")
    private Integer recordCheck;

    /**
     * 修改审核（0：待审核， 1：审核通过  2.审核不通过）
     */
    @TableField("UPDATECHECK")
    private Integer updateCheck;

    /**
     * 保存新关联关系url的id
     */
    @TableField("URLIDS")
    private String urlIds;


    /**
     * 审核人
     */
    @TableField("BARGEAUDITBYNAME")
    private String bargeAuditByName;


    /**
     * 审核人id
     */
    @TableField("BARGEAUDITBYID")
    private Long bargeAuditById;

    /**
     * 审核时间
     */
    @TableField("BARGEAUDITTIME")
    private String bargeAuditTime;

    /**
     * 是否已删除标识（0-已删除，1-生效）
     */
    @TableField("ISDELETE")
    private Integer isDelete;

    /**
     * pb6_bargeinfo表的id
     */
    @TableField("PB6BARGEINFOID")
    private Long pb6BargeInfoId;

    // 装载重量最小
    @TableField("LOADING_WEIGHT_MIN")
    private String loadingWeightMin;

    // 装载重量最大
    @TableField("LOADING_WEIGHT_MAX")
    private String loadingWeightMax;

    // 不允许装载货物
    @TableField("CARGO_NOT_ALLOWED")
    private String cargoNotAllowed;

}

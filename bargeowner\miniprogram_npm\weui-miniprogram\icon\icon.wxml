<wxs module="utils">
    var double = function(a) {
        return 2*a
    };
    var ifSpecialIcon = function(v) {
      return v === 'arrow' || v === 'back'
    }
    module.exports = {
      double: double,
      ifSpecialIcon: ifSpecialIcon
    }
</wxs>

<view class="{{extClass}} weui-icon" style="background:{{color}};width:{{size}}px;height:{{ utils.ifSpecialIcon(icon) ? utils.double(size) : size}}px;mask-image:url({{src}});-webkit-mask-image:url({{src}});-moz-mask-image:url({{src}})"></view>

package com.ruoyi.consignor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.barge.domain.bo.BargeCapacityPublishBO;
import com.ruoyi.barge.domain.vo.CargoPublishVO;
import com.ruoyi.consignor.domain.CargoSourceInfo;
import com.ruoyi.consignor.domain.bo.CargoSourceBO;
import com.ruoyi.consignor.domain.vo.CargoSourceVO;

import java.util.List;

/**
 * @Description 货主的货源持久层接口
 * <AUTHOR>
 * @Date 2020/7/24 15:34
 */
public interface ConsignorCargoSourceMapper extends BaseMapper<CargoSourceInfo> {

    // 根据货源号查询货源信息
    List<CargoSourceVO> selectCargoSource(CargoSourceBO cargoSourceBO);

    /**
     * 驳船主端获取货源信息列表 TODO 后续完善sql
     * @param bargeCapacityPublishBO
     * @return
     */
    List<CargoPublishVO> getCargoSourceListOfBarge(BargeCapacityPublishBO bargeCapacityPublishBO);


}

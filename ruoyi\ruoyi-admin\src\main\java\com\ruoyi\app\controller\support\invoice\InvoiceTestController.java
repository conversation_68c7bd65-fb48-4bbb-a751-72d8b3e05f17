package com.ruoyi.app.controller.support.invoice;

import com.ruoyi.Invoice.domain.bo.InvoiceBO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.ShipInvoiceHistory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Random;

/**
 * <AUTHOR>
 * @Date 2020/12/7 11:37
 * @Description:
 */
@RestController
@RequestMapping("/invoice")
public class InvoiceTestController {

    @Autowired
    private InvoiceUtil invoiceUtil;

    /**
     * 官网例子
     * http://localhost:8080/gzgapp/invoice/kp
     * {"msg":"操作成功","code":200,"data":{"status":"0000","message":"同步成功","fpqqlsh":"20120714412401467741"}}
     */
    @GetMapping("/kp")
    public AjaxResult kp(){

        InvoiceDTO invoiceDTO=new InvoiceDTO();

        invoiceDTO.setIdentity(InvoiceUtil.IDENTITY);

        InvoiceDTO.Order order=new InvoiceDTO.Order();
        order.setBuyername("浙江爱信诺");
        order.setTaxnum("124511234993295177");
        order.setPhone("***********");
        order.setAddress("");
        order.setAccount("");
        order.setTelephone("");
        order.setOrderno("nuonuo012345678");
        order.setInvoicedate("2018-10-31 19:16:51");
        order.setClerk("黄芝");
        order.setSaleaccount("");
        order.setSalephone("");
        order.setSaleaddress("");
        order.setSaletaxnum(InvoiceUtil.SALETAXNUM);
        order.setKptype("1");
        order.setMessage("");
        order.setPayee("");
        order.setChecker("");
        order.setFpdm("");
        order.setFphm("");
        order.setTsfs("2");
        order.setEmail("<EMAIL>");
        order.setQdbz("");
        order.setQdxmmc("");
        order.setDkbz("");
        order.setDeptid("");
        order.setClerkid("");
        order.setInvoiceLine("");
        order.setCpybz("");
        order.setBillinfono("");

        InvoiceDTO.Detail detail=new InvoiceDTO.Detail();
        detail.setGoodsname("苹果");
        detail.setNum("1");
        detail.setPrice("1");
        detail.setHsbz("1");
        detail.setTaxrate("0.13");
        detail.setSpec("");
        detail.setUnit("吨");
        detail.setSpbm("***********");
        detail.setZsbm("");
        detail.setFphxz("0");
        detail.setYhzcbs("0");
        detail.setZzstsgl("");
        detail.setLslbs("");
        detail.setKce("");
        detail.setTaxfreeamt("");
        detail.setTax("");
        detail.setTaxamt("");

        order.setDetail(Arrays.asList(detail));

        invoiceDTO.setOrder(order);

        InvoiceKpVO invoiceKpVO=invoiceUtil.kp(invoiceDTO);
        System.out.println(invoiceKpVO);
        if(invoiceKpVO.getStatus().equals("0000")){

            ShipInvoiceHistory shipInvoiceHistory=new ShipInvoiceHistory();

            return AjaxResult.success(invoiceKpVO);
        }else{
            throw new CustomException(invoiceKpVO.getMessage());
        }
    }

    @GetMapping("/kpyy")
    public AjaxResult kpyy(){
        InvoiceBO invoiceBO=new InvoiceBO();
        invoiceBO.setInvoicetype("0");
        invoiceBO.setWaterwayCargoId("BBZ042012281026");
//        InvoiceDTO invoiceDTO=new InvoiceDTO();
//        InvoiceDTO.Order order=new InvoiceDTO.Order();
        invoiceBO.setBuyername("浙江爱信诺");
        invoiceBO.setTaxnum("124511234993295177");
        invoiceBO.setPhone("***********");
        invoiceBO.setAddress("");
        invoiceBO.setAccount("");
        invoiceBO.setTelephone("");
        invoiceBO.setOrderno("nuonuo012378952");
        invoiceBO.setFpqqlsh("5369846");
        invoiceBO.setInvoicedate(DateUtils.getTime());
        invoiceBO.setClerk("黄芝");
        invoiceBO.setSaleaccount("");
        invoiceBO.setSalephone("");
//        invoiceBO.setSaleaddress("");
        invoiceBO.setSaletaxnum(InvoiceUtil.SALETAXNUM);
        invoiceBO.setKptype("1");
        invoiceBO.setMessage("");
        invoiceBO.setPayee("");
        invoiceBO.setChecker("");
        invoiceBO.setFpdm("");
        invoiceBO.setFphm("");
        invoiceBO.setTsfs("2");
        invoiceBO.setEmail("<EMAIL>");
        invoiceBO.setQdbz("");
        invoiceBO.setQdxmmc("");
        invoiceBO.setDkbz("");
        invoiceBO.setDeptid("");
        invoiceBO.setClerkid("");
//        invoiceBO.setInvoiceLine("");
        invoiceBO.setCpybz("");
        invoiceBO.setBillinfono("");

        InvoiceBO.Detail detail=new InvoiceBO.Detail();
//        InvoiceDTO.Detail detail=new InvoiceDTO.Detail();
        detail.setGoodsname("苹果");
        detail.setNum("1");
        detail.setPrice("19.41");
        detail.setHsbz("1");
        detail.setTaxrate("0.13");
        detail.setSpec("");
        detail.setUnit("吨");
        detail.setSpbm("***********");
        detail.setZsbm("");
        detail.setFphxz("0");
        detail.setYhzcbs("0");
        detail.setZzstsgl("");
        detail.setLslbs("");
        detail.setKce("");
        detail.setTaxfreeamt("");
        detail.setTax("");
        detail.setTaxamt("");

        invoiceBO.setDetail(Arrays.asList(detail));
        return AjaxResult.success(invoiceUtil.invoicekp(invoiceBO));
    }
    /**
     * http://localhost:8080/gzgapp/invoice/kpcx?fpqqlsh=22102515590901795905
     * http://localhost:8080/gzgapp/invoice/kpcx?orderno=nuonuo012345678
     */
    @GetMapping("/kpcx")
    public AjaxResult kpcx(@RequestParam(value="fpqqlsh",required=false) String fpqqlsh,@RequestParam(value="orderno",required=false) String orderno){
        InvoiceKpcxVO invoiceKpcxVO=null;
        if(StringUtils.isNotEmpty(fpqqlsh)){
            invoiceKpcxVO=invoiceUtil.kpcxByFpqqlsh(fpqqlsh);
        }else if(StringUtils.isNotEmpty(orderno)){
            invoiceKpcxVO=invoiceUtil.kpcxByOrderno(orderno);
        }else{
            throw new CustomException("fpqqlsh和orderno不能都为空!");
        }

        System.out.println(invoiceKpcxVO);
        if(invoiceKpcxVO.getResult().equals("success")){
            return AjaxResult.success(invoiceKpcxVO);
        }else{
            return AjaxResult.success(invoiceKpcxVO);
        }
    }

    /**
     * 测试所有字段
     * http://localhost:8080/gzgapp/invoice/kp1
     * {"msg":"操作成功","code":200,"data":{"status":"0000","message":"同步成功","fpqqlsh":"20120716181201469683"}}
     */
    @GetMapping("/kp1")
    public AjaxResult kp1(){
        InvoiceDTO invoiceDTO=new InvoiceDTO();

        invoiceDTO.setIdentity(InvoiceUtil.IDENTITY);

        InvoiceDTO.Order order=new InvoiceDTO.Order();
        order.setBuyername("粤河源货2319");
        order.setTaxnum("**********");
        order.setPhone("***********");
        order.setAddress("b");
        order.setAccount("c");
        order.setTelephone("d");
        order.setOrderno("nuonuo0123456789");
        order.setInvoicedate("2020-01-01 00:00:00");
        order.setClerk("刘洁菲");
        order.setSaleaccount("e");
        order.setSalephone("f");
        order.setSaleaddress("g");
        order.setSaletaxnum(InvoiceUtil.SALETAXNUM);
        order.setKptype("1");
        order.setMessage("h");
        order.setPayee("i");
        order.setChecker("j");
        order.setFpdm("");
        order.setFphm("");
        order.setTsfs("2");
        order.setEmail("<EMAIL>");
        order.setQdbz("");
        order.setQdxmmc("l");
        order.setDkbz("");
        order.setDeptid("m");
        order.setClerkid("n");
        order.setInvoiceLine("");
        order.setCpybz("");
        order.setBillinfono("");

        InvoiceDTO.Detail detail=new InvoiceDTO.Detail();
        detail.setGoodsname("苹果");
        detail.setNum("1");
        detail.setPrice("1");
        detail.setHsbz("1");
        detail.setTaxrate("0.13");
        detail.setSpec("");
        detail.setUnit("吨");
        detail.setSpbm("***********");
        detail.setZsbm("");
        detail.setFphxz("0");
        detail.setYhzcbs("0");
        detail.setZzstsgl("");
        detail.setLslbs("");
        detail.setKce("");
        detail.setTaxfreeamt("");
        detail.setTax("");
        detail.setTaxamt("");

        order.setDetail(Arrays.asList(detail));

        invoiceDTO.setOrder(order);

        InvoiceKpVO invoiceKpVO=invoiceUtil.kp(invoiceDTO);
//        System.out.println(invoiceKpVO);
        if(invoiceKpVO.getStatus().equals("0000")){

            ShipInvoiceHistory shipInvoiceHistory=new ShipInvoiceHistory();

            return AjaxResult.success(invoiceKpVO);
        }else{
            throw new CustomException(invoiceKpVO.getMessage());
        }
    }

    /**
     * 测试物流公司发票
     * http://localhost:8080/gzgapp/invoice/kp2
     * http://localhost:8080/gzgapp/invoice/kpcx?fpqqlsh=20120816393601499257
     */
    @GetMapping("/kp2")
    public AjaxResult kp2(){
        InvoiceDTO invoiceDTO=new InvoiceDTO();

        invoiceDTO.setIdentity(InvoiceUtil.IDENTITY);

        InvoiceDTO.Order order=new InvoiceDTO.Order();
        order.setBuyername("粤河源货2319");
        order.setTaxnum("");
        order.setPhone("***********");
        order.setAddress("");
        order.setAccount("");
        order.setTelephone("");
        order.setOrderno(getRandomOrderNo());
        order.setInvoicedate("2020-01-01 00:00:00");
        order.setClerk("刘洁菲");
        order.setSaleaccount("中国建设银行黄埔支行44001470901053000321");
        order.setSalephone("********");
        order.setSaleaddress("广州市黄埔区港前路531号");
        order.setSaletaxnum(InvoiceUtil.SALETAXNUM);
        order.setKptype("1");
        order.setMessage("");
        order.setPayee("卢碧君");
        order.setChecker("陈泽敏");
        order.setFpdm("");
        order.setFphm("");
        order.setTsfs("2");
        order.setEmail("<EMAIL>");
        order.setQdbz("");
        order.setQdxmmc("");
        order.setDkbz("");
        order.setDeptid("");
        order.setClerkid("");
        order.setInvoiceLine("");
        order.setCpybz("");
        order.setBillinfono("");

        InvoiceDTO.Detail detail=new InvoiceDTO.Detail();
        detail.setGoodsname("代理费");
        detail.setNum("1");
        detail.setPrice("19.41");// 719.41>500，改为19.41
        detail.setHsbz("1");
        detail.setTaxrate("0.06");
        detail.setSpec("");
        detail.setUnit("吨");
        detail.setSpbm("*********");
        detail.setZsbm("");
        detail.setFphxz("0");
        detail.setYhzcbs("0");
        detail.setZzstsgl("");
        detail.setLslbs("");
        detail.setKce("");
        detail.setTaxfreeamt("");
        detail.setTax("");
        detail.setTaxamt("");

        order.setDetail(Arrays.asList(detail));

        invoiceDTO.setOrder(order);

        InvoiceKpVO invoiceKpVO=invoiceUtil.kp(invoiceDTO);
        System.out.println(invoiceKpVO);
        if(invoiceKpVO.getStatus().equals("0000")){

            ShipInvoiceHistory shipInvoiceHistory=new ShipInvoiceHistory();

            return AjaxResult.success(invoiceKpVO);
        }else{
            throw new CustomException(invoiceKpVO.getMessage());
        }
    }

    /**
     * 测试物流公司红票
     * http://localhost:8080/gzgapp/invoice/khp3?c_fpdm=************&c_fphm=19088469
     * http://localhost:8080/gzgapp/invoice/kpcx?fpqqlsh=20120810525501491201
     */
    @GetMapping("/khp3")
    public AjaxResult khp3(@RequestParam("c_fpdm") String c_fpdm,@RequestParam("c_fphm") String c_fphm){
        InvoiceDTO invoiceDTO=new InvoiceDTO();

        invoiceDTO.setIdentity(InvoiceUtil.IDENTITY);

        InvoiceDTO.Order order=new InvoiceDTO.Order();
        order.setBuyername("粤河源货2319");
        order.setTaxnum("");
        order.setPhone("***********");
        order.setAddress("");
        order.setAccount("");
        order.setTelephone("");
        order.setOrderno(getRandomOrderNo());
        order.setInvoicedate("2020-01-01 00:00:00");
        order.setClerk("刘洁菲");
        order.setSaleaccount("中国建设银行黄埔支行44001470901053000321");
        order.setSalephone("********");
        order.setSaleaddress("广州市黄埔区港前路531号");
        order.setSaletaxnum(InvoiceUtil.SALETAXNUM);
        order.setKptype("2");// 1正票2红牌
        order.setMessage("对应正数发票代码:"+c_fpdm+"号码:"+c_fphm);// ************为c_fpdm，********为c_fphm
        order.setPayee("卢碧君");
        order.setChecker("陈泽敏");
        order.setFpdm(c_fpdm);// 发票代码************
        order.setFphm(c_fphm);// 发票号码********
        order.setTsfs("2");
        order.setEmail("<EMAIL>");
        order.setQdbz("");
        order.setQdxmmc("");
        order.setDkbz("");
        order.setDeptid("");
        order.setClerkid("");
        order.setInvoiceLine("");
        order.setCpybz("");
        order.setBillinfono("");

        InvoiceDTO.Detail detail=new InvoiceDTO.Detail();
        detail.setGoodsname("代理费");
        detail.setNum("-1");// 冲红时项目数量为负数
        detail.setPrice("19.41");
        detail.setHsbz("1");
        detail.setTaxrate("0.06");
        detail.setSpec("");
        detail.setUnit("吨");
        detail.setSpbm("***********");
        detail.setZsbm("");
        detail.setFphxz("0");
        detail.setYhzcbs("0");
        detail.setZzstsgl("");
        detail.setLslbs("");
        detail.setKce("");
        detail.setTaxfreeamt("");
        detail.setTax("");
        detail.setTaxamt("");

        order.setDetail(Arrays.asList(detail));

        invoiceDTO.setOrder(order);

        InvoiceKpVO invoiceKpVO=invoiceUtil.kp(invoiceDTO);
        System.out.println(invoiceKpVO);
        if(invoiceKpVO.getStatus().equals("0000")){

            ShipInvoiceHistory shipInvoiceHistory=new ShipInvoiceHistory();

            return AjaxResult.success(invoiceKpVO);
        }else{
            throw new CustomException(invoiceKpVO.getMessage());
        }
    }


    @GetMapping("/khp4")
    public AjaxResult khp4(@RequestParam("c_fpdm") String c_fpdm,@RequestParam("c_fphm") String c_fphm){
        InvoiceDTO invoiceDTO=new InvoiceDTO();

        invoiceDTO.setIdentity(InvoiceUtil.IDENTITY);

        InvoiceDTO.Order order=new InvoiceDTO.Order();
        order.setBuyername("浙江爱信诺");
        order.setClerk("admin");
        order.setEmail("<EMAIL>");


        order.setFpdm(c_fpdm);// 发票代码************
        order.setFphm(c_fphm);// 发票号码********
        order.setInvoicedate("2021-03-24 11:33:47");
        order.setKptype("2");
        order.setOrderno(getRandomOrderNo());
        order.setPhone("***********");
        order.setTsfs("2");

        order.setAddress("");
        order.setAccount("");
        order.setTelephone("");

        order.setTaxnum("124511234993295177");//购方税号
        order.setSaletaxnum(InvoiceUtil.SALETAXNUM);//销方税号

        order.setSaleaccount("中国建设银行黄埔支行44001470901053000321");
        order.setSalephone("********");
        order.setSaleaddress("广州市黄埔区港前路531号");

        order.setKptype("2");// 1正票2红牌
        order.setMessage("对应正数发票代码:"+c_fpdm+"号码:"+c_fphm);// ************为c_fpdm，********为c_fphm
        order.setPayee("卢碧君");
        order.setChecker("陈泽敏");



        order.setQdbz("");
        order.setQdxmmc("");
        order.setDkbz("");
        order.setDeptid("");
        order.setClerkid("");
        order.setInvoiceLine("");
        order.setCpybz("");
        order.setBillinfono("");

        InvoiceDTO.Detail detail=new InvoiceDTO.Detail();
        detail.setGoodsname("苹果");
        detail.setHsbz("1");
        detail.setKce("");
        detail.setLslbs("");
        detail.setNum("-1");// 冲红时项目数量为负数
        detail.setPrice("1");
        detail.setSpbm("***********");
        detail.setSpec("");
        detail.setTax("");
        detail.setTaxamt("");
        detail.setTaxfreeamt("");


        detail.setTaxrate("0.06");

        detail.setUnit("吨");

        detail.setZsbm("");
        detail.setFphxz("0");
        detail.setYhzcbs("0");
        detail.setZzstsgl("");




        order.setDetail(Arrays.asList(detail));

        invoiceDTO.setOrder(order);

        InvoiceKpVO invoiceKpVO=invoiceUtil.kp(invoiceDTO);
        System.out.println(invoiceKpVO);
        if(invoiceKpVO.getStatus().equals("0000")){

            ShipInvoiceHistory shipInvoiceHistory=new ShipInvoiceHistory();

            return AjaxResult.success(invoiceKpVO);
        }else{
            throw new CustomException(invoiceKpVO.getMessage());
        }
    }

    public static String getRandomOrderNo(){
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))+new Random().nextInt(1000000);
    }
}

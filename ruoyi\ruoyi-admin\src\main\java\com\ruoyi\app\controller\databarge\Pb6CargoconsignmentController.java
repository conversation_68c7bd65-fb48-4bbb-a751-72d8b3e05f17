package com.ruoyi.app.controller.databarge;


import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.databarge.service.Pb6CargoconsignmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * ????? 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-23
 */
@RestController
@RequestMapping("/barge/cargoconsignment")
public class Pb6CargoconsignmentController {

    @Autowired
    Pb6CargoconsignmentService pb6CargoconsignmentService;


    /**
     * @param
     * @return
     * @description 生成水路运单
     * <AUTHOR>
     * @date 2024/1/26 17:17
     */
    @PostMapping("/generateWaterWayCargo")
    public AjaxResult generateWaterWayCargo(@RequestBody List<Long> consignmentIds){
        return pb6CargoconsignmentService.generateWaterWayCargo(consignmentIds);
    }

    /**
     * @param
     * @return
     * @description 新增驳船信息
     * <AUTHOR>
     * @date 2024/1/26 17:17
     */
    @PostMapping("/addCargoconsignmentDeatil")
    public AjaxResult addCargoconsignmentDeatil(@RequestBody Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail){
        return pb6CargoconsignmentService.addCargoconsignmentDeatil(pb6Cargoconsignmentdetail);
    }


    /**
     * @param
     * @return
     * @description 修改驳船信息
     * <AUTHOR>
     * @date 2024/1/26 17:17
     */
    @PostMapping("/editCargoconsignmentDeatil")
    public AjaxResult editCargoconsignmentDeatil(@RequestBody Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail){
        return pb6CargoconsignmentService.updateCargoconsignmentDeatil(pb6Cargoconsignmentdetail);
    }

    /**
     * @param
     * @return
     * @description 删除驳船信息
     * <AUTHOR>
     * @date 2024/1/26 17:17
     */
    @PostMapping("/deleteCargoconsignmentDeatil")
    public AjaxResult deleteCargoconsignmentDeatil(@RequestBody Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail){
        return pb6CargoconsignmentService.deleteCargoconsignmentDeatil(pb6Cargoconsignmentdetail);
    }

    /**
     * @param
     * @return
     * @description 删除托运单
     * @date 2023/8/21 15:45
     */
    @PostMapping("/deleteCargoconsignment")
    public AjaxResult deleteCargoconsignment(@RequestBody List<Long> consignmentIds){
        return pb6CargoconsignmentService.deleteCargoconsignment(consignmentIds);
    }

    /**
     * @param
     * @return
     * @description 作废水路运单
     * @date 2023/8/21 15:45
     */
    @PostMapping("/cancelCargoconsignment")
    public AjaxResult cancelCargoconsignment(@RequestBody List<Long> detailIds){
        return pb6CargoconsignmentService.cancelCargoconsignment(detailIds);
    }

}

package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/5 9:50
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
@TableName("SYS_MESSAGE")
@KeySequence("SEQ_SYS_MESSAGE")
public class SysMessage {

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 消息模板id
     */
    @TableField("MESSAGE_MODEL_ID")
    private Long messageModelId;

    /**
     * 消息标题
     */
    @TableField("TITLE")
    private String title;

    /**
     * 消息内容
     */
    @TableField("CONTENT")
    private String content;

    /**
     * 消息创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 消息创建时间
     */
    @TableField("WATER_CARGO_ID")
    private String waterCargoId;
}

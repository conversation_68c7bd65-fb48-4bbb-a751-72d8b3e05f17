package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * Description: 用于移动端查询预约报到和已配载的船的信息
 *
 * @Author: ChenJin on 2021/6/11.
 * @Date: 2021/6/11 8:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "PB6_WATER_CARGO_MOBILE")
public class Pb6WaterCargoMobile {

    private String shipname; //大船名

    private String cargename; //货名

    private String waterwaycargoid; //水路运单号

    private String bargename; //驳船名

    private Long mmsi; //驳船mmsi号

    private String consigner; //发货人

    private String consignee; //收货人

    private BigDecimal rationweight; //配载吨数

    private Integer flagbargestate; //驳船状态

    private BigDecimal cargoportcharge; //货物港务费

    private BigDecimal serviceagentcharge; //围油栏费

    private BigDecimal businessagentcharge; //代理费

    private BigDecimal berthcharge; //停泊费

    private BigDecimal totalcharge; //总费用

    private BigDecimal distance; //距离新沙点的距离

    private int status; //数据状态区分： 0 预约报到的  1 配载未离港的  2 作业中的
}

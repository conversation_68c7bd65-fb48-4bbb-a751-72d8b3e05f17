package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * <p>
 * 发票经手人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "INVOICE_PERSONNEL")
@KeySequence("SEQ_INVOICE_PERSONNEL")
public class InvoicePersonnel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /** 公司ID */
    @TableField("COMID")
    private Long comid;

    /** 收款人 */
    @TableField("PAYEE")
    private String payee;

    /** 复核人 */
    @TableField("REVIEWER")
    private String reviewer;

    /** 开票人 */
    @TableField("DRAWER")
    private String drawer;

    /** 创建人 */
    @TableField("CREATE_BY")
    private String createBy;

    /** 创建时间 */
    @TableField("CREATE_TIME")
    private String createTime;

    @TableField("UPDATE_BY")
    private String updateBy;

    /** 更新时间 */
    @TableField("UPDATE_TIME")
    private String updateTime;

    /** 负责人(业务点)*/
    @TableField("RESPONSIBLE_PERSON")
    private String responsiblePerson;
}

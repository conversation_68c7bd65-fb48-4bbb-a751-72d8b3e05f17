package com.ruoyi.contacts.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.barge.domain.bo.BargeUserBO;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.barge.service.BargeConsignService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.*;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.vo.WaterwayCargoVO;
import com.ruoyi.common.enums.*;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.*;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.consignor.domain.vo.ConsignorConsignVO;
import com.ruoyi.consignor.service.ConsignorConsignService;
import com.ruoyi.contacts.service.ContactsConsignService;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description 托运单联系人业务实现
 * <AUTHOR>
 * @Date 2020/8/18  9:22
 */
@Service
@Slf4j
public class ContactsConsignServiceImpl implements ContactsConsignService {

    @Autowired
    private BargeConsignService bargeConsignService;

    @Autowired
    private ConsignorConsignService consignorConsignService;

    @Autowired
    private CargocmentdetailMapper cargocmentdetailMapper;

    @Autowired
    private CargocmentMapper cargocmentMapper;

    @Autowired
    private BargeCenterService bargeCenterService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private WaterwayCargoMapper waterwayCargoMapper;

    @Autowired
    private BargeCheckMessageMapper bargeCheckMessageMapper;

    @Autowired
    private UploadAddressMapper uploadAddressMapper;


    /**
     * 支付（月结、现结）
     *
     * @param cargocmentdetailBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult defray(CargocmentdetailBO cargocmentdetailBO) {
        AjaxResult defray = bargeConsignService.defray(cargocmentdetailBO);
        return defray;
    }

    /**
     * 预约、取消预约
     *
     * @param id
     * @param wxAppOintmentTime
     * @param wxOperateState
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult reservation(Long id, String wxAppOintmentTime, Integer wxOperateState) {
        return bargeConsignService.reservation(id, wxAppOintmentTime, wxOperateState);
    }

    /**
     * 待确认审核
     *
     * @param cargocmentdetailBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void check(CargocmentdetailBO cargocmentdetailBO) {
        Cargoconsignmentdetail cargoconsignmentdetail = new Cargoconsignmentdetail();

        if (StringUtils.isNull(cargocmentdetailBO.getId())) {
            log.error("托运单明细表主键id不能为空");
            throw new CustomException("托运单明细表主键id不能为空");
        }

        Cargoconsignmentdetail cargoconsignmentdetail1 = cargocmentdetailMapper.selectById(cargocmentdetailBO.getId());
        if (StringUtils.isNotEmpty(cargoconsignmentdetail1.getApplyModify())) {
            if (cargoconsignmentdetail1.getApplyModify().equals(BookingNoteStatusEnum.CHARGEBACK_ING_CHECK.getCode().toString())
            || cargoconsignmentdetail1.getApplyModify().equals(BookingNoteStatusEnum.MODIFICATION_ING_CHECK.getCode().toString())) {
                log.info("当前运单正进行退/改单审核，不可操作");
                throw new CustomException("当前运单正进行退/改单审核，不可操作");
            }
        }
        cargoconsignmentdetail.setId(cargocmentdetailBO.getId());
        cargoconsignmentdetail.setWxOperateState(WxOperateStatus.WAIT_PAY.getCode());
        cargoconsignmentdetail.setChargeBalanceType(cargocmentdetailBO.getChargeBalanceType());

        log.info("ContactsConsignServiceImpl - check - 托运单联系人确认托运单(前)：{}", cargoconsignmentdetail);
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 插入消息
        BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();
        bargeCheckMessage.setMType((short) 11);
        bargeCheckMessage.setApplyManId(user.getUserId());
        bargeCheckMessage.setApplyMan(user.getUserName());
        bargeCheckMessage.setApplyTime(new Date());
        bargeCheckMessage.setAuditFlag((short) 0);
        bargeCheckMessage.setBargeName(cargoconsignmentdetail1.getBargeName());
        bargeCheckMessage.setConsignFlag(cargoconsignmentdetail1.getConsignFlag());
        bargeCheckMessage.setRemark("请及时配载托运单");
        bargeCheckMessageMapper.insert(bargeCheckMessage);

        log.info("ContactsConsignServiceImpl - check - 托运单联系人确认托运单(后)：{}", bargeCheckMessage);

        cargocmentdetailMapper.updateById(cargoconsignmentdetail);

    }



    /**
     * 查询托运单列表
     *
     * @param status null.全部， 0.待审核， 4.进行中， 5.已完成， 11.差额退款审批， 1.待支付/预约，2.待确认， 10.待报到
     * @return
     */
    @Override
    public List<ConsignorConsignVO> bookingNoteList(Long status, String searchValue, String beginTime, String endTime) {
        List<ConsignorConsignVO> consignorConsignVOS = new ArrayList<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();

        consignorConsignVOS = cargocmentdetailMapper.contactPersonWaybillList(status, searchValue, user.getPhonenumber(), user.getUserId(), beginTime, endTime);


        // 获取托运单资料
        consignorConsignVOS.forEach(item -> {
            QueryWrapper<UploadAddress> wrapper = new QueryWrapper<>();
            wrapper.eq("LINK_ID", item.getConsignDetailId())
                    .eq("LINK_TYPE",40)
                    .eq("STATUS", 1);
            uploadAddressMapper.selectList(wrapper);

        });

        return consignorConsignVOS;
    }


    /**
     *  水路运单详情
     * @param id
     * @return
     */
    @Override
    public WaterwayCargoVO waybillDetail(Long id, Integer status) {
        WaterwayCargoVO waterwayCargoVO = consignorConsignService.waybillDetail(id, status);
        return waterwayCargoVO;
    }

    /**
     *  差额退款申请
     * @param cargocmentdetailBO
     */
    @Override
    public AjaxResult balanceRefund(CargocmentdetailBO cargocmentdetailBO) {
        /**
         * 1、要判断当前用户是否是支付人
         * 2、现结，且支付金额大于0
         * 3、存在配载数大于实装数的情况
         */
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Boolean flag = false;
        Cargoconsignmentdetail cargoconsignmentdetail = cargocmentdetailMapper.selectById(cargocmentdetailBO.getId());
        //  判断当前用户是否是支付人
        if (!user.getUserId().equals(cargoconsignmentdetail.getPayUserId())) {
            log.error("当前用户不是支付人");
            return AjaxResult.error("您不是该托运单支付人");
        }

        // 判断是否现结
       if (cargoconsignmentdetail.getChargeBalanceType().equals(PayWayEnum.MONTHLY_PAY.getCodeName())) {
           flag = true;
       }

        // 判断是否付金额大于0
        if (FlagBargeState.LEAVING.getCode().equals(cargoconsignmentdetail.getFlagBargeState())) {
            // 根据水路运单号查询水路运单
            List<WaterwayCargo> waterwayCargos = waterwayCargoMapper.selectList(new LambdaQueryWrapper<WaterwayCargo>()
                    .eq(WaterwayCargo::getWaterwayCargoId, cargoconsignmentdetail.getWaterwayCargoId()));

            if (waterwayCargos.size() <= 0) {
                log.error("还没有水路运单");
                return AjaxResult.error("还没有水路运单");
            }

            // 获取到总费用
            String totalCharge = waterwayCargos.get(0).getTotalCharge();

            //判断是否付金额大于0
            BigDecimal bigDecimal = new BigDecimal(totalCharge);
            int i = bigDecimal.compareTo(BigDecimal.ZERO);
            if (i==0 || i==-1) {
                flag = true;
            }
        }

        // 存在配载数大于实装数的情况
        // 配载数
        String rationPiece = cargoconsignmentdetail.getRationPiece();
        BigDecimal bigDecimal = new BigDecimal(rationPiece);

        // 实装数
        String workWeight = cargoconsignmentdetail.getWorkWeight();
        BigDecimal shipBigDecimal = new BigDecimal(workWeight);
        if (StringUtils.isNotNull(bigDecimal) && StringUtils.isNotNull(workWeight)) {
            int result = bigDecimal.compareTo(shipBigDecimal);
            if (result==-1 || result==0) {
                flag = true;
            }
        } else {
            flag = true;
        }

        if (flag) {
            log.error("差额退款失败，没有满足条件");
            throw new CustomException("差额退款失败，没有满足条件");
        } else {
            // 向MESSAGE表插入记录 申请差额退款
            BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();

            bargeCheckMessage.setMType(BargeCheckMessageType.BALANCE_REFUND_AUDIT.getCode().shortValue());
            bargeCheckMessage.setApplyManId(user.getUserId());
            bargeCheckMessage.setApplyMan(user.getUserName());
            bargeCheckMessage.setApplyTime(new Date());
            bargeCheckMessage.setAuditFlag(CheckEnum.WAIT_CHECK.getCode().shortValue());
            bargeCheckMessage.setBargeName(cargoconsignmentdetail.getBargeName());
            bargeCheckMessage.setWaterWayCargoId(cargoconsignmentdetail.getWaterwayCargoId());
            bargeCheckMessage.setPb6CargoConsignmentDetailId(cargoconsignmentdetail.getId());
            bargeCheckMessage.setConsignFlag(cargoconsignmentdetail.getConsignFlag());

            bargeCheckMessageMapper.insert(bargeCheckMessage);

            log.info("差额退款申请成功");
        }

        return AjaxResult.success("差额退款申请成功");
    }


    /**
     *  修改个人信息
     * @param bargeUserBO
     * @return
     */
    @Override
    public AjaxResult personRecord(BargeUserBO bargeUserBO) {
        return bargeCenterService.personRecord(bargeUserBO);
    }

    /**
     *  查看个人信息详情
     * @return
     */
    @Override
    public AjaxResult recordDetail() {

        Map<String, Object> map = new HashMap<>();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysUser personRecord = sysUserMapper.selectUserById(user.getUserId());

        map.put("personRecordDetail",personRecord);
        return AjaxResult.success("获取个人详情成功",map);
    }

}

<!--pages/consigncontact/fapiao/fapiao.wxml-->
<view class="fapiao-page" hover-class="none" hover-stop-propagation="false">
  <view class="fapiao-item" hover-class="none" hover-stop-propagation="false">
    <text class="fapiao-title" selectable="false" space="false" decode="false">发票类型:<text class="require">*</text></text>
    <view class="fapiao-item-label" hover-class="none" hover-stop-propagation="false">
      <radio-group bindchange="handleTypeChange" class="fapiao-type">
        <label class="" wx:for="{{fapiaoTypeList}}" class="fapiao-type-item" wx:key="type">
          <radio value="{{item.type}}" checked="{{item.type == fapiaoType}}" color="#00426B"/>
          <text class="" selectable="false" space="false" decode="false">{{item.name}}</text>
        </label>
      </radio-group>
    </view>
  </view>
  <view class="" hover-class="none" hover-stop-propagation="false">
    <view class="fapiao-item" hover-class="none" hover-stop-propagation="false">
      <text class="fapiao-title" selectable="false" space="false" decode="false">抬头类型:<text class="require">*</text></text>
      <view class="fapiao-item-label" hover-class="none" hover-stop-propagation="false">
        <radio-group bindchange="handleHeadTypeChange" class="fapiao-type">
          <label class="" wx:for="{{fapiaoHeadTypeList}}" class="fapiao-type-item" wx:key="type">
            <radio value="{{item.type}}" checked="{{item.type == fapiaoHeadType}}" color="#00426B"/>
            <text class="" selectable="false" space="false" decode="false">{{item.name}}</text>
          </label>
        </radio-group>
      </view>
    </view>
    <picker bindchange="handlePickChange" class="fapiao-header" value="{{fapiaoIndex}}" range="{{fapiaoHead}}">
      <view class="fapiao-item" hover-class="none" hover-stop-propagation="false">
        <text class="fapiao-title" selectable="false" space="false" decode="false">抬头:<text class="require">*</text></text>
        <view class="fapiao-content" hover-class="none" hover-stop-propagation="false">
          <view class="" hover-class="none" hover-stop-propagation="false">
            {{fapiaoHead[fapiaoIndex]}}
          </view>
          <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{20}}"></mp-icon>
        </view>
      </view>
    </picker>
    <view class="fapiao-item" wx:if="{{fapiaoHeadType==0}}" hover-class="none" hover-stop-propagation="false">
      <text class="fapiao-title" selectable="false" space="false" decode="false">纳税人识别号:<text class="require">*</text></text>
      <input type="text" bindinput="handleValueInput" value="{{saveParamData.taxnum}}" data-key="saveParamData.taxnum" class="fapiao-item-ipt" />
    </view>
    <view class="fapiao-item" hover-class="none" hover-stop-propagation="false">
      <text class="fapiao-title" selectable="false" space="false" decode="false">手机:<text class="require">*</text></text>
      <input type="number" bindinput="handleValueInput" value="{{saveParamData.phone}}" maxlength="11" data-key="saveParamData.phone" class="fapiao-item-ipt" />
    </view>
<!--     <view class="fapiao-item" hover-class="none" wx:if="{{fapiaoHeadType==0}}" hover-stop-propagation="false">
      <text class="fapiao-title" selectable="false" space="false" decode="false">地址:<text class="require">*</text></text>
      <input type="text" bindinput="handleValueInput" value="{{saveParamData.address}}" data-key="saveParamData.address" class="fapiao-item-ipt" />
    </view>
    <view class="fapiao-item" hover-class="none" wx:if="{{fapiaoHeadType==0}}" hover-stop-propagation="false">
      <text class="fapiao-title" selectable="false" space="false" decode="false">银行账号:<text class="require">*</text></text>
      <input type="number" bindinput="handleValueInput" value="{{saveParamData.account}}" maxlength="20" data-key="saveParamData.account" class="fapiao-item-ipt" />
    </view> -->
    <view class="fapiao-item" hover-class="none" hover-stop-propagation="false">
      <text class="fapiao-title" selectable="false" space="false" decode="false">邮箱:</text>
      <input type="text" bindinput="handleValueInput" value="{{saveParamData.email}}" data-key="saveParamData.email" class="fapiao-item-ipt" />
    </view>
  </view>
<!--     <view wx:if="{{fapiaoType == 1}}" class="fapiaotips">提示:开具专票请联系现场业务点处理</view> -->
</view>
<view class="login-wrap" hover-class="none" hover-stop-propagation="false">
  <button class="login-btn" bindtap="handleSubmit" wx:if="{{fapiaoType == 0}}">提交</button>
  <button class="login-btn" bindtap="handleProSubmit" wx:if="{{fapiaoType == 1}}">提交</button>
</view>
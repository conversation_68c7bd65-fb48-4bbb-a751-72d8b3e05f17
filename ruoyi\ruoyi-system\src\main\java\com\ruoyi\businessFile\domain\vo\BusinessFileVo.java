package com.ruoyi.businessFile.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/11 8:26
 */
@Data
public class BusinessFileVo {

    @TableId(value = "file_id",type = IdType.INPUT)
    private String fileId;

    //文件归属类型
    @TableField("file_business_type")
    private String fileBusinessType;

    //放到数据库中
    @TableField("file_business_id")
    private String fileBusinessID;

    @TableField("file_type_name")
    private String fileTypeName;

    @TableField("file_sort")
    private Integer fileSort;

    @TableField("file_name")
    private String fileName;

    @TableField("file_sysid")
    private Long fileSysid;

    private String fileRemark;

    private String url;

}

<!--pages/consignment/consignmanager/consignmanager.wxml-->
<view class="consign-page" hover-class="none" hover-stop-propagation="false">
  <view class="consign-search" hover-class="none" hover-stop-propagation="false">
     <view class="search-area datesearch" hover-class="none" hover-stop-propagation="false">
      <view class="search-area" hover-class="none" hover-stop-propagation="false">
      <input type="text" class="search-ipt" placeholder="出库单号/发货编号"  value="{{searchValue}}" bindinput="searchValueInput"/>
      <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-icon" wx:if="{{searchValue}}" bindtap="deleteSearchValue"></mp-icon>
      <mp-icon class="search-icon" type="field" icon="search" color="#aaaaaa" size="{{30}}" wx:if="{{!searchValue}}"></mp-icon>
    </view>
      <button class="publish-btn search-btn" bindtap="uploadAgent">上传代理</button>
    </view>
  </view>
  <view class="consign-content" hover-class="none" hover-stop-propagation="false">
    <view class="data-item" wx:key="index" wx:for="{{listData}}" hover-class="none" hover-stop-propagation="false" data-item="{{item}}">
      <view class="data-item-title" hover-class="none" hover-stop-propagation="false">
        <view class="item-title-left" hover-class="none" hover-stop-propagation="false">
          <text class="" selectable="false" space="false" decode="false">发货编号：</text>
          <text class="" selectable="false" space="false" decode="false">{{item.deliveryNo}}</text>
        </view>
        <view class="data-item-right" hover-class="none" hover-stop-propagation="false">
          {{item.auditSign === 0 ? '未审核': (item.auditSign === 1 ? '审核通过' : '审核不通过')}}
        </view>
      </view>
      <view class="data-content" hover-class="none" hover-stop-propagation="false">
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">出库单号：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.coutFormId}}</text>
        </view>
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">吨数：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.planWeight}}</text>
        </view>
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">申请时间：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.createTime}}</text>
        </view>
      </view>
      <view class="data-operate" hover-class="none" hover-stop-propagation="false">
        <view>
          <button class="oper-btn" catchtap="updateAgentDelivery" wx:if="{{ item.auditSign === 0 }}" data-item="{{item}}">修改</button>
        </view>
        <view>
          <button class="oper-btn" catchtap="deleteAgentDelivery" wx:if="{{ item.auditSign === 0 }}"  data-item="{{item}}">删除</button>
        </view>
      </view>
    </view>
  </view>
</view>

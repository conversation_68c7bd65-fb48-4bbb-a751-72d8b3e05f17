package com.ruoyi.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.barge.domain.vo.BargeCapacityPublishVO;
import com.ruoyi.barge.domain.vo.BargeConsignDetailVO;
import com.ruoyi.barge.domain.vo.BargeConsignVO;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.vo.CargocmentVO;
import com.ruoyi.common.domain.vo.CargocmentdetailVO;
import com.ruoyi.consignor.domain.vo.ConsignorConsignVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CargocmentdetailMapper extends BaseMapper<Cargoconsignmentdetail> {

    /**
     * 驳船主获取托运单列表
     * @param cargocmentdetailBO
     * @return
     */
    List<BargeConsignVO> getConsignListOfBarge(CargocmentdetailBO cargocmentdetailBO);


    /**
     * 搜索托运单
     * @param searchValue
     * @return
     */
    List<ConsignorConsignVO> searchConsign(@Param("searchValue") String searchValue, @Param("companyName") String companyName,@Param("phone") String phone);

    /**
     *  退/改单审核失败-恢复数据
     * @param cargoconsignmentdetailInfo
     * @return
     */
    int RestoreData(Cargoconsignmentdetail cargoconsignmentdetailInfo);

    // 查询用户派过的驳船
    List<BargeInfo> recordBarge(Long userId);

    /**
     * 退单改单审核列表查询
     * @param cargocmentdetailBO
     * @return
     */
    IPage<CargocmentdetailVO> auditList(Page<CargocmentdetailVO> page, @Param("cargocmentdetailBO") CargocmentdetailBO cargocmentdetailBO);


    IPage<CargocmentdetailVO> getMonthAuditList(Page<CargocmentdetailVO> page, @Param("cargocmentdetailBO") CargocmentdetailBO cargocmentdetailBO);

    /**
     * 根据各个不同的tab栏获取对应的托运单
     * @param cargocmentdetailBO
     * @return
     */
    List<CargocmentVO> bargebillsList(CargocmentdetailBO cargocmentdetailBO);


    // 货主获取托运单列表
    List<ConsignorConsignVO> waybillList(@Param("status") Long status, @Param("searchValue") String searchValue, @Param("companyName") String companyName, @Param("userId") Long userId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);


    // 联系人获取托运单列表
    List<ConsignorConsignVO> contactPersonWaybillList(@Param("status") Long status, @Param("searchValue") String searchValue, @Param("phoneNumber") String phoneNumber, @Param("userId") Long userId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);


    /**
     * 获取驳船主托运单详情
     * @param cargocmentdetailBO
     * @return
     */
    BargeConsignDetailVO getConsignDetail(CargocmentdetailBO cargocmentdetailBO);

    /**
     *  批量修改托运单明细
     * @return
     * @param cargoconsignmentdetails
     */
    int updateList(List<Cargoconsignmentdetail> cargoconsignmentdetails);


    /**
     *  取消改单/取消退单
     */
    int cancelOperation(CargocmentdetailBO cargocmentdetailBO);



}

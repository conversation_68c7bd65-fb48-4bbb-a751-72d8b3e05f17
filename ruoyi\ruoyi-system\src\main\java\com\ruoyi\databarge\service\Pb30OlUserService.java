package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.Pb30OlUser;
import com.ruoyi.databarge.domain.dto.Pb30OlUserSearchDTO;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/11/16.
 * @Date: 2020/11/16 8:35
 */
public interface Pb30OlUserService extends IService<Pb30OlUser> {

    IPage<Pb30OlUser> searchCheckedBargeCompany(Pb30OlUserSearchDTO pb30OlUserSearchDTO);

    IPage<Pb30OlUser> searchNotCheckBargeCompany(Pb30OlUserSearchDTO pb30OlUserSearchDTO);

    List<Pb30OlUser> searchAllClerksByCompanyId(Long companyId);
}

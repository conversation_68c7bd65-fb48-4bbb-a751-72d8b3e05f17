package com.ruoyi.web.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.Invoice.domain.bo.InvoiceBO;
import com.ruoyi.Invoice.service.WxInvoiceService;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.app.controller.support.fdd.FddCompanyService;
import com.ruoyi.app.controller.support.invoice.InvoiceKpVO;
import com.ruoyi.app.controller.support.invoice.InvoiceUtil;
import com.ruoyi.app.controller.support.print.PrintUtil;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.base.service.StampLogService;
import com.ruoyi.common.async.AsyncBaseStamp;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.UploadAddress;
import com.ruoyi.common.enums.PayWayEnum;
import com.ruoyi.common.enums.UserType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.service.FtpService;
import com.ruoyi.common.service.UploadAddressService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.dto.XGPdfDTO;
import com.ruoyi.databarge.service.Pb6BargeworkService;
import com.ruoyi.databarge.service.Pb6CargoconsignmentService;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import oshi.jna.platform.mac.SystemB;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021年06月22日 13:44
 */
@Slf4j
@Component
public class BargeUtils {

    /**
     * 构造私有化
     */
    private BargeUtils() {
    }

    public static final String ALL_FILE = "ALL";

    public static final String WATERWAYCARGO_FILENAME = "水路货物运单.pdf";

    public static final String CARGOHANDOVERCERTIFICATE_FILENAME = "驳船装货交接凭证.pdf";

    public static final String CARGOHANDOVER_FILUNAME = "货物交接清单.pdf";

    // 驳船告知书
    public static final String BARGE_NOTICE_FILENAME = "驳船告知书.pdf";

    // 安全告知书
    public static final String SAFETY_NOTICE_FILENAME = "安全告知书.pdf";

    // 船长声明
    public static final String CAPTAIN_STATEMENT_FILENAME = "船长声明.pdf";

    // 安全检查表
    public static final String SAFETY_CHECKLIST_FILENAME = "安全检查表.pdf";

    // 安全装货确认书
    public static final String SAFETY_LOAD_FILENAME = "安全装货确认书.pdf";

    // 港盛公司 ID
    private static final String LOGISTICS_COMPANY_ID = "26467047e6f2439496e8f43a09d2e2a3";

    // 港盛章文件名
    private static final String LOGISTICS_COMPANY_STAMP = "广州港盛国际船舶代理有限公司业务专用章（8）";

    private static final String PIER_ID = "e194eccf8d364ac68d3b4352f91c1c42";

    private static final String LS_PIER_ID = "6010d5647d894640a4e35f45b3e74652";

    private static final String XG_PIER_ID = "0a1a18f6bbee4ba486afde5fbe53bc1d";

    private static final String LH_PIER_ID = "18e553df273b4afeaa85742eae853a20";//广州外轮理货有限公司

    private static final String PIER_INVENTORY_STAMP = "广州港新沙港务有限公司交接清单章";

    private static final String LS_PIER_INVENTORY_STAMP = "广州港股份有限公司南沙粮食通用码头分公司沿海交接专用章";

    private static final String XG_PIER_INVENTORY_STAMP = "新港沿海交接专用章";

    private static final String XGLH_PIER_INVENTORY_STAMP = "新港理货部电子章";

    private static final String PIER_CERTIFICATE_STAMP = "驳船交接凭证章";

    private static final String XG_PIER_CERTIFICATE_STAMP = "新港驳船装货专用章";

    private static PrintUtil printUtil;

    private static ShipFddUserRelService shipFddUserRelService;

    private static UploadAddressService uploadAddressService;

    private static Pb6BargeworkService pb6BargeworkService;

    private static CargocmentdetailService cargocmentdetailService;

    private static Pb6CargoconsignmentService pb6CargoconsignmentService;

    private static ISysUserService iSysUserService;

    private static BargeCenterService bargeCenterService;

    private static SysUserBargeMapper sysUserBargeMapper;

    private static FddCompanyService fddCompanyService;

    private static FddCommonService fddCommonService;

    private static FtpService ftpService;

    private static WxInvoiceService wxInvoiceService;

    private static StampLogService stampLogService;

    private static InvoiceUtil invoiceUtil;

    @Autowired
    ApplicationContext applicationContext;

    @PostConstruct
    public void init() {
        BargeUtils.printUtil = applicationContext.getBean(PrintUtil.class);
        BargeUtils.shipFddUserRelService = applicationContext.getBean(ShipFddUserRelService.class);
        BargeUtils.uploadAddressService = applicationContext.getBean(UploadAddressService.class);
        BargeUtils.pb6BargeworkService = applicationContext.getBean(Pb6BargeworkService.class);
        BargeUtils.cargocmentdetailService = applicationContext.getBean(CargocmentdetailService.class);
        BargeUtils.pb6CargoconsignmentService = applicationContext.getBean(Pb6CargoconsignmentService.class);
        BargeUtils.iSysUserService = applicationContext.getBean(ISysUserService.class);
        BargeUtils.bargeCenterService = applicationContext.getBean(BargeCenterService.class);
        BargeUtils.sysUserBargeMapper = applicationContext.getBean(SysUserBargeMapper.class);
        BargeUtils.fddCompanyService = applicationContext.getBean(FddCompanyService.class);
        BargeUtils.fddCommonService = applicationContext.getBean(FddCommonService.class);
        BargeUtils.ftpService = applicationContext.getBean(FtpService.class);
        BargeUtils.wxInvoiceService = applicationContext.getBean(WxInvoiceService.class);
        BargeUtils.invoiceUtil = applicationContext.getBean(InvoiceUtil.class);
        BargeUtils.stampLogService = applicationContext.getBean(StampLogService.class);
    }


    /**
     * 根据驳船id查询驳船主印章
     *
     * @param bargeId 驳船id
     * @return 驳船主用户印章实体
     */
    public static ShipFddUserRel getShipFddUserRelOfBargeUser(Long bargeId, Long userId) {

        if (StringUtils.isNotNull(userId)) {
            QueryWrapper<ShipFddUserRel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("TYPE", "1")
                    .eq("SHIP_USER_ID", userId)
                    .eq("REVIEW_STATUS", 1)
                    .eq("SHIP_ID", bargeId);
            // 查找驳船主印章
            ShipFddUserRel shipFddUserRel = shipFddUserRelService.getOne(queryWrapper);
            if (StringUtils.isNotNull(shipFddUserRel)) {
                return shipFddUserRel;
            }
        }

        // （1）先查询驳船主是否有审核通过的印章
        List<ShipFddUserRel> shipFddUserRelList = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                .eq(ShipFddUserRel::getType, "1")
                .eq(ShipFddUserRel::getReviewStatus, "1")
                .eq(ShipFddUserRel::getShipId, bargeId));

        // 若无，再查询物流公司上传的印章
        if (shipFddUserRelList == null || shipFddUserRelList.size() <= 0) {
            shipFddUserRelList = shipFddUserRelService.list(Wrappers.<ShipFddUserRel>lambdaQuery()
                    .eq(ShipFddUserRel::getType, "4")
                    .eq(ShipFddUserRel::getReviewStatus, "1")
                    .eq(ShipFddUserRel::getShipId, bargeId));
        }
        // 3）以上两种情况都没有，则不用盖船章

        return shipFddUserRelList == null || shipFddUserRelList.size() <= 0 ? null : shipFddUserRelList.get(0);
    }

    /**
     * 根据船公司id查找船公司印章
     *
     * @param companyId 船公司id
     * @return 船公司印章实体
     */
    public static ShipFddUserRel getShipFddUserRelOfCompany(Long companyId) {
        SysUser sysUser = getCompanyUser(companyId);
        QueryWrapper<ShipFddUserRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("TYPE", "2")
                .eq("SHIP_USER_ID", sysUser.getUserId())
                .eq("REVIEW_STATUS", 1);
        // 查找船公司印章
        ShipFddUserRel shipFddUserRel = shipFddUserRelService.getOne(queryWrapper);
        // 申请企业实名证书
        if (StringUtils.isNull(shipFddUserRel)) {
            throw new CustomException("盖章查找挂靠公司印章为空");
        }
        if (!"Y".equals(shipFddUserRel.getShipCompanyIsApply())) {
            fddCompanyService.companyCertifiedApply(shipFddUserRel.getFddAccountId(), shipFddUserRel.getVerifiedSerialNo());
            // 更新
            ShipFddUserRel rel = new ShipFddUserRel();
            rel.setId(shipFddUserRel.getId());
            rel.setShipCompanyIsApply("Y");
            shipFddUserRelService.updateById(rel);
        }

        return shipFddUserRel;
    }

    /**
     * 根据水路运单号查询现有盖章文件列表（货物交接清单、水路货物运单、广州港新沙港务有限公司驳船装货交接凭证）
     *
     * @param waterwayCargoId 水路运单编号
     * @return 文件列表
     */
    public static List<UploadAddress> getStampFiles(String waterwayCargoId) {
        return uploadAddressService.getUploadAddressList(null, waterwayCargoId);
    }

    /**
     * 获取原始盖章文件
     *
     * @param waterwayCargoId 水路运单编号
     * @param type            1-广州港新沙港务有限公司驳船装货交接凭证.pdf 2-货物交接清单.pdf 3-水路货物运单.pdf
     * @return HashMap<String, Object> key-filename
     */
    public static HashMap<String, Object> getOriginalStampFile(String waterwayCargoId, Integer type) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        String fileName = "";
        HashMap<String, Object> map = new HashMap<>();
        // Pb6Bargework pb6Bargework = pb6BargeworkService.getBaseMapper().selectOne(new QueryWrapper<Pb6Bargework>().eq("WATERWAYCARGOID", waterwayCargoId));
        // if (pb6Bargework != null) {
        //     map.put("pb6BargeworkId", pb6Bargework.getId());
        // } else {
        //     map.put("pb6BargeworkId", null);
        // }
        switch (type) {
            // case 1:
            //     if (pb6Bargework != null) {
            //         printUtil.printBCJJPZ(pb6Bargework.getId(), byteArrayOutputStream);
            //         fileName = CARGOHANDOVERCERTIFICATE_FILENAME;
            //     }
            //     break;
            case 2:
                printUtil.printHWJJQD(waterwayCargoId, byteArrayOutputStream);
                fileName = CARGOHANDOVER_FILUNAME;
                break;
            case 3:
                printUtil.printSLHWYD(waterwayCargoId, byteArrayOutputStream);
                fileName = WATERWAYCARGO_FILENAME;
                break;
            case 4:
                printUtil.printCBGZS(waterwayCargoId, byteArrayOutputStream);
                fileName = BARGE_NOTICE_FILENAME;
                break;
            case 5:
                printUtil.printAQGZS(waterwayCargoId, byteArrayOutputStream);
                fileName = SAFETY_NOTICE_FILENAME;
                break;
            case 6:
                printUtil.printCZSM(waterwayCargoId, byteArrayOutputStream);
                fileName = CAPTAIN_STATEMENT_FILENAME;
                break;
            case 7:
                printUtil.printACJC(waterwayCargoId, byteArrayOutputStream);
                fileName = SAFETY_CHECKLIST_FILENAME;
                break;
            case 8:
                printUtil.printAQQRS(waterwayCargoId, byteArrayOutputStream);
                fileName = SAFETY_LOAD_FILENAME;
                break;
            default:
                break;
        }
        map.put("fileName", fileName);
        map.put("outStream", byteArrayOutputStream);
        // 关闭流
        IoUtil.close(byteArrayOutputStream);
        return map;
    }


    /**
     * @param
     * @return
     * @description 安全装货确认书盖章
     * <AUTHOR>
     * @date 2024/12/19 16:17
     */
    public static void defrayStampSafetyLoad(String waterwayCargoId) {

        SysUser user = SecurityUtils.getLoginUser().getUser();


        // 查询托运单明细
        Cargoconsignmentdetail detail = cargocmentdetailService.getOne(new QueryWrapper<Cargoconsignmentdetail>()
                .eq("waterwaycargoid", waterwayCargoId));

        if (StringUtils.isNull(detail)) {
            log.error("未找到对应托运单明细数据");
            throw new CustomException("未找到对应托运单明细数据");
        }

        // 查找驳船主
        Long bargeId = detail.getBargeId();
        if (StringUtils.isNull(bargeId)) {
            String msg = StringUtils.format("托运单：{}的BARGEID字段为空，驳船id：{}", waterwayCargoId, bargeId);
            log.error(msg);
            throw new CustomException(msg);
        }
        SysUser bargeUser = getBargeUser(bargeId);
        log.info("驳船主实体：{}", bargeUser);

        // 异步处理盖章
        AsyncBaseStamp.submit(() -> {
            try {
                defrayStampSafetyLoad(detail, bargeUser);
                // 保存盖章日志
                stampLogService.insertStampLog(SAFETY_LOAD_FILENAME, Long.valueOf(waterwayCargoId),"0",null,user.getNickName());
            } catch (Exception e) {
                // 保存盖章日志
                stampLogService.insertStampLog(SAFETY_LOAD_FILENAME, Long.valueOf(waterwayCargoId),"1",e.getMessage(),user.getNickName());
                log.error("报错了", e);
                log.error("驳船告知书：{}，盖{}报错了", waterwayCargoId, SAFETY_LOAD_FILENAME);
                e.printStackTrace();
            }
        });


    }


    /**
     * @param
     * @return
     * @description 告知书盖章
     * <AUTHOR>
     * @date 2024/12/19 16:17
     */
    public static void defrayStampNotice(Pb6Waterwaycargo pb6Waterwaycargo){

        SysUser user = SecurityUtils.getLoginUser().getUser();

        String waterwayCargoId = String.valueOf(pb6Waterwaycargo.getId());

        // 查询托运单明细
        Cargoconsignmentdetail detail = cargocmentdetailService.getOne(new QueryWrapper<Cargoconsignmentdetail>()
                .eq("waterwaycargoid", pb6Waterwaycargo.getId()));

        if (StringUtils.isNull(detail)) {
            log.error("未找到对应托运单明细数据");
            throw new CustomException("未找到对应托运单明细数据");
        }

        // 查找驳船主
        Long bargeId = detail.getBargeId();
        if (StringUtils.isNull(bargeId)) {
            String msg = StringUtils.format("托运单：{}的BARGEID字段为空，驳船id：{}", waterwayCargoId, bargeId);
            log.error(msg);
            throw new CustomException(msg);
        }
        SysUser bargeUser = getBargeUser(bargeId);
        log.info("驳船主实体：{}", bargeUser);

        // 查询托运单
        Pb6Cargoconsignment pb6Cargoconsignment = pb6CargoconsignmentService.getById(detail.getConsignId());

        // 如果是A类货物，则盖四个文件，否则只需要盖章驳船告知书, AB类也算A类
        if("A".equals(pb6Cargoconsignment.getCargoType()) || "AB".equals(pb6Cargoconsignment.getCargoType())){
            // 异步处理盖章
            AsyncBaseStamp.submit(() -> {

                try {
                    defrayStampBargeNotice(detail, bargeUser);
                    // 保存盖章日志
                    stampLogService.insertStampLog(BARGE_NOTICE_FILENAME, pb6Waterwaycargo.getId(),"0",null,user.getNickName());
                } catch (Exception e) {
                    // 保存盖章日志
                    stampLogService.insertStampLog(SAFETY_CHECKLIST_FILENAME, pb6Waterwaycargo.getId(),"1",e.getMessage(),user.getNickName());
                    log.error("报错了", e);
                    log.error("驳船告知书：{}，盖{}报错了", waterwayCargoId, BARGE_NOTICE_FILENAME);
                    e.printStackTrace();
                }
                try {
                    defrayStampSafeNotice(detail, bargeUser);
                    // 保存盖章日志
                    stampLogService.insertStampLog(SAFETY_NOTICE_FILENAME, pb6Waterwaycargo.getId(),"0",null,user.getNickName());
                } catch (Exception e) {
                    // 保存盖章日志
                    stampLogService.insertStampLog(SAFETY_CHECKLIST_FILENAME, pb6Waterwaycargo.getId(),"1",e.getMessage(),user.getNickName());
                    log.error("报错了", e);
                    log.error("安全告知书：{}，盖{}报错了", waterwayCargoId, SAFETY_NOTICE_FILENAME);
                    e.printStackTrace();
                }
                try {
                    defrayStampCaptainNotice(detail, bargeUser);
                    // 保存盖章日志
                    stampLogService.insertStampLog(CAPTAIN_STATEMENT_FILENAME, pb6Waterwaycargo.getId(),"0",null,user.getNickName());
                } catch (Exception e) {
                    // 保存盖章日志
                    stampLogService.insertStampLog(SAFETY_CHECKLIST_FILENAME, pb6Waterwaycargo.getId(),"1",e.getMessage(),user.getNickName());
                    log.error("报错了", e);
                    log.error("船长声明：{}，盖{}报错了", waterwayCargoId, CAPTAIN_STATEMENT_FILENAME);
                    e.printStackTrace();
                }
                try {
                    defrayStampSafeCheckList(detail, bargeUser);
                    // 保存盖章日志
                    stampLogService.insertStampLog(SAFETY_CHECKLIST_FILENAME, pb6Waterwaycargo.getId(),"0",null,user.getNickName());
                } catch (Exception e) {
                    // 保存盖章日志
                    stampLogService.insertStampLog(SAFETY_CHECKLIST_FILENAME, pb6Waterwaycargo.getId(),"1",e.getMessage(),user.getNickName());
                    log.error("报错了", e);
                    log.error("安全检查表：{}，盖{}报错了", waterwayCargoId, SAFETY_CHECKLIST_FILENAME);
                    e.printStackTrace();
                }
            });
        }else{
            // 异步处理盖章
            AsyncBaseStamp.submit(() -> {
                try {
                    defrayStampBargeNotice(detail, bargeUser);
                    // 保存盖章日志
                    stampLogService.insertStampLog(BARGE_NOTICE_FILENAME, pb6Waterwaycargo.getId(),"0",null,user.getNickName());
                } catch (Exception e) {
                    // 保存盖章日志
                    stampLogService.insertStampLog(SAFETY_CHECKLIST_FILENAME, pb6Waterwaycargo.getId(),"1",e.getMessage(),user.getNickName());
                    log.error("报错了", e);
                    log.error("驳船告知书：{}，盖{}报错了", waterwayCargoId, BARGE_NOTICE_FILENAME);
                    e.printStackTrace();
                }
            });
        }


    }


    /**
     * 根据水路运单编号盖章
     *
     * @param waterwayCargoId 水路运单编号
     * @param stampName       需要盖章的文件
     */
    public static void defrayStamp(String waterwayCargoId, String stampName) {

        SysUser user = SecurityUtils.getLoginUser().getUser();

        // 查询托运单明细
        Cargoconsignmentdetail detail = cargocmentdetailService.getOne(new QueryWrapper<Cargoconsignmentdetail>()
                .eq("waterwaycargoid", waterwayCargoId));

        if (StringUtils.isNull(detail)) {
            log.error("未找到对应托运单明细数据");
            throw new CustomException("未找到对应托运单明细数据");
        } else if (PayWayEnum.MONTHLY_PAY.getCodeName().equals(detail.getChargeBalanceType())
                && (StringUtils.isNull(detail.getWxMonthChargeById()) || StringUtils.isBlank(detail.getWxMonthChargeByName()))) {
            String msg = StringUtils.format("托运单：{}，支付方式：{}，月结公司id(wxmonthchagebyid)：{}，月结公司名称(wxmonthchagebyname)：{}。月结公司id和月结公司名称不能为空",
                    waterwayCargoId, detail.getChargeBalanceType(),
                    detail.getWxMonthChargeById(), detail.getWxMonthChargeByName());
            log.error(msg);
            throw new CustomException(msg);
        }

        // 查找驳船主
        Long bargeId = detail.getBargeId();
        if (StringUtils.isNull(bargeId)) {
            String msg = StringUtils.format("托运单：{}的BARGEID字段为空，驳船id：{}", waterwayCargoId, bargeId);
            log.error(msg);
            throw new CustomException(msg);
        }
        SysUser bargeUser = getBargeUser(bargeId);
        log.info("驳船主实体：{}", bargeUser);

        // 异步处理盖章
        switch (stampName) {
            case WATERWAYCARGO_FILENAME:
                AsyncBaseStamp.submit(() -> {
                    try {
                        defrayStampWaterwaycargo(detail, bargeUser);
                        // 保存盖章日志
                        stampLogService.insertStampLog(WATERWAYCARGO_FILENAME, Long.valueOf(waterwayCargoId),"0",null,user.getNickName());
                    } catch (Exception e) {
                        // 保存盖章日志
                        stampLogService.insertStampLog(WATERWAYCARGO_FILENAME, Long.valueOf(waterwayCargoId),"1",e.getMessage(),user.getNickName());
                        log.error("报错了", e);
                        log.error("水路运单：{}，盖{}报错了", waterwayCargoId, WATERWAYCARGO_FILENAME);
                        e.printStackTrace();
                    }
                });
                break;
            case CARGOHANDOVER_FILUNAME:
                AsyncBaseStamp.submit(() -> {
                    try {
                        defrayStampCargoHandover(detail, bargeUser);
                    } catch (Exception e) {
                        log.error("报错了", e);
                        log.error("水路运单：{}，盖{}报错了", waterwayCargoId, CARGOHANDOVER_FILUNAME);
                        e.printStackTrace();
                    }
                });
                break;
            case CARGOHANDOVERCERTIFICATE_FILENAME:
                AsyncBaseStamp.submit(() -> {
                    try {
                        defrayStampCargoHandoverCertificate(detail, bargeUser);
                    } catch (Exception e) {
                        log.error("报错了", e);
                        log.error("水路运单：{}，盖{}报错了", waterwayCargoId, CARGOHANDOVERCERTIFICATE_FILENAME);
                        e.printStackTrace();
                    }
                });
                break;
            case ALL_FILE:
                AsyncBaseStamp.submit(() -> {
                    try {
                        defrayStampWaterwaycargo(detail, bargeUser);
                    } catch (Exception e) {
                        log.error("报错了", e);
                        log.error("水路运单：{}，盖{}报错了", waterwayCargoId, WATERWAYCARGO_FILENAME);
                        e.printStackTrace();
                    }
                });
                AsyncBaseStamp.submit(() -> {
                    try {
                        defrayStampCargoHandover(detail, bargeUser);
                    } catch (Exception e) {
                        log.error("报错了", e);
                        log.error("水路运单：{}，盖{}报错了", waterwayCargoId, CARGOHANDOVER_FILUNAME);
                        e.printStackTrace();
                    }
                });
                AsyncBaseStamp.submit(() -> {
                    try {
                        defrayStampCargoHandoverCertificate(detail, bargeUser);
                    } catch (Exception e) {
                        log.error("报错了", e);
                        log.error("水路运单：{}，盖{}报错了", waterwayCargoId, CARGOHANDOVERCERTIFICATE_FILENAME);
                        e.printStackTrace();
                    }
                });
                break;
            default:
                log.warn("没有对任何文件盖章");
                break;
        }
    }

    /**
     * 船舶告知书盖章
     *
     * @param cargoconsignmentdetail 水路运单明细实体
     * @param
     */
    private static void defrayStampBargeNotice(Cargoconsignmentdetail cargoconsignmentdetail , SysUser bargeUser){
        log.info("开始对{}盖章 =>", BARGE_NOTICE_FILENAME);

        HashMap<String, Object> map = new HashMap<>();
        File cargoFile;
        Long pb6BargeworkId;
        try {
            HashMap<String, Object> cargoMap = getOriginalStampFile(cargoconsignmentdetail.getWaterwayCargoId(), 4);
            pb6BargeworkId = (Long) cargoMap.get("pb6BargeworkId");
            cargoFile = File.createTempFile("cargo_" + UUID.randomUUID(), ".pdf");
            cargoFile.deleteOnExit();
            FileUtil.writeBytes(((ByteArrayOutputStream) cargoMap.get("outStream")).toByteArray(), cargoFile);
        } catch (Exception e) {
            log.error("获取文件失败");
            throw new CustomException("获取文件失败", e);
        }

        // 开始盖章
        String docNo = "YD" + IdUtil.objectId();

        log.info(cargoconsignmentdetail.getWaterwayCargoId()+"开始调用FDD：docNo="+docNo+";cargoFile="+cargoFile);
        // 1.上传并创建合同
        log.info("1.上传并创建合同");
        fddCommonService.uploadAndSaveContract(null, LOGISTICS_COMPANY_ID, docNo, cargoFile);

        ShipFddUserRel shipFddUserRel;

        // 现结盖船主章
        Long bargeId = cargoconsignmentdetail.getBargeId();
        shipFddUserRel = getShipFddUserRelOfBargeUser(bargeId, StringUtils.isNull(bargeUser) ? null : bargeUser.getUserId());

        // 港盛公司章
        // fddCommonService.autoSignNotice(docNo, null, LOGISTICS_COMPANY_ID, LOGISTICS_COMPANY_STAMP, IdUtils.fastSimpleUUID(), 620.0f, 782.0f,2);

        // 船主章
        log.info("3.船主章");
        if (StringUtils.isNotNull(shipFddUserRel)) {
            BargeInfo bargeInfo = getBarge(cargoconsignmentdetail.getBargeId());
            fddCommonService.autoSignNotice(docNo, null, shipFddUserRel.getFddAccountId(), bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 332.5f, 958.6f,2);
        }

        // 检测法大大账号
        log.info("4.检测法大大账号");
        System.out.println(shipFddUserRel);
        checkShipFddUserRel(shipFddUserRel, BARGE_NOTICE_FILENAME);

        // 3.查看合同
        log.info("5.查看合同");
        log.info(fddCommonService.viewContract(docNo));
        // 下载合同
        ByteArrayOutputStream bos = fddCommonService.downloadContract(docNo);
        log.info("6.FDD盖章流程结束");
        // 4.保存合同
        map.put("fileName", BARGE_NOTICE_FILENAME);
        map.put("outStream", bos);
        map.put("pb6BargeworkId", pb6BargeworkId);
        List<HashMap<String, Object>> list = new ArrayList<>();
        list.add(map);
        IoUtil.close(bos);
        Long bargeUserId = StringUtils.isNull(bargeUser) || StringUtils.isNull(bargeUser.getUserId()) ? shipFddUserRel.getShipUserId() : bargeUser.getUserId();
        ftpService.saveNoticeFile(list, cargoconsignmentdetail.getWaterwayCargoId(), bargeUserId, cargoconsignmentdetail.getWxMonthChargeById(), docNo);

    }

    /**
     * 安全告知书盖章
     *
     * @param cargoconsignmentdetail 水路运单明细实体
     * @param
     */
    private static void defrayStampSafeNotice(Cargoconsignmentdetail cargoconsignmentdetail , SysUser bargeUser){
        log.info("开始对{}盖章 =>", SAFETY_NOTICE_FILENAME);

        HashMap<String, Object> map = new HashMap<>();
        File cargoFile;
        Long pb6BargeworkId;
        try {
            HashMap<String, Object> cargoMap = getOriginalStampFile(cargoconsignmentdetail.getWaterwayCargoId(), 5);
            pb6BargeworkId = (Long) cargoMap.get("pb6BargeworkId");
            cargoFile = File.createTempFile("cargo_" + UUID.randomUUID(), ".pdf");
            cargoFile.deleteOnExit();
            FileUtil.writeBytes(((ByteArrayOutputStream) cargoMap.get("outStream")).toByteArray(), cargoFile);
        } catch (Exception e) {
            log.error("获取文件失败");
            throw new CustomException("获取文件失败", e);
        }

        // 开始盖章
        String docNo = "YD" + IdUtil.objectId();

        log.info(cargoconsignmentdetail.getWaterwayCargoId()+"开始调用FDD：docNo="+docNo+";cargoFile="+cargoFile);
        // 1.上传并创建合同
        log.info("1.上传并创建合同");
        fddCommonService.uploadAndSaveContract(null, LOGISTICS_COMPANY_ID, docNo, cargoFile);

        ShipFddUserRel shipFddUserRel;

        // 现结盖船主章
        Long bargeId = cargoconsignmentdetail.getBargeId();
        shipFddUserRel = getShipFddUserRelOfBargeUser(bargeId, StringUtils.isNull(bargeUser) ? null : bargeUser.getUserId());

        // 船主章
        log.info("3.船主章");
        if (StringUtils.isNotNull(shipFddUserRel)) {
            BargeInfo bargeInfo = getBarge(cargoconsignmentdetail.getBargeId());
            fddCommonService.autoSignNotice(docNo, null, shipFddUserRel.getFddAccountId(), bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 532.5f, 988.6f,0);
        }

        // 检测法大大账号
        log.info("4.检测法大大账号");
        System.out.println(shipFddUserRel);
        checkShipFddUserRel(shipFddUserRel, SAFETY_NOTICE_FILENAME);

        // 3.查看合同
        log.info("5.查看合同");
        log.info(fddCommonService.viewContract(docNo));
        // 下载合同
        ByteArrayOutputStream bos = fddCommonService.downloadContract(docNo);
        log.info("6.FDD盖章流程结束");
        // 4.保存合同
        map.put("fileName", SAFETY_NOTICE_FILENAME);
        map.put("outStream", bos);
        map.put("pb6BargeworkId", pb6BargeworkId);
        List<HashMap<String, Object>> list = new ArrayList<>();
        list.add(map);
        IoUtil.close(bos);
        Long bargeUserId = StringUtils.isNull(bargeUser) || StringUtils.isNull(bargeUser.getUserId()) ? shipFddUserRel.getShipUserId() : bargeUser.getUserId();
        ftpService.saveNoticeFile(list, cargoconsignmentdetail.getWaterwayCargoId(), bargeUserId, cargoconsignmentdetail.getWxMonthChargeById(), docNo);

    }

    /**
     * 船长声明盖章
     *
     * @param cargoconsignmentdetail 水路运单明细实体
     * @param
     */
    private static void defrayStampCaptainNotice(Cargoconsignmentdetail cargoconsignmentdetail , SysUser bargeUser){
        log.info("开始对{}盖章 =>", CAPTAIN_STATEMENT_FILENAME);

        HashMap<String, Object> map = new HashMap<>();
        File cargoFile;
        Long pb6BargeworkId;
        try {
            HashMap<String, Object> cargoMap = getOriginalStampFile(cargoconsignmentdetail.getWaterwayCargoId(), 6);
            pb6BargeworkId = (Long) cargoMap.get("pb6BargeworkId");
            cargoFile = File.createTempFile("cargo_" + UUID.randomUUID(), ".pdf");
            cargoFile.deleteOnExit();
            FileUtil.writeBytes(((ByteArrayOutputStream) cargoMap.get("outStream")).toByteArray(), cargoFile);
        } catch (Exception e) {
            log.error("获取文件失败");
            throw new CustomException("获取文件失败", e);
        }

        // 开始盖章
        String docNo = "YD" + IdUtil.objectId();

        log.info(cargoconsignmentdetail.getWaterwayCargoId()+"开始调用FDD：docNo="+docNo+";cargoFile="+cargoFile);
        // 1.上传并创建合同
        log.info("1.上传并创建合同");
        fddCommonService.uploadAndSaveContract(null, LOGISTICS_COMPANY_ID, docNo, cargoFile);

        ShipFddUserRel shipFddUserRel;

        // 现结盖船主章
        Long bargeId = cargoconsignmentdetail.getBargeId();
        shipFddUserRel = getShipFddUserRelOfBargeUser(bargeId, StringUtils.isNull(bargeUser) ? null : bargeUser.getUserId());

        // 船主章
        log.info("3.船主章");
        if (StringUtils.isNotNull(shipFddUserRel)) {
            BargeInfo bargeInfo = getBarge(cargoconsignmentdetail.getBargeId());
            fddCommonService.autoSignNotice(docNo, null, shipFddUserRel.getFddAccountId(), bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 490.5f, 688.6f,0);
        }

        // 检测法大大账号
        log.info("4.检测法大大账号");
        System.out.println(shipFddUserRel);
        checkShipFddUserRel(shipFddUserRel, CAPTAIN_STATEMENT_FILENAME);

        // 3.查看合同
        log.info("5.查看合同");
        log.info(fddCommonService.viewContract(docNo));
        // 下载合同
        ByteArrayOutputStream bos = fddCommonService.downloadContract(docNo);
        log.info("6.FDD盖章流程结束");
        // 4.保存合同
        map.put("fileName", CAPTAIN_STATEMENT_FILENAME);
        map.put("outStream", bos);
        map.put("pb6BargeworkId", pb6BargeworkId);
        List<HashMap<String, Object>> list = new ArrayList<>();
        list.add(map);
        IoUtil.close(bos);
        Long bargeUserId = StringUtils.isNull(bargeUser) || StringUtils.isNull(bargeUser.getUserId()) ? shipFddUserRel.getShipUserId() : bargeUser.getUserId();
        ftpService.saveNoticeFile(list, cargoconsignmentdetail.getWaterwayCargoId(), bargeUserId, cargoconsignmentdetail.getWxMonthChargeById(), docNo);

    }

    /**
     * 安全检查表盖章
     *
     * @param cargoconsignmentdetail 水路运单明细实体
     * @param
     */
    private static void defrayStampSafeCheckList(Cargoconsignmentdetail cargoconsignmentdetail , SysUser bargeUser){
        log.info("开始对{}盖章 =>", SAFETY_CHECKLIST_FILENAME);

        HashMap<String, Object> map = new HashMap<>();
        File cargoFile;
        Long pb6BargeworkId;
        try {
            HashMap<String, Object> cargoMap = getOriginalStampFile(cargoconsignmentdetail.getWaterwayCargoId(), 7);
            pb6BargeworkId = (Long) cargoMap.get("pb6BargeworkId");
            cargoFile = File.createTempFile("cargo_" + UUID.randomUUID(), ".pdf");
            cargoFile.deleteOnExit();
            FileUtil.writeBytes(((ByteArrayOutputStream) cargoMap.get("outStream")).toByteArray(), cargoFile);
        } catch (Exception e) {
            log.error("获取文件失败");
            throw new CustomException("获取文件失败", e);
        }

        // 开始盖章
        String docNo = "YD" + IdUtil.objectId();

        log.info(cargoconsignmentdetail.getWaterwayCargoId()+"开始调用FDD：docNo="+docNo+";cargoFile="+cargoFile);
        // 1.上传并创建合同
        log.info("1.上传并创建合同");
        fddCommonService.uploadAndSaveContract(null, LOGISTICS_COMPANY_ID, docNo, cargoFile);

        ShipFddUserRel shipFddUserRel;

        // 现结盖船主章
        Long bargeId = cargoconsignmentdetail.getBargeId();
        shipFddUserRel = getShipFddUserRelOfBargeUser(bargeId, StringUtils.isNull(bargeUser) ? null : bargeUser.getUserId());

        // 船主章
        log.info("3.船主章");
        if (StringUtils.isNotNull(shipFddUserRel)) {
            BargeInfo bargeInfo = getBarge(cargoconsignmentdetail.getBargeId());
            // 第一页
            fddCommonService.autoSignNotice(docNo, null, shipFddUserRel.getFddAccountId(), bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 288.5f, 208.6f,0);
            // 第二页
            fddCommonService.autoSignNotice(docNo, null, shipFddUserRel.getFddAccountId(), bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 200.5f, 968.6f,1);
        }

        // 检测法大大账号
        log.info("4.检测法大大账号");
        System.out.println(shipFddUserRel);
        checkShipFddUserRel(shipFddUserRel, SAFETY_CHECKLIST_FILENAME);

        // 3.查看合同
        log.info("5.查看合同");
        log.info(fddCommonService.viewContract(docNo));
        // 下载合同
        ByteArrayOutputStream bos = fddCommonService.downloadContract(docNo);
        log.info("6.FDD盖章流程结束");
        // 4.保存合同
        map.put("fileName", SAFETY_CHECKLIST_FILENAME);
        map.put("outStream", bos);
        map.put("pb6BargeworkId", pb6BargeworkId);
        List<HashMap<String, Object>> list = new ArrayList<>();
        list.add(map);
        IoUtil.close(bos);
        Long bargeUserId = StringUtils.isNull(bargeUser) || StringUtils.isNull(bargeUser.getUserId()) ? shipFddUserRel.getShipUserId() : bargeUser.getUserId();
        ftpService.saveNoticeFile(list, cargoconsignmentdetail.getWaterwayCargoId(), bargeUserId, cargoconsignmentdetail.getWxMonthChargeById(), docNo);

    }


    /**
     * 安全装货确认书盖章
     *
     * @param cargoconsignmentdetail 水路运单明细实体
     * @param
     */
    private static void defrayStampSafetyLoad(Cargoconsignmentdetail cargoconsignmentdetail , SysUser bargeUser){
        log.info("开始对{}盖章 =>", SAFETY_LOAD_FILENAME);

        HashMap<String, Object> map = new HashMap<>();
        File cargoFile;
        Long pb6BargeworkId;
        try {
            HashMap<String, Object> cargoMap = getOriginalStampFile(cargoconsignmentdetail.getWaterwayCargoId(), 8);
            pb6BargeworkId = (Long) cargoMap.get("pb6BargeworkId");
            cargoFile = File.createTempFile("cargo_" + UUID.randomUUID(), ".pdf");
            cargoFile.deleteOnExit();
            FileUtil.writeBytes(((ByteArrayOutputStream) cargoMap.get("outStream")).toByteArray(), cargoFile);
        } catch (Exception e) {
            log.error("获取文件失败");
            throw new CustomException("获取文件失败", e);
        }

        // 开始盖章
        String docNo = "YD" + IdUtil.objectId();

        log.info(cargoconsignmentdetail.getWaterwayCargoId()+"开始调用FDD：docNo="+docNo+";cargoFile="+cargoFile);
        // 1.上传并创建合同
        log.info("1.上传并创建合同");
        fddCommonService.uploadAndSaveContract(null, LOGISTICS_COMPANY_ID, docNo, cargoFile);

        ShipFddUserRel shipFddUserRel;

        // 现结盖船主章
        Long bargeId = cargoconsignmentdetail.getBargeId();
        shipFddUserRel = getShipFddUserRelOfBargeUser(bargeId, StringUtils.isNull(bargeUser) ? null : bargeUser.getUserId());

        // 船主章
        log.info("3.船主章");
        if (StringUtils.isNotNull(shipFddUserRel)) {
            BargeInfo bargeInfo = getBarge(cargoconsignmentdetail.getBargeId());
            fddCommonService.autoSignNotice(docNo, null, shipFddUserRel.getFddAccountId(), bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 568.5f, 432.6f,0);
        }

        // 检测法大大账号
        log.info("4.检测法大大账号");
        System.out.println(shipFddUserRel);
        checkShipFddUserRel(shipFddUserRel, SAFETY_LOAD_FILENAME);

        // 3.查看合同
        log.info("5.查看合同");
        log.info(fddCommonService.viewContract(docNo));
        // 下载合同
        ByteArrayOutputStream bos = fddCommonService.downloadContract(docNo);
        log.info("6.FDD盖章流程结束");
        // 4.保存合同
        map.put("fileName", SAFETY_LOAD_FILENAME);
        map.put("outStream", bos);
        map.put("pb6BargeworkId", pb6BargeworkId);
        List<HashMap<String, Object>> list = new ArrayList<>();
        list.add(map);
        IoUtil.close(bos);
        Long bargeUserId = StringUtils.isNull(bargeUser) || StringUtils.isNull(bargeUser.getUserId()) ? shipFddUserRel.getShipUserId() : bargeUser.getUserId();
        ftpService.saveNoticeFile(list, cargoconsignmentdetail.getWaterwayCargoId(), bargeUserId, cargoconsignmentdetail.getWxMonthChargeById(), docNo);

    }

    /**
     * 水路货物运单.pdf 盖章
     *
     * @param cargoconsignmentdetail 水路运单明细实体
     * @param bargeUser              驳船主用户实体
     */
    private static void defrayStampWaterwaycargo(Cargoconsignmentdetail cargoconsignmentdetail, SysUser bargeUser) {
        log.info("开始对{}盖章 =>", WATERWAYCARGO_FILENAME);

        HashMap<String, Object> map = new HashMap<>();
        File cargoFile;
        Long pb6BargeworkId;
        try {
            HashMap<String, Object> cargoMap = getOriginalStampFile(cargoconsignmentdetail.getWaterwayCargoId(), 3);
            pb6BargeworkId = (Long) cargoMap.get("pb6BargeworkId");
            cargoFile = File.createTempFile("cargo_" + UUID.randomUUID(), ".pdf");
            cargoFile.deleteOnExit();
            FileUtil.writeBytes(((ByteArrayOutputStream) cargoMap.get("outStream")).toByteArray(), cargoFile);
        } catch (Exception e) {
            log.error("获取文件失败");
            throw new CustomException("获取文件失败", e);
        }

        ShipFddUserRel shipFddUserRel;
        // 判断支付方式
        if (PayWayEnum.WX_PAY.getCodeName().equals(cargoconsignmentdetail.getChargeBalanceType())) {
            // 现结盖船主章
            Long bargeId = cargoconsignmentdetail.getBargeId();
            shipFddUserRel = getShipFddUserRelOfBargeUser(bargeId, StringUtils.isNull(bargeUser) ? null : bargeUser.getUserId());
        } else {
            // 月结盖船公司章
            shipFddUserRel = getShipFddUserRelOfCompany(cargoconsignmentdetail.getWxMonthChargeById());
        }
        // 开始盖章
        String docNo = "YD" + IdUtil.objectId();

        log.info(cargoconsignmentdetail.getWaterwayCargoId()+"开始调用FDD：docNo="+docNo+";cargoFile="+cargoFile);
        // 1.上传并创建合同
        log.info("1.上传并创建合同");
        fddCommonService.uploadAndSaveContract(null, LOGISTICS_COMPANY_ID, docNo, cargoFile);
        // 物流公司章
        log.info("2.港盛公司章");
        fddCommonService.autoSign(docNo, null, LOGISTICS_COMPANY_ID, LOGISTICS_COMPANY_STAMP, IdUtils.fastSimpleUUID(), 560.0f, 612.0f);
        if (cargoconsignmentdetail.getFlagBargeState().equals("6")){
            // 物流公司对单章
            fddCommonService.autoSign(docNo, null, "66d1507358234975916dfac9fc4e544c", "船代部退单章", IdUtils.fastSimpleUUID(), 530.0f, 460.0f);
        }
        if (PayWayEnum.WX_PAY.getCodeName().equals(cargoconsignmentdetail.getChargeBalanceType())) {
            // 船主章
            log.info("3.船主章");
            if (StringUtils.isNotNull(shipFddUserRel)) {
                BargeInfo bargeInfo = getBarge(cargoconsignmentdetail.getBargeId());
                fddCommonService.autoSign(docNo, null, shipFddUserRel.getFddAccountId(), bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 145.0f, 612.0f);
            }
        } else {
            log.info("3.船公司章");
            // 船公司章
            if (StringUtils.isNotNull(shipFddUserRel)) {
                fddCommonService.autoSign(docNo, null, shipFddUserRel.getFddAccountId(), cargoconsignmentdetail.getWxMonthChargeByName(), IdUtils.fastSimpleUUID(), 145.0f, 612.0f);
            }
        }

        // 检测法大大账号
        log.info("4.检测法大大账号");
        System.out.println(shipFddUserRel);
        checkShipFddUserRel(shipFddUserRel, WATERWAYCARGO_FILENAME);

        // 3.查看合同
        log.info("5.查看合同");
        log.info(fddCommonService.viewContract(docNo));
        // 下载合同
        ByteArrayOutputStream bos = fddCommonService.downloadContract(docNo);
        log.info("6.FDD盖章流程结束");
        // 4.保存合同
        map.put("fileName", WATERWAYCARGO_FILENAME);
        map.put("outStream", bos);
        map.put("pb6BargeworkId", pb6BargeworkId);
        List<HashMap<String, Object>> list = new ArrayList<>();
        list.add(map);
        IoUtil.close(bos);
        Long bargeUserId = StringUtils.isNull(bargeUser) || StringUtils.isNull(bargeUser.getUserId()) ? shipFddUserRel.getShipUserId() : bargeUser.getUserId();
        ftpService.saveCargoFile(list, cargoconsignmentdetail.getWaterwayCargoId(), bargeUserId, cargoconsignmentdetail.getWxMonthChargeById(), docNo);
    }

    /**
     * 货物交接清单.pdf 盖章
     *
     * @param cargoconsignmentdetail 水路运单明细实体
     * @param bargeUser              驳船用户实体
     */
    private static void defrayStampCargoHandover(Cargoconsignmentdetail cargoconsignmentdetail, SysUser bargeUser) {
        log.info("开始对{}盖章 => ", CARGOHANDOVER_FILUNAME);
        File checklistFile;
        Long checkPb6BargeworkId;
        try {
            // 获取清单
            HashMap<String, Object> checklistMap = getOriginalStampFile(cargoconsignmentdetail.getWaterwayCargoId(), 2);
            checkPb6BargeworkId = (Long) checklistMap.get("pb6BargeworkId");
            checklistFile = File.createTempFile("cargo_" + UUID.randomUUID(), ".pdf");
            checklistFile.deleteOnExit();
            FileUtil.writeBytes(((ByteArrayOutputStream) checklistMap.get("outStream")).toByteArray(), checklistFile);
        } catch (Exception e) {
            log.error("获取文件失败");
            throw new CustomException("获取文件失败");
        }

        // 获取船主印章
        ShipFddUserRel shipFddUserRel = getShipFddUserRelOfBargeUser(cargoconsignmentdetail.getBargeId(), StringUtils.isNull(bargeUser) ? null : bargeUser.getUserId());
        String customerId = null;
        if (StringUtils.isNotNull(shipFddUserRel)) {
            customerId = shipFddUserRel.getFddAccountId();
        }

        // 检测法大大账号
        checkShipFddUserRel(shipFddUserRel, CARGOHANDOVER_FILUNAME);

        // 获取船名
        BargeInfo bargeInfo = getBarge(cargoconsignmentdetail.getBargeId());

        // 合同编号
        String checklistFileNo = "QD" + IdUtil.objectId();

        // 1.上传并创建合同
        fddCommonService.uploadAndSaveContract(null, LOGISTICS_COMPANY_ID, checklistFileNo, checklistFile);
        Pb6Cargoconsignment pb6Cargoconsignment=pb6CargoconsignmentService.getOne(Wrappers.<Pb6Cargoconsignment>lambdaQuery().
                eq(Pb6Cargoconsignment::getConsignflag,cargoconsignmentdetail.getConsignFlag()));
        if (pb6Cargoconsignment.getComid()==3||pb6Cargoconsignment.getComid()==16){
            // 物流公司章
            fddCommonService.autoSign(checklistFileNo, null, LOGISTICS_COMPANY_ID, LOGISTICS_COMPANY_STAMP, IdUtils.fastSimpleUUID(), 204.0f,576.5f);

            XGPdfDTO xgPdfDTO=printUtil.searchXGPdfDTO(cargoconsignmentdetail.getWaterwayCargoId());
            if(pb6Cargoconsignment.getComid()==3){
                if (xgPdfDTO.getLhszjs()>0) {
                    //理货章
                    fddCommonService.autoSign(checklistFileNo, null, LH_PIER_ID, XGLH_PIER_INVENTORY_STAMP, IdUtils.fastSimpleUUID(), 682.0f, 576.5f);
                }
                // 码头章
                fddCommonService.autoSign(checklistFileNo, null, XG_PIER_ID, XG_PIER_INVENTORY_STAMP, IdUtils.fastSimpleUUID(), 431.0f,576.5f);
            }else{
                fddCommonService.autoSign(checklistFileNo, null, LS_PIER_ID, LS_PIER_INVENTORY_STAMP, IdUtils.fastSimpleUUID(), 431.0f, 576.5f);
            }

            // 驳船主章
            if (StringUtils.isNotNull(shipFddUserRel)) {
                fddCommonService.autoSign(checklistFileNo, null, customerId, bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 920.0f,576.5f);
            }
        }else{
            // 物流公司章
            fddCommonService.autoSign(checklistFileNo, null, LOGISTICS_COMPANY_ID, LOGISTICS_COMPANY_STAMP, IdUtils.fastSimpleUUID(), 258.0f, 515.0f);

            // 驳船主章
            if (StringUtils.isNotNull(shipFddUserRel)) {
                fddCommonService.autoSign(checklistFileNo, null, customerId, bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 865.0f, 515.0f);
            }

            // 码头章
            if (pb6Cargoconsignment.getComid()==1){
                fddCommonService.autoSign(checklistFileNo, null, PIER_ID, PIER_INVENTORY_STAMP, IdUtils.fastSimpleUUID(), 562.0f, 515.0f);
            }else if(pb6Cargoconsignment.getComid()==3 ||pb6Cargoconsignment.getComid()==4){
                fddCommonService.autoSign(checklistFileNo, null, XG_PIER_ID, XG_PIER_INVENTORY_STAMP, IdUtils.fastSimpleUUID(), 562.0f, 515.0f);
            }

        }



        // 3.查看合同
        log.info(fddCommonService.viewContract(checklistFileNo));

        // 下载合同
        ByteArrayOutputStream bos = fddCommonService.downloadContract(checklistFileNo);

        // 4.保存合同
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("fileName", CARGOHANDOVER_FILUNAME);
        hashMap.put("outStream", bos);
        hashMap.put("pb6BargeworkId", checkPb6BargeworkId);
        List<HashMap<String, Object>> list = new ArrayList<>();
        list.add(hashMap);
        Long bargeUserId = StringUtils.isNull(bargeUser) || StringUtils.isNull(bargeUser.getUserId()) ? shipFddUserRel.getShipUserId() : bargeUser.getUserId();
        ftpService.saveCargoFile(list, cargoconsignmentdetail.getWaterwayCargoId(), bargeUserId, null, checklistFileNo);
    }

    /**
     * 广州港新沙港务有限公司驳船装货交接凭证.pdf 盖章
     *
     * @param cargoconsignmentdetail 水路运单明细实体
     * @param bargeUser              驳船用户实体
     */
    public static void defrayStampCargoHandoverCertificate(Cargoconsignmentdetail cargoconsignmentdetail, SysUser bargeUser) {
        log.info("开始对{}盖章 => ", CARGOHANDOVERCERTIFICATE_FILENAME);

        // 凭证临时文件
        File certificateFile;
        Long certificatePb6BargeworkId;

        try {
            // 获取凭证
            HashMap<String, Object> certificateMap = getOriginalStampFile(cargoconsignmentdetail.getWaterwayCargoId(), 1);
            certificatePb6BargeworkId = (Long) certificateMap.get("pb6BargeworkId");
            certificateFile = File.createTempFile("cargo_" + UUID.randomUUID(), ".pdf");
            certificateFile.deleteOnExit();
            FileUtil.writeBytes(((ByteArrayOutputStream) certificateMap.get("outStream")).toByteArray(), certificateFile);
        } catch (Exception e) {
            log.error("获取文件失败");
            throw new CustomException("获取文件失败");
        }

        // 获取船主印章
        ShipFddUserRel shipFddUserRel = getShipFddUserRelOfBargeUser(cargoconsignmentdetail.getBargeId(), StringUtils.isNull(bargeUser) ? null : bargeUser.getUserId());
        String customerId = null;
        if (StringUtils.isNotNull(shipFddUserRel)) {
            customerId = shipFddUserRel.getFddAccountId();
        }

        // 检测法大大账号
        checkShipFddUserRel(shipFddUserRel, CARGOHANDOVERCERTIFICATE_FILENAME);

        // 获取船名
        BargeInfo bargeInfo = getBarge(cargoconsignmentdetail.getBargeId());

        // 合同编号
        String certificateFileNo = "PZ" + IdUtil.objectId();

        // 1.上传并创建合同
        fddCommonService.uploadAndSaveContract(null, LOGISTICS_COMPANY_ID, certificateFileNo, certificateFile);

        Pb6Cargoconsignment pb6Cargoconsignment=pb6CargoconsignmentService.getOne(Wrappers.<Pb6Cargoconsignment>lambdaQuery().
                eq(Pb6Cargoconsignment::getConsignflag,cargoconsignmentdetail.getConsignFlag()));

        if (pb6Cargoconsignment.getComid()==3||pb6Cargoconsignment.getComid()==16){

            XGPdfDTO xgPdfDTO=printUtil.searchXGPdfDTO(cargoconsignmentdetail.getWaterwayCargoId());
            if (pb6Cargoconsignment.getComid()==3){
                if (xgPdfDTO.getLhszjs()>0){
                    //理货章
                    fddCommonService.autoSign(certificateFileNo, null, LH_PIER_ID, XGLH_PIER_INVENTORY_STAMP, IdUtils.fastSimpleUUID(), 500.0f,495.0f);
                }
                //码头章
                fddCommonService.autoSign(certificateFileNo, null, XG_PIER_ID, XG_PIER_CERTIFICATE_STAMP, IdUtils.fastSimpleUUID(), 238.0f,496.0f);
            }else{
                fddCommonService.autoSign(certificateFileNo, null, LS_PIER_ID, LS_PIER_INVENTORY_STAMP, IdUtils.fastSimpleUUID(), 238.0f, 496.0f);
            }

            // 驳船主章
            if (StringUtils.isNotNull(shipFddUserRel)) {
                fddCommonService.autoSign(certificateFileNo, null, customerId, bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 850.0f,497.0f);
            }
        }else {
            // 驳船主章
            if (StringUtils.isNotNull(shipFddUserRel)) {
                fddCommonService.autoSign(certificateFileNo, null, customerId, bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 805.0f, 472.0f);
            }
            // 码头章
            if (pb6Cargoconsignment.getComid()==1){
                fddCommonService.autoSign(certificateFileNo, null, PIER_ID, PIER_CERTIFICATE_STAMP, IdUtils.fastSimpleUUID(), 338.0f, 472.0f);
            }else if (pb6Cargoconsignment.getComid()==3 ||pb6Cargoconsignment.getComid()==4){
                fddCommonService.autoSign(certificateFileNo, null, XG_PIER_ID, XG_PIER_CERTIFICATE_STAMP, IdUtils.fastSimpleUUID(), 338.0f, 472.0f);
            }

        }


        // 3.查看合同
        log.info(fddCommonService.viewContract(certificateFileNo));

        // 下载合同
        ByteArrayOutputStream baos = fddCommonService.downloadContract(certificateFileNo);

        // 4.保存合同
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("fileName", CARGOHANDOVERCERTIFICATE_FILENAME);
        hashMap.put("outStream", baos);
        hashMap.put("pb6BargeworkId", certificatePb6BargeworkId);
        List<HashMap<String, Object>> list = new ArrayList<>();
        list.add(hashMap);
        Long bargeUserId = StringUtils.isNull(bargeUser) || StringUtils.isNull(bargeUser.getUserId()) ? shipFddUserRel.getShipUserId() : bargeUser.getUserId();
        ftpService.saveCargoFile(list, cargoconsignmentdetail.getWaterwayCargoId(), bargeUserId, null, certificateFileNo);
    }

    /**
     * 根据水路运单编号开票
     *
     * @param waterwayCargoId 水路运单编号
     */
    public static void invoicing(String waterwayCargoId) {
        AsyncBaseStamp.submit(() -> {
            try {
                log.info("开始对{}开票 => ", waterwayCargoId);

                // 获取发票信息
                InvoiceBO invoiceBO = new InvoiceBO();
                invoiceBO.setWaterwayCargoId(waterwayCargoId);
                AjaxResult resultHistory = wxInvoiceService.getInvoice(invoiceBO);
                ShipInvoiceHistory history = (ShipInvoiceHistory) resultHistory.get("data");

                if (StringUtils.isNotNull(history) && "0".equals(history.getInvoicetype())) {

                    // 检查开票条件
                    invoiceBO.setInvoicetype("0");
                    AjaxResult resultCheck = wxInvoiceService.checkInvoice(invoiceBO);

                    if (HttpStatus.SUCCESS == (int) resultCheck.get("code")) {

                        // 开票
                        invoiceBO = new InvoiceBO();
                        BeanUtils.copyBeanProp(invoiceBO, history);
                        AjaxResult resultKp = invoiceUtil.invoicekp(invoiceBO);
                        Object obj = resultKp.get("data");

                        if (StringUtils.isNotNull(obj)) {
                            InvoiceKpVO invoiceKpVO = (InvoiceKpVO) obj;
                            if ("0000".equals(invoiceKpVO.getStatus())) {
                                // 删除发票信息
                                wxInvoiceService.removeById(invoiceBO.getId());
                                log.info("开票成功 <= ");
                            } else {
                                log.error("开票失败 <= " + invoiceKpVO.getMessage());
                            }
                        }
                    } else {
                        log.error("开票失败 <= " + resultCheck.get("msg"));
                    }
                } else {
                    log.error("开票失败 <= " + waterwayCargoId + "不存在发票信息");
                }
            } catch (Exception e) {
                log.error("报错了", e);
                log.error("水路运单：{}，开票报错了", waterwayCargoId);
                e.printStackTrace();
            }
        });
    }

    /**
     * 根据驳船id查找驳船主用户
     *
     * @param bargeId 驳船id主键
     * @return 驳船主用户
     */
    private static SysUser getBargeUser(Long bargeId) {
        return sysUserBargeMapper.queryBargeUserByBargeId(null, bargeId);
    }

    /**
     * 根据公司id查找船公司用户
     *
     * @param companyId 公司id
     * @return 船公司用户
     */
    private static SysUser getCompanyUser(Long companyId) {
        SysUser user = new SysUser();
        user.setCompanyId(companyId);
        user.setUserType(UserType.CARRIERADMIN.getCode());
        return iSysUserService.getUserByPhone(user);
    }

    /**
     * 根据驳船主键id获取驳船信息
     *
     * @param bargeId 驳船主键id
     * @return 驳船实体
     */
    private static BargeInfo getBarge(Long bargeId) {
        return bargeCenterService.getBargeById(bargeId);
    }

    /**
     * 检测发大大账号
     *
     * @param shipFddUserRel 法大大实体
     * @param fileName       文件名称
     */
    private static void checkShipFddUserRel(ShipFddUserRel shipFddUserRel, String fileName) {
        log.info("文件：{}，获取到的法大大实体：{}", fileName, shipFddUserRel);
        if (StringUtils.isNotNull(shipFddUserRel) && StringUtils.isBlank(shipFddUserRel.getFddAccountId())) {
            log.error("洪工！看这里！文件：{}，法大大账号：{}", fileName, shipFddUserRel.getFddAccountId());
            throw new CustomException("法大大账号不能为空");
        }
    }
}

package com.ruoyi.businessFile.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 14:58
 */
@TableName("business_file_type")
@Data
@KeySequence(value = "SEQ_BUSINESS_FILE_TYPE")
public class BusinessFileType extends BaseEntity {

    @TableId(value = "file_type_id",type = IdType.INPUT)
    private Long fileTypeId;

    //文件归属标志
    @TableField("file_business_type")
    private String fileBusinessType;

    @TableField("file_type_name")
    private String fileTypeName;

    @TableField("file_is_must")
    private String fileIsMust;

    @TableField("file_sort")
    private Integer fileSort;

    @TableField("file_is_enable")
    private String fileIsEnable;

    @TableField("file_is_only")
    private String fileIsOnly;

}

package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import com.ruoyi.databarge.domain.UploadAddressDomain;
import com.ruoyi.databarge.domain.dto.BargePictureSearchDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/10/9.
 * @Date: 2020/10/9 14:33
 */
public interface UploadAddressDomainService extends IService<UploadAddressDomain> {

    //数据公司 -->托运单界面查询托运单对应图片
    List<UploadAddressDomain> searchCargoconsignmentPictureByMainId(Long id);

    //数据公司 -->托运单审核改单查询文件关联
    List<UploadAddressDomain> searchLinkFileForUpdateByLinkId(Long linkId);

    //数据公司 -->批量更新数据
    void updateUploadAddressList(List<UploadAddressDomain> uploadAddressList);

    //数据公司 -->消息界面查询驳船备案对应图片
    List<UploadAddressDomain> searchBargePicturesByIdAndStatus(BargePictureSearchDTO bargePictureSearchDTO);

    // 查询驳船附件照片
    List<UploadAddressDomain> searchBargeAttachmentPic(BargePictureSearchDTO bargePictureSearchDTO);

    // 查询驳船需备案下载文件，包括水路运单、驳船通知书等
    List<UploadAddressDomain> searchBargeNeedRecordDownloadFile(Long id);

    //数据公司 -->查询驳船所有人的图片
    List<UploadAddressDomain> searchBargeOwnerIdentityPic(BargePictureSearchDTO bargePictureSearchDTO);

    //数据公司 -->驳船基本信息界面更改驳船图片
    boolean updatePictureForShipInfo(MultipartFile file, Long bargeId, Integer picType) throws SftpException, JSchException, IOException;

    //数据公司 -->驳船基本信息界面更改驳船图片
    boolean uploadAgentDeliveryImage(MultipartFile file, Long agentDeliveryId, Integer isAdd) throws SftpException, JSchException, IOException;

    List<UploadAddressDomain> searchBargeOwnerIdentityPicByUploadUserId(BargePictureSearchDTO bargePictureSearchDTO);

    List<UploadAddressDomain> searchPb6WatercargoFile(String waterwaycargoid);

    List<UploadAddressDomain> searchPb6WatercargoFileApp(String id);
}

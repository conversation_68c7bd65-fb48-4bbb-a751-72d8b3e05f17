package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.PubPort;
import com.ruoyi.databarge.domain.dto.CountryDTO;
import com.ruoyi.databarge.domain.dto.PubPortSearchDTO;

import java.util.List;

/**
 * <p>
 * ??? 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
public interface PubPortService extends IService<PubPort> {

    IPage<PubPort> searchPageByNameAndCountry(PubPortSearchDTO pubPortSearchDTO);

    boolean updateWLInfo(PubPort pubPort);

    List<CountryDTO> searchCountryByCName(String countrycname);

    List<PubPort> searchPortByName(String searchName);

    public String getPortcnameById(Long id);

    List<PubPort> searchWlPortByName(String bargeName);
}

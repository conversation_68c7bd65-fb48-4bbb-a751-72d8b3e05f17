package com.ruoyi.barge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.barge.domain.vo.BargeConsignDetailVO;
import com.ruoyi.barge.domain.vo.BargeConsignVO;
import com.ruoyi.barge.service.BargeConsignService;
import com.ruoyi.carrier.domain.CompanyMonthlyCode;
import com.ruoyi.carrier.mapper.CompanyMonthlyCodeMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.*;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.bo.UserMessageBO;
import com.ruoyi.common.domain.vo.BargeCompanyVO;
import com.ruoyi.common.domain.vo.BargeInfoVO;
import com.ruoyi.common.enums.*;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.*;
import com.ruoyi.common.service.impl.AppNoticeServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.databarge.domain.Pb6Cargoconsignment;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.domain.vo.PortLoadingMsgVO;
import com.ruoyi.databarge.mapper.Pb6BargeCheckMessageMapper;
import com.ruoyi.databarge.mapper.ShipFddUserRelMapper;
import com.ruoyi.databarge.service.Pb6WaterwaycargoService;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/10 10:44
 */
@Slf4j
@Service
public class BargeConsignServiceImpl implements BargeConsignService {

    @Autowired
    private CargocmentdetailMapper cargocmentdetailMapper;
    @Autowired
    private UploadAddressMapper uploadAddressMapper;
    @Autowired
    private AppNoticeServiceImpl appNoticeService;
    @Autowired
    private BargeCheckMessageMapper bargeCheckMessageMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private CompanyMonthlyCodeMapper companyMonthlyCodeMapper;
    @Autowired
    private Pb6BargeCheckMessageMapper pb6BargeCheckMessageMapper;
    @Autowired
    private BargeCompanyMapper bargeCompanyMapper;
    @Autowired
    private WaterwayCargoMapper waterwayCargoMapper;
    @Autowired
    private ShipFddUserRelMapper shipFddUserRelMapper;

    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;

    @Value("${dataLocal.cargo}")
    private String dataLocal;

    /**
     * 获取托运单列表
     *
     * @param cargocmentdetailBO
     * @return
     */
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    @Override
    public AjaxResult getConsignList(CargocmentdetailBO cargocmentdetailBO) {
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userId = loginUser.getUserId();

        cargocmentdetailBO.setUserId(userId);

        List<BargeConsignVO> list = cargocmentdetailMapper.getConsignListOfBarge(cargocmentdetailBO);

        log.info("获取托运单列表成功");

        return AjaxResult.success("获取托运单列表成功", list);
    }

    /**
     * 驳船主托运单详情
     * @param cargocmentdetailBO
     * @return
     */
    @Override
    public AjaxResult getConsignDetail(CargocmentdetailBO cargocmentdetailBO) {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        BargeConsignDetailVO detail = cargocmentdetailMapper.getConsignDetail(cargocmentdetailBO);
        QueryWrapper<UploadAddress> wrapper = new QueryWrapper<>();
        wrapper.eq("LINK_ID", detail.getConsignId())
                .eq("LINK_TYPE", UploadDataType.CONSIGN_DATA.getType())
                .eq("STATUS", 1)
                .eq("BAK_FLAG", 1);
        List<UploadAddress> list = uploadAddressMapper.selectList(wrapper);
        list.forEach(item -> {
            String url = item.getUrl();
            String[] u = url.split("/olorder");
            item.setUrl(dataLocal + u[1]);
        });

        // 查找驳船挂靠公司名
        BargeInfoVO bargeInfoVO = sysUserMapper.queryBargeByUserId(userId);

        // 2021-08-23 洪工要求更改这里的取值
//        BargeCompanyVO bargeCompanyVO = bargeCompanyMapper.queryCompanyByBargeId(bargeInfoVO.getId(), userId);
//        if (bargeCompanyVO != null) {
//            detail.setBargeCallCompanyName(bargeCompanyVO.getCFullName());
//        }
        List<String> name = shipFddUserRelMapper.selectTtByBargeId(bargeInfoVO.getId());
        name.forEach(item ->{
            if(StringUtils.isNotBlank(item)){
                detail.setBargeCallCompanyName(item);
            }
        });
        // 2021-08-23 洪工要求更改这里的取值

        detail.setDataList(list);
        PortLoadingMsgVO portLoadingMsgVO = pb6BargeCheckMessageMapper.searchPortLoadingInfo(detail.getWaterWayCargoId());
        if (portLoadingMsgVO != null) {
            detail.setTscargoweightValue(portLoadingMsgVO.getTscargoweightValue());
        }
        return AjaxResult.success(detail);
    }


    /**
     * 驳船组确认
     * @param cargocmentdetailBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult confirmConsign(CargocmentdetailBO cargocmentdetailBO) {
        // 判断当前托运单是否可操作
        boolean bl = this.isChargeback(cargocmentdetailBO.getId());
        if (bl) {
            log.info("当前运单正进行退/改单审核，不可操作");
            return AjaxResult.error("当前运单正进行退/改单审核，不可操作");
        }

        SysUser user = SecurityUtils.getLoginUser().getUser();

        Cargoconsignmentdetail cargoconsignmentdetail = cargocmentdetailMapper.selectById(cargocmentdetailBO.getId());

        log.info("BargeConsignServiceImpl - confirmConsign - 托运单实体(确认前)：{}", cargoconsignmentdetail);

        Cargoconsignmentdetail detail = new Cargoconsignmentdetail();
        detail.setId(cargocmentdetailBO.getId());
        // detail.setChargeBalanceType(PayWayEnum.codeToCodeName(cargocmentdetailBO.getChargeBalanceType()));
        detail.setChargeBalanceType("现结");
        if (ConfirmEnum.CONFIRM.getCode().equals(cargocmentdetailBO.getState())) {
            detail.setWxOperateState(WxOperateStatus.PASS_BARGE_RESERVE.getCode());
        } else {
            detail.setRationWeight(cargocmentdetailBO.getRationWeight());
            detail.setWxConfirmCheck(CheckEnum.WAIT_CHECK.getCode());
        }

        // Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getById(detail.getWaterwayCargoId());
        // if (pb6Waterwaycargo != null) {
        //     pb6Waterwaycargo.setFlagbargestate("6");
        //     pb6WaterwaycargoService.updateById(pb6Waterwaycargo);
        // }

        // 驳船状态设置为已确认
        detail.setFlagBargeState("6");
        // 设置确认时间
        detail.setWxConfirmTime(new Date());

        cargocmentdetailMapper.updateById(detail);

        // 插入消息
        BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();
        bargeCheckMessage.setMType((short) 11);
        bargeCheckMessage.setApplyManId(user.getUserId());
        bargeCheckMessage.setApplyMan(user.getUserName());
        bargeCheckMessage.setApplyTime(new Date());
        bargeCheckMessage.setAuditFlag((short) 0);
        bargeCheckMessage.setBargeName(cargoconsignmentdetail.getBargeName());
        bargeCheckMessage.setConsignFlag(cargoconsignmentdetail.getConsignFlag());
        // bargeCheckMessage.setRemark("请及时配载托运单");
        bargeCheckMessageMapper.insert(bargeCheckMessage);

        log.info("BargeConsignServiceImpl - confirmConsign - 插入PB6_BARGE_CHECK_MESSAGE表实体：{}", bargeCheckMessage);
        log.info("BargeConsignServiceImpl - confirmConsign - 驳船：{} 插入PB6_BARGE_CHECK_MESSAGE表,id => {}", cargoconsignmentdetail.getBargeName(), bargeCheckMessage.getId());

        return AjaxResult.success("成功", bargeCheckMessage.getId().toString());
    }

    @Override
    public AjaxResult cancelConfirmConsign(CargocmentdetailBO cargocmentdetailBO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();

        Cargoconsignmentdetail cargoconsignmentdetail = cargocmentdetailMapper.selectById(cargocmentdetailBO.getId());

        log.info("BargeConsignServiceImpl - confirmConsign - 托运单实体(确认前)：{}", cargoconsignmentdetail);

        cargoconsignmentdetail.setWxOperateState(WxOperateStatus.WAIT_BARGE_CONFIRM.getCode());

        // 驳船状态设置为已配载
        cargoconsignmentdetail.setFlagBargeState("3");

        cargocmentdetailMapper.updateById(cargoconsignmentdetail);

        // 插入消息
        BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();
        bargeCheckMessage.setMType((short) 10);
        bargeCheckMessage.setApplyManId(user.getUserId());
        bargeCheckMessage.setApplyMan(user.getUserName());
        bargeCheckMessage.setApplyTime(new Date());
        bargeCheckMessage.setAuditFlag((short) 0);
        bargeCheckMessage.setBargeName(cargoconsignmentdetail.getBargeName());
        bargeCheckMessage.setConsignFlag(cargoconsignmentdetail.getConsignFlag());
        bargeCheckMessage.setRemark("驳船组取消确认托运单");
        bargeCheckMessageMapper.insert(bargeCheckMessage);

        log.info("BargeConsignServiceImpl - confirmConsign - 插入PB6_BARGE_CHECK_MESSAGE表实体：{}", bargeCheckMessage);

        return AjaxResult.success("成功", bargeCheckMessage.getId().toString());
    }

    /**
     * 支付
     * @param cargocmentdetailBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult defray(CargocmentdetailBO cargocmentdetailBO) {
        // 判断当前托运单是否可操作
        boolean bl = this.isChargeback(cargocmentdetailBO.getId());
        if (bl) {
            log.info("当前运单正进行退/改单审核，不可操作");
            return AjaxResult.error("当前运单正进行退/改单审核，不可操作");
        }

        // 判断是否可以支付
        boolean blPay = this.checkPay(cargocmentdetailBO.getId());
        if (blPay) {
            log.info("当前运单已离港/已退单/已改单，不可操作");
            return AjaxResult.error("当前运单已离港/已退单/已改单，不可操作");
        }

        /**
         * 1.判断当前是否已经配载，如果没有配载则提示-当前托运没有配载，请联系管理员进行配载
         * 2.判断支付方式
         *      2.1 月结
         *          发送消息给船公司
         *      2.2 微信支付现结
         */

        // 1.判断当前是否已经配载
        Cargoconsignmentdetail detail = cargocmentdetailMapper.selectById(cargocmentdetailBO.getId());
        if (detail == null || !FlagBargeState.STOWAGE.getCode().equals(detail.getFlagBargeState())) {
            log.error("当前托运没有配载，请联系管理员进行配载");
            return AjaxResult.error("当前托运没有配载，请联系管理员进行配载");
        } else if (!"Y".equals(detail.getIsOffline())) {
            log.error("当前订单不能支付");
            return AjaxResult.error("当前订单不能支付");
        }
        log.warn("水路运单编号：{}，isOffline:{}", detail.getWaterwayCargoId(), detail.getIsOffline());

        // 2.判断支付方式
        String patWay = cargocmentdetailBO.getChargeBalanceType();

        if (PayWayEnum.MONTHLY_PAY.getCode().equals(patWay)) {
            // 2.1 月结

            Long companyId = cargocmentdetailBO.getCompanyId();
            if (null == companyId) {
                log.error("月结方式，船公司不能为空");
                return AjaxResult.error("月结方式，船公司不能为空");
            }

            // 月结码
            String monthlyCode = cargocmentdetailBO.getMonthlyCode();

            Cargoconsignmentdetail consign = new Cargoconsignmentdetail();
            consign.setId(cargocmentdetailBO.getId());
            consign.setChargeBalanceType(PayWayEnum.MONTHLY_PAY.getCodeName());
            consign.setWxMonthChargeById(cargocmentdetailBO.getCompanyId());
            consign.setWxMonthChargeByName(cargocmentdetailBO.getCompanyName());
            consign.setPayUserId(SecurityUtils.getLoginUser().getUser().getUserId());
            consign.setMthApplyCheckTime(DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss"));
            if (StringUtils.isBlank(monthlyCode)) {
                consign.setWxOperateState(WxOperateStatus.WAIT_COMPANY_CHECK.getCode());
            } else {
                UpdateWrapper<CompanyMonthlyCode> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("company_id", companyId)
                        .eq("monthly_code", monthlyCode)
                        .eq("IS_USED", "0");

                // 判断该月结码是否被使用
                CompanyMonthlyCode code = companyMonthlyCodeMapper.selectOne(updateWrapper);
                if (com.ruoyi.common.utils.StringUtils.isNull(code)) {
                    log.info("月结码"+monthlyCode+"不正确或已被使用");
                    return AjaxResult.error("月结码"+monthlyCode+"不正确或已被使用");
                }
                consign.setWxOperateState(WxOperateStatus.WAIT_BARGE_RESERVE.getCode());
                consign.setMonthlyCodeId(code.getId());
            }

            int result = cargocmentdetailMapper.updateById(consign);

            if (result > 0 && StringUtils.isNotBlank(monthlyCode)) {
                // 更新月结码使用状态

                SysUser sysUser = SecurityUtils.getLoginUser().getUser();

                CompanyMonthlyCode companyMonthlyCode = new CompanyMonthlyCode();
                companyMonthlyCode.setCompanyId(companyId);
                companyMonthlyCode.setMonthlyCode(monthlyCode);
                companyMonthlyCode.setUseUserId(sysUser.getUserId());
                companyMonthlyCode.setUseCompanyId(sysUser.getCompanyId());
                companyMonthlyCode.setUsedTime(DateUtils.getNowDate());
                companyMonthlyCode.setIsUsed("1");
                companyMonthlyCode.setUseBargeId(cargocmentdetailBO.getBargeId());

                int codeResult = companyMonthlyCodeMapper.updateCode(companyMonthlyCode);

                if (codeResult <= 0) {
                    log.info("月结码" + monthlyCode + "已被使用");
                    throw new CustomException("月结码" + monthlyCode + "已被使用");
                }

                // pb6_waterwaycargo.Cargosize写入月结公司名称
                WaterwayCargo waterwayCargo = waterwayCargoMapper.selectOne(new QueryWrapper<WaterwayCargo>()
                        .eq("WATERWAYCARGOID", cargocmentdetailBO.getWaterwayCargoId()));
                waterwayCargo.setCARGOSIZE(cargocmentdetailBO.getCompanyName());
                //把水路运单更改为月结
                waterwayCargo.setCHARGEBALANCETYPE(PayWayEnum.MONTHLY_PAY.getCodeName());
                waterwayCargoMapper.updateById(waterwayCargo);
            }


            // 发送消息给船公司 全体人员
            List<Long> userIds = sysUserMapper.selectUserListByCompanyId(companyId);
            UserMessageBO userMessageBO = new UserMessageBO();
            userMessageBO.setSendMessageType(SendMessageEnum.BARGE_MONTHLY_CHECK.getCode());
            //userMessageBO.setUserId(cargocmentdetailBO.getCompanyId());
            userMessageBO.setUserIds(userIds);
            HashMap<String, Object> map = new HashMap<>();
            map.put("consignFlag", detail.getConsignFlag());
            map.put("name", SecurityUtils.getLoginUser().getUser().getNickName());
            map.put("bargeName", detail.getBargeName());
            userMessageBO.setParams(map);
            appNoticeService.sendMessage(userMessageBO);

            log.info("月结成功");
            return AjaxResult.success("月结成功", cargocmentdetailBO);

        } else if(PayWayEnum.WX_PAY.getCode().equals(patWay)) {
            Cargoconsignmentdetail consign = new Cargoconsignmentdetail();
            consign.setId(cargocmentdetailBO.getId());
            consign.setChargeBalanceType(PayWayEnum.WX_PAY.getCodeName());
            consign.setPayUserId(SecurityUtils.getLoginUser().getUser().getUserId());
            cargocmentdetailMapper.updateById(consign);

            // 修改pb6_waterwaycargo
            WaterwayCargo waterwayCargo = waterwayCargoMapper.selectOne(new LambdaQueryWrapper<WaterwayCargo>()
                    .eq(WaterwayCargo::getWaterwayCargoId, cargocmentdetailBO.getWaterwayCargoId()));
            waterwayCargo.setCHARGEBALANCETYPE(PayWayEnum.WX_PAY.getCodeName());
            waterwayCargo.setCARGOSIZE(null);
            waterwayCargoMapper.updateById(waterwayCargo);

            return AjaxResult.success("现结成功", cargocmentdetailBO);
        }

        log.error("支付失败，支付方式不正确");
        return AjaxResult.error("支付失败，支付方式不正确");
    }

    /**
     * 预约
     * @param id 托运单明细主键id
     * @param wxAppOintmentTime 预约时间
     * @param wxOperateState 预约、取消预约
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult reservation(Long id, String wxAppOintmentTime, Integer wxOperateState) {
        // 判断当前托运单是否可操作  2021.11.23 jinn 添加预约限制
        boolean bl = this.isChargeback(id);
        if (bl) {
            log.info("当前运单正进行退/改单审核，不可操作");
            return AjaxResult.error("当前运单正进行退/改单审核，不可操作");
        }

        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        Cargoconsignmentdetail detail = new Cargoconsignmentdetail();
        detail.setId(id);

        if (WxOperateStatus.PASS_BARGE_RESERVE.getCode().equals(wxOperateState)) {
            if (StringUtils.isBlank(wxAppOintmentTime)) {
                log.info("预约时间不能为空");
                return AjaxResult.error("预约失败，预约时间不能为空");
            }
            detail.setWxAppointmentTime(wxAppOintmentTime);
            detail.setWxOperateState(WxOperateStatus.PASS_BARGE_RESERVE.getCode());
        } else if (WxOperateStatus.CANCEL_RESERVE.getCode().equals(wxOperateState)) {
            detail.setWxOperateState(WxOperateStatus.WAIT_BARGE_RESERVE.getCode());
        } else {
            log.info("预约失败");
            return AjaxResult.error("预约失败");
        }
        detail.setWxOperateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        detail.setWxointmentmId(userId);

        cargocmentdetailMapper.updateById(detail);

        log.info("预约成功");

        return AjaxResult.success("预约成功");
    }

    /**
     * 申请差额退款
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult refund(CargocmentdetailBO cargocmentdetailBO) {
        Long id = cargocmentdetailBO.getId();

        //根据id查找托运单明细xinxi
        Cargoconsignmentdetail detail = cargocmentdetailMapper.selectById(id);
        if (detail == null) {
            //throw new CustomException("此运单明细不存在");
            log.error("此运单明细不存在");
            return AjaxResult.error("此运单明细不存在");
        }

        String chargeBalanceType = detail.getChargeBalanceType();

        // 配载吨数
        String rationWeight = detail.getRationWeight();

        // 实际吨数
        String workWeight = detail.getWorkWeight();

        if (StringUtils.isNotBlank(workWeight) && StringUtils.isNotBlank(rationWeight)
                && Double.parseDouble(workWeight) >= Double.parseDouble(rationWeight)) {
            return AjaxResult.error("实装数大于配载吨数");
        }

        if (!PayWayEnum.WX_PAY.getCodeName().equals(chargeBalanceType)) {
            return AjaxResult.error("支付方式为月结不能申请差额退款");
        }

        // 走差额退款流程

        return null;
    }

    /**
     * 判断托运单明细是否退改单中  2021.11.23 jinn 添加预约限制
     * @param detailId 托运单明细主键id
     * @return false-未进行退改单，true-退改单
     */
    private boolean isChargeback(Long detailId) {
        Cargoconsignmentdetail detail = cargocmentdetailMapper.selectById(detailId);

        //2021.11.23 jinn 添加预约限制
        if(StringUtils.isNotBlank(detail.getWaterwayCargoId())){
            Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.searchWaterwaycargoByWaterwaycargoid(detail.getWaterwayCargoId());
            pb6WaterwaycargoService.canOrderOrForcast(pb6Waterwaycargo, 1);
        }
        //2021.11.23 jinn 添加预约限制
        if (StringUtils.isNotBlank(detail.getApplyModify()) ) {
            switch (detail.getApplyModify()) {
                case "0":
                case "1":
                case "3":
                    return true;
                default:
                    return false;
            }
        }
        return false;
    }

    /**
     * 支付检测 离港或者退单改单的不能支付
     *
     * @param detailId 托运单明细主键id
     * @return false-能支付，true-不能支付
     */
    private boolean checkPay(Long detailId) {
        Cargoconsignmentdetail detail = cargocmentdetailMapper.selectById(detailId);
        if (StringUtils.isNotBlank(detail.getFlagBargeState())) {
            switch (detail.getFlagBargeState()) {
                case "5":
                case "6":
                case "7":
                    return true;
                default:
                    return false;
            }
        }
        return false;
    }


}

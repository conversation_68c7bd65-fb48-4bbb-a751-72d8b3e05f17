package com.ruoyi.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.BargeInfoAudit;

/**
 * @Description 运单接口
 * <AUTHOR>
 * @Date 2020/8/5 9:53
 */
public interface BargeInfoAuditService extends IService<BargeInfoAudit> {

    int addCarrierBarge(BargeInfoAudit bargeInfoAudit);
    AjaxResult addBarge(BargeInfoAudit bargeInfoAudit);
    //BargeInfoVO getBargeResource(BargeInfoAudit bargeInfoAudit);

    AjaxResult updateBarge(BargeInfoAudit bargeInfoAudit);
}

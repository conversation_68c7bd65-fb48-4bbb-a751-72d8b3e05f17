package com.ruoyi.barge.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/11 11:17
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class BargeCapacityPublishVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 驳船主键id
     */
    private Long bargeId;

    /**
     * 驳船名称
     */
    private String bargeName;

    /**
     * 驳船识别号
     */
    private String bargeIdentifier;

    /**
     * 电话
     */
    private String phone;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 运力，吨
     */
    private String capacity;
}

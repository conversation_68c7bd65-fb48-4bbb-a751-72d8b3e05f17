package com.ruoyi.app.controller.support.fdd;

import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fdd.api.client.dto.*;
import com.fdd.api.client.release.base.ClientFactory;
import com.fdd.api.client.res.RestResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.uuid.IdUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/10/16 11:39
 * @Description: FDD公共服务类
 */
@Service
@AllArgsConstructor
@Slf4j
public class FddCommonService {

    private ClientFactory clientFactory;

    /**
     * 新增印章
     * @param customerId FDD账户customerId
     * @param sealName 印章名
     * @param file 印章文件
     */
    public void uploadAndSaveSeal(String customerId, String sealName, File file) {
        UploadAndSaveSealDTO uploadAndSaveSealDTO=new UploadAndSaveSealDTO();
        uploadAndSaveSealDTO.setAccountId(customerId);// 客户编号
        uploadAndSaveSealDTO.setName(sealName);// 印章名
        uploadAndSaveSealDTO.setFile(file);// 印章文件
        uploadAndSaveSealDTO.setSealExtension("png");// 印章扩展名
        uploadAndSaveSealDTO.setImageMatting(1);// 印章图片背景是否调整为透明，0否（默认），1是(1自动调整大小)

        try {
            RestResult restResult=clientFactory.multifunctionClient().uploadAndSaveSeal(uploadAndSaveSealDTO);// RestResult{code='1', msg='添加印章成功', data=null}
            if("1".equals(restResult.getCode())){
                // nothing to do
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除印章
     * @param customerId FDD账户customerId
     * @param sealName 印章名
     */
    public void deleteSeal(String customerId,String sealName) {
        SealNameDTO sealNameDTO=new SealNameDTO();
        sealNameDTO.setAccountId(customerId);// 客户编号
        sealNameDTO.setName(sealName);// 印章名

        try {
            RestResult restResult=clientFactory.sealClient().deleteSeal(sealNameDTO);// RestResult{code='1', msg='删除印章成功', data=null}
            if("1".equals(restResult.getCode())){
                // nothing to do
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除所有印章
     * @param customerId FDD账户customerId
     */
    public void deleteAllSeal(String customerId) {
        this.sealList(customerId).stream().forEach(item->{
            this.deleteSeal(customerId,item.getName());
        });
    }

    /**
     * 修改印章
     * @param accountNo FDD账户customerId
     * @param sealName 印章名
     * @param file 印章文件
     */
    public void uploadAndUpdateSeal(String accountNo,String sealName,File file){
        log.info("修改印章：账号："+accountNo+"章名："+sealName);
        UploadAndUpdateSealDTO uploadAndUpdateSealDTO=new UploadAndUpdateSealDTO();
        uploadAndUpdateSealDTO.setAccountId(accountNo);// 需修改的印章所属的客户编号
        uploadAndUpdateSealDTO.setName(sealName);// 原有印章名称
        uploadAndUpdateSealDTO.setFile(file);// 印章文件
        uploadAndUpdateSealDTO.setSealExtension("png");// 印章扩展名
        uploadAndUpdateSealDTO.setImageMatting(0);// 印章图片背景是否调整为透明，0否（默认），1是
        try {
            RestResult restResult=clientFactory.multifunctionClient().uploadAndUpdateSeal(uploadAndUpdateSealDTO);// RestResult{code='1', msg='替换印章成功', data=null}
            if("1".equals(restResult.getCode())){
                // nothing to do
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询所有印章
     * @param customerId FDD账户customerId
     */
    public List<CompanySealVO> sealList(String customerId){
        AccountIdDTO accountIdDTO=new AccountIdDTO();
        accountIdDTO.setAccountId(customerId);// 客户编号

        try {
            RestResult restResult=clientFactory.sealClient().sealList(accountIdDTO);// RestResult{code='1', msg='操作成功', data=[{"accountId":"524945f093fd40919864db339fb3c99e","path":"20200921161243_6df598baaae347bd9","isDefault":false,"createdDate":"2020-09-21 16:12:44","name":"gzport公章100","id":"0338c070b1284d6da10350d19f3615a1"},{"accountId":"524945f093fd40919864db339fb3c99e","path":"20200918145329_bb8c58b60add497b9","isDefault":true,"createdDate":"2020-09-18 14:53:30","name":"广州港测试","id":"3e40c5b34de7486e9372966a827dbd60"}]}
            if("1".equals(restResult.getCode())){
                JSONArray jsonArray=(JSONArray)restResult.getData();
                Iterator iterator=jsonArray.iterator();
                List<CompanySealVO> companySealVOList=new ArrayList<>(jsonArray.size());
                while (iterator.hasNext()){
                    JSONObject jsonObject=(JSONObject)iterator.next();
                    companySealVOList.add(new CompanySealVO(
                            jsonObject.getString("id"),
                            jsonObject.getString("name"),
                            jsonObject.getString("createdDate"),
                            jsonObject.getBoolean("isDefault"),
                            jsonObject.getString("path"),
                            jsonObject.getString("accountId")
                    ));
                }
                return companySealVOList;
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 下载印章
     * @param customerId 客户编号
     * @param name 印章名
     */
    public ByteArrayInputStream downloadSeal(String customerId,String name){
        SealNameDTO sealNameDTO=new SealNameDTO();
        sealNameDTO.setAccountId(customerId);
        sealNameDTO.setName(name);

        try {
            RestResult restResult=clientFactory.sealClient().downloadSeal(sealNameDTO);// RestResult{code='1', msg='操作成功', data=java.io.ByteArrayInputStream@f06a39e}
            if("1".equals(restResult.getCode())){
                ByteArrayInputStream byteArrayInputStream=(ByteArrayInputStream)restResult.getData();
                return byteArrayInputStream;
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 上传并创建合同
     * @param personCustomerId 文档个人归属者客户编号
     * @param companyCustomerId 文档企业归属者客户编号
     * @param docNo 合同编号
     * @param file 合同文件
     */
    public void uploadAndSaveContract(String personCustomerId,String companyCustomerId,String docNo,File file){
        log.info("文档个人归属者客户编号:"+personCustomerId+"归属者客户编号:"+companyCustomerId+"合同编号:"+docNo);
        int lastPointIndex=file.getName().lastIndexOf('.');
        String fileName=file.getName().substring(0,lastPointIndex);
        String fileType=file.getName().substring(lastPointIndex);
        UploadAndSaveContractDTO uploadAndSaveContractDTO=new UploadAndSaveContractDTO();
        uploadAndSaveContractDTO.setDocType(fileType);// 文件后缀，如.doc,.png
        uploadAndSaveContractDTO.setUploadType(1);// 上传类型，1文件，2图片
        uploadAndSaveContractDTO.setFile(file);// 文件
        uploadAndSaveContractDTO.setDocNo(docNo);// 合同编号
        uploadAndSaveContractDTO.setPersonCustomerId(personCustomerId);// 文档个人归属者客户编号，个人和企业不能同时为空
        uploadAndSaveContractDTO.setCompanyCustomerId(companyCustomerId);// 文档企业归属者客户编号，个人和企业不能同时为空
        uploadAndSaveContractDTO.setDocTitle(fileName);// 标题
        uploadAndSaveContractDTO.setDocExtension(fileType);// 扩展名

        try {
            RestResult restResult=clientFactory.multifunctionClient().uploadAndSaveContract(uploadAndSaveContractDTO);// RestResult{code='1', msg='操作成功', data=null}
            if("1".equals(restResult.getCode())){
                // nothing to do
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 自动签署
     * @param docNo 合同编号
     * @param personCustomerId 个人客户编号
     * @param companyCustomerId 公司客户编号
     * @param sealName 印章名
     * @param transactionNo 事务编号
     * @param x 横坐标
     * @param y 纵坐标
     */
    public AutoSignVO autoSign(String docNo,String personCustomerId,String companyCustomerId,String sealName,String transactionNo,double x,double y){
        AutoSignDTO autoSignDTO=new AutoSignDTO();
        autoSignDTO.setDocNo(docNo);// 合同编号，上面的合同编号
        autoSignDTO.setPersonSignerId(personCustomerId);// 签署个人客户编号和企业客户编号不能同时为空
        autoSignDTO.setCompanySignerId(companyCustomerId);// 签署个人客户编号和企业客户编号不能同时为空
        autoSignDTO.setAutoArchive(2);// 是否自动归档，1自动归档（默认），2不自动归档，一个合同可以盖多个章，但归档后不能再盖章
        autoSignDTO.setNotifyUrl("http://************/prod-api/callback/fddNotifyUrl");// 异步通知地址
        autoSignDTO.setSealName(sealName);// 印章名称，不传则采用系统默认章
        autoSignDTO.setLocateMethod(2);// 定位方式，1关键字，2坐标
        List<SearchLocationDTO> locateCoordinates=new ArrayList<>();
        locateCoordinates.add(new SearchLocationDTO(0,x,y));
        autoSignDTO.setLocateCoordinates(locateCoordinates);
        autoSignDTO.setTransactionNo(transactionNo);// 交易号

        try {
            RestResult restResult=clientFactory.signClient().autoSign(autoSignDTO);// RestResult{code='1', msg='操作成功', data={"downloadUrl":"http://121.32.24.215:8070/contract/download?appId=100000&signType=SHA256&sign=RkU2REQwMkQ3MTI2MkQ4ODhFRDM5NEMyQTQwRjM0MzYyRjI4RjUzQjI1OEJCOERBMzJGMkM0QzMzMzQ2MkJGRA==&timestamp=2020-09-18 14:54:40&bizContent=JTdCJTIyZG9jTm8lMjIlM0ElMjJoeGwlRTYlQjAlQjQlRTglQjclQUYlRTglQjQlQTclRTclODklQTklRTglQkYlOTAlRTUlOEQlOTUlRTUlOTAlODglRTUlOTAlOEMzNCUyMiU3RA==","viewUrl":"http://121.32.24.215:8070/contract/view?appId=100000&signType=SHA256&sign=RkU2REQwMkQ3MTI2MkQ4ODhFRDM5NEMyQTQwRjM0MzYyRjI4RjUzQjI1OEJCOERBMzJGMkM0QzMzMzQ2MkJGRA==&timestamp=2020-09-18 14:54:40&bizContent=JTdCJTIyZG9jTm8lMjIlM0ElMjJoeGwlRTYlQjAlQjQlRTglQjclQUYlRTglQjQlQTclRTclODklQTklRTglQkYlOTAlRTUlOEQlOTUlRTUlOTAlODglRTUlOTAlOEMzNCUyMiU3RA=="}}
            if("1".equals(restResult.getCode())){
                JSONObject jsonObject=(JSONObject)restResult.getData();
                String downloadUrl=jsonObject.getString("downloadUrl");
                String viewUrl=jsonObject.getString("viewUrl");
                return new AutoSignVO(downloadUrl,viewUrl);
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * @param
     * @return
     * @description 告知书自动签署
     * <AUTHOR>
     * @date 2024/12/19 17:10
     */
    public AutoSignVO autoSignNotice(String docNo,String personCustomerId,String companyCustomerId,String sealName,String transactionNo,double x,double y,Integer pageNum){
        AutoSignDTO autoSignDTO=new AutoSignDTO();
        autoSignDTO.setDocNo(docNo);// 合同编号，上面的合同编号
        autoSignDTO.setPersonSignerId(personCustomerId);// 签署个人客户编号和企业客户编号不能同时为空
        autoSignDTO.setCompanySignerId(companyCustomerId);// 签署个人客户编号和企业客户编号不能同时为空
        autoSignDTO.setAutoArchive(2);// 是否自动归档，1自动归档（默认），2不自动归档，一个合同可以盖多个章，但归档后不能再盖章
        autoSignDTO.setNotifyUrl("http://************/prod-api/callback/fddNotifyUrl");// 异步通知地址
        autoSignDTO.setSealName(sealName);// 印章名称，不传则采用系统默认章
        autoSignDTO.setLocateMethod(2);// 定位方式，1关键字，2坐标
        List<SearchLocationDTO> locateCoordinates=new ArrayList<>();
        locateCoordinates.add(new SearchLocationDTO(pageNum,x,y));
        autoSignDTO.setLocateCoordinates(locateCoordinates);
        autoSignDTO.setTransactionNo(transactionNo);// 交易号

        try {
            RestResult restResult=clientFactory.signClient().autoSign(autoSignDTO);// RestResult{code='1', msg='操作成功', data={"downloadUrl":"http://121.32.24.215:8070/contract/download?appId=100000&signType=SHA256&sign=RkU2REQwMkQ3MTI2MkQ4ODhFRDM5NEMyQTQwRjM0MzYyRjI4RjUzQjI1OEJCOERBMzJGMkM0QzMzMzQ2MkJGRA==&timestamp=2020-09-18 14:54:40&bizContent=JTdCJTIyZG9jTm8lMjIlM0ElMjJoeGwlRTYlQjAlQjQlRTglQjclQUYlRTglQjQlQTclRTclODklQTklRTglQkYlOTAlRTUlOEQlOTUlRTUlOTAlODglRTUlOTAlOEMzNCUyMiU3RA==","viewUrl":"http://121.32.24.215:8070/contract/view?appId=100000&signType=SHA256&sign=RkU2REQwMkQ3MTI2MkQ4ODhFRDM5NEMyQTQwRjM0MzYyRjI4RjUzQjI1OEJCOERBMzJGMkM0QzMzMzQ2MkJGRA==&timestamp=2020-09-18 14:54:40&bizContent=JTdCJTIyZG9jTm8lMjIlM0ElMjJoeGwlRTYlQjAlQjQlRTglQjclQUYlRTglQjQlQTclRTclODklQTklRTglQkYlOTAlRTUlOEQlOTUlRTUlOTAlODglRTUlOTAlOEMzNCUyMiU3RA=="}}
            if("1".equals(restResult.getCode())){
                JSONObject jsonObject=(JSONObject)restResult.getData();
                String downloadUrl=jsonObject.getString("downloadUrl");
                String viewUrl=jsonObject.getString("viewUrl");
                return new AutoSignVO(downloadUrl,viewUrl);
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }


    /**
     * 查看合同
     * @param docNo 合同编号
     */
    public String viewContract(String docNo){
        ContractNoDTO contractNoDTO=new ContractNoDTO();
        contractNoDTO.setDocNo(docNo);

        try {
            RestResult restResult = clientFactory.contractClient().viewContract(contractNoDTO);// RestResult{code='1', msg='操作成功', data=http://121.32.24.215:8070/#/webs?markId=8f25402b5f0643278df9f1973c1617d8&isView=1&lang=zh}
            if("1".equals(restResult.getCode())){
                return (String) restResult.getData();
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 下载合同 从输入流读取，从输出流写出
     * @param docNo 合同编号
     * @return ByteArrayOutputStream
     */
    public ByteArrayOutputStream downloadContract(String docNo) {
        ContractDownloadDTO contractDownloadDTO = new ContractDownloadDTO();
        contractDownloadDTO.setDocNo(docNo);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            RestResult restResult = clientFactory.contractClient().downloadContract(contractDownloadDTO);
            if("1".equals(restResult.getCode())){
                ByteArrayInputStream bis = (ByteArrayInputStream) restResult.getData();
                IoUtil.copy(bis, bos, 65536);
                IoUtil.close(bis);
                IoUtil.close(bos);
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bos;
    }

    public static void main(String[] args) {
//        testUploadAndSave();
//        testDelete();
        testBCJJPZ();
//        testHWJJQD();
//        testSLHWYD();
    }

    public static void testUploadAndSave(){

        ClientFactory clientFactory=ClientFactory.instance("http://10.197.34.26:8070/","100000","7Jij63g87a94AIg2i3JJEG1G");
        FddCommonService fddCommonService=new FddCommonService(clientFactory);
        String customerId="bfbc9936e98d4fb3b6fa6ef5791f7531";
        File file=new File("C:\\Users\\<USER>\\Desktop\\xxx.png");

        String sealName="xxx(1)";
        fddCommonService.uploadAndSaveSeal(customerId,sealName,file);

    }
    public static void testDelete(){

        ClientFactory clientFactory=ClientFactory.instance("http://10.197.34.26:8070/","100000","7Jij63g87a94AIg2i3JJEG1G");
        FddCommonService fddCommonService=new FddCommonService(clientFactory);
        String customerId="bfbc9936e98d4fb3b6fa6ef5791f7531";
        String sealName="8";
        fddCommonService.deleteSeal(customerId,sealName);

    }
    public static void testBCJJPZ(){
        ClientFactory clientFactory=ClientFactory.instance("http://10.197.34.26:8070/","100000","7Jij63g87a94AIg2i3JJEG1G");
        FddCommonService fddCommonService=new FddCommonService(clientFactory);
        FddCompanyService fddCompanyService=new FddCompanyService(clientFactory);

        String customerId="bfbc9936e98d4fb3b6fa6ef5791f7531";
        File file=new File("C:\\Users\\<USER>\\Desktop\\驳船装货交接凭证.pdf");
        String docNo="test111111111111329";

        // 1.上传并创建合同
        fddCommonService.uploadAndSaveContract(null,customerId,docNo,file);

        String sealName="测试驳船yy";

        String sealName2="xxx(1)";
//        String sealName4=null;

        // 2.免验证签署
        AutoSignVO autoSignVO1=fddCommonService.autoSign(docNo,null,customerId,sealName,IdUtils.fastSimpleUUID(),238.0f,496.0f);
        AutoSignVO autoSignVO2=fddCommonService.autoSign(docNo,null,customerId,sealName,IdUtils.fastSimpleUUID(),500.0f,495.0f);
        AutoSignVO autoSignVO3=fddCommonService.autoSign(docNo,null,customerId,sealName,IdUtils.fastSimpleUUID(),850.0f,497.0f);

        // 3.查看合同
        System.out.println(fddCommonService.viewContract(docNo));
    }
    public static void testHWJJQD(){
        ClientFactory clientFactory=ClientFactory.instance("http://10.197.34.26:8070/","100000","7Jij63g87a94AIg2i3JJEG1G");
        FddCommonService fddCommonService=new FddCommonService(clientFactory);
        FddCompanyService fddCompanyService=new FddCompanyService(clientFactory);

        String customerId="bfbc9936e98d4fb3b6fa6ef5791f7531";
        File file=new File("C:\\Users\\<USER>\\Desktop\\货物交接清单.pdf");
        String docNo="test111111111111328";

        // 1.上传并创建合同
        fddCommonService.uploadAndSaveContract(null,customerId,docNo,file);

        String sealName="测试驳船yy";

        // 2.免验证签署
        AutoSignVO autoSignVO1=fddCommonService.autoSign(docNo,null,customerId,sealName,IdUtils.fastSimpleUUID(),204.0f,576.5f);
        AutoSignVO autoSignVO2=fddCommonService.autoSign(docNo,null,customerId,sealName,IdUtils.fastSimpleUUID(),431.0f,576.5f);
        AutoSignVO autoSignVO3=fddCommonService.autoSign(docNo,null,customerId,sealName,IdUtils.fastSimpleUUID(),682.0f,576.5f);
        AutoSignVO autoSignVO4=fddCommonService.autoSign(docNo,null,customerId,sealName,IdUtils.fastSimpleUUID(),920.0f,576.5f);

        // 3.查看合同
        System.out.println(fddCommonService.viewContract(docNo));
    }
    public static void testSLHWYD(){
        ClientFactory clientFactory=ClientFactory.instance("http://10.197.34.26:8070/","100000","7Jij63g87a94AIg2i3JJEG1G");
        FddCommonService fddCommonService=new FddCommonService(clientFactory);
        FddCompanyService fddCompanyService=new FddCompanyService(clientFactory);

        String customerId="bfbc9936e98d4fb3b6fa6ef5791f7531";
        File file=new File("D:\\Data\\Desktop\\水路货物运单.pdf");
        String docNo="test111111111111119";

        // 1.上传并创建合同
        fddCommonService.uploadAndSaveContract(null,customerId,docNo,file);

        String sealName="物流公司章175.png";

        // 2.免验证签署
        AutoSignVO autoSignVO1=fddCommonService.autoSign(docNo,null,customerId,sealName,IdUtils.fastSimpleUUID(),530.0f,612.0f);
        AutoSignVO autoSignVO2=fddCommonService.autoSign(docNo,null,customerId,sealName,IdUtils.fastSimpleUUID(),113.0f,612.0f);

        // 3.查看合同
        System.out.println(fddCommonService.viewContract(docNo));
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class CompanySealVO{
        private String id;// id
        private String name;// 名称
        private String createdDate;// 创建时间
        private Boolean isDefault;// 是否默认
        private String path;// 路径
        private String accountId;// 账户id
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class AutoSignVO{
        private String downloadUrl;
        private String viewUrl;
    }
}

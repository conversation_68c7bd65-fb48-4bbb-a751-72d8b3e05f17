package com.ruoyi.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.domain.OlUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/18 10:51
 */
public interface OlUserMapper extends BaseMapper<OlUser> {
    int updateOlUser(OlUser olUser);

    List<OlUser> searchAdminByCompanyId(@Param("companyId") Long companyId);
}
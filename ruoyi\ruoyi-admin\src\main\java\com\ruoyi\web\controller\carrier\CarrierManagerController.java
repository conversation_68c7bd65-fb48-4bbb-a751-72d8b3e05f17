package com.ruoyi.web.controller.carrier;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fdd.api.client.dto.SealNameDTO;
import com.fdd.api.client.release.base.ClientFactory;
import com.fdd.api.client.res.RestResult;
import com.jcraft.jsch.ChannelSftp;
import com.ruoyi.app.controller.support.ftp.FtpTemplate;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.carrier.domain.CarrierUser;
import com.ruoyi.carrier.service.CarrierInfoService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.domain.OLPicture;
import com.ruoyi.common.domain.OlUser;
import com.ruoyi.common.enums.UserType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.BargeInfoService;
import com.ruoyi.common.service.OlPictureService;
import com.ruoyi.common.service.OlUserService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.ftp.FtpUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.AuthorizeService;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.controller.common.CommonController;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description pc端业务
 * <AUTHOR>
 * @Date 2020/8/24 23:26
 */
@RestController
@RequestMapping("/carrierweb")
public class CarrierManagerController extends BaseController {

    @Autowired
    CarrierInfoService carrierInfoService;
    @Autowired
    BargeInfoService bargeInfoService;
    @Autowired
    BargeCenterService bargeCenterService;
    @Autowired
    OlPictureService olPictureService;
    @Autowired
    ShipFddUserRelService shipFddUserRelService;//fdd印章审核资料
    @Autowired
    OlUserService olUserService;
    @Autowired
    ISysMenuService iSysMenuService;
    @Autowired
    AuthorizeService authorizeService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private FtpUtils ftpUtils;
    @Autowired
    private CommonController commonController;
    @Autowired
    private ClientFactory clientFactory;
    @Autowired
    private FtpTemplate ftpTemplate;
    @Autowired
    private ISysUserService iSysUserService;
    /**
     * 获取船公司用户列表
     * @param user
     * @return
     */
    @RequestMapping("/userList")
    public TableDataInfo sysUserList(@RequestBody SysUser user){
        user.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
        startPage();
        List<CarrierUser> carrierUserList = carrierInfoService.getCarrierUsers(user);
        carrierUserList.forEach(item -> {
            OLPicture picture = new OLPicture();
            picture.setUserId(item.getAccount());
            QueryWrapper<OLPicture> wrapper = new QueryWrapper<>(picture);
            List<OLPicture> list = olPictureService.list(wrapper);
            list.forEach(ol -> {
                /*String url = ol.getImageUrl();
                String newUrl = dataLocal + url.substring(url.indexOf("/ConsignPicture")) + ol.getImageInfo() + "." + ol.getImageType();
                ol.setImageUrl(newUrl);*/
                String base64 = ftpUtils.downloadBase64(ol.getImageUrl() + ol.getImageInfo() + "." + ol.getImageType());
                ol.setBase64Img(base64);
            });
            item.setPictureList(list);
        });
        return getDataTable(carrierUserList);
    }
    /**
     * 修改个人信息
     */
    @RequestMapping("/updateUserInfo")
    public AjaxResult updateUserInfo(@RequestBody OlUser olUser){

        olUserService.updateOlUser(olUser);
        return AjaxResult.success();
    }


    /**
     * 获取用户备案资料列表，后期加印章资料获取
     * @param olPicture
     * @return
     */
    @PostMapping("/getRecordUrl")
    public AjaxResult getRecordUrl(@RequestBody OLPicture olPicture){
        List<OLPicture> list = olPictureService.list(new QueryWrapper<OLPicture>()
                .eq("userid", olPicture.getUserId())
        );


        SysUser user = sysUserMapper.selectUserById(olPicture.getId());

        ShipFddUserRel seal = null;
        logger.info(user.getUserType());
        if (UserType.CARRIERADMIN.getCode().equals(user.getUserType())) {
            ShipFddUserRel fddUserRel = new ShipFddUserRel();
            fddUserRel.setType("2");
            fddUserRel.setShipUserId(user.getUserId());
            QueryWrapper<ShipFddUserRel> fddUserRelQueryWrapper = new QueryWrapper<>(fddUserRel);
            seal = shipFddUserRelService.getBaseMapper().selectOne(fddUserRelQueryWrapper);
            //seal = shipFddUserRelService.getOne(new QueryWrapper<ShipFddUserRel>().eq("SHIP_USER_ID", user.getUserId()).eq("type", "2"));
        }

        // 下载印章图片
        String fileBase64 = null;
        logger.info(String.valueOf(seal));
        logger.info(String.valueOf(user));
        if (seal!=null) {
            SealNameDTO sealNameDTO=new SealNameDTO();
            sealNameDTO.setAccountId(seal.getFddAccountId());
            sealNameDTO.setName(user.getCompanyName());
            try {
                RestResult restResult=clientFactory.sealClient().downloadSeal(sealNameDTO);

                ByteArrayInputStream bais=(ByteArrayInputStream)restResult.getData();
                if("1".equals(restResult.getCode())){
                    fileBase64 = Base64.encode(IOUtils.toByteArray(bais));
                    bais.close();
                } else {
                    File file = File.createTempFile(UUID.randomUUID().toString(), ".png");
                    file.deleteOnExit();
                    OutputStream os = new FileOutputStream(file);
                    ftpTemplate.download(seal.getSealUrl(), os);
                    fileBase64 = Base64.encode(IOUtils.toByteArray(IoUtil.toStream(file)));
                    os.close();
                }
            }catch (Exception e){
                e.printStackTrace();
                return AjaxResult.success("获取印章失败!");
            }
        }

        AjaxResult result = AjaxResult.success();
        result.put("olPictureUrls",list);
        result.put("fileBase64",fileBase64);
        return result;
    }

    /**
     * pc端通用下载
     */
    @PostMapping("/download")
    public void weChatDownload(String url, HttpServletResponse response, HttpServletRequest request) throws Exception {
        String downloadName = StringUtils.substringAfterLast(url, "/");
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/x-download");
        response.setHeader("Content-Disposition",
                "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, downloadName));
        // 货主端、托运联系人、船公司端上传ftp服务器
        // 参数1-下载目标文件的路径，参数2-输出流
        ftpUtils.downloadFile(url, response.getOutputStream());

    }



    /**
     * 备案文件修改
     * @return
     */
    @RequestMapping("/updateFiles")
    @Transactional
    public AjaxResult updateFiles(@RequestParam("files") MultipartFile[] files,@RequestParam("userId") String userId, @RequestParam("gmUserId") Long gmUserId){
        List<OLPicture> list=new ArrayList<>();
        try {
            ChannelSftp channelSftp = ftpUtils.getConnect();

            //上传到ftp并保存路径到数据库
            if(files.length>0){
                for(int i = 0;i < files.length;i++){
                    MultipartFile file = files[i];
                    //保存文件
                    if (!file.isEmpty()){

                        String url = ftpUtils.uploadFiles(file, userId, channelSftp);
                        OLPicture olPicture=new OLPicture();
                        String[] split = file.getOriginalFilename().split("\\.");

                        olPicture.setUserId(userId);
                        olPicture.setImageInfo(split[0]);
                        olPicture.setImageUrl(url+"/");
                        olPicture.setImageName(parseImageName(split[0]));
                        olPicture.setImageType(split[1]);

                        list.add(olPicture);
                    }
                }
            }
            OlUser olUser = new OlUser();
            olUser.setId(gmUserId);
            OlUser ou = olUserService.getById(gmUserId);
            String passport =  ou.getPassport();
            if (StringUtils.isNotBlank(passport)) {
                String[] passportArray = passport.split(",");
                List<String> passportList = new ArrayList<>(Arrays.asList(passportArray));
                passportList.removeIf("2"::equals);
                olUser.setPassport(StringUtils.join(passportList, ","));
            }
            olUser.setIsAudit("0");
            olUser.setUserId(userId);
            olUserService.updateOlUser(olUser);
            olPictureService.savepictrues(list,userId);
            return AjaxResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error();
        }


    }
    private String parseImageName(String simpleName){
        switch (simpleName){
            case "yyzz":
                return "三证合一营业执照";
            case "sqs":
                return "船公司授权书";
            case "frsfzzmxx":
                return "法人身份证反面信息";
            case "frsfzfmxx":
                return "法人身份证正面信息";
            case "sfzzmxx":
                return "身份证反面信息";
            case "sfzfmxx":
                return "身份证正面信息";
            default:
                return null;
        }
    }

    @Transactional
    @PostMapping("/salesCheck")
    public AjaxResult salesCheck(@RequestBody SysUser user) {
        if (user.getGmUserId() == null) {
            return AjaxResult.error("业务员id为空");
        }
        OlUser olUser = new OlUser();
        olUser.setId(user.getGmUserId());
        OlUser ol = olUserService.getById(olUser.getId());
        String passPort = ol.getPassport();
        if (StringUtils.isNotEmpty(passPort)) {
            olUser.setPassport("2");
        } else {
            olUser.setPassport(passPort + ",2");
        }
        olUserService.updateById(olUser);
        return AjaxResult.success("审核成功");
    }

    @Transactional
    @PostMapping("/updateSeal")
    public AjaxResult updateSeal(@RequestBody String body) {
        JSONObject jsonObject = JSONUtil.parseObj(body);
        String seal = jsonObject.getStr("sealBase64");
        Long userId = Long.parseLong(jsonObject.getStr("userId"));

        if (StringUtils.isNotEmpty(seal)) {
            // 查找船公司印章文件
            ShipFddUserRel shipFddUserRel = new ShipFddUserRel();
            shipFddUserRel.setShipUserId(userId);
            shipFddUserRel.setType("2");
            QueryWrapper<ShipFddUserRel> queryWrapper = new QueryWrapper<>(shipFddUserRel);
            ShipFddUserRel userRel = shipFddUserRelService.getOne(queryWrapper);

            if (StringUtils.isNull(userRel)) {
                logger.error("印章文件不存在");
                return AjaxResult.error("印章文件不存在");
            }

            // 上传印章
            String path = commonController.saveSeal(seal, userId);
            logger.info("path；"+path);
            if (StringUtils.isEmpty(path)) {
                throw new CustomException("保存印章返回路径为空");
            }
            // 保存印章信息
            shipFddUserRel = new ShipFddUserRel();
            shipFddUserRel.setId(userRel.getId());
            shipFddUserRel.setNewSealUrl(path);
            shipFddUserRelService.updateById(shipFddUserRel);
        }

        // 修改审核状态
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        SysUser user = iSysUserService.getUserByPhone(sysUser);
        OlUser olUser = olUserService.getById(user.getGmUserId());
        String passport = olUser.getPassport();
        OlUser ol = new OlUser();
        ol.setId(olUser.getId());
        if (StringUtils.isNotEmpty(passport)) {
            String[] parts = passport.split(",");
            List<String> list = new ArrayList<>();
            for (String part : parts) {
                if (!"2".equals(part)) {
                    list.add(part);
                }
            }
            //OlUser ol = new OlUser();
            //ol.setId(olUser.getId());
            ol.setPassport(CollUtil.join(list,","));
            //olUserService.updateById(ol);
        }
        ol.setUpDateTime(DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss"));
        olUserService.updateById(ol);
        return AjaxResult.success("修改成功");
    }
}

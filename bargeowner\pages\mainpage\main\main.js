// pages/mainpage/main/main.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 1,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getNoticeListDatas()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    if(this.data.activeTab == 1) {
      const supplyChild = this.selectComponent("#supply-com");
      supplyChild.getSupplyData()
      // console.log(supplyChild.getSupplyData());
      console.log('la1');
    }else {
      const transportChild = this.selectComponent("#transport-com");
      transportChild.getCapacityData()
      console.log('la2');
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
   
  },

  //获取信息通知列表
  getNoticeListDatas(){
    let that = this 
    // 查询条件参数
    let param =  {
      searchValue:"",//搜索关键字
      beginTime: "",//开始时间
      endTime: "",//结束时间
    }
    app.$post(app.$url.mine.getNoticeList,param).then(res=>{
      if(res.code == 200){
        // isRead消息状态，0-未读(有红点)，1-已读
        let unReadArr = res.data.filter(item=>{ return item.isRead === "0" })

        wx.setStorageSync('noticeNum', JSON.stringify(unReadArr.length))
        if(unReadArr && unReadArr.length != 0) {
          wx.setTabBarBadge({
            index: 2,
            text: JSON.stringify(unReadArr.length)
          })
        }else{
          wx.removeTabBarBadge({
            index: 2
          })
        }
        
      }else {
      app.$message(res.msg)
      }
    })
  },

  // 点击切换tab
  handleTabChange(e) {
    console.log(e);
    if(this.data.activeTab == e.currentTarget.dataset.tabid) {
      return;
    }else {
      this.setData({
        'activeTab': e.currentTarget.dataset.tabid
      });
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
package com.ruoyi.businessFile.controller;

import com.ruoyi.businessFile.domain.SysOss;
import com.ruoyi.businessFile.service.SysOssService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/10 15:15
 */
@RestController
@RequestMapping("/commonDownload")
public class CommonDownloadController {

    @Autowired
    private SysOssService ossService;

    @PostMapping("/downloadMinioto")
    public void downloadMinioto(HttpServletResponse response, @RequestParam("fileId") Long fileId) throws IOException {

        try {
            // 根据文件ID获取文件信息
            SysOss sysOss = ossService.getById(fileId);

            if (sysOss != null) {
                // 获取文件路径，这里需要根据你的实际情况来处理
                String filePath = sysOss.getFileMinioPath(); // 假设 SysOss 中有文件路径的字段，请根据实际情况调整

                // 读取文件内容
                Path path = Paths.get(filePath);
                byte[] fileContent = Files.readAllBytes(path);

                // 设置响应头
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + sysOss.getFileOriginName());
                response.setCharacterEncoding("UTF-8"); // 设置编码

                // 将文件内容写入响应流
                try (OutputStream outputStream = response.getOutputStream()) {
                    outputStream.write(fileContent);
                    outputStream.flush();
                }
            } else {
                // 处理文件不存在的情况，你可以根据实际需求返回适当的错误信息
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("File not found");
            }
        } catch (IOException e) {
            e.printStackTrace();
            // 处理异常
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("Internal Server Error");
        }

        }


}

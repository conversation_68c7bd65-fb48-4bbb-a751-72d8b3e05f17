package com.ruoyi.api.mapping;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.api.controller.ReportApi;
import com.ruoyi.api.domain.BargeParam;
import com.ruoyi.databarge.domain.Pb6Cargoconsignment;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.ship.domain.ShipInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/6/13 16:26
 */
@Mapper
public interface ReportApiMapper extends BaseMapper<ReportApi> {

    // 统计大船量
    public List<ShipInfo> getShipCount(BargeParam bargeParam);

    // 统计驳船量
    public List<Pb6Cargoconsignmentdetail> getBargeCount(BargeParam bargeParam);

    // 根据大船id查询托运单信息
    public List<Pb6Cargoconsignment> selectByShipInfoId(Long shipInfoId);

}

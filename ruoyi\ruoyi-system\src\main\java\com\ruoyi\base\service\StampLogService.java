package com.ruoyi.base.service;

import com.ruoyi.base.domain.StampLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;

/**
* <AUTHOR>
* @description 针对表【STAMP_LOG(盖章日志)】的数据库操作Service
* @createDate 2024-12-24 16:51:31
*/
public interface StampLogService extends IService<StampLog> {

    /**
     * 新增盖章日志
     *
     * @param fileName 文件名 detailId 托运单下驳船详情id
     * @return 结果
     */
    AjaxResult insertStampLog(String fileName,Long waterwayId,String status,String result,String stampBy);

}

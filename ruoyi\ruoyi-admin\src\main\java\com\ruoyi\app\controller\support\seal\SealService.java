package com.ruoyi.app.controller.support.seal;

import com.alibaba.fastjson.JSONObject;
import com.fdd.api.client.dto.CertificationCompanyUrlDTO;
import com.fdd.api.client.dto.RegisterDTO;
import com.fdd.api.client.dto.VerifiedCertApplyDTO;
import com.fdd.api.client.release.base.ClientFactory;
import com.fdd.api.client.res.RestResult;
import com.ruoyi.common.exception.CustomException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SealService {

    private ClientFactory clientFactory;

    /**
     * 注册
     * @param account 账号
     * @param companyName 企业名称
     * @return FDD账户accountId
     */
    public String register(String account,String companyName) {
        RegisterDTO registerDTO=new RegisterDTO();
        registerDTO.setType(2);// 2为企业
        registerDTO.setAccount(account);// 需要注册的账号
        registerDTO.setAdminAccountId(null);// 管理员客户编号
        registerDTO.setCompanyName(companyName);// 企业名称，类型为企业时

        try {
            RestResult restResult=clientFactory.accountClient().register(registerDTO);// RestResult{code='1', msg='添加成功', data=524945f093fd40919864db339fb3c99e}，data就是企业编号
            if("1".equals(restResult.getCode())){
                return (String)restResult.getData();
            }else{
                throw new CustomException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 企业实名认证
     * @param accountId FDD账户accountId
     * @param notifyUrl 异步通知地址
     * @return 认证信息
     */
    public CompanyCertificationUrlVO companyCertificationUrl(String accountId,String notifyUrl){
        CertificationCompanyUrlDTO certificationCompanyUrlDTO=new CertificationCompanyUrlDTO();
        certificationCompanyUrlDTO.setCustomerId(accountId);// 客户编号
        certificationCompanyUrlDTO.setPageModify(1);// 用户是否能修改扫码后的认证页面信息1-允许，2-不允许（如之前已认证通过，重新认证默认不允许修改页面）
        certificationCompanyUrlDTO.setVerifiedWay(0);// 实名认证套餐类型0：标准方案（对公打款+纸质审核）；1：对公打款；2：纸质审核',
        certificationCompanyUrlDTO.setIsRepeatVerified(1);// 1-首次认证，2-重新认证
        certificationCompanyUrlDTO.setCompanyInfo(null);// 企业信息
        certificationCompanyUrlDTO.setBankInfo(null);// 对公账号信息
        certificationCompanyUrlDTO.setCompanyPrincipalType(null);// 企业负责人身份1.法人，2代理人
        certificationCompanyUrlDTO.setLegalInfo(null);// 法人信息
        certificationCompanyUrlDTO.setAgentInfo(null);// 代理人信息
        certificationCompanyUrlDTO.setNotifyUrl(notifyUrl);// 异步通知地址
        certificationCompanyUrlDTO.setReturnUrl(null);// returnUrl
        certificationCompanyUrlDTO.setIsSendSMS(null);// 是否发送实名验证短信1:发送（需传“企业负责人手机号码”才发送短信），0:否，默认是0:否
        certificationCompanyUrlDTO.setApplicationFormPath(null);// 企业认证申请表
        certificationCompanyUrlDTO.setVerifiedSerialno(null);// 企业负责人实名认证序列号
        certificationCompanyUrlDTO.setManagerVerifiedWay(null);// 管理员认证方式0.三要素标准方案；（默认）1.三要素补充方案；2.四要素标准方案；3.四要素补充方案；4.纯三要素方案；5.纯四要素方案
        certificationCompanyUrlDTO.setApplyCert(null);// 认证成功后自动申请实名证书0：不申请，（默认）1：自动申请
        certificationCompanyUrlDTO.setCertMode(null);// 证书类型0：云端证书（默认）1：本地证书

        try {
            RestResult restResult=clientFactory.certificationClient().companyCertificationUrl(certificationCompanyUrlDTO);// RestResult{code='1', msg='操作成功', data={"transactionNo":"79b86f8166824992880839e1b54354dd","url":"https://t-test.fadada.com/pOUda19VF"}}
            if("1".equals(restResult.getCode())){
                JSONObject jsonObject=(JSONObject)restResult.getData();
                return new CompanyCertificationUrlVO(jsonObject.getString("transactionNo"),jsonObject.getString("url"));
            }else{
                throw new CustomException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    public void companyCertifiedApply(String accountId,String verifiedSerialNo){
        VerifiedCertApplyDTO verifiedCertApplyDTO=new VerifiedCertApplyDTO();
        verifiedCertApplyDTO.setCustomerId(accountId);// 客户编号
        verifiedCertApplyDTO.setVerifiedSerialNo(verifiedSerialNo);// 实名认证序列号
        verifiedCertApplyDTO.setCertType(2);// 证书类型1-编号证书，2-实名证书. (目前只能申请实名证书，与传的参数无关)
        try {
            RestResult restResult=clientFactory.certificateClient().certifiedApply(verifiedCertApplyDTO);// RestResult{code='1', msg='申请认证证书成功', data={"startTime":"**************","endTime":"**************"}}
            if("1".equals(restResult.getCode())){
                JSONObject jsonObject=(JSONObject)restResult.getData();
                String startTime=jsonObject.getString("startTime");
                String endTime=jsonObject.getString("endTime");
            }else{
                throw new CustomException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class CompanyCertificationUrlVO{
        private String transactionNo;// 事务编号
        private String url;// 认证地址
    }
}

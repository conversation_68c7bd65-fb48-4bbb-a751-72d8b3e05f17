package zqit.app;

import cn.hutool.core.io.IoUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.RuoYiApplication;
import com.ruoyi.app.controller.databarge.Pb6BargeworkController;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.app.controller.support.fdd.FddCompanyService;
import com.ruoyi.app.controller.support.fdd.FddShipOwnerService;
import com.ruoyi.app.controller.support.fdd.Sha256Util;
import com.ruoyi.app.controller.support.ftp.FtpTemplate;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.enums.PayWayEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.databarge.domain.Pb6Bargeinfo;
import com.ruoyi.databarge.domain.Pb6Bargework;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.domain.dto.XGPdfDTO;
import com.ruoyi.databarge.mapper.Pb6BargeworkMapper;
import com.ruoyi.databarge.mapper.Pb6WaterwaycargoMapper;
import com.ruoyi.databarge.mapper.ShipFddUserRelMapper;
import com.ruoyi.databarge.service.Pb6BargeinfoService;
import com.ruoyi.databarge.service.Pb6BargeworkService;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.utils.BargeUtils;
import com.sun.scenario.effect.impl.sw.sse.SSEBlend_SRC_OUTPeer;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.swing.text.html.parser.Entity;
import javax.xml.bind.SchemaOutputResolver;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/1/4 11:01
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = RuoYiApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class FddTest {
    @Autowired
    private FddCompanyService fddCompanyService;
    @Autowired
    private FddCommonService fddCommonService;
    @Autowired
    private FddShipOwnerService fddShipOwnerService;
    @Autowired
    ShipFddUserRelMapper shipFddUserRelMapper;
    @Autowired
    Pb6BargeinfoService pb6BargeinfoService;
    @Autowired
    private FtpTemplate ftpTemplate;

    @Autowired
    private ShipFddUserRelService shipFddUserRelService;

    @Autowired
    private Pb6BargeworkMapper pb6BargeworkMapper;
    @Autowired
    private Pb6BargeworkService pb6BargeworkService;
    @Autowired
    private ISysUserService iSysUserService;
    @Autowired
    private Pb6WaterwaycargoMapper pb6WaterwaycargoMapper;
    @Autowired
    private Pb6BargeworkController pb6BargeworkController;

    @Test
    public void testFddCompany() throws Exception{
        CloseableHttpClient client = HttpClients.createDefault();
        JSONObject postJson = new JSONObject();
        postJson.put("ciphertext", "11");
        postJson.put("pwd", "22");

        HttpUriRequest post = RequestBuilder.get("https://bulkcargo.gzport.com/prod-api/plan/exterInterface/confirmShipLoadMainForZL").
                setEntity(new StringEntity(postJson.toString(), ContentType.APPLICATION_JSON)).build();
        System.out.println("sss");
        HttpResponse postRes = client.execute(post);
//        System.out.println( pb6BargeworkController.isGreenBarge((long)1,"霞运36"));
//        CloseableHttpClient client= HttpClients.createDefault();
//        JSONObject json=new JSONObject();
//        json.put("un","ApiUser139");
//        json.put("pwd","6a15a02ad19b0558af0eb03b7fffc6eb");
//        HttpUriRequest post= RequestBuilder.post("https://gps-outsideservice.csiitl.com/OutsideService/Token").
//                setEntity(new StringEntity(json.toString(), ContentType.APPLICATION_JSON)).build();
//         HttpResponse res=client.execute(post);
//         System.out.println(EntityUtils.toString(res.getEntity()));
//         String a=EntityUtils.toString(res.getEntity());
//         JSONObject jsonObject=JSONObject.parseObject(a);
//
//        System.out.println(jsonObject.get("content"));
//        String head="Bearer "+jsonObject.getJSONObject("content").getString("token");
//        JSONObject json1=new JSONObject();
//        json1.put("WharfId",139);
//        json1.put("ShipName","abc123");
//        HttpUriRequest get=RequestBuilder.get("https://gps-outsideservice.csiitl.com/OutsideService/ShipDeclareCompleteExistsByWharf").
//                setHeader("Authorization",head).setEntity(new StringEntity(json1.toString(), ContentType.APPLICATION_JSON)).build();
//        HttpResponse getRes=client.execute(get);
//        String b=EntityUtils.toString(getRes.getEntity());
//        JSONObject getJsonObject=JSONObject.parseObject(b);
//        System.out.println(getJsonObject);
//        System.out.println(getJsonObject.getJSONObject("content"));
//        System.out.println(getJsonObject.getJSONObject("content").getBoolean("hasArrived"));
//        List<Map<String,Object>> cargotypeList=pb6BargeworkService.findCargoNameByCoutformid("BCK022200280");
//        if (cargotypeList.size()>0){
//            System.out.println("32");
//        }else{
//            System.out.println("31");
//        }
        /*String customerId=fddCompanyService.companyRegister("test1111111111111111","测试1111111111111111");// 88b41c4bdcfb4f5dba4ab892775a35fa
        FddCompanyService.CompanyCertificationUrlVO companyCertificationUrlVO=fddCompanyService.companyCertificationUrl(customerId,"http://ship.gzport.com/");
        System.out.println(companyCertificationUrlVO);*/
//        String customerId=fddCompanyService.companyRegister(IdUtils.fastSimpleUUID(),"测试1111111111111111");
//        System.out.println(customerId);
//        String transactionNo= IdUtils.fastSimpleUUID();
//        File file=new File("C:\\Users\\<USER>\\Desktop\\测试驳船yy.png");
//        String evidenceNo=fddShipOwnerService.shipOwnerSaveHashEvidence(
//                "1b5eac6a6738444ba753588f522c7934",
//                "测试驳船yy的存证",
//                "170997的存证描述",
//                file.getName(),
//                ""+file.lastModified()/1000,
//                ""+file.length(),
//                Sha256Util.getSha256(file.getName()),
//                transactionNo);
//        String evidenceNumberCert=fddShipOwnerService.applyEvidenceNumberCert("ee32c4bdfb254800b7a62df8b40ffc48",evidenceNo);
//        String customerId="a0a841562db4448fb8884acc81366377";
//        File file=new File("C:\\Users\\<USER>\\Desktop\\测试驳船yy.png");
//        String sealName="粤英德货6902";
//        fddCommonService.uploadAndSaveSeal(customerId,sealName,file);

       JSONArray restResult=fddCompanyService.getCompanyUserInfos("广州港盛国际船舶代理有限公司",null);
       System.out.println(restResult);
       List<FddCommonService.CompanySealVO> companySealVOList=fddCommonService.sealList("26467047e6f2439496e8f43a09d2e2a3");
       System.out.println(companySealVOList);
//        String[] names=new String[]{"广州港股份有限公司黄埔港务分公司"};
//        for (String name:names){
//            ByteArrayInputStream byteArrayInputStream=fddCommonService.downloadSeal("8571c49ac2d748bab3e40f9abd718a80",name);
//            FileOutputStream fileOutputStream=new FileOutputStream(new File("C:\\Users\\<USER>\\Desktop\\"+name+".png"));
//            fileOutputStream.write(IOUtils.toByteArray(byteArrayInputStream));
//        }


        //        JSONArray restResult=fddCompanyService.getCompanyUserInfos("广州外轮理货有限公司",null);
//        System.out.println(restResult);
//        List<FddCommonService.CompanySealVO> companySealVOList=fddCommonService.sealList("18e553df273b4afeaa85742eae853a20");
//        System.out.println(companySealVOList);
//        String[] names=new String[]{"新港理货部电子章"};
//        for (String name:names){
//            ByteArrayInputStream byteArrayInputStream=fddCommonService.downloadSeal("18e553df273b4afeaa85742eae853a20",name);
//            FileOutputStream fileOutputStream=new FileOutputStream(new File("C:\\Users\\<USER>\\Desktop\\"+name+".png"));
//            fileOutputStream.write(IOUtils.toByteArray(byteArrayInputStream));
//        }
             //驳船主身份改公司身份
//        List<ShipFddUserRel> shipFddUserRelList=shipFddUserRelMapper.getShipFddUserRel();
//        System.out.println(shipFddUserRelList.size());
//        for (int i=0;i<shipFddUserRelList.size();i++){
//                ShipFddUserRel shipFddUserRel=shipFddUserRelList.get(i);
//                Pb6Bargeinfo pb6Bargeinfo=pb6BargeinfoService.getById(shipFddUserRel.getShipId());
//                if (pb6Bargeinfo!=null){
//                    String sealName=pb6Bargeinfo.getBargename();// 公章名称直接为船名
//                    String transactionNo= IdUtils.fastSimpleUUID();
//                    File file=null;
//                    try {
//                        file = File.createTempFile("ship-", ".png");
//                        file.deleteOnExit();
//                        ftpTemplate.download(shipFddUserRel.getSealUrl(),new FileOutputStream(file));
//                    }catch (Exception e){
//                        throw new CustomException("印章文件不存在!",e);
//                    }
//                    String accountNo=fddCompanyService.companyRegister(transactionNo,"驳船主");
//                    String evidenceNo=fddShipOwnerService.shipOwnerSaveHashEvidence(
//                            accountNo,
//                            shipFddUserRel.getShipId()+"的存证",
//                            shipFddUserRel.getShipId()+"的存证描述",
//                            file.getName(),
//                            ""+file.lastModified()/1000,
//                            ""+file.length(),
//                            Sha256Util.getSha256(file.getName()),
//                            transactionNo);
//                    String evidenceNumberCert=fddShipOwnerService.applyEvidenceNumberCert(accountNo,evidenceNo);
//                    fddCommonService.uploadAndSaveSeal(accountNo,sealName,file);
//
//                    shipFddUserRel.setFddAccountId(accountNo);
//                    shipFddUserRel.setRemarks("驳船主改企业");
//                    shipFddUserRelService.updateById(shipFddUserRel);
//                }
//        }
//
//        System.out.println("完成");



//        //盖章
//        String customerId="e194eccf8d364ac68d3b4352f91c1c42";//个人
//        String customerIdWL="66d1507358234975916dfac9fc4e544c";//物流公司
//
//        File file=new File("C:\\Users\\<USER>\\Desktop\\test.pdf");
//        String docNo="test111111111111338";
//        // 1.上传并创建合同
//        fddCommonService.uploadAndSaveContract(null,customerId,docNo,file);
//        String sealName="广州港新沙港务有限公司";
//        String sealNameWL="广州港船舶代理业务专用章(1)";
//        // 2.免验证签署
//        FddCommonService.AutoSignVO autoSignVO1=fddCommonService.autoSign(docNo,null,customerId,sealName,IdUtils.fastSimpleUUID(),620.0f,900.0f);
////        FddCommonService.AutoSignVO autoSignVO2=fddCommonService.autoSign(docNo,null,customerIdWL,sealNameWL,IdUtils.fastSimpleUUID(),595.0f,912.0f);
//        System.out.println("1");
//        System.out.println(fddCommonService.viewContract(docNo));


    }
}

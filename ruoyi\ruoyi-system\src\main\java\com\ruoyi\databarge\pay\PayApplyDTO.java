package com.ruoyi.databarge.pay;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class PayApplyDTO {
    private Reqmsg reqmsg;

    @Getter
    @Setter
    @Builder
    public static class Reqmsg{
        private Reqhead reqhead;
        private String cdtrid;
        private String amt;
        private String billid;
        private String billdesc;
        private String operuser;
        private String remark;
        private String reqtype;
        private String costcode;
        private String resulturl;
        private String paymentnm;
        private String paymentacct;
        private String signature;
    }
}

package com.ruoyi.databarge.domain.vo;

import lombok.Data;

@Data
public class Pb6BargeWorkVo {
    private String waterwaycargoid;
    private String bargename;
    private String outorinformid;
    private String uniqecode;
    private String shipname;
    private String rationweight;
    private String cargename;
    private String consignee;
    private String beginport;
    private String midport;
    private String endport;
    private String cargosize;
    private String consigner;
    private String contactphone;
    private String registerprincipal;
    private String registertime;
    private String applymodify;
    private String flagbargestate;
    private String endworktime;
    private String tscargoweightValue; //实装吨数
    private String sendpractime;
    private String confirmloadingover; //是否确认实装数
    private String isFile;//是否有文件
    private String phoneNumber;//预约人电话
}

package com.ruoyi.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.barge.domain.SysUserBargeBak;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.SysUserBargeBakService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.*;
import com.ruoyi.common.enums.*;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.*;
import com.ruoyi.common.service.BargeCompanyLinkService;
import com.ruoyi.common.service.BargeInfoAuditService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.mapper.ShipFddUserRelMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 驳船审核备份实现
 * <AUTHOR>
 * @Date 2020/8/5 9:53
 */
@Service
public class BargeInfoAuditServiceImpl extends ServiceImpl<BargeInfoAuditMapper, BargeInfoAudit> implements BargeInfoAuditService {
    @Autowired
    BargeCompanyLinkService bargeCompanyLinkService;

    @Autowired
    private UploadAddressMapper uploadAddressMapper;

    @Autowired
    private BargeCheckMessageMapper bargeCheckMessageMapper;

    @Autowired
    private ShipFddUserRelMapper shipFddUserRelMapper;

    @Autowired
    private BargeInfoMapper bargeInfoMapper;

    @Autowired
    private BargeInfoAuditMapper bargeInfoAuditMapper;

    @Autowired
    private BargeCompanyLinkMapper bargeCompanyLinkMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysUserBargeBakService sysUserBargeBakService;


    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;


    @Override
    @Transactional
    public int addCarrierBarge(BargeInfoAudit bargeInfoAudit) {
        //Long recorderId = bargeInfoAudit.getRecorderId();
        SysUser user = SecurityUtils.getLoginUser().getUser();

        String time = DateUtils.getTime();
        if(bargeInfoAudit.getPb6bargeInfoId()!=null){//已存在的驳船(修改、挂靠操作)

            if(bargeInfoAudit.getCheckFlag()!=null){//对驳船的备案的修改操作

                //设置驳船修改备案状态
                bargeInfoAudit.setCheckFlag(2);
                bargeInfoAudit.setUpdateCheck(0);
                bargeInfoAudit.setIsDelete(1);
                bargeInfoAudit.setModifyManId(user.getUserId());
                bargeInfoAudit.setModifyMan(user.getNickName());
                bargeInfoAudit.setModifyDate(time);
                //判断是否已有此驳船待审批的数据
                List<BargeInfoAudit> bargeInfoAudits = baseMapper.selectList(
                        new QueryWrapper<BargeInfoAudit>()
                                .eq(bargeInfoAudit.getId() != null, "id", bargeInfoAudit.getId())
                                .eq("pb6bargeInfoId", bargeInfoAudit.getPb6bargeInfoId())
                                .eq("ISDELETE", 1)
                );
                if(bargeInfoAudits.size()>0 && bargeInfoAudit.getId() != null){//有待审核的此驳船，在此基础上更新
                    baseMapper.updateById(bargeInfoAudit);
                }else{
                    baseMapper.insert(bargeInfoAudit);
                }
            }
            //如果涉及到挂靠审核，则需要保存挂靠关系
            BargeCompanyLink bargeCompanyLink = bargeInfoAudit.getBargeCompanyLink();
            if(bargeCompanyLink !=null){
                bargeCompanyLink.setBargeId(bargeInfoAudit.getPb6bargeInfoId());
                bargeCompanyLinkService.bingdingBarge(bargeCompanyLink);
            }
        }else{//未存在的驳船备案（备案、挂靠）
            //如果涉及到挂靠审核，则需要保存挂靠关系
            bargeInfoAudit.setPb6bargeInfoId(baseMapper.generateBargeId());
            bargeInfoAudit.setRecorderId(user.getUserId());
            bargeInfoAudit.setRecorder(user.getNickName());
            bargeInfoAudit.setModifyManId(user.getUserId());
            bargeInfoAudit.setModifyMan(user.getNickName());
            bargeInfoAudit.setCheckFlag(1);
            bargeInfoAudit.setRecordCheck(0);
            bargeInfoAudit.setRecordDate(time);
            bargeInfoAudit.setIsDelete(1);
            this.save(bargeInfoAudit);

            /*SysUserBargeBak sysUserBargeBak = new SysUserBargeBak();
            sysUserBargeBak.setBargeId(bargeInfoAudit.getPb6bargeInfoId());
            sysUserBargeBak.setBargeAuditId(bargeInfoAudit.getId());
            sysUserBargeBak.setUserId(bargeInfoAudit.getModifyManId());
            sysUserBargeBakService.save(sysUserBargeBak);*/


            //如果涉及到挂靠审核，则需要保存挂靠关系
            BargeCompanyLink bargeCompanyLink = bargeInfoAudit.getBargeCompanyLink();
            if(bargeCompanyLink !=null){
                bargeCompanyLink.setBargeId(bargeInfoAudit.getPb6bargeInfoId());
                bargeCompanyLinkService.bingdingBarge(bargeCompanyLink);
            }
        }
        return 1;
    }

    @Override
    @Transactional
    public AjaxResult addBarge(BargeInfoAudit bargeInfoAudit) {

        // 唯一性校验
        AjaxResult result = this.checkOnlyBarge(bargeInfoAudit);
        if (500 == (int) result.get("code")) {
            return result;
        }
        //多次备案校验
        AjaxResult result1 = this.addBargeCheck(bargeInfoAudit);
        if (500 == (int) result1.get("code")) {
            return result1;
        }

        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        Long userId = loginUser.getUserId();
        boolean flag = bargeInfoAudit.getId() != null;

        Long bargeUserId = null;
        if (bargeInfoAudit.getBargeCompanyLink() != null) {
            // 挂靠校验
            result = this.checkBargeCompany(bargeInfoAudit.getBargeCompanyLink());
            if (500 == (int) result.get("code")) {
                return result;
            }
            if (bargeInfoAudit.getBargeCompanyLink().getId() == null) {
                BargeCompanyLink bargeCompanyLink = bargeInfoAudit.getBargeCompanyLink();
                // 新增挂靠
                //bargeCompanyLink.setIsAudit(CheckFlagEnum.BINDING_AUDIT.getCode());
                // 判断是否是修改备案
                if (bargeInfoAudit.getId() != null) {
                    bargeUserId = bargeInfoMapper.selectById(bargeInfoAudit.getPb6bargeInfoId()).getRecorderId();
                    bargeCompanyLink.setCheckFlag(true);
                    bargeCompanyLink.setCreateById(bargeUserId);
                    bargeInfoAudit.setBargeCompanyLink(bargeCompanyLink);
                }
            }
        }
        // 获取驳船recorder用户
        SysUser recorderUser = null;
        if (StringUtils.isNotNull(bargeUserId)) {
            recorderUser = sysUserMapper.selectUserById(bargeUserId);
        }

        SysUserBarge sysUserBarge = sysUserBargeMapper.selectOne(new LambdaQueryWrapper<SysUserBarge>().eq(SysUserBarge::getBargeId,bargeInfoAudit.getPb6bargeInfoId()));
        SysUserBargeBak sysUserBargeBak = sysUserBargeBakService.getOne(new LambdaQueryWrapper<SysUserBargeBak>().eq(SysUserBargeBak::getBargeId,bargeInfoAudit.getPb6bargeInfoId()));
        if(sysUserBargeBak != null){
            throw new CustomException("该驳船已有他人或本人提交记录");
        }

        //1.新增驳船关联关系
        this.addCarrierBarge(bargeInfoAudit);
        sysUserBargeBak = new SysUserBargeBak();
        sysUserBargeBak.setBargeAuditId(bargeInfoAudit.getId());
        sysUserBargeBak.setBargeId(bargeInfoAudit.getPb6bargeInfoId());
        sysUserBargeBak.setUserId(userId);
        sysUserBargeBakService.save(sysUserBargeBak);

        // 自有船设置驳船主
        /*Long bargeUserId;
        if (BargeBindingType.OWN.getCode().equals(bargeInfoAudit.getBargeCompanyLink().getBindingType())) {
            // 获取驳船联系电话
            String phone = bargeInfoAudit.getContactPhone();
            // 查找驳船主
            SysUser sysUser = new SysUser();
            sysUser.setPhonenumber(phone);
            sysUser.setUserType(UserType.BARGEADMIN.getCode());
            SysUser bargeUser = sysUserMapper.selectUserByPhone(sysUser);
            if (bargeUser != null) {
                log.error("当前联系电话已绑定驳船主，请重新输入");
                throw new CustomException("当前联系电话已绑定驳船主，请重新输入");
            }

            // 注册为驳船主
            sysUser = new SysUser();
            sysUser.setUserType(null);//首次登陆注册，设置用户类型为空，在界面选择绑定。
            sysUser.setUserName(phone);
            sysUser.setPhonenumber(phone);
            sysUser.setNickName(phone);
            sysUser.setUserType(UserType.BARGEADMIN.getCode());

            // 查询驳船主角色 ROLE_KEY - bargeAdmin
            List<SysRole> sysRoles = sysRoleMapper.selectRoleAll();
            List<Long> roleList = new ArrayList<>();
            sysRoles.forEach(role->{
                if ("bargeAdmin".equals(role.getRoleKey())) {
                    roleList.add(role.getRoleId());
                }
            });
            sysUser.setRoleIds(roleList.toArray(new Long[roleList.size()]));
            //用户添加，授权
            iSysUserService.insertUser(sysUser);
            bargeUserId = sysUser.getUserId();

            // 插入sys_user_barge表
            SysUserBarge sysUserBarge = new SysUserBarge();
            sysUserBarge.setUserId(bargeUserId);
            sysUserBarge.setBargeId(bargeInfoAudit.getPb6bargeInfoId());
            sysUserBargeMapper.insert(sysUserBarge);

            // 更新recorderId
            BargeInfoAudit bia = new BargeInfoAudit();
            bia.setId(bargeInfoAudit.getId());
            bia.setRecorderId(bargeUserId);
            bargeInfoAuditMapper.updateById(bargeInfoAudit);
        }*/

        // 资料
        List<UploadAddress> dataList = bargeInfoAudit.getDataList();
        if (dataList != null) {
            //2.新增驳船资料关联关系
            // 删除旧资料
            QueryWrapper<UploadAddress> queryWrapper = new QueryWrapper<>();
            /*queryWrapper.eq("UPLOAD_USER_ID", bargeUserId==null?userId:bargeUserId)
                    .eq("LINK_TYPE", UploadDataType.BARGE_RECORD_DATA.getType())
                    .eq("LINK_ID", bargeInfoAudit.getPb6bargeInfoId())
                    .eq("BAK_FLAG", 1);*/
            queryWrapper.eq("LINK_TYPE", UploadDataType.BARGE_RECORD_DATA.getType())
                    .eq("LINK_ID", bargeInfoAudit.getPb6bargeInfoId())
                    .eq("BAK_FLAG", 1);
            UploadAddress ua = new UploadAddress();
            ua.setStatus(0);
            uploadAddressMapper.update(ua,queryWrapper);

            //删除所有委托书图片
            uploadAddressMapper.delete(new LambdaQueryWrapper<UploadAddress>().eq(UploadAddress::getLinkId,userId).eq(UploadAddress::getLinkType,53));

            //如果上传多张，只取一张
            for (UploadAddress uploadAddress : dataList){
                if(uploadAddress.getDataType().equals(53)){
                    uploadAddress.setLinkId(userId);
                    uploadAddressMapper.insert(uploadAddress);
                    break;
                }
            }

            SysUser finalRecorderUser = recorderUser;
            dataList.forEach(data -> {
                UploadAddress upload = new UploadAddress();
                upload.setId(data.getId());
                upload.setLinkId(bargeInfoAudit.getPb6bargeInfoId());
                upload.setUploadUserId(finalRecorderUser==null?userId:finalRecorderUser.getUserId());
                upload.setUploadUserName(finalRecorderUser==null?loginUser.getNickName():finalRecorderUser.getNickName());
                upload.setStatus(1);
                uploadAddressMapper.updateById(upload);
            });
        }

        // 上传印章
        Long shipFddUserRelId = this.saveSeal(bargeInfoAudit.getSealImg(), userId, bargeInfoAudit);

        BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();
        // 判断是修改备案还是新增备案
        List<BargeInfoAudit> bargeInfoAudits = baseMapper.selectList(
                new QueryWrapper<BargeInfoAudit>()
                        .eq(bargeInfoAudit.getId() != null, "id", bargeInfoAudit.getId())
                        .eq("pb6bargeInfoId", bargeInfoAudit.getPb6bargeInfoId())
                        .eq("ISDELETE", 1)
        );
        QueryWrapper<BargeCompanyLink> bclWrapper = new QueryWrapper<>();
        bclWrapper.eq("BARGEID", bargeInfoAudit.getPb6bargeInfoId())
                .eq("STATUS", 0)
                .eq("ISDELETE", 1)
                .eq("AUDITSTATUS",0);
        if (bargeInfoAudit.getBargeCompanyLink() != null && bargeInfoAudit.getBargeCompanyLink().getCompanyId() != null) {
            bclWrapper.eq("companyId", bargeInfoAudit.getBargeCompanyLink().getCompanyId() != null);
        }
        BargeCompanyLink bcl = bargeCompanyLinkService.getBaseMapper().selectOne(bclWrapper);
        BargeInfo bi = bargeInfoMapper.selectById(bargeInfoAudit.getPb6bargeInfoId());
        if(bi != null && bargeInfoAudits.size() > 0 && bcl == null && flag){//有待审核的此驳船，在此基础上更新
            // 修改备案
            bargeCheckMessage.setMType(BargeCheckMessageType.BARGE_INFORMATION_MODIFICATION_REVIEW.getCode().shortValue());
        }else{
            // 新增备案
            if (bargeInfoAudit.getBargeCompanyLink() != null) {
                // 备案加挂靠
                bargeCheckMessage.setPb6BargeCompanyId(bargeInfoAudit.getBargeCompanyLink().getId());
                bargeCheckMessage.setMType(BargeCheckMessageType.BARGE_WAS_REGISTERED_AND_CHECK.getCode().shortValue());
            } else {
                bargeCheckMessage.setMType(BargeCheckMessageType.BARGE_FILING_AUDIT.getCode().shortValue());
            }
        }

        // 插入表PB6_BARGE_CHECK_MESSAGE
        bargeCheckMessage.setApplyManId(loginUser.getUserId());
        // user对象
        SysUser sysUser = sysUserMapper.selectUserById(loginUser.getUserId());
        bargeCheckMessage.setApplyMan(sysUser.getNickName());
        bargeCheckMessage.setApplyTime(new Date());
        bargeCheckMessage.setAuditFlag((short) 0);
        bargeCheckMessage.setPb6BargeInfoAuditId(bargeInfoAudit.getId());
        bargeCheckMessage.setBargeName(bargeInfoAudit.getBargeName());
        bargeCheckMessage.setShipFddUserRelId(shipFddUserRelId);

        bargeCheckMessageMapper.insert(bargeCheckMessage);

        return AjaxResult.success("备案成功");
    }

    @Transactional
    @Override
    public AjaxResult updateBarge(BargeInfoAudit bargeInfoAudit) {
        //Long loginUserId = SecurityUtils.getLoginUser().getUser().getUserId();

        if (bargeInfoAudit.getBargeCompanyLink().getIsAudit().intValue() != CheckFlagEnum.CANCEL_BINDING_AUDIT.getCode()) {
            // 挂靠校验
            AjaxResult result = this.checkBargeCompany(bargeInfoAudit.getBargeCompanyLink());
            if (500 == (int) result.get("code")) {
                return result;
            }
        }

        //Long userId = bargeInfoMapper.selectById(bargeInfoAudit.getPb6bargeInfoId()).getRecorderId();

        BargeInfoAudit audit = bargeInfoAuditMapper.selectById(bargeInfoAudit.getId());
        //BargeInfo bargeInfo = bargeInfoMapper.selectById(bargeInfoAudit.getPb6bargeInfoId());
        Long userId;
        if (StringUtils.isNull(audit)) {
            userId = bargeInfoMapper.selectById(bargeInfoAudit.getPb6bargeInfoId()).getRecorderId();
        } else {
            userId = audit.getRecorderId();
        }

        BargeCompanyLink companyLink = bargeInfoAudit.getBargeCompanyLink();

        // 上传印章
        this.saveSeal(bargeInfoAudit.getSealImg(), userId, bargeInfoAudit);

        //判断是否已有此驳船待审批的数据
        List<BargeInfoAudit> bargeInfoAudits = baseMapper.selectList(
                new QueryWrapper<BargeInfoAudit>()
                        .eq(bargeInfoAudit.getId() != null, "id", bargeInfoAudit.getId())
                        .eq("pb6bargeInfoId", bargeInfoAudit.getPb6bargeInfoId())
                        .eq("ISDELETE", 1)
        );

        BargeCheckMessage bcm = null;
        if(bargeInfoAudits.size()>0 && bargeInfoAudit.getId() != null){
            //有待审核的此驳船，在此基础上更新
            // 查询消息表
            BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();
            bargeCheckMessage.setApplyManId(userId);
            bargeCheckMessage.setPb6BargeInfoAuditId(bargeInfoAudit.getId());
            bargeCheckMessage.setAuditFlag((short) 0);
            QueryWrapper<BargeCheckMessage> msgWrapper = new QueryWrapper<>(bargeCheckMessage).orderByDesc("APPLYTIME");

            List<BargeCheckMessage> list = bargeCheckMessageMapper.selectList(msgWrapper);

            if (list.size() > 0) {
                bcm = list.get(0);
                Integer isAudit = bargeInfoAudit.getBargeCompanyLink().getIsAudit();
                if (isAudit.intValue() == CheckFlagEnum.BINDING_AUDIT.getCode()
                        || isAudit.intValue() == CheckFlagEnum.UPDATE_BINDING_AUDIT.getCode()) {
                    //绑定审批 / 修改挂靠
                    bcm.setMType(BargeCheckMessageType.BARGE_WAS_REGISTERED_AND_CHECK.getCode().shortValue());
                } else if (isAudit.intValue() == CheckFlagEnum.CANCEL_BINDING_AUDIT.getCode()) {
                    // 取消挂靠
                    bcm.setMType(BargeCheckMessageType.BARGE_FILING_AUDIT.getCode().shortValue());
                }
                if (bcm.getAuditFlag() != 0) {
                    bargeCheckMessageMapper.deleteById(bcm.getId());
                    bcm.setId(null);
                }
                bcm.setApplyTime(DateUtils.getNowDate());
                // 取消挂靠不用审核
                if (isAudit.intValue() == CheckFlagEnum.CANCEL_BINDING_AUDIT.getCode()) {
                    // 取消挂靠将印章改为通过
                    ShipFddUserRel shipFddUserRel = new ShipFddUserRel();
                    shipFddUserRel.setType(FddUserType.BARGE_USER.getCode());
                    shipFddUserRel.setShipId(bargeInfoAudit.getPb6bargeInfoId());
                    QueryWrapper<ShipFddUserRel> sfr = new QueryWrapper<>(shipFddUserRel);
                    shipFddUserRel = new ShipFddUserRel();
                    shipFddUserRel.setReviewStatus("1");
                    shipFddUserRelMapper.update(shipFddUserRel, sfr);

                    // 取消挂靠不用审核 消息为审核通过
                    if (bcm.getAuditFlag() != 0) {
                        bcm.setAuditFlag((short) 1);
                    }
                }
                if (bcm.getAuditFlag() != 0) {
                    bargeCheckMessageMapper.insert(bcm);
                } else {
                    bargeCheckMessageMapper.updateById(bcm);
                }
                companyLink.setBindingType(companyLink.getUpdateBindingType());
            } else {
                companyLink.setCheckFlag(true);
            }
        }else{
            // checkFlag - true
            companyLink.setCheckFlag(true);
        }
        if (bargeInfoAudit.getBargeCompanyLink().getIsAudit().intValue() == CheckFlagEnum.BINDING_AUDIT.getCode()) {
            companyLink.setBindingType(companyLink.getUpdateBindingType());
            companyLink.setUpdateBindingType(null);
        }
        companyLink.setBargeName(bargeInfoAudit.getBargeName());
        bargeInfoAudit.setBargeCompanyLink(companyLink);

        bargeCompanyLinkService.bingdingBarge(bargeInfoAudit.getBargeCompanyLink());

        log.warn("驳船挂靠信息id:"+bargeInfoAudit.getBargeCompanyLink().getId());
        if (StringUtils.isNotNull(bcm) && StringUtils.isNotNull(bargeInfoAudit.getBargeCompanyLink().getId())) {
            BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();
            bargeCheckMessage.setId(bcm.getId());
            bargeCheckMessage.setPb6BargeCompanyId(bargeInfoAudit.getBargeCompanyLink().getId());
            bargeCheckMessageMapper.updateById(bargeCheckMessage);
        }

        return AjaxResult.success();
    }

    /**
     * 保存印章
     * @param sealImg basic64印章
     * @param userId 用户id
     * @param bargeInfoAudit 驳船备份实体
     */
    protected Long saveSeal(String sealImg, Long userId, BargeInfoAudit bargeInfoAudit) {
        if (StringUtils.isNotBlank(sealImg)) {
            String sealUrl = bargeInfoAudit.getSealPath();

            ShipFddUserRel shipFddUserRel = new ShipFddUserRel();
            shipFddUserRel.setType(FddUserType.SHIP_COMPANY_BARGE.getCode());
            shipFddUserRel.setShipUserId(userId);
            shipFddUserRel.setShipId(bargeInfoAudit.getPb6bargeInfoId());

            // 查询原有的数据
            QueryWrapper<ShipFddUserRel> qw = new QueryWrapper<>(shipFddUserRel);
            ShipFddUserRel rel = shipFddUserRelMapper.selectOne(qw);
            if (rel == null) {
                shipFddUserRel.setSealUrl(sealUrl);
                shipFddUserRel.setReviewStatus("0");
                //shipFddUserRel.setShipUserId(userId);
                //shipFddUserRel.setShipId(bargeInfoAudit.getPb6bargeInfoId());
                shipFddUserRelMapper.insert(shipFddUserRel);
                return shipFddUserRel.getId();
            } else {
                rel.setSealUrl(sealUrl);
                rel.setReviewStatus("0");
                //shipFddUserRel.setShipUserId(userId);
                //rel.setShipId(bargeInfoAudit.getPb6bargeInfoId());
                shipFddUserRelMapper.updateById(rel);
                return rel.getId();
            }
        }
        return null;
    }

    /**
     * 校验唯一性 驳船名称 驳船识别号
     * @param bargeInfoAudit
     * @return
     */
    private AjaxResult checkOnlyBarge(BargeInfoAudit bargeInfoAudit) {
        QueryWrapper<BargeInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("BARGEID", bargeInfoAudit.getBargeId())
                .ne(bargeInfoAudit.getPb6bargeInfoId()!=null,"ID", bargeInfoAudit.getPb6bargeInfoId());
        List<BargeInfo> list = bargeInfoMapper.selectList(wrapper);

        if (list != null && list.size() > 0) {
            for (BargeInfo item : list) {
                if (bargeInfoAudit.getBargeId().equals(item.getBargeId())) {
                    log.error("该驳船标识已存在");
                    return AjaxResult.error("该驳船标识已存在");
                }
            }
        }

        return AjaxResult.success();
    }

    /**
     * 挂靠校验
     * @param bargeCompany
     * @return
     */
    private AjaxResult checkBargeCompany(BargeCompanyLink bargeCompany) {
        if (StringUtils.isNull(bargeCompany.getBargeId())) {
            return AjaxResult.success();
        }
        QueryWrapper<BargeCompanyLink> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bargeId", bargeCompany.getBargeId())
                .eq("isDelete", 1)
                .eq("auditStatus", 1)
                .ne("companyId", bargeCompany.getCompanyId());
        List<BargeCompanyLink> list = bargeCompanyLinkMapper.selectList(queryWrapper);
        if (list.size() > 0) {
            log.error("改驳船已挂靠公司");
            return AjaxResult.error("改驳船已挂靠公司");
        }
        return AjaxResult.success();
    }

    /**
     * 新增驳船重复校验
     * @param bargeInfoAudit
     * @return
     */
    private AjaxResult addBargeCheck(BargeInfoAudit bargeInfoAudit) {
        if (StringUtils.isNull(bargeInfoAudit.getId())) {
            Long companyId = SecurityUtils.getLoginUser().getUser().getCompanyId();
            List<Long> userList = sysUserMapper.selectList(new QueryWrapper<SysUser>()
                    .eq("COMPANY_ID", companyId)
                    .in("USER_TYPE", UserType.CONSIGNOR.getCode(),
                            UserType.CARRIERADMIN.getCode(),
                            UserType.CARRIEUSER.getCode()))
                    .stream()
                    .map(SysUser::getUserId)
                    .collect(Collectors.toList());
            QueryWrapper<BargeInfoAudit> wrapper = new QueryWrapper<>();
            wrapper.eq("BARGEID", bargeInfoAudit.getBargeId())
                    .and(i -> i.eq("RECORDCHECK", 0).or().eq("UPDATECHECK", 0));
            if(userList.size() != 0){
                wrapper.in("RECORDERID", userList);
            }
            List<BargeInfoAudit> list = bargeInfoAuditMapper.selectList(wrapper);

            if (list != null && list.size() > 0) {
                return AjaxResult.error("该驳船您已新增备案，若要修改信息请选择驳船修改");
            }
        }

        return AjaxResult.success();
    }
}

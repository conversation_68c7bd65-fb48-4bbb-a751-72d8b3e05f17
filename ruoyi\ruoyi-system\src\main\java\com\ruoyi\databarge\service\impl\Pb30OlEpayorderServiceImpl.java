package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.Pb30OlEpayorder;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.domain.dto.Pb30OlEpayorderDetailSearchDTO;
import com.ruoyi.databarge.domain.vo.Pb30OlEpayorderDetailVO;
import com.ruoyi.databarge.domain.vo.WaterCargoTagClickVO;
import com.ruoyi.databarge.mapper.Pb30OlEpayorderMapper;
import com.ruoyi.databarge.pay.PayDetailsVO;
import com.ruoyi.databarge.service.Pb30OlEpayorderService;
import com.ruoyi.databarge.service.Pb6WaterwaycargoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2020/11/17 9:26
 * @Description:
 */
@Service
public class Pb30OlEpayorderServiceImpl extends ServiceImpl<Pb30OlEpayorderMapper, Pb30OlEpayorder> implements Pb30OlEpayorderService {

    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;

    @Override
    public IPage<Pb30OlEpayorderDetailVO> searchPageDetailVO(Pb30OlEpayorderDetailSearchDTO pb30OlEpayorderDetailSearchDTO) {
        int pageNum = Optional.ofNullable(pb30OlEpayorderDetailSearchDTO.getPageNum()).orElse(0);
        int pageSize = Optional.ofNullable(pb30OlEpayorderDetailSearchDTO.getPageSize()).orElse(-1);
        return super.baseMapper.searchPageDetailVO(new Page<>(pageNum, pageSize),pb30OlEpayorderDetailSearchDTO);
    }

    @Override
    public List<Pb30OlEpayorderDetailVO> searchPageDetailVONoPage(Pb30OlEpayorderDetailSearchDTO pb30OlEpayorderDetailSearchDTO) {
        return super.baseMapper.searchPageDetailVO(pb30OlEpayorderDetailSearchDTO);
    }

    @Override
    public Pb30OlEpayorder searchOriginOrder(String waterwaycargono) {
        return super.baseMapper.searchOriginOrder(waterwaycargono);
    }

    @Override
    public List<PayDetailsVO> searchPayDetails(String waterwaycargono) {
        return super.baseMapper.searchPayDetails(waterwaycargono);
    }

    @Override
    public WaterCargoTagClickVO searchWaterCargoTagClickVO(String waterwaycargono) {
        Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.searchWaterwaycargoByWaterwaycargoid(waterwaycargono);
        if(StringUtils.isNotBlank(pb6Waterwaycargo.getChargebalancetype()) && pb6Waterwaycargo.getChargebalancetype().equals("现结")){
            WaterCargoTagClickVO waterCargoTagClickVO = super.baseMapper.searchWaterCargoCashTagClick(waterwaycargono);
            if(waterCargoTagClickVO == null){
                waterCargoTagClickVO = new WaterCargoTagClickVO();
                waterCargoTagClickVO.setStatus("未支付");
                waterCargoTagClickVO.setAlreadymount(new BigDecimal("0"));
            }
            return waterCargoTagClickVO;
        } else if(StringUtils.isNotBlank(pb6Waterwaycargo.getChargebalancetype()) && pb6Waterwaycargo.getChargebalancetype().equals("月结")){
            return super.baseMapper.searchWaterCargoMonthlyTagClick(waterwaycargono);
        }
        return null;
    }
}

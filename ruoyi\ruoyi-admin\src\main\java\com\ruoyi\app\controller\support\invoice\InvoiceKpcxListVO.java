package com.ruoyi.app.controller.support.invoice;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class InvoiceKpcxListVO {
    private String c_orderno;//订单编号
    private String c_fpqqlsh;//发票请求流水号
    /*开票状态： 2 :开票完成（ 最终状态），其他状态分别为: 20:开票中;
    21:开票成功签章中;22:开票失败;24:
    开票成功签章失败;3:发票已作废31:发票作废中
    备注：22、24 状态时，无需再查询，请确认开票失败原因以及签章失败原因；3、31 只针对纸票
    注：请以该状态码区分发票状态
    */
    private String c_status;
    private String c_msg;//开票信息，成功或者失败的信息
    private String c_url;//发票 pdf 地址
    private String c_jpg_url;//发票详情地址
    private String c_kprq;//开票日期
    private String c_fpdm;//发票代码
    private String c_fphm;//发票号码
    private String c_bhsje;//不含税金额
    private String c_hjse;//合计税额
    private String c_resultmsg;//结果信息
    private String c_invoiceid;//发票主键
    private String c_buyername;//购方名称
    private String c_taxnum;//购方税号
    private String c_invoice_line;//发票种类
    private String c_jym;//校验码
    private String qrCode;//二维码
    private String machineCode;//税控设备号（机器编码）
    private String cipherText;//发票密文
    private String c_clerk;//开票员
    private String c_payee;//收款人
    private String c_checker;//复核人
    private String c_salerAccoun;//销方银行账号
    private String c_salerTel;//销方电话
    private String c_salerAddres;//销方地址
    private String c_salerTaxNu;//销方税号
    private String c_remark;//备注
    private String productOilFlag;// 成品油标志：0 非成品油，1 成品油
    private String c_imgUrls;//jpg 图片地址，清单票发票主信息与清单信息以”,”隔开
    private String extensionNumber;//分机号
    private List<invoiceItems> invoiceItems;

    @Getter
    @Setter
    public static class invoiceItems{
        private String itemName;//商品名称
        private String itemUnit;//单位
        private String itemPrice;//单价
        private String itemTaxRate;//税率
        private String itemNum;//数量
        private String itemSumAmount;//金额
        private String itemTaxAmount;//税额

        private String itemSpec;//规格型号
        private String itemCode;//商品编码
        private String isIncludeTax;//含税标识 true：含税 false：不含税
        private String invoiceLineProperty;//发票行性质0, 正常行;1,折扣行;2,被扣行
        private String zeroRateFlag;//零税率标识:空：非零税率，1：免税，2：不征税，3：普通零税率
        private String favouredPolicyName;//优惠政策名称（增值税特殊管理）
        private String favouredPolicyFlag;//优惠政策标识:0：不使用;1：使用
    }


}

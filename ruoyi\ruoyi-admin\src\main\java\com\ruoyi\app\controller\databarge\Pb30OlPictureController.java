package com.ruoyi.app.controller.databarge;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.app.controller.support.fdd.FddCompanyService;
import com.ruoyi.app.controller.support.ftp.FtpTemplate;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ftp.FtpUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.databarge.domain.Pb30OlPicture;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.service.Pb30OlPictureService;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import com.ruoyi.system.service.ISysUserService;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/11/17.
 * @Date: 2020/11/17 15:45
 */
@RestController
@RequestMapping("/barge/pb30OlPicture")
public class Pb30OlPictureController {

    @Autowired
    private Pb30OlPictureService pb30OlPictureService;

    @Autowired
    private FtpUtils ftpUtils;

    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ShipFddUserRelService shipFddUserRelService;
    @Autowired
    private FtpTemplate ftpTemplate;

    @PostMapping("/searchByUserId")
    public AjaxResult searchByUserId(@RequestBody String userId){
        List<Pb30OlPicture> list = pb30OlPictureService.searchByUserId(userId);
        list.forEach(ol -> {
            String base64 = ftpUtils.downloadBase64(ol.getImageUrl() + ol.getImageInfo() + "." + ol.getImageType());
            ol.setBase64Img("data:image/jpeg;base64," + base64);
        });
        return AjaxResult.success(list);
    }

    // ---------王伦辉---------
    @GetMapping("/getSealImageByPb30OlUserId")
    public AjaxResult getSealImage(@RequestParam("pb30OlUserId") Long pb30OlUserId){
        SysUser sysUser = sysUserService.selectUserByGmUserId(pb30OlUserId);
        if(sysUser == null){
            throw new CustomException("该船公司管理员没有补充船公司资料，无法审核通过！");
        }
        List<ShipFddUserRel> shipFddUserRelList = shipFddUserRelService.list(new QueryWrapper<ShipFddUserRel>().lambda()
                .eq(ShipFddUserRel::getShipUserId, sysUser.getUserId())
                .eq(ShipFddUserRel::getType,"2"));// 2船公司
        if(shipFddUserRelList.size() > 1){
            throw new CustomException("发现多个法大大审核数据，该船公司无法审核通过！");
        } else if(shipFddUserRelList.size() == 0){
            throw new CustomException("未找到法大大审核数据，该船公司无法审核通过！");
        }

        ShipFddUserRel shipFddUserRel=shipFddUserRelList.get(0);
        try {
            ByteArrayOutputStream sealUrlOutputStream=new ByteArrayOutputStream();
            ftpTemplate.download(shipFddUserRel.getSealUrl(),sealUrlOutputStream);
            Map<String,String> map=new HashMap<>();
            map.put("shipFddUserRelId",""+shipFddUserRel.getId());
            map.put("sealUrl","data:image/jpeg;base64,"+Base64.encode(sealUrlOutputStream.toByteArray()));
            if(StringUtils.isNotEmpty(shipFddUserRel.getNewSealUrl())){
                ByteArrayOutputStream newSealUrlOutputStream=new ByteArrayOutputStream();
                ftpTemplate.download(shipFddUserRel.getNewSealUrl(),newSealUrlOutputStream);
                map.put("newSealUrl","data:image/jpeg;base64,"+Base64.encode(newSealUrlOutputStream.toByteArray()));
            }

            return AjaxResult.success(map);
        }catch (Exception e){
            throw new CustomException("印章文件不存在!",e);

        }
    }
    // ---------王伦辉---------
}

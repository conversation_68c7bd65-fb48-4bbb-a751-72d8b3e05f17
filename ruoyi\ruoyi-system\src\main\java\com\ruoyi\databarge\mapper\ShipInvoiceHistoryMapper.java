package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.databarge.domain.Pb6BargeCheckMessage;
import com.ruoyi.databarge.domain.ShipInvoiceHistory;
import com.ruoyi.databarge.domain.dto.Pb6BargeCheckMessageSearchDTO;
import com.ruoyi.databarge.domain.dto.ShipInvoiceHistorySearchDTO;
import com.ruoyi.databarge.domain.vo.ShipInvoiceHistoryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 发票开票记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
public interface ShipInvoiceHistoryMapper extends BaseMapper<ShipInvoiceHistory> {

    IPage<ShipInvoiceHistoryVO> searchPage(Page<ShipInvoiceHistoryVO> page, @Param("shipInvoiceHistorySearchDTO") ShipInvoiceHistorySearchDTO shipInvoiceHistorySearchDTO);

    List<Map<String,Object>> searchTaiTou(@Param("waterwaycargoid") String waterwaycargoid);

    ShipInvoiceHistory searchShipInvoiceHistoryBybuyername(@Param("buyername") String buyername,@Param("type") String type);
    //下载
    List<ShipInvoiceHistoryVO> searchDownload(@Param("shipInvoiceHistorySearchDTO") ShipInvoiceHistorySearchDTO shipInvoiceHistorySearchDTO);

}

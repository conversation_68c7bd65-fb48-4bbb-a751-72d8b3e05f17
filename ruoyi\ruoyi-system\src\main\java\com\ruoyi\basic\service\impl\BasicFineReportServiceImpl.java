package com.ruoyi.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicFineReport;
import com.ruoyi.basic.mapper.BasicFineReportMapper;
import com.ruoyi.basic.service.BasicFineReportService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Service;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1
 * @Description
 */
@Service

public class BasicFineReportServiceImpl extends ServiceImpl<BasicFineReportMapper, BasicFineReport> implements BasicFineReportService {

    @Autowired
    private BasicFineReportMapper basicFineReportMapper;

    public List<BasicFineReport> basicFineReportList(BasicFineReport basicFineReport) {

        return this.baseMapper.selectFineReportList(basicFineReport);
    }
//    public addFineReport<BasicFineReport>

//@Test
//    public void testList(){
//    QueryWrapper<BasicFineReport> queryWrapper = new QueryWrapper<>();
//    queryWrapper.eq("id", 29);
//    System.out.println(baseMapper.selectList(queryWrapper));
//}
//
//    @Test
//    public void testList1(){
//        basicFineReportMapper().selectFineReportList(new BasicFineReport());
//    }

}

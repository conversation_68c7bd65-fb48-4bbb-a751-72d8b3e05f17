package com.ruoyi.common.enums;

/**
 *  托运单办理人审核
 *
 * @Description 托运单办理人审核
 * <AUTHOR>
 * @Date 2020/8/10  10:56
 */
public enum CheckStatusEnum {

    /**
     *  0:不审核  1:退单审核, 2:改单审核
     */
    NO_CHECK(0, "不审核"),
    CHARGEBACK_CHECK(1, "退单审核"),
    MODIFICATION_CHECK(2, "改单审核");

    private final Integer code;

    private final String codeName;

    CheckStatusEnum(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

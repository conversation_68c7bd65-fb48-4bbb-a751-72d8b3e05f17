package com.ruoyi.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.vo.CargocmentVO;
import com.ruoyi.common.domain.vo.CargocmentdetailVO;

import java.util.List;

/**
 * @Description 运单详情接口
 * <AUTHOR>
 * @Date 2020/8/5 9:53
 */
public interface CargocmentdetailService extends IService<Cargoconsignmentdetail> {

    public void startAudit(CargocmentdetailBO cargocmentdetailBO);

    IPage<CargocmentdetailVO> auditList(CargocmentdetailBO cargocmentdetailBO);

    List<CargocmentVO> bargebillsList(CargocmentdetailBO cargocmentdetailBO);

    // 待确认-物流公司审核处理结果
    void undeterminedDispose(Cargoconsignmentdetail cargoconsignmentdetail);
}

/* pages/mainpage/main/transportcom/transport.wxss */
.search-area{
  height: 70rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.search-warp{
  position: fixed;
  top: 140rpx;
  left: 0rpx;
  right: 0rpx;
  height: 170rpx;
  background: #ffffff;
  z-index: 100;
  border-bottom: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  padding: 0 20rpx;
}
.supply-com{
  width: 100%;
}
.search-ipt-wrap{
  flex-shrink: 0;
  width: 48%;
  height: 70rpx;
  padding: 0 15rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
}
.search-ipt-wrap1{
  flex-shrink: 0;
  width: 48%;
  height: 70rpx;
  padding: 0 15rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.weight-wrap{
  flex-shrink: 0;
  width: 50%;
  height: 70rpx;
  display: flex;
  align-items: center;
}
.search-ipt{
  height: 70rpx;
  line-height: 66rpx;
  flex:1;
  font-size: 26rpx;
}
.close-icon{
  flex-shrink: 0;
  width: 40rpx;
  text-align: center;
  height: 70rpx;
  line-height: 70rpx;
  color: #ddd;
}
.close-iconp{
  flex-shrink: 0;
  position: absolute;
  right: -6rpx;
  top: 12rpx;
  width: 40rpx;
  height:auto;
  color: #ddd;
}
.search-zaizhong {
  height: 70rpx;
  line-height: 66rpx;
  font-size: 26rpx;
  flex:1;
}
.search-data-box{
  position: relative;
  height: 70rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  flex-shrink: 0;
  flex: 1;
  display: flex;
  align-items: center;
}
.search-icon{
  flex-shrink: 0;
}
.data-item {
  border: 1px solid #ddd;
  margin-top: 30rpx;
  background: #ffffff;
  border-radius: 10rpx;
  padding: 10rpx 0rpx;
}
.data-list{
  padding: 10rpx 20rpx;
  margin-top: 170rpx;
  background: #f5f5f5;
}
.data-item-title{
  height: 80rpx;
  border-bottom: 1px solid #ddd;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx;
}
.detail-icon{
  flex-shrink: 0;
  padding: 0 5rpx;
}
.data-item-text{
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.data-item-message{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 0 10rpx;
}
.message-wrap{
  min-width: 50%;
  display: flex;
  flex-direction: row;
  height: 60rpx;
}
.message-wrap-title{
  color: #999;
  font-size: 28rpx;
}
.message-wrap-value{
  color: #00426B;
  font-size: 28rpx;
}
.search-warp .publish-btn{
  width: 150rpx;
  border: none;
  border-radius: 10rpx;
  height: 60rpx;
  line-height: 60rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
  padding: 0rpx;
  background: #00426B;
  color: #fff;
  font-weight: normal;
}
.search-warp .publish-btn.search-btn {
  width: 120rpx;
}
.padding-text{
  padding: 0 5rpx;
  flex-shrink: 0;
}
.search-date-text{
  height: 60rpx;
  width: 85%;
  position: absolute;
  left: 0;
  top: 15rpx;
  padding: 0 20rpx;
  color: #808080;
}
.search-date-selected{
  height: 60rpx;
  width: 85%;
  position: absolute;
  left: 0;
  top: 15rpx;
  padding: 0 5rpx;
  color: #333;
}
.message-operate{
  height: 70rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}
.message-operate .operate-btn{
  height: 60rpx;
  width: 120rpx;
  color: #00426B;
  border: 1px solid #00426B;
  background: #ffffff;
  font-size: 28rpx;
  padding: 0rpx;
  margin: 0rpx 0rpx 0rpx 20rpx;
  line-height: 56rpx;
  border-radius: 20rpx;
}

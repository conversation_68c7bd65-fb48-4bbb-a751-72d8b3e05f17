package com.ruoyi.carrier.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/12/24 11:11
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
@TableName("COMPANY_MONTHLY_CODE")
@KeySequence("SEQ_COMPANY_MONTHLY_CODE")
public class CompanyMonthlyCode {

    /**
     * 主键id
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 月结码船公司id
     */
    @TableField("COMPANY_ID")
    private Long companyId;

    /**
     * 月结码（随机6位数字）
     */
    @TableField("MONTHLY_CODE")
    private String monthlyCode;

    /**
     * 使用月结码的用户id
     */
    @TableField("USE_USER_ID")
    private Long useUserId;

    /**
     * 使用月结码的驳船id
     */
    @TableField("USE_BARGE_ID")
    private Long useBargeId;

    /**
     * 使用月结码的公司id
     */
    @TableField("USE_COMPANY_ID")
    private Long useCompanyId;

    /**
     * 是否使用（0-未使用，1-已使用）
     */
    @TableField("IS_USED")
    private String isUsed;

    /**
     * 使用月结码时间
     */
    @TableField("USED_TIME")
    private Date usedTime;

    /**
     * 创建月结码时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;
}

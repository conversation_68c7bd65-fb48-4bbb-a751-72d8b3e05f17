package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Description:
 *
 * @Author: ChenJin on 2021/7/13.
 * @Date: 2021/7/13 9:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "AGENT_DELIVERY")
@KeySequence("SEQ_AGENT_DELIVERY")
public class AgentDelivery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 出库单号
     */
    @TableField("COUT_FORM_ID")
    private String coutFormId;

    /**
     * 发货编号
     */
    @TableField("DELIVERY_NO")
    private String deliveryNo;

    /**
     * 驳船名
     */
    @TableField("BARGE_NAME")
    private String bargeName;

    /**
     * 计划吨数
     */
    @TableField("PLAN_WEIGHT")
    private String planWeight;

    /**
     * 审核标识，0未审核， 1审核通过， 2审核不通过
     */
    @TableField("AUDIT_SIGN")
    private int auditSign;

    /**
     * 审核时间
     */
    @TableField("AUDIT_TIME")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Timestamp auditTime;

    /**
     * 删除标识 0未删除，1已删除
     */
    @TableField("DELETE_FLAG")
    private int deleteFlag;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Timestamp createTime;

    /**
     * 修改时间（审核不填入这个时间）
     */
    @TableField("UPDATE_TIME")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Timestamp updateTime;
}

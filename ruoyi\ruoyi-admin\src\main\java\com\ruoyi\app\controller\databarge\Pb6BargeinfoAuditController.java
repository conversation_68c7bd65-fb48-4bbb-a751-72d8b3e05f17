package com.ruoyi.app.controller.databarge;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.dadiyang.equator.Equator;
import com.github.dadiyang.equator.FieldInfo;
import com.github.dadiyang.equator.GetterBaseEquator;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.barge.domain.SysUserBargeBak;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.SysUserBargeBakService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.Pb6Bargeinfo;
import com.ruoyi.databarge.domain.Pb6BargeinfoAudit;
import com.ruoyi.databarge.domain.dto.Pb6BargeinfoAuditSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeCompanyResultVO;
import com.ruoyi.databarge.service.Pb6BargeinfoAuditService;
import com.ruoyi.databarge.service.Pb6BargeinfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/8/27.
 * @Date: 2020/8/27 11:20
 */
@RestController
@RequestMapping("/barge/bargeinfo-audit")
public class Pb6BargeinfoAuditController {

    @Autowired
    private Pb6BargeinfoAuditService pb6BargeinfoAuditService;

    @Autowired
    private Pb6BargeinfoService pb6BargeinfoService;

    @Autowired
    private SysUserBargeBakService sysUserBargeBakService;

    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    @PostMapping("/searchbyid")
    public AjaxResult searchById(@RequestBody Long id){
        BargeCompanyResultVO bargeCompanyResultVO = new BargeCompanyResultVO();
        SysUserBargeBak sysUserBargeBak = sysUserBargeBakService.getOne(new LambdaQueryWrapper<SysUserBargeBak>().eq(SysUserBargeBak::getBargeAuditId,id));
        if(sysUserBargeBak != null){
            bargeCompanyResultVO = pb6BargeinfoAuditService.searchPb6BargeinfoAuditByBakId(id);
        }
        else {
            bargeCompanyResultVO = pb6BargeinfoAuditService.searchPb6BargeinfoAuditById(id);
        }
        if(bargeCompanyResultVO != null){
            return AjaxResult.success(bargeCompanyResultVO);
        } else {
            return AjaxResult.error("没有找到驳船备份信息");
        }
    }

    @PostMapping("/compareDiffField")
    public AjaxResult compareDiffField(@RequestBody Long id){
        Pb6BargeinfoAudit pb6BargeinfoAudit = pb6BargeinfoAuditService.getById(id);
        Pb6Bargeinfo pb6Bargeinfo = pb6BargeinfoService.getById(pb6BargeinfoAudit.getPb6BargeInfoId());

        Pb6Bargeinfo pb6Bargeinfo1 = new Pb6Bargeinfo();
        BeanUtils.copyProperties(pb6BargeinfoAudit, pb6Bargeinfo1);

        //获取不同的属性
        Equator equator = new GetterBaseEquator();
        List<FieldInfo> fieldInfoList1 = equator.getDiffFields(pb6Bargeinfo1,pb6Bargeinfo);
        return AjaxResult.success(fieldInfoList1);
    }
}

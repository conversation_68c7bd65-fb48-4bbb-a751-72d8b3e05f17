package com.ruoyi.common.domain.bo;

import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/12 14:17
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class UploadBO {

    /**
     * 关联id
     */
    @NotBlank(message = "关联id不能为空")
    private Long linkId;

    /**
     * 资料类型
     */
    @NotBlank(message = "资料类型不能为空")
    private String linkType;

    /**
     * 公司id
     */
    private Long linkCompanyId;

    /**
     * 资料名称
     */
    private Integer dataType;
}

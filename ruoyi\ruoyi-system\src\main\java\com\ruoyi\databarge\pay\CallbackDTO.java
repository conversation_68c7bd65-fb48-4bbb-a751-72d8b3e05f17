package com.ruoyi.databarge.pay;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Description: 统一回调方法的数据
 *
 * @Author: ChenJin on 2021/2/1.
 * @Date: 2021/2/1 9:51
 */
@Getter
@Setter
@ToString
public class CallbackDTO {

    private Resmsg resmsg;

    @Getter
    @Setter
    @ToString
    public static class Resmsg{
        private String orderid; // 订单号
        private String chanflow; // 渠道流水号
        private String biztype; // 交易类型
        private String payorgcd; // 支付渠道编号
        private String payorgnm; // 支付渠道名称
        private String paysts; // 状态
        private String payDate; // 支付日期
        private String amt; // 金额
        private String payid; // 支付渠道交易号
        private String busprocd; // 业务处理码
        private String busprodesc; // 业务处理描述
        private String signature; // 签名串
    }
}

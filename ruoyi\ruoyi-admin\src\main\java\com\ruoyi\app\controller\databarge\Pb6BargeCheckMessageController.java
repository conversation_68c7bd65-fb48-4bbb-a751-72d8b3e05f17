package com.ruoyi.app.controller.databarge;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.app.controller.support.print.PrintUtil;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.bo.UserMessageBO;
import com.ruoyi.common.enums.BargeCheckMessageType;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.AppNoticeService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.message.WechatMessageUtil;
import com.ruoyi.databarge.DataScope;
import com.ruoyi.databarge.domain.Pb6BargeCheckMessage;
import com.ruoyi.databarge.domain.Pb6Cargoconsignmentdetail;
import com.ruoyi.databarge.domain.dto.XGPdfDTO;
import com.ruoyi.databarge.domain.vo.Pb6BargeCheckMessageVO;
import com.ruoyi.databarge.mapper.Pb6BargeCheckMessageMapper;
import com.ruoyi.databarge.service.Pb6CargoconsignmentdetailService;
import com.ruoyi.framework.web.service.WebSocketServer;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.databarge.domain.dto.Pb6BargeCheckMessageSearchDTO;
import com.ruoyi.databarge.service.Pb6BargeCheckMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 驳船审核消息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@RestController
@RequestMapping("/barge/barge-check-message")
public class Pb6BargeCheckMessageController {

    @Autowired
    private Pb6BargeCheckMessageService pb6BargeCheckMessageService;

    @Autowired
    private Pb6CargoconsignmentdetailService pb6CargoconsignmentdetailService;

    @Autowired
    private AppNoticeService appNoticeService;

    @Autowired
    private Pb6BargeCheckMessageMapper pb6BargeCheckMessageMapper;

    @Autowired
    private PrintUtil printUtil;

    @Autowired
    private WechatMessageUtil messageUtil;

    @PostMapping("/searchPage")
    public AjaxResult searchPage(@RequestBody Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO) {
        DataScope.getDataScope("c", pb6BargeCheckMessageSearchDTO, Boolean.TRUE);
        return AjaxResult.success(pb6BargeCheckMessageService.searchPage(pb6BargeCheckMessageSearchDTO));
    }

    @GetMapping("/dealLoadMessage")
    public AjaxResult dealLoadMessage(){
        // 2021.07.27 查找消息时，先查所有的未配载的消息处理
        List<Pb6BargeCheckMessage> pb6BargeCheckMessageList = pb6BargeCheckMessageService.list(new LambdaQueryWrapper<Pb6BargeCheckMessage>().eq(Pb6BargeCheckMessage::getAuditflag, 0)
                .eq(Pb6BargeCheckMessage::getMtype, 11)
                .eq(Pb6BargeCheckMessage::getDelflag, 0));
        for(Pb6BargeCheckMessage pb6BargeCheckMessage: pb6BargeCheckMessageList){
            if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(pb6BargeCheckMessage.getConsignflag())){
                List<Pb6Cargoconsignmentdetail> pb6CargoconsignmentdetailList = pb6CargoconsignmentdetailService.list(
                        new LambdaQueryWrapper<Pb6Cargoconsignmentdetail>()
                                .eq(Pb6Cargoconsignmentdetail::getConsignflag, pb6BargeCheckMessage.getConsignflag())
                                .eq(Pb6Cargoconsignmentdetail::getBargename, pb6BargeCheckMessage.getBargename()));
                for(Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail: pb6CargoconsignmentdetailList){
                    if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWaterwaycargoid())){
                        //已配载
                        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
                        pb6BargeCheckMessage.setAuditmanid(sysUser.getUserId());
                        pb6BargeCheckMessage.setAuditman(sysUser.getUserName());
                        pb6BargeCheckMessage.setAudittime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
                        pb6BargeCheckMessage.setAuditflag(1);
                        pb6BargeCheckMessage.setWaterwaycargoid(pb6Cargoconsignmentdetail.getWaterwaycargoid());
                        pb6BargeCheckMessageService.updateById(pb6BargeCheckMessage);
                    }
                }
            }
        }
        // 2021.07.27 查找消息时，先查所有的未配载的消息处理
        return AjaxResult.success();
    }

    @PostMapping("/searchPagePort")
    public AjaxResult searchPagePort(@RequestBody Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO) {
        return AjaxResult.success(pb6BargeCheckMessageService.searchPagePort(pb6BargeCheckMessageSearchDTO));
    }


    /**
     * 物流公司配载后发消息给到驳船主(发给所有订阅的人)
     *
     * @param consignId       运单id
     * @param consignDetailId 运单明细id
     */
    @PostMapping("/sendMessageForStowageOver")
    public AjaxResult sendMessageForStowageOver(@RequestParam("consignId") Long consignId, @RequestParam("consignDetailId") Long consignDetailId) {
        Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailService.getById(consignDetailId);
        if (StringUtils.isNotBlank(pb6Cargoconsignmentdetail.getWaterwaycargoid())) {
            Map<String, Object> params = new HashMap<>();
            params.put("first", "订单生成通知");
            params.put("keyword1", DateUtils.getTime());
            params.put("keyword2", "托运单" + pb6Cargoconsignmentdetail.getConsignflag() + "已配载");
            params.put("keyword3", pb6Cargoconsignmentdetail.getWaterwaycargoid());
            params.put("remark", "长航已完成配载，请您及时支付，并预约到港时间。");
            UserMessageBO userMessageBO = new UserMessageBO();
            userMessageBO.setParams(params);
            userMessageBO.setSendMessageType("zo14mXjFoYPv7W_fmiCsvhIyCSLKJW3iRYMt-W1tbEE");
            userMessageBO.setConsignId(consignId);
            userMessageBO.setConsignDetailId(consignDetailId);
            return appNoticeService.sendMessage(userMessageBO);
        } else {
            return AjaxResult.error("未找到水路运单号，请检查托运单信息");
        }
    }

    @Log(title = "驳船发送实装数", businessType = BusinessType.INSERT)
    @PostMapping("/sendMessageForPortLoadingOver")
    public AjaxResult sendMessageForPortLoadingOver(@RequestParam("waterWayCargoId") String waterWayCargoId) throws URISyntaxException {
        XGPdfDTO xgPdfDTO = printUtil.searchXGPdfDTO(waterWayCargoId);
        try {
            pb6BargeCheckMessageService.sendMessageForPortLoadingOver(waterWayCargoId,xgPdfDTO);
        }
        catch (Exception e){
            messageUtil.updateAccessToken();
            pb6BargeCheckMessageService.sendMessageForPortLoadingOver(waterWayCargoId,xgPdfDTO);
        }
        return AjaxResult.success();
    }

    //更新可配载的消息记录为已配载
    @Log(title = "驳船消息管理", businessType = BusinessType.UPDATE)
    @PostMapping("/updateMessageForCargoConStowage")
    public AjaxResult updateMessageForCargoConStowage(@RequestBody Pb6BargeCheckMessage pb6BargeCheckMessage) {
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        pb6BargeCheckMessage.setAuditmanid(sysUser.getUserId());
        pb6BargeCheckMessage.setAuditman(sysUser.getUserName());
        pb6BargeCheckMessage.setAudittime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
        pb6BargeCheckMessage.setAuditflag(1);
        return AjaxResult.success(pb6BargeCheckMessageService.updateById(pb6BargeCheckMessage));
    }

    @Log(title = "驳船消息管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteMessage")
    public AjaxResult deleteMessage(@RequestBody Pb6BargeCheckMessage pb6BargeCheckMessage) {
        if(pb6BargeCheckMessage.getAuditflag() != 0){
            throw new CustomException("该消息已审核，不可删除");
        }
        pb6BargeCheckMessage.setDelflag(1);
        return AjaxResult.success(pb6BargeCheckMessageService.updateById(pb6BargeCheckMessage));
    }

    //获取可配载消息数
    @GetMapping("/stowageMsgNum")
    public AjaxResult stowageMsgNum() {
        return AjaxResult.success(pb6BargeCheckMessageService.stowageMsgNum());
    }

    @GetMapping("/test")
    public AjaxResult test() throws IOException {
        Pb6BargeCheckMessage pb6BargeCheckMessage = pb6BargeCheckMessageService.getById(1);
        WebSocketServer.sendInfo(pb6BargeCheckMessage.getConsignflag(), "1");
        return AjaxResult.success();
    }

    @PostMapping("/searchPortMessagePage")
    public AjaxResult searchPortMessagePage(@RequestBody Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO) {
        DataScope.getDataScope("a", pb6BargeCheckMessageSearchDTO, Boolean.FALSE);
        IPage<Pb6BargeCheckMessageVO> pb6BargeCheckMessageVOIPage= pb6BargeCheckMessageService.searchPortMessagePage(pb6BargeCheckMessageSearchDTO);
        List<Pb6Cargoconsignmentdetail> list = pb6CargoconsignmentdetailService.list(new LambdaQueryWrapper<Pb6Cargoconsignmentdetail>().isNotNull(Pb6Cargoconsignmentdetail::getCancelreservationreason));
        list.forEach(item ->{
            Pb6BargeCheckMessageVO pb6BargeCheckMessageVO = new Pb6BargeCheckMessageVO();
            pb6BargeCheckMessageVO.setBargename(item.getBargename());
            pb6BargeCheckMessageVO.setApplytime(item.getCanceltime());
            pb6BargeCheckMessageVO.setWaterwaycargoid(item.getWaterwaycargoid());
            pb6BargeCheckMessageVO.setRemark("自动取消预约");
            pb6BargeCheckMessageVOIPage.getRecords().add(pb6BargeCheckMessageVO);
        });
        if (pb6BargeCheckMessageVOIPage.getRecords().size()>0){
            for(int i=0;i<pb6BargeCheckMessageVOIPage.getRecords().size();i++){
                if(pb6BargeCheckMessageVOIPage.getRecords().get(i).getMtype() == null){
                    pb6BargeCheckMessageVOIPage.getRecords().get(i).setMessageContent(pb6BargeCheckMessageVOIPage.getRecords().get(i).getBargename()+":"+pb6BargeCheckMessageVOIPage.getRecords().get(i).getApplytime().toString().substring(0,19)+"自动取消预约");
                }
                else if (pb6BargeCheckMessageVOIPage.getRecords().get(i).getMtype().equals("7")){
                    pb6BargeCheckMessageVOIPage.getRecords().get(i).setMessageContent(pb6BargeCheckMessageVOIPage.getRecords().get(i).getBargename()+" : "+pb6BargeCheckMessageVOIPage.getRecords().get(i).getApplytime().toString().substring(0,19)+"提交退单审核");
                }else if (pb6BargeCheckMessageVOIPage.getRecords().get(i).getMtype().equals("9")){
                    pb6BargeCheckMessageVOIPage.getRecords().get(i).setMessageContent(pb6BargeCheckMessageVOIPage.getRecords().get(i).getBargename()+" : "+pb6BargeCheckMessageVOIPage.getRecords().get(i).getApplytime().toString().substring(0,19)+"提交改单审核");
                }else{
                    pb6BargeCheckMessageVOIPage.getRecords().get(i).setMessageContent(pb6BargeCheckMessageVOIPage.getRecords().get(i).getBargename()+" : "+pb6BargeCheckMessageVOIPage.getRecords().get(i).getApplytime().toString().substring(0,19)+"自动报到");
                }
            }
        }
        return AjaxResult.success(pb6BargeCheckMessageVOIPage);
    }

    @GetMapping("/countBargeNumber")
    public AjaxResult countBargeNumber(){
        return AjaxResult.success(pb6BargeCheckMessageMapper.countBargeNumber());
    }
}




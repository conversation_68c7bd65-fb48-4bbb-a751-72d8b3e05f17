package com.ruoyi.barge.domain.vo;

import lombok.*;

/**
 * 驳船主托运 vo
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/10 14:38
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class BargeConsignVO {

    /**
     * 托运单明细id
     */
    private Long consignDetailId;

    /**
     * 驳船主键id
     */
    private Long bargeInfoId;

    /**
     * 托号
     */
    private String consignFlag;

    /**
     * 货物名称
     */
    private String cargeName;

    /**
     * 起运港
     */
    private String beginPort;

    /**
     * 目的港
     */
    private String endPort;

    /**
     * 托运单重量
     */
    private String rationWeight;

    /**
     * 托运联系人
     */
    private String wxRationContactNumber;

    /**
     * 收货人
     */
    private String consignee;

    /**
     * 驳船状态 0：未审核 1：已审核 2：审核不通过 3：已配载 4：已报到 5：已离港 6:已退单
     */
    private String flagBargeState;

    /**
     * 状态
     */
    private Integer wxOperateState;

    /**
     * 驳船名称
     */
    private String bargeName;

    /**
     * 计算方式 费用结算方式，现结、月结
     */
    private String chargeBalanceType;

    /**
     * 是否订阅
     */
    private Boolean isSubscription;

    /**
     * 托运主表id
     */
    private Long consignId;

    private Integer wxConfirmCheck;

    private String bargeTel;

    /**
     * 月结公司id
     */
    private Long wxMonthChargeById;

    /**
     * 月结公司名称
     */
    private String wxMonthChargeByName;

    /**
     * 货方联系方式
     */
    private String customerUserId;

    /**
     * 支付人
     */
    private Long payUserId;

    /**
     * 退改单状态 0为申请退单中，1为申请退单通过，2为申请退单失败,3为申请改单中，4为申请改单成功，5为申请改单失败
     */
    private String applyModify;

    /**
     * 月结码
     */
    private String monthlyCode;

    /**
     * 创建时间-办单时间
     */
    private String applyDate;

    /**
     * 申请日期
     */
    private String recordDate;

    /**
     * 出库单号、
     */
    private String outOrInformId;

    // 水路运单ID
    private String waterwaycargoid;

    // 驳船航次
    private String bargeNumber;

    // 大船名
    private String shipName;

    // 货物类别
    private String cargoType;
    

    //以下用于船长声明
    //提单号
    private String billNo;

    //货量
    private String totalTonnage;

    //计划日期
    private String plannedDepartureDate;

    //海事处
    private String maritimeOffice;

    //泊位
    private String berth;
}

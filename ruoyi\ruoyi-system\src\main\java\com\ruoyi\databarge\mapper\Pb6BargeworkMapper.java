package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.databarge.domain.Pb6Bargework;
import com.ruoyi.databarge.domain.dto.Pb6BargeWorkDTO;
import com.ruoyi.databarge.domain.vo.Pb11TallysheetVO;
import com.ruoyi.databarge.domain.vo.Pb6BargeWorkVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface Pb6BargeworkMapper extends BaseMapper<Pb6Bargework> {
    String searchUniqecode(@Param("coutformid") String coutformid);
    IPage<Pb6BargeWorkVo> searchPagePb6BargeWorkVo(Page<Pb6Bargework> page, @Param("pb6BargeWorkDTO")Pb6BargeWorkDTO pb6BargeWorkDTO);
    List<Pb6Bargework> searchPb6BargeWorkRepeat(@Param("registertime") String registertime,@Param("bargename") String bargename);
    String searchPhonenumber(@Param("waterwaycargoid") String waterwaycargoid);//新沙驳船报到增加预约人电话
    List<Pb11TallysheetVO> searchPb11Tallysheet(@Param("waterwaycargoid") String waterwaycargoid);
    List<Pb6Bargework> pb6BargeworkCancle();
    List<String> searchShipmentrate(@Param("coutformid") String coutformid);

    //通过出库单号查找大货类名称
    String findCargoTypeByCoutformid(@Param("coutformid") String coutformid);
    //新港判断货类
    List<Map<String,Object>> findCargoNameByCoutformid(@Param("coutformid") String coutformid);
}

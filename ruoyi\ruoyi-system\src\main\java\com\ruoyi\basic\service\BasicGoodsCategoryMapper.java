package com.ruoyi.basic.service;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.basic.domain.BasicGoodsCategory;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【BASIC_GOODS_CATEGORY】的数据库操作Mapper
* @createDate 2023-11-16 11:35:21
* @Entity com.ruoyi.project.basic.domain.BasicGoodsCategory
*/
public interface BasicGoodsCategoryMapper extends BaseMapper<BasicGoodsCategory> {

    // 货类List
    List<BasicGoodsCategory> selectBasicGoodsCategoryList(BasicGoodsCategory basicGoodsCategory);

}





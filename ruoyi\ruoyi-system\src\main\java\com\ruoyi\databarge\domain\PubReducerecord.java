package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 出库单预扣解扣记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PUB_REDUCERECORD")
@KeySequence("SEQ_PUB_REDUCERECORD")
public class PubReducerecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;
    /**
     * 指令编号
     */
    @TableField("ORDERNUM")
    private String ordernum;

    /**
     * 消减吨数
     */
    @TableField("REDUCEWEIGHT")
    private String reduceweight;

    /**
     * 操作类型
     */
    @TableField("OPERATETYPE")
    private String operatetype;

    /**
     * 操作模块
     */
    @TableField("OPERATEPART")
    private String operatepart;

    /**
     * 操作时间
     */
    @TableField("OPERATETIME")
    private String operatetime;

    /**
     * 是否预扣
     */
    @TableField("YUKOU")
    private Long yukou;

    /**
     * 预扣重量
     */
    @TableField("YUKOUWEIGHT")
    private String yukouweight;

    /**
     * 是否解扣
     */
    @TableField("JIEKOU")
    private Long jiekou;

    /**
     * 解扣重量
     */
    @TableField("JIEKOUWEIGHT")
    private String jiekouweight;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 出库单号
     */
    @TableField("COUTFORMNUM")
    private String coutformnum;


}

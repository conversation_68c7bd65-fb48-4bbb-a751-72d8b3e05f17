package com.ruoyi.contacts.service;

import com.ruoyi.barge.domain.bo.BargeUserBO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.Customer;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.dto.CargocmentDTO;
import com.ruoyi.common.domain.vo.WaterwayCargoVO;
import com.ruoyi.consignor.domain.vo.ConsignorConsignVO;

import java.util.List;

/**
 * @Description 托运单联系人业务接口
 * <AUTHOR>
 * @Date 2020/8/18  9:21
 */
public interface ContactsConsignService {

    // 支付（月结、现结）
    AjaxResult defray(CargocmentdetailBO cargocmentdetailBO);

    // 预约、取消预约
    AjaxResult reservation(Long id, String wxAppOintmentTime, Integer wxOperateState);

    // 待确认审核
    void check(CargocmentdetailBO cargocmentdetailBO);

    // 查询托运单列表
    List<ConsignorConsignVO> bookingNoteList(Long status, String searchValue, String beginTime, String endTime);

    // 水路运单详情
    WaterwayCargoVO waybillDetail(Long id, Integer status);

    // 差额退款申请
    AjaxResult balanceRefund(CargocmentdetailBO cargocmentdetailBO);

    // 个人信息保存
    AjaxResult personRecord(BargeUserBO bargeUserBO);

    // 个人详情
    AjaxResult recordDetail();
}

package com.ruoyi.businessFile.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.businessFile.domain.BusinessFile;
import com.ruoyi.businessFile.domain.BusinessFileType;
import com.ruoyi.businessFile.domain.VFileSummary;
import com.ruoyi.businessFile.domain.vo.BusinessFileVo;
import com.ruoyi.businessFile.domain.vo.FileVo;
import com.ruoyi.businessFile.mapper.BusinessFileMapper;
import com.ruoyi.businessFile.mapper.VFileSummaryMapper;
import com.ruoyi.businessFile.service.BusinessFileService;
import com.ruoyi.businessFile.service.BusinessFileTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 15:03
 */
@Service
@Transactional(rollbackFor=Exception.class)
public class BusinessFileServiceImpl extends ServiceImpl<BusinessFileMapper, BusinessFile> implements BusinessFileService {

    @Autowired
    BusinessFileTypeService businessFileTypeService;

    @Autowired
    BusinessFileMapper businessFileMapper;

    @Autowired
    VFileSummaryMapper vFileSummaryMapper;

    @Override
    public List<FileVo> listConFileByfileBusinessID(FileVo fileVo) {
        BusinessFile businessFile = new BusinessFile();

        businessFile.setFileBusinessType(fileVo.getFileBusinessType());
        businessFile.setFileBusinessID(fileVo.getFileBusinessID());

        return businessFileMapper.listBusinessFile(businessFile);
    }

    @Override
    public VFileSummary listVFileSummary(String fileBusinessType, String fileBusinessId) {
        VFileSummary vFileSummary=vFileSummaryMapper.selectOne(new QueryWrapper<VFileSummary>().lambda().eq(VFileSummary::getFileBusinessId,fileBusinessId)
                .eq(VFileSummary::getFileBusinessType,fileBusinessType));
        return vFileSummary;
    }

    @Override
    public BusinessFileVo selectBusinessFileByFileId(String fileId) {
        BusinessFileVo businessFileVo = baseMapper.selectBusinessFileByFileId(fileId);
        // 处理url，url格式为/gzcw/businessFiles/2024/3/11/中谷码头_20240311081810099.xls，处理为https://scbs.gzport.com/businessFile/2024/3/11/中谷码头_20240311081810099.xls
        // 即https://scbs.gzport.com/businessFile+ subString（19）
        String url = businessFileVo.getUrl();
        url = "https://scbs.gzport.com/businessFile" + url.substring(19);
        businessFileVo.setUrl(url);
        return businessFileVo;
    }

    @Override
    public boolean isAllFileUpload(Long fileBusinessId) {
        // 当前大船上传的所有文件
        List<BusinessFile> businessFiles = businessFileMapper.selectFileListByShipId(fileBusinessId);
        // 大船所需上传的文件类型
        List<BusinessFileType> businessFileTypes = businessFileTypeService.selectAllRequiredShipFile();

        // 遍历所需文件类型，判断是否都已上传
        for (BusinessFileType businessFileType : businessFileTypes) {
            String fileTypeName = businessFileType.getFileTypeName();
            // 判断fileTypeName是否在businessFiles中能找到
            boolean flag = false;
            for (BusinessFile businessFile : businessFiles) {
                if (fileTypeName.equals(businessFile.getFileTypeName())) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                return false;
            }
        }
        return true;
    }

}

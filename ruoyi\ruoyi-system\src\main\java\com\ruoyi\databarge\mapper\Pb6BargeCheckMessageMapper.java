package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.domain.SysUserMessage;
import com.ruoyi.common.domain.vo.UserMessageVO;
import com.ruoyi.databarge.domain.Pb6BargeCheckMessage;
import com.ruoyi.databarge.domain.dto.Pb6BargeCheckMessageSearchDTO;
import com.ruoyi.databarge.domain.vo.Pb6BargeCheckMessageVO;
import com.ruoyi.databarge.domain.vo.PortLoadingMsgVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 驳船审核消息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
public interface Pb6BargeCheckMessageMapper extends BaseMapper<Pb6BargeCheckMessage> {

    IPage<Pb6BargeCheckMessage> searchPage(Page<Pb6BargeCheckMessage> page, @Param("pb6BargeCheckMessageSearchDTO") Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO);

    PortLoadingMsgVO searchPortLoadingInfo(@Param("waterwaycargoid") String waterwaycargoid);
    Long searchPortLoadingAmt(@Param("waterwaycargoid") String waterwaycargoid);
    IPage<Pb6BargeCheckMessageVO> searchPortMessagePage(Page<Pb6BargeCheckMessageVO> page,@Param("pb6BargeCheckMessageSearchDTO") Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO);
    String countBargeNumber();

    IPage<Pb6BargeCheckMessage> searchPagePort(Page<Pb6BargeCheckMessage> page, @Param("pb6BargeCheckMessageSearchDTO") Pb6BargeCheckMessageSearchDTO pb6BargeCheckMessageSearchDTO);

    UserMessageVO officialAccountInfor(SysUserMessage sysUserMessage);
}

package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;


/**
 * @Description 运单明细实体
 * <AUTHOR>
 * @Date 2020/8/4 14:52
 */
@Setter
@Getter
@ToString
@TableName(value = "PB6_CARGOCONSIGNMENTDETAIL")
@KeySequence("SEQ_PB6_CARGOCONSIGNMENTDETAIL")
public class Cargoconsignmentdetail {
    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    @TableField("bargename")
    private String bargeName;

    @TableField("consignflag")
    private String consignFlag;

    @TableField("consignid")
    private Long consignId;

    @TableField("rationpiece")
    private String rationPiece;

    @TableField("rationweight")
    private String rationWeight;

    @TableField("chargeback")
    private String chargeBack;

    @TableField("isverificate")
    private String isVerificate;

    @TableField("isverificatech")
    private String isVerificatech;

    @TableField("isverificatemaritime")
    private String isVerificatemariTime;

    @TableField("verificateman")
    private String verificateMan;

    @TableField("verificatemanmari")
    private String verificateManMari;

    @TableField("verificatereason")
    private String verificateReason;

    @TableField("verificatereasonmari")
    private String verificateReasonMari;

    @TableField("verificatetime")
    private String verificateTime;

    @TableField("verificatetimemari")
    private String verificatetImeMari;

    @TableField("isfinished")
    private String isFinished;

    @TableField("onineresourse")
    private String onineresourse;

    @TableField("flagbargestate")
    private String flagBargeState;

    @TableField("waterwaycargoid")
    private String waterwayCargoId;

    @TableField("cargoconsigncheckreason")
    private String cargoConsignCheckReason;

    @TableField("bargetel")
    private String bargeTel;

    @TableField("applymodify")
    private String applyModify;

    @TableField("shipowner")
    private String shipowner;

    @TableField("chargebalancetype")
    private String chargeBalanceType;

    @TableField("shippingconame")
    private String shippingCoName;

    @TableField("isconfirm")
    private String isConfirm;

    @TableField("mmsi")
    private String mmsi;

    @TableField("serialnumber")
    private String serialNumber;

    @TableField("modifyreason")
    private String modifyreason;

    @TableField("customeruserid")
    private String customerUserId;

    @TableField("applytime")
    private String applyTime;

    @TableField("isharry")
    private String isHarry;

    @TableField("workweight")
    private String workWeight;

    /**
     * 小程序操作状态(0:待驳船主确认 1:待支付  2:待船公司审批(月结)，3船公司审批不通过(月结) 4:待驳船主预约
     * 5:驳船主已预约 6:取消预约 )
     */
    @TableField("wxoperatestate")
    private Integer wxOperateState;
    /**
     * 是否托运单办理人审核(差额退款申请审核专用) 0：待审核，1：审核通过，2：审核不通过
     */
    @TableField("iswxcheck")
    private Integer isWxCheck;
    /**
     * 物流公司差额退款审核（0：待审核，1：审核通过，2：审核不通过）
     */
    @TableField("WXCONFIRMCHECK")
    private Integer wxConfirmCheck;


    /**
     * 驳船预约时间
     */
    @TableField("wxappointmenttime")
    private String wxAppointmentTime;
    /**
     * 月结算审核单位(船公司id)
     */
    @TableField("wxmonthchagebyid")
    private Long wxMonthChargeById;
    /**
     * 月结算审核单位(船公司名称)
     */
    @TableField("wxmonthchagebyname")
    private String wxMonthChargeByName;
    /**
     * 托运单联系人电话
     */
    @TableField("wxrationcontactnumber")
    private String wxRationContactNumber;

    /**
     * 驳船主键id
     */
    @TableField("bargeid")
    private Long bargeId;

    /**
     *  退单改单流程(7:待审核(退单改单) 8:审核通过(退单改单) 9:审核不通过(退单改单))
     */
/*
    /*@TableField("wxapplymodifyaudit")
    private Integer wxApplyModifyAudit;*/


    /**
     * 退/改单(审核)失败原因
     */
    @TableField("failurereasons")
    private String failureReasons;

    @TableField("wxcreatebyid")
    private Long wxCreateById;//创建人
    @TableField("wxcreatetime")
    private String wxCreateTime;//创建时间
    @TableField("wxupdatebyid")
    private Long wxUpdateById;//修改人
    @TableField("wxupdatetime")
    private String wxUpdateTime;//修改时间

/*
    @TableField("WXAPPLYMAN")
    private String wxApplyman;//退单改单提交人
    @TableField("WXAPPLYTIME")
    private String wxApplytime;//退单改单提交时间
    @TableField("WXAPPLYAUDITMAN")
    private String wxApplyAuditman;//退单改单审核人
    @TableField("WXAPPLYAUDITTIME")
    private String wxApplyAudittime;//退单改单审核时间
*/

    /**
     *  预约人
     */
    @TableField("WXOINTMENTMID")
    private Long wxointmentmId;

    /**
     * 操作时间 预约
     */
    @TableField("WXOPERATETIME")
    private String wxOperateTime;



    /**
     * 当前流程节点： 0提交审核，1物流公司直接审核，2码头审核， 3物流公司间接审核
     */
    @TableField("WXNODE")
    private Integer WxNode;


    /**
     * 退改单当前流程节点审核状态： 0未审核，1审核不通过
     */
    @TableField("AUDITSTATES")
    private Integer auditStates;


    /**
     * 新托运明细表ID
     */
    @TableField("NEWDETAILID")
    private Long newDetailId;

    /**
     * 支付人
     */
    @TableField("PAYUSERID")
    private Long payUserId;

    /**
     * 新托运单标志(0：正常办理，1：新生成的托运单)
     */
    @TableField("NEWWAYBILL")
    private String newWaybill;

    @TableField("MONTHLYCODEID")
    private Long monthlyCodeId;

    @TableField(exist = false)
    private String tscargoweightValue; //实装吨数

    /**
     * 申请月结审批时间
     */
    @TableField("MTHAPPLYCHECKTIME")
    private String mthApplyCheckTime;

    @TableField("ISOFFLINE")
    private String isOffline;

    private Date wxConfirmTime;

    private Date wxGenerateTime;
}

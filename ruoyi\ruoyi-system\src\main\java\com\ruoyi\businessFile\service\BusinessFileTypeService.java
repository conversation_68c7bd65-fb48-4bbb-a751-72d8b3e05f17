package com.ruoyi.businessFile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.businessFile.domain.BusinessFile;
import com.ruoyi.businessFile.domain.BusinessFileType;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 15:02
 */
public interface BusinessFileTypeService extends IService<BusinessFileType> {

    List<BusinessFileType> list(BusinessFileType businessFileType);

    AjaxResult insert(BusinessFileType businessFileType);

    AjaxResult update(BusinessFileType businessFileType);

    List<BusinessFileType> getFileTypeName();

    // 大船必须上传的文件类型
    List<BusinessFileType> selectAllRequiredShipFile();

}

package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.databarge.domain.UploadAddressDomain;
import com.ruoyi.databarge.domain.dto.BargePictureSearchDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/11/6.
 * @Date: 2020/11/6 10:54
 */
public interface UploadAddressDomainMapper extends BaseMapper<UploadAddressDomain> {

    List<UploadAddressDomain> searchBargeOwnerIdentityPic(@Param("bargePictureSearchDTO") BargePictureSearchDTO bargePictureSearchDTO);

    List<UploadAddressDomain> searchBargeOwnerIdentityPicByUploadUserId(@Param("bargePictureSearchDTO") BargePictureSearchDTO bargePictureSearchDTO);

    List<UploadAddressDomain> searchPb6WatercargoFile(@Param("id") String id);

    List<UploadAddressDomain> searchPb6WatercargoFileByWaterwaycargoId(@Param("waterwaycargoid") String waterwaycargoid);

    String countWaterwaycargoNumber();

    List<UploadAddressDomain> searchUploadAddress(@Param("startTime") String startTime,@Param("endTime") String endTime);
}

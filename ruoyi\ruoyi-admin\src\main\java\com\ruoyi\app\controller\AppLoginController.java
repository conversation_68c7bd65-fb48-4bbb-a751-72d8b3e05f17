package com.ruoyi.app.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.common.utils.wechart.WxAppletOpenidUtil;
import com.ruoyi.common.utils.wechart.WxOpenIdPo;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysUserService;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.spec.InvalidParameterSpecException;
import java.util.Arrays;

/**
 * @Description app登陆
 * <AUTHOR>
 * @Date 2020/7/24 16:51
 */
@RestController
@RequestMapping("/appLogin")
public class AppLoginController {


    @Autowired
    private SysLoginService loginService;

    @Autowired
    private WxAppletOpenidUtil wxAppletOpenidUtil;

    @Autowired
    private TokenService tokenService;


    @Autowired
    private ISysUserService userService;

    /**
     * 登录方法
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        loginBody.setLoginType("app");
        // 生成令牌
        String token = loginService.login(loginBody.getLoginType(),loginBody.getUserType(),loginBody.getWxCode(),loginBody.getPhoneNumber(),loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid(),loginBody.getIndexNumber());
        if (ObjectUtil.isNotNull(loginBody.getIndexNumber()) && !"gzonlineuser".equals(loginBody.getUserType())){
            switch (loginBody.getIndexNumber()){
                case 1:
                    return new AjaxResult(700,token);
            }
        }
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 驳船用户首次登陆选择用户类型
     * @param sysUser
     * @return
     */
    @PostMapping("/updateBargeUserType")
    public AjaxResult updateBargeUserType(@RequestBody SysUser sysUser){
        //更新数据库
        userService.updateUser(sysUser);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        user.setUserType(sysUser.getUserType());
        tokenService.setLoginUser(loginUser);
        return AjaxResult.success();
    }

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/getWxOpenId")
    public AjaxResult getWxOpenId(@RequestBody LoginBody loginBody)
    {
        WxOpenIdPo wxOpenIdPo = null;
        if("bargeuser".equalsIgnoreCase(loginBody.getUserType())){
            wxOpenIdPo=wxAppletOpenidUtil.GetWxOpenIdBarge(loginBody.getWxCode());
        }else if("wabilluser".equalsIgnoreCase(loginBody.getUserType()) || "gzonlineuser".equalsIgnoreCase(loginBody.getUserType())){
            wxOpenIdPo=wxAppletOpenidUtil.GetWxOpenIdConsignor(loginBody.getWxCode());
        }
        System.out.println(wxOpenIdPo.toString());
        return AjaxResult.success(wxOpenIdPo);
    }

    /**
     * 解密用户敏感数据获取用户信息
     * @param --sessionKey 数据进行加密签名的密钥
     * @param --encryptedData 包括敏感数据在内的完整用户信息的加密数据
     * @param --iv 加密算法的初始向量
     */
    @PostMapping("/getWxUserInfo")
    public AjaxResult getUserInfo(@RequestBody LoginBody loginBody){
        // 被加密的数据
        byte[] dataByte = Base64.decode(loginBody.getEncryptedData());
        // 加密秘钥
        byte[] keyByte = Base64.decode(loginBody.getSessionKey());
        // 偏移量
        byte[] ivByte = Base64.decode(loginBody.getIv());
        try {
            // 如果密钥不足16位，那么就补足.  这个if 中的内容很重要
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding","BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, "UTF-8");
                return AjaxResult.success(JSONObject.parseObject(result));
            }
        }catch (NoSuchProviderException e) {
            e.printStackTrace();
        }catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (BadPaddingException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (InvalidAlgorithmParameterException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (InvalidParameterSpecException e) {
            e.printStackTrace();
        }
        return null;
    }

}

package com.ruoyi.common.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.domain.Cargoconsignment;
import com.ruoyi.common.domain.UploadAddress;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @Description 运单查询类
 * <AUTHOR>
 * @Date 2020/8/5 9:47
 */
@Setter
@Getter
public class CargocmentBO extends BaseEntity {

    /**
     * 文件信息
     */
    private List<UploadAddress> uploadAddress;

    /**
     * 月结算审核单位(船公司id)
     */
    private Long wxMonthChargeById;

    /**
     * 月结算审核单位(船公司名称)
     */
    private String wxMonthChargeByName;

    /**
     * 运单信息
     */
    private Cargoconsignment cargoconsignment;

    /**
     * 派船时选择的驳船
     */
    private List<BargeInfoBO> bargeInfos;

    /**
     * 是否加急
     */
    private String isHarry;

    /**
     * 可配载重量（出库单中带出来的）
     */
    private String weightValue;

    private String amt;

    /**
     * 可配载件数（出库单中带出来的）
     */
    private String pieceValue;

    /**
     * 旧文件的id
     */
    private String fileIds;


    /**
     * 托运单配载重量  等改单确定下来再决定删 或留
     */
//    private String rationWeight;

    /**
     * 托运单明细表id
     */
    private List<Long> ids;


    /**
     * 区别是货主添加明细单  1.货主  其他.null (不用填)
     */
    private Integer flag;


    private Long companyId;//查询参数


    /**
     * 航区： A级、B级、null
     */
    private String navigatingZone;

    private String tscargoweightValue; //实装吨数

}

package com.ruoyi.common.utils.ftp;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import com.ruoyi.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.io.*;

/**
 * <AUTHOR>
 * @date 2021年06月30日 15:50
 */
@Slf4j
public class BargeSftpUtil {

    private SftpPool pool;

    public BargeSftpUtil(SftpPool pool) {
        this.pool = pool;
    }

    /**
     * sftp服务器路径
     */
    @Value("${barge.ftp.path.cargoFilePath}")
    private volatile String ftpBasePath;

    /**
     * 港盛sftp服务器路径
     */
    @Value("${barge.ftp.path.uploadCargoFileLocation}")
    private volatile String uploadCargoFileLocation;


    /**
     * 上传文件
     *
     * @throws SftpException
     * @throws IOException
     */
    public String uploadFile(InputStream in, String filename, String waterwayCargoId) throws SftpException, IOException, JSchException {
        // ChannelSftp sftp = pool.borrowObject();

        // 测试，直接存在本地 d盘 uploadTest\barge目录下
        // String newPath = "D:/uploadTest/cargoFile/" + waterwayCargoId;

        String newPath = uploadCargoFileLocation + waterwayCargoId;

        // try {
        //     mkdirs(sftp, newPath);
        //     sftp.cd(newPath);
        //     sftp.put(in, filename);
        // } catch (SftpException e) {
        //     log.error("BargeSftpUtil - uploadFile - 上传文件失败", e);
        // } finally {
        //     pool.returnObject(sftp);
        // }

        File dir = new File(newPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File dest = new File(newPath + "/" + filename);
        try (OutputStream out = new FileOutputStream(dest)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            log.error("BargeSftpUtil - uploadFile - 上传文件失败", e);
        }

        return newPath + "/" + filename;
    }

    /**
     * 下载文件
     *
     * @throws SftpException SftpException
     * @throws IOException   IOException
     */
    public void downloadFile(String path, OutputStream os) throws Exception {
        ChannelSftp sftp = pool.borrowObject();
        sftp.get(path, os);
        pool.returnObject(sftp);
    }

    /**
     * 删除文件
     *
     * @param dir  远程目录
     * @param name 远程文件名
     */
    public void delete(String dir, String name) {
        ChannelSftp sftp = pool.borrowObject();
        try {
            sftp.cd(dir);
            sftp.rm(name);
        } catch (SftpException e) {
            throw new CustomException("sftp删除文件出错", e);
        } finally {
            pool.returnObject(sftp);
        }
    }

    /**
     * 递归创建多级目录
     *
     * @param dir 多级目录
     */
    private void mkdirs(ChannelSftp sftp, String dir) {
        String[] folders = dir.split("/");
        try {
            sftp.cd("/");
            for (String folder : folders) {
                if (folder.length() > 0) {
                    try {
                        sftp.cd(folder);
                    } catch (Exception e) {
                        sftp.mkdir(folder);
                        sftp.cd(folder);
                    }
                }
            }
        } catch (SftpException e) {
            throw new CustomException("sftp创建目录出错", e);
        }
    }

}

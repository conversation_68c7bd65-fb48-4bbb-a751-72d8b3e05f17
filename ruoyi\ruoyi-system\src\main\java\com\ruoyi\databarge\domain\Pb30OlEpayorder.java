package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/11/17 9:25
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PB30_OL_EPAYORDER")
@KeySequence("SEQ_PB30_OL_EPAYORDER")
public class Pb30OlEpayorder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号（系统用）
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 流水号（16位）
     */
    @TableField("ORDERID")
    private String orderid;

    /**
     * 订单生成时间
     */
    @TableField("STARTTIME")
    private String starttime;

    /**
     * 订单结束时间
     */
    @TableField("ENDTIME")
    private String endtime;

    /**
     * 订单描述
     */
    @TableField("ORDERDESCRIP")
    private String orderdescrip;

    /**
     * 金额
     */
    @TableField("AMOUNT")
    private String amount;

    /**
     * 订单状态：0未支付；1支付成功；2支付失败；3处理中；4待退款 ；5 退款成功； 6退款失败；7退款中；8退款待复核；9订单关闭；
     */
    @TableField("STATUS")
    private String status;

    /**
     * 客户id
     */
    @TableField("CUSTOMERID")
    private String customerid;

    /**
     * 客户名称
     */
    @TableField("CUSTOMERNAME")
    private String customername;

    /**
     * 公司id
     */
    @TableField("COMID")
    private String comid;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 订单类型：0：支付；1：退款
     */
    @TableField("PAYTYPE")
    private String paytype;

    /**
     * 渠道流水号
     */
    @TableField("CHANFLOW")
    private String chanflow;

    /**
     * 渠道账号
     */
    @TableField("CHANID")
    private String chanid;

    /**
     * 支付平台订单号
     */
    @TableField("EPAYORDERID")
    private String epayorderid;

    /**
     * 支付时间/退款时间
     */
    @TableField("PAYTIME")
    private String paytime;

    /**
     * 原订单号（退款用）
     */
    @TableField("REFUNDORDERID")
    private String refundorderid;

    /**
     * 原渠道流水号（退款用）
     */
    @TableField("REFUNDCHANFLOW")
    private String refundchanflow;

    /**
     * 更改状态标识
     */
    @TableField("FINANCECHECK")
    private String financecheck;

    /**
     * 经办人
     */
    @TableField("PAYPERSON")
    private String payperson;

    /**
     * 联系电话
     */
    @TableField("CONTACTCELLPHONE")
    private String contactcellphone;

    /**
     * 业务类型：0出库单支付1驳船支付;2移动端出库单支付
     */
    @TableField("BUSINESSTYPE")
    private String businesstype;


}

package com.ruoyi.app.controller.databarge;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.databarge.domain.Pb6BargeCompany;
import com.ruoyi.databarge.domain.vo.Pb6BargeCompanyVO;
import com.ruoyi.databarge.service.Pb6BargeCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/9/1.
 * @Date: 2020/9/1 14:19
 */
@RestController
@RequestMapping("/barge/barge-company")
public class Pb6BargeCompanyController {

    @Autowired
    private Pb6BargeCompanyService pb6BargeCompanyService;

    @PostMapping("/searchbyid")
    public AjaxResult searchById(@RequestBody Long id){
        return AjaxResult.success(pb6BargeCompanyService.searchById(id));
    }

    @PostMapping("/searchByBargeId")
    public AjaxResult searchByBargeId(@RequestBody Long bargeId){
        List<Pb6BargeCompany> pb6BargeCompanyList=pb6BargeCompanyService.list(Wrappers.<Pb6BargeCompany>lambdaQuery()
                .eq(Pb6BargeCompany::getBargeid,bargeId)
                .eq(Pb6BargeCompany::getAuditstatus,"1")
                .eq(Pb6BargeCompany::getIsDelete,"1"));
        Pb6BargeCompany pb6BargeCompany=new Pb6BargeCompany();
        if(pb6BargeCompanyList.size()==0){
            return AjaxResult.success(pb6BargeCompany);
        }else if(pb6BargeCompanyList.size()>=2){
            throw new CustomException("数据错误,挂靠了两个公司!");
        }else{
             pb6BargeCompany=pb6BargeCompanyList.get(0);
            return AjaxResult.success(pb6BargeCompany);
        }

    }

    /**
     * 挂靠判断当前的挂靠公司是否已经挂靠
     * 1、新增挂靠时，判断已有挂靠与当前挂靠客户名存在相同，则不可挂靠，不存在相同则可挂靠
     * 2、修改挂靠时，判断已有挂靠没有与当前挂靠客户名相同的情况可修改成功，或者存在相同，但 ID 值相同，说明是同一条信息，可修改，其他情况均不可修改
     * @param pb6BargeCompanyVO 挂靠表 VO
     * @param sign 标识： 1、新增； 2、修改
     * @return 返回true，可继续操作， 返回false，弹窗提示不能操作
     */
//    @PostMapping("/addBargeCompanyCheck/{sign}")
//    public AjaxResult addBargeCompanyCheck(@RequestBody Pb6BargeCompanyVO pb6BargeCompanyVO, @PathVariable("sign") Integer sign){

        //该查询只查询得到生效的记录
//        List<Pb6BargeCompanyVO> pb6BargeCompanyVOList = pb6BargeCompanyService.searchByBargeId(pb6BargeCompanyVO.getBargeid());
//        if(sign == 1){
//            for(Pb6BargeCompanyVO vo: pb6BargeCompanyVOList){
//                if(vo.getCfullname().equals(pb6BargeCompanyVO.getCfullname())){
//                    return AjaxResult.success(false);
//                }
//            }
//            return AjaxResult.success(true);
//        } else {
//            int i = 0; //计数器，记录重复个数，以防万一数据错误对某个公司挂靠了两条有效记录
//            Pb6BargeCompanyVO companyVO = new Pb6BargeCompanyVO(); //保存重复客户的信息
//            for(Pb6BargeCompanyVO vo: pb6BargeCompanyVOList){
//                if(vo.getCfullname().equals(pb6BargeCompanyVO.getCfullname())){
//                    i++;
//                    companyVO = vo;
//                }
//            }
//            if(i == 0){ //说明没有重复的，修改的客户名可挂靠
//                return AjaxResult.success(true);
//            } else if(i == 1 && companyVO.getId().equals(pb6BargeCompanyVO.getId())){
//                // 说明是同一条信息，可修改
//                return AjaxResult.success(true);
//            } else {
//                return AjaxResult.success(false);
//            }
//        }
//    }

    /**
     * 通过船公司ID，查找所有挂靠在该公司下的所有船信息
     * @param companyid 船公司ID
     * @return 挂靠列表
     */
    @PostMapping("/searchByCompanyId")
    public AjaxResult searchByCompanyId(@RequestBody Long companyid){
        return AjaxResult.success(pb6BargeCompanyService.searchByCompanyId(companyid));
    }

}

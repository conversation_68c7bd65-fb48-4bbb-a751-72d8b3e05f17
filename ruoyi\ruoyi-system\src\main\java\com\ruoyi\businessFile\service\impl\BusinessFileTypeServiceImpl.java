package com.ruoyi.businessFile.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.businessFile.domain.BusinessFileType;
import com.ruoyi.businessFile.mapper.BusinessFileTypeMapper;
import com.ruoyi.businessFile.service.BusinessFileTypeService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 15:09
 */
@Service
@Transactional(rollbackFor=Exception.class)
public class BusinessFileTypeServiceImpl  extends ServiceImpl<BusinessFileTypeMapper, BusinessFileType> implements BusinessFileTypeService {

    @Autowired
    BusinessFileTypeMapper businessFileTypeMapper;

    @Override
    public List<BusinessFileType> list(BusinessFileType businessFileType) {
        QueryWrapper<BusinessFileType> queryWrapper=new QueryWrapper<>();

//        if(StringUtils.isNotNull(businessFileType)){
//           if(StringUtils.isNotEmpty(businessFileType.getFileBusinessType())){
//               queryWrapper.lambda().like(BusinessFileType::getFileBusinessType,businessFileType.getFileBusinessType());
//           }
//           if(businessFileType.getFileIsEnable()==null){
//               queryWrapper.lambda().like(BusinessFileType::getFileIsEnable,businessFileType.getFileIsEnable());
//           }
//           queryWrapper.orderByAsc("FILE_SORT");
//        }

        queryWrapper.lambda()
                .eq(StringUtils.isNotEmpty(businessFileType.getFileBusinessType()),BusinessFileType::getFileBusinessType,businessFileType.getFileBusinessType())
                .eq(StringUtils.isNotNull(businessFileType.getFileIsEnable()),BusinessFileType::getFileIsEnable,businessFileType.getFileIsEnable())
                .orderByAsc(BusinessFileType::getFileBusinessType);

        return this.list(queryWrapper);
    }

    @Override
    public AjaxResult insert(BusinessFileType businessFileType) {
        QueryWrapper<BusinessFileType> queryWrapper=new QueryWrapper<>();

        queryWrapper.lambda().eq(BusinessFileType::getFileBusinessType, businessFileType.getFileBusinessType())
                .eq(BusinessFileType::getFileTypeName,businessFileType.getFileTypeName());
        BusinessFileType check = super.getOne(queryWrapper);
        if (StringUtils.isNotNull(check)) {
            return AjaxResult.error("存在相同文件类型");
        }

        return this.save(businessFileType)?AjaxResult.success():AjaxResult.error();
    }

    @Override
    public AjaxResult update(BusinessFileType businessFileType) {
        QueryWrapper<BusinessFileType> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BusinessFileType::getFileTypeName,businessFileType.getFileTypeName())
//                .eq(BusinessFileType::getFileBusinessTypeName,businessFileType.getFileBusinessTypeName())
                .ne(BusinessFileType::getFileTypeId,businessFileType.getFileTypeId());
        List<BusinessFileType> checklist=this.list(queryWrapper);
        if(checklist.size()>0){
            return AjaxResult.error("存在相同文件类型");
        }
        return this.updateById(businessFileType)?AjaxResult.success():AjaxResult.error();
    }

    @Override
    public List<BusinessFileType> getFileTypeName() {
        List<BusinessFileType> businessFileTypeList=
                businessFileTypeMapper.selectList(new QueryWrapper<BusinessFileType>().lambda().eq(BusinessFileType::getFileBusinessType,"CON"));
        return businessFileTypeList;
    }

    @Override
    public List<BusinessFileType> selectAllRequiredShipFile() {
        return baseMapper.selectAllRequiredShipFile();
    }

}

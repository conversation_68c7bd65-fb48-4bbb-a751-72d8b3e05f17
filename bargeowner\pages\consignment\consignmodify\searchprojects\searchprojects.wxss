/* pages/scancollect/chooseprojects/searchprojects/searchprojects.wxss */
.search-dialog{
  width: 100%;
  height:100%;
  display: flex;
  flex-direction: column;
  padding:30rpx;
}
.search-dialog .search-header{
  width: 100%;
  height: 70rpx;
  display: flex;
  justify-content: center;
}
.search-dialog .pull-icon{
  color:#c8c9ca;
  font-size:30rpx;
}
.search-dialog .search-content{
  flex-shrink: 0;
  width: 100%;
  height:70rpx;
  display: flex;
  align-items: center;
  
}
.search-dialog .head-input{
  flex:1;
  height: 100%;
  display: flex;
  align-items: center;
  color:#333;
  font-size: 28rpx;
  font-weight: 500;
}
.search-dialog .submit-btn1{
  flex-shrink: 0;
  width: 150rpx;
  background: rgb(0, 66, 107);
  height: 70rpx;
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 30rpx;
}
.search-dialog  .search-page{
  flex-shrink: 0;
  width: 100%;
  height: 80%;
  margin-top: 40rpx;
}
.search-dialog .project-list{
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  padding:0 15rpx;
}
.search-dialog  .each-project{
  flex-shrink: 0;
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  padding:8rpx 0rpx;
  border-bottom:1rpx solid #ddd;
  box-shadow: 0rpx -3rpx 3rpx 0rpx rgba(51,51,51,0.04);
}
.search-dialog .active-each-project{
  background:#d8e7ff;
}
.search-dialog .search-icon1{
  flex-shrink: 0;
  font-size:26rpx;
  color:#ddd;
}
.search-dialog .pro-title{
  width: 90%;
  padding:0 5rpx;
  color:#333;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  margin-left: 15rpx;
}
.each-project-wrap{
  width: 100%;
  height: auto;
}
.search-ipt-wrap{
  flex:1;
  height: 70rpx;
  padding: 0 15rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
}
.close-icon{
  flex-shrink: 0;
  width: 40rpx;
  text-align: center;
  height: 70rpx;
  line-height: 70rpx;
  color: #ddd;
}
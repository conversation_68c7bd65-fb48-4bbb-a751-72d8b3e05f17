package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 用户订阅托运单 实体类
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/5 10:36
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
@TableName("SYS_USER_SUBSCRIPTION")
public class SysUserSubscription {

    /**
     * 用户id
     */
    @TableField("USER_ID")
    private Long userId;

    /**
     * 托运单id
     */
    @TableField("CONSIGN_ID")
    private Long consignId;

    /**
     * 托运单明细表id
     */
    @TableField("CONSIGN_DETAIL_ID")
    private Long consignDetailId;

    /**
     * 微信公众号的openid
     */
    private String publicOpenId;
}

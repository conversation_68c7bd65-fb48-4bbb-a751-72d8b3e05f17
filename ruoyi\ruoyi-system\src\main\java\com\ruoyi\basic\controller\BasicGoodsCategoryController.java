package com.ruoyi.basic.controller;


import com.ruoyi.basic.domain.BasicGoodsCategory;
import com.ruoyi.basic.service.impl.BasicGoodsCategoryServiceImpl;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023/11/16 12:25
 */
@RequestMapping("/basic/goodsCategory")
@RestController
public class BasicGoodsCategoryController extends BaseController {

    @Autowired
    BasicGoodsCategoryServiceImpl basicGoodsCategoryService;

    /**
     * @param
     * @return
     * @description lsit
     * <AUTHOR>
     * @date 2023/11/16 14:43
     */
    @GetMapping("/list")
    public TableDataInfo list(BasicGoodsCategory basicGoodsCategory)
    {   startPage();
        List<BasicGoodsCategory> list = basicGoodsCategoryService.selectBasicGoodsCategoryList(basicGoodsCategory);
        return getDataTable(list);
    }

    // 货类List
    @PostMapping("/GoodsCategorylist")
    public AjaxResult GoodsCategorylist()
    {
        List<BasicGoodsCategory> list = basicGoodsCategoryService.selectBasicGoodsCategoryList(new BasicGoodsCategory());

        List<String> goodsCategoryList = new ArrayList<>();

        // goodsCategoryList.add("无");

        for (BasicGoodsCategory basicGoodsCategory : list) {
            goodsCategoryList.add(basicGoodsCategory.getGoodsCategory());
        }

        return AjaxResult.success(goodsCategoryList);
    }

    /**
     * @param
     * @return
     * @description add
     * @date 2023/8/21 15:45
     */
    @PostMapping("add")
    public AjaxResult add(@RequestBody BasicGoodsCategory basicGoodsCategory) {
        return basicGoodsCategoryService.addBasicGoodsCategory(basicGoodsCategory);
    }

    /**
     * @param
     * @return
     * @description edit
     * @date 2023/8/21 15:45
     */
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody BasicGoodsCategory basicGoodsCategory) {
        return basicGoodsCategoryService.updateBasicGoodsCategory(basicGoodsCategory);
    }

    /**
     * @param
     * @return
     * @description delete
     * @date 2023/8/21 15:45
     */
    @DeleteMapping("delete/{goodsId}")
    public AjaxResult delete(@PathVariable Long goodsId){
        return AjaxResult.success(basicGoodsCategoryService.deleteBasicGoodsCategory(String.valueOf(goodsId)));
    }

    /**
     * @param
     * @return
     * @description get
     * <AUTHOR>
     * @date 2023/11/16 16:31
     */
    @GetMapping("get/{goodsId}")
    public AjaxResult get(@PathVariable Long goodsId){
        return AjaxResult.success(basicGoodsCategoryService.getById(goodsId));
    }



}

package com.ruoyi.databarge.pay;

import lombok.Getter;
import lombok.Setter;

/**
 * Description: 用于给界面展示的支付明细
 * 通过水路运单号查询其下的支付和退款信息
 * @Author: ChenJin on 2021/2/5.
 * @Date: 2021/2/5 16:00
 */
@Getter
@Setter
public class PayDetailsVO {

    private Long id; //支付明细表的ID
    private String orderid; // 16位订单号
    private String paytime; // 付款时间
    private String payman; // 付款人
    private String paytype; // 付款类型： 现结、月结
    private Integer paysign; // 订单类型：0：支付；1：退款
    private String count; // 总金额
    private Integer status; // 订单状态：0未支付；1支付成功；2支付失败；3处理中；4待退款 ；5 退款成功； 6退款失败；7退款中；8退款待复核；9订单关闭；
    private String actualmount; //实付金额
    private String contactphone; // 付款人联系方式
    private Integer resourse; // 支付方式：0网页端支付；1移动端支付；2出库单支付；3扫码枪支付； 4小程序支付
}

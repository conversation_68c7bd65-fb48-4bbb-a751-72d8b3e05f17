package com.ruoyi.app.controller.support.seal;

import com.fdd.api.client.release.base.ClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SealConfig {
    @Bean
    public ClientFactory clientFactory(){
        //ClientFactory factory = ClientFactory.instance("http://10.197.34.26:8070/","100000","7Jij63g87a94AIg2i3JJEG1G");// 测试库
        ClientFactory factory = ClientFactory.instance("https://esign-api.gzport.com/","100000","Dj8jeJ02HF5jFd8F0GG9fE8B");// 正式库
        return factory;
    }
}

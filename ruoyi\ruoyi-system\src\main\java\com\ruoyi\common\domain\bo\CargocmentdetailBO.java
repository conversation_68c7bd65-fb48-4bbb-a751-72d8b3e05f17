package com.ruoyi.common.domain.bo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.*;

import java.util.List;


/**
 * @Description 运单类数据详情
 * <AUTHOR>
 * @Date 2020/8/5 9:49
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class CargocmentdetailBO extends BaseEntity {

    /**
     * 托运单明细表主键id
     */
    private Long id;

    /**
     * 小程序操作状态
     */
    private Integer wxOperateState;

    /**
     * 预约时间
     */
    private String wxAppOintmentTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 支付方式
     */
    private String chargeBalanceType;

    /**
     * 船公司id
     */
    private Long companyId;

    /**
     * 船公司名称
     */
    private String companyName;

    /**
     * 运单主表id
     */
    private Long consignId;
    /**
     * 运单编号id
     */
    private String consignFlag;


    /**
     * 1-退单，2-改单
     */
    private Integer state;//前端传递的条件


    /**
     * 托运单重量
     */
    private String rationWeight;

    /**
     * 托运单联系人
     */
    private String wxRationContactNumber;

    /**
     * 驳船主联系电话
     */
    private String bargeTel;

    /**
     * 是否只修改托运单联系人
     */
    private Boolean updateRationContactNumber;

    /**
     * 目的港
     */
    private String endPort;


    /**
     * 驳船状态 0：未审核 1：已审核 2：审核不通过 3：已配载 4：已报到 5：已离港 6:已退单
     */
    private String flagBargeState;


    /**
     * 审核运单列表
     */
    private List<Long> detailIds;


    //审核结果0-不同意 1-同意
    private Integer auditResult;
    // 1退单 2改单 3月结 4差额退款  （船公司使用）
    private Integer auditFlag;
    //审核状态（7:待审核(退单改单) 8:审核通过(退单改单) 9:审核不通过(退单改单)）
    private Integer auditState;

    private String auditReasons;//审核结果说明

    /**
     * 退单、改单 原因
     */
    private String modifyreason;


    /**
     * 1.取消改单, 2.取消退单
     */
    private Integer stateOperation;


    /**
     *  审核节点状态
     */
    private Integer WXnode;

    /**
     * 营业厅申请改单/退单（0为申请退单中，1为申请退单通过，2为申请退单失败,3为申请改单中，
     * 4为申请改单成功，5为申请改单失败
     */
    private String applyModify;



    private Integer tabState;//tab栏各个状态，除待审核以外（1.待支付/待预约；1-进行中，2-已完成 3-全部）


    /**
     * 是否托运单办理人审核(差额退款申请审核专用) 0：待审核，1：审核通过，2：审核不通过
     */
    private Integer isWxCheck;

    /**
     * 月结码
     */
    private String monthlyCode;

    /**
     * 驳船id
     */
    private Long bargeId;

    /**
     * 水路运单编号
     */
    private String waterwayCargoId;

    /**
     * 驳船主用户id
     */
    private Long bargeUserId;

    private Long pageNum;

    private Long pageSize;

    // 大船名
    private String shipName;

    //以下用于船长声明
    //提单号
    private String billNo;

    //货量
    private String totalTonnage;

    //计划日期
    private String plannedDepartureDate;

    //海事处
    private String maritimeOffice;

    //泊位
    private String berth;
}

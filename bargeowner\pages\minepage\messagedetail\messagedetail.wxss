/* pages/minepage/messagedetail/messagedetail.wxss */
.message-detail-page {
  width: 100%;
  height: 100%;
  display: flex;
  padding-top: 20rpx;
  flex-direction: column;
  justify-content: center;
}

.message-title {
  color: #666666;
  font-size: 34rpx;
  text-align: center;
  line-height: 50rpx;
  height: 50rpx;
}

.message-content {
  color: #333333;
  font-size: 30rpx;
  line-height: 60rpx;
  padding: 60rpx 20rpx;
}

.pdf-button {
  display: inline;
  padding-top: 0rpx;
  padding-left: 10rpx;
}
.detail-content {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 0 20rpx;
}

.data-detail-content {
  min-width: 50%;
  max-width: 98%;
  display: flex;
  flex-direction: row;
  height: 70rpx;
  font-size: 28rpx;
  align-items: center;
}

.detail-title {
  color: #999999;
}
.blno-title {
  width: 115rpx;
}
.detail-value {
  padding-left: 15rpx;
  color: #333333;
  word-break: break-all;
}
.blno-value {
  width: 580rpx;
}

.table {
  border: 0px solid darkgray;
}

.tr {
  display: flex;
  width: 100%;
  justify-content: center;
  height: 3rem;
  align-items: center;
}

.td {
  width: 40%;
  justify-content: center;
  text-align: center;
  font-size: 22rpx;
}

.bg-w {
  background: snow;
}

.bg-g {
  background: #E6F3F9;
}

.th {
  width: 40%;
  justify-content: center;
  background: #cbcedd;
  color: rgb(0, 0, 0);
  display: flex;
  height: 3rem;
  align-items: center;
}
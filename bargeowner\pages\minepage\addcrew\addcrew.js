// pages/minepage/addcrew/addcrew.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //表单
    form:{
      nickName:"",//姓名
      phonenumber:"",//手机
    }
  },

  /**
   * 
   * 方法
   */
  //姓名input
  nickNameInput(e){
    this.setData({
      "form.nickName":e.detail.value
    })
  },

  //手机input
  phonenumberInput(e){
    this.setData({
      "form.phonenumber":e.detail.value
    })
  },

  // 点击确定
  handleConfirm() {
    let param = {
      ...this.data.form
    }
    if(!param.nickName){
      app.$message("请输入姓名")
      return
    }
    if(!param.phonenumber){
      app.$message("请输入手机号")
      return
    }
    //手机校验
    let phoneReg = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[2-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/
    if(param.phonenumber && !phoneReg.test(param.phonenumber)){
      app.$message("手机输入格式不正确")
      return;
    }
    app.$post(app.$url.mine.addCrew,param).then(res=>{
      if(res.code == 200){
        app.$message(res.msg)
        if(res.data){
          let crewObj = {
            ...res.data,
            menus:[]
          }
          wx.navigateBack({
            delta: 1
          });

        }
      }else {
        app.$message(res.msg)
      }
    })
   
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
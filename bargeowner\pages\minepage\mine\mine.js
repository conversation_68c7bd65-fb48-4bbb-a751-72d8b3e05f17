// pages/minepage/mine/mine.js
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    //用户类型userType：00系统用户， 01-注册用户，10-货主，11-托运单联系人，12-船公司管理员，13-船公司业务员，14-驳船管理员，15-驳船业务员
    userInfos:"",//用户信息
    nickName: '',
    imgUrl:'',
    CustomBar: app.globalData.CustomBar,
    StatusBar: app.globalData.StatusBar,
    userAuthBtnShow:false,//是否显示获取用户信息按钮
    recordResult:"",//备案流程状态
    noticeNum:0,//信息通知数量
  },

  /**
   * 
   * 方法
   */
  //获取信息通知列表
  getNoticeListDatas(){
    let that = this 
    // 查询条件参数
    let param =  {
      searchValue:"",//搜索关键字
      beginTime: "",//开始时间
      endTime: "",//结束时间
    }
    app.$post(app.$url.mine.getNoticeList,param).then(res=>{
      if(res.code == 200){
        // isRead消息状态，0-未读(有红点)，1-已读
        let unReadArr = res.data.filter(item=>{ return item.isRead === "0" })
        that.setData({
          noticeNum: unReadArr.length
        })

        wx.setStorageSync('noticeNum', JSON.stringify(unReadArr.length))
        if(unReadArr && unReadArr.length != 0) {
          wx.setTabBarBadge({
            index: 2,
            text: JSON.stringify(unReadArr.length)
          })
        }else{
          wx.removeTabBarBadge({
            index: 2
          })
        }
      }else {
      app.$message(res.msg)
      }
    })
  },

  //查询是否备案成功
  checkRecordIsStatus(){
    let recordResult = this.data.recordResult //审核状态
    app.$post(app.$url.mine.checkRecordIsSuccess,{}).then(res=>{
      if(res.code == 200){
        console.log("res3",res)
        /**
         * 判断备案状态recordStatus备案状态： 1-审核中、2-审核通过、3-审核不通过、4-没有进行驳船备案
         */
        if(res.data.recordStatus){
          //1 备案审核中
          if(res.data.recordStatus == 1){
            recordResult = "审核中"
          }}
          if(res.data.recordStatus == 2){
            //2 管理员驳船备案成功
            recordResult = "审核通过"
          }
          if(res.data.recordStatus == 3){
          //3 备案审核不通过
            recordResult = "审核不通过"
          }
          if(res.data.recordStatus == 4){
          //4 没有进行驳船备案
            recordResult = "未进行驳船备案"
          }
          this.setData({
            recordResult,
          })
      
      }else {
        app.$message(res.msg)
      }
    })
  },

  //获取用户信息授权
  bindGetUserInfo(){
    wx.getUserInfo({
      withCredentials: 'false',
      lang: 'zh_CN',
      timeout:10000,
      success: (result)=>{
        this.setData({
          'nickName': result.userInfo.nickName,
          'imgUrl': result.userInfo.avatarUrl,
          userAuthBtnShow:false
        })
        console.log(result)
      },
      fail: (error) => {
        console.log(error)
      },
      complete: () => {

      }
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {


    
  
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    let that = this
    // const accountInfo = wx.getAccountInfoSync();
    // console.log(accountInfo)
    let userInfos = wx.getStorageSync("userInfo")?JSON.parse(wx.getStorageSync("userInfo")):""//用户信息
    this.setData({
      userInfos,
    })
    //判断是否已经授权
    wx.getSetting({
      success (res) {
        if(res.authSetting['scope.userInfo']){
          //已经授权，直接调用getUserInfo
          that.bindGetUserInfo()
        }else{
          //未授权显示获取用户信息按钮
          that.setData({
            userAuthBtnShow:true
          })
        }
        console.log(res.authSetting)
      }
    })
    this.checkRecordIsStatus()
    this.getNoticeListDatas()
  },


  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  // 跳转到信息列表页面
  handleMessagePage() {
    wx.navigateTo({
      url: '/pages/minepage/message/message',
      success: (result) => {
        
      },
      fail: () => {},
      complete: () => {}
    });
  },
  // 跳转到船员列表页面
  handleCrewpage() {
    wx.navigateTo({
      url: '/pages/minepage/crewlist/crewlist',
      success: (result) => {
        
      },
      fail: () => {},
      complete: () => {}
    });
  },
  // 跳转到备案页面
  handleRecordPage() {
    wx.showModal({
      title: '提示',
      content: '是否需要修改备案信息？',
      success: (res) => {
        if (res.confirm) {
          // 用户点击确定，跳转到备案页面
          wx.navigateTo({
            url: '/pages/minepage/record/record',
            success: (result) => {},
            fail: () => {},
            complete: () => {}
          });
        } else {
          // 用户点击取消，不做任何操作，直接返回
          return;
        }
      }
    });
  },
  // 跳转到查询代理发货页面
  handleAgentdeliveryPage() {
    wx.navigateTo({
      url: '/pages/agentdelivery/query/query',
      success: (result) => {
        
      },
      fail: () => {},
      complete: () => {}
    });
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
package com.ruoyi.basic.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicCustomer;
import com.ruoyi.basic.domain.BasicRate;
import com.ruoyi.basic.domain.BasicShip;
import com.ruoyi.basic.domain.BasicTerminal;
import com.ruoyi.basic.mapper.BasicRateMapper;
import com.ruoyi.basic.mapper.SlaveMapper;
import com.ruoyi.basic.service.BasicRateService;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【BASIC_RATE】的数据库操作Service实现
* @createDate 2023-11-20 09:31:59
*/
@Service
public class BasicRateServiceImpl extends ServiceImpl<BasicRateMapper, BasicRate>
    implements BasicRateService {

    @Autowired
    private SlaveMapper slaveMapper;

    @Override
    public List<BasicRate> selectBasicRateList(BasicRate basicRate) {
        return baseMapper.selectBasicRateList(basicRate);
    }

    @Override
    public AjaxResult addBasicRate(BasicRate basicRate) {

        // 判断当前费率是否已存在，如果存在，提示不允许新增
        BasicRate rate = baseMapper.selectBasicRateByRate(basicRate);

        if(rate != null){
            return AjaxResult.error("费率已存在");
        }

        if(baseMapper.insert(basicRate) > 0){
            return AjaxResult.success("新增费率成功");
        }
        return AjaxResult.error("新增费率失败");
    }

    @Override
    public AjaxResult updateBasicRate(BasicRate basicRate) {
        if(baseMapper.updateById(basicRate) > 0){
            return AjaxResult.success("修改费率成功");
        }
        return AjaxResult.error("修改费率失败");
    }

    @Override
    public AjaxResult deleteBasicRate(Long rateId) {
        if(baseMapper.deleteById(rateId) > 0){
            return AjaxResult.success("删除费率成功");
        }
        return AjaxResult.error("删除费率失败");
    }

    @Override
    public List<BasicCustomer> selectBasicCustomerList() {
        return slaveMapper.selectBasicCustomerList();
    }

    @Override
    public List<BasicShip> selectBasicShipList() {
        return slaveMapper.selectBasicShipList();
    }

    @Override
    public List<BasicTerminal> selectBasicTerminalList() {
        return slaveMapper.selectBasicTerminalList();
    }
}





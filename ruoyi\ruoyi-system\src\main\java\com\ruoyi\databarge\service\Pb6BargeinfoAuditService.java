package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.Pb6BargeinfoAudit;
import com.ruoyi.databarge.domain.dto.Pb6BargeinfoAuditSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeCompanyResultVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/8/27.
 * @Date: 2020/8/27 11:18
 */
public interface Pb6BargeinfoAuditService extends IService<Pb6BargeinfoAudit> {

    BargeCompanyResultVO searchPb6BargeinfoAuditById(Long pb6BargeInfoAuditId);

    BargeCompanyResultVO searchPb6BargeinfoAuditByBakId(Long pb6BargeInfoAuditId);

    List<Pb6BargeinfoAudit> selectPb6BargeinfoAuditListByBargeName( String bargename);
}

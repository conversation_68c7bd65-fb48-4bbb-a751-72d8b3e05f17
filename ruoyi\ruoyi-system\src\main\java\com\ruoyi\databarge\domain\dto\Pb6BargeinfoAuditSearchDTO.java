package com.ruoyi.databarge.domain.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/8/27.
 * @Date: 2020/8/27 14:13
 */
@Getter
@Setter
public class Pb6BargeinfoAuditSearchDTO {

    private String bargeName;

    private String mmsi;

    private String bargeId;

    private Integer isRent;

    private String bargeCompany;

    // 是否有驳船主 空，查所有， 1查有备案的， 2临时租用， 3长期租用， 4自有， 0查没有备案的
    private String haveBargeOwner;

    //是否印章有船公司名称（物流公司） Y
    private String auditstate;

    private Long pageNum;

    private Long pageSize;

    // 驳船组或者船员手机号码
    private String phone;
}

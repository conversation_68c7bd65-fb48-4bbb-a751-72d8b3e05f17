package com.ruoyi.app.controller.support.print;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.enums.PayWayEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.service.FtpService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.web.utils.BargeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class SlydService {
    @Autowired
    private FddCommonService fddCommonService;
    @Autowired
    private CargocmentdetailService cargocmentdetailService;
    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;
    private BargeUtils bargeUtils;
    @Autowired
    private BargeCenterService bargeCenterService;
    @Autowired
    private FtpService ftpService;

    @Async
    public void chargebackStamp(String waterwayCargoId){
        log.info("退单后重新生成水路运单 =>"+waterwayCargoId);
        // 查询托运单明细
        Cargoconsignmentdetail detail = cargocmentdetailService.getOne(new QueryWrapper<Cargoconsignmentdetail>()
                .eq("waterwaycargoid", waterwayCargoId));
        // 查找驳船主
        Long bargeId = detail.getBargeId();
        if (StringUtils.isNull(bargeId)) {
            String msg = StringUtils.format("托运单：{}的BARGEID字段为空，驳船id：{}", waterwayCargoId, bargeId);
            log.error(msg);
            throw new CustomException(msg);
        }
        SysUser bargeUser = sysUserBargeMapper.queryBargeUserByBargeId(null, bargeId);

        HashMap<String, Object> map = new HashMap<>();
        File cargoFile;
        Long pb6BargeworkId;
        try {
            HashMap<String, Object> cargoMap = bargeUtils.getOriginalStampFile(detail.getWaterwayCargoId(), 3);
            pb6BargeworkId = (Long) cargoMap.get("pb6BargeworkId");
            cargoFile = File.createTempFile("cargo_" + UUID.randomUUID(), ".pdf");
            cargoFile.deleteOnExit();
            FileUtil.writeBytes(((ByteArrayOutputStream) cargoMap.get("outStream")).toByteArray(), cargoFile);
        } catch (Exception e) {
            log.error("获取文件失败");
            throw new CustomException("获取文件失败", e);
        }
        ShipFddUserRel shipFddUserRel;
        // 判断支付方式
        if (PayWayEnum.WX_PAY.getCodeName().equals(detail.getChargeBalanceType())) {
            // 现结盖船主章
            shipFddUserRel = bargeUtils.getShipFddUserRelOfBargeUser(bargeId, StringUtils.isNull(bargeUser) ? null : bargeUser.getUserId());
        } else {
            // 月结盖船公司章
            shipFddUserRel = bargeUtils.getShipFddUserRelOfCompany(detail.getWxMonthChargeById());
        }
        // 开始盖章
        String docNo = "YD" + IdUtil.objectId();
        // 1.上传并创建合同
        fddCommonService.uploadAndSaveContract(null,"66d1507358234975916dfac9fc4e544c", docNo, cargoFile);
        // 物流公司章
        fddCommonService.autoSign(docNo, null, "66d1507358234975916dfac9fc4e544c", "广州港船舶代理业务专用章(1)", IdUtils.fastSimpleUUID(), 530.0f, 612.0f);
        // 物流公司章
        fddCommonService.autoSign(docNo, null, "66d1507358234975916dfac9fc4e544c", "船代部退单章", IdUtils.fastSimpleUUID(), 530.0f, 460.0f);
        if (PayWayEnum.WX_PAY.getCodeName().equals(detail.getChargeBalanceType())) {
            // 船主章
            if (StringUtils.isNotNull(shipFddUserRel)) {
                BargeInfo bargeInfo = bargeCenterService.getBargeById(bargeId);
                fddCommonService.autoSign(docNo, null, shipFddUserRel.getFddAccountId(), bargeInfo.getBargeName(), IdUtils.fastSimpleUUID(), 113.0f, 612.0f);
            }
        } else {
            // 船公司章
            if (StringUtils.isNotNull(shipFddUserRel)) {
                fddCommonService.autoSign(docNo, null, shipFddUserRel.getFddAccountId(), detail.getWxMonthChargeByName(), IdUtils.fastSimpleUUID(), 113.0f, 612.0f);
            }
        }

        // 3.查看合同
        log.info(fddCommonService.viewContract(docNo));
        // 下载合同
        ByteArrayOutputStream bos = fddCommonService.downloadContract(docNo);
        // 4.保存合同
        map.put("fileName", "水路货物运单.pdf");
        map.put("outStream", bos);
        map.put("pb6BargeworkId", pb6BargeworkId);
        List<HashMap<String, Object>> list = new ArrayList<>();
        list.add(map);
        IoUtil.close(bos);
        Long bargeUserId = StringUtils.isNull(bargeUser) || StringUtils.isNull(bargeUser.getUserId()) ? shipFddUserRel.getShipUserId() : bargeUser.getUserId();
        ftpService.saveCargoFile(list, detail.getWaterwayCargoId(), bargeUserId, detail.getWxMonthChargeById(), docNo);
        log.info("退单生成水路运单完成");
    }
}

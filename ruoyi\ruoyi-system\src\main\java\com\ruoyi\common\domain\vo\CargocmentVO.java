package com.ruoyi.common.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.domain.Cargoconsignment;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import lombok.*;

import java.util.List;

/**
 * @Description 托运单响应实体
 * <AUTHOR>
 * @Date 2020/8/5 9:48
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
public class CargocmentVO extends Cargoconsignment {
    private String detailStatus;
    private String toAuditFlag;

    /**
     * 驳船状态 0：未审核 1：已审核 2：审核不通过 3：已配载 4：已报到 5：已离港 6:已退单
     */
    private String flagBargeState;


    /**
     * 办单类型1-货主；   2-船公司
     */
    private Integer wxApplyUserType;

    /**
     * 状态
     */
    private Integer wxOperateState;

    /**
     * 托运单明细id
     */
    private Long consignDetailId;

    /**
     * 托运单主表id
     */
    private Long consignId;

    /**
     * 退改单状态
     */
    private String applyModify;


    /**
     * 驳船名称
     */
    private String bargeName;


    /**
     * 托运单重量
     */
    private String rationWeight;


    /**
     * 新托运单标志(0：正常办理，1：新生成的托运单)
     */
    private String newWaybill;


    List<Cargoconsignmentdetail> cargoconsignmentdetailList;

    /**
     * 是否订阅
     */
    private String isSubscription;

    /**
     * 出库单号、
     */
    private String outOrInformId;
}

package com.ruoyi.app.controller.support.fdd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fdd.api.client.dto.*;
import com.fdd.api.client.release.base.ClientFactory;
import com.fdd.api.client.res.RestResult;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2020/10/16 10:41
 * @Description: FDD企业服务类
 */
@Service
@AllArgsConstructor
public class FddCompanyService {

    private ClientFactory clientFactory;

    /**
     * 企业注册
     * @param account 账号
     * @param companyName 企业名称
     * @return Fdd账户customerId
     */
    public String companyRegister(String account,String companyName) {
        RegisterDTO registerDTO=new RegisterDTO();
        registerDTO.setType(2);// 2为企业
        registerDTO.setAccount(account);// 需要注册的账号
        registerDTO.setAdminAccountId(null);// 管理员客户编号
        registerDTO.setCompanyName(companyName);// 企业名称，类型为企业时不能为空

        try {
            RestResult restResult=clientFactory.accountClient().register(registerDTO);// RestResult{code='1', msg='添加成功', data=524945f093fd40919864db339fb3c99e}，data就是企业编号
            if("1".equals(restResult.getCode())){
                return (String)restResult.getData();
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 企业实名认证
     * @param customerId FDD账户customerId
     * @param notifyUrl 异步通知地址
     * @return CompanyCertificationUrlVO：事务编号和认证url
     */
    public CompanyCertificationUrlVO companyCertificationUrl(String customerId,String notifyUrl){
        CertificationCompanyUrlDTO certificationCompanyUrlDTO=new CertificationCompanyUrlDTO();
        certificationCompanyUrlDTO.setCustomerId(customerId);// 客户编号
        certificationCompanyUrlDTO.setPageModify(1);// 用户是否能修改扫码后的认证页面信息1-允许，2-不允许（如之前已认证通过，重新认证默认不允许修改页面）
        certificationCompanyUrlDTO.setVerifiedWay(0);// 实名认证套餐类型0：标准方案（对公打款+纸质审核）；1：对公打款；2：纸质审核',
        certificationCompanyUrlDTO.setIsRepeatVerified(1);// 1-首次认证，2-重新认证
        certificationCompanyUrlDTO.setCompanyInfo(null);// 企业信息
        certificationCompanyUrlDTO.setBankInfo(null);// 对公账号信息
        certificationCompanyUrlDTO.setCompanyPrincipalType(null);// 企业负责人身份1.法人，2代理人
        certificationCompanyUrlDTO.setLegalInfo(null);// 法人信息
        certificationCompanyUrlDTO.setAgentInfo(null);// 代理人信息
        certificationCompanyUrlDTO.setNotifyUrl(notifyUrl);// 异步通知地址
        certificationCompanyUrlDTO.setReturnUrl(null);// returnUrl
        certificationCompanyUrlDTO.setIsSendSMS(null);// 是否发送实名验证短信1:发送（需传“企业负责人手机号码”才发送短信），0:否，默认是0:否
        certificationCompanyUrlDTO.setApplicationFormPath(null);// 企业认证申请表
        certificationCompanyUrlDTO.setVerifiedSerialno(null);// 企业负责人实名认证序列号
        certificationCompanyUrlDTO.setManagerVerifiedWay(null);// 管理员认证方式0.三要素标准方案；（默认）1.三要素补充方案；2.四要素标准方案；3.四要素补充方案；4.纯三要素方案；5.纯四要素方案
        certificationCompanyUrlDTO.setApplyCert(null);// 认证成功后自动申请实名证书0：不申请，（默认）1：自动申请
        certificationCompanyUrlDTO.setCertMode(null);// 证书类型0：云端证书（默认）1：本地证书

        try {
            RestResult restResult=clientFactory.certificationClient().companyCertificationUrl(certificationCompanyUrlDTO);// RestResult{code='1', msg='操作成功', data={"transactionNo":"79b86f8166824992880839e1b54354dd","url":"https://t-test.fadada.com/pOUda19VF"}}
            if("1".equals(restResult.getCode())){
                JSONObject jsonObject=(JSONObject)restResult.getData();
                return new CompanyCertificationUrlVO(jsonObject.getString("transactionNo"),jsonObject.getString("url"));
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 企业实名认证状态查询
     * @param customerId FDD账户customerId
     * @return JSONObject：JSON对象信息
     */
    public JSONObject companyCertificationStatus(String customerId) {
        CustomerIdDTO customerIdDTO=new CustomerIdDTO();
        customerIdDTO.setCustomerId(customerId);// 客户编号

        try {
            RestResult restResult=clientFactory.certificationClient().companyCertificationStatus(customerIdDTO);// RestResult{code='1', msg='操作成功', data={"verifiedSerialNo":"79b86f8166824992880839e1b54354dd","verifiedStatus":4,"verifiedStatusDesc":"审核通过","companyVerifiedInfoVO":{"creditImagePath":"20200918142837_5132ba210b464557a","companyName":"广州港测试","bankName":"","auditFailReason":"","subbranchProvince":"","legalPersonName":"洪小林","subbranchName":"","companyPrincipalType":0,"subbranchCity":"","auditTime":"2020-09-18 14:28:31","verifiedWay":0,"bankNo":"","creditNo":"1111"},"personVerifiedInfoVO":{"birthday":"","address":"广州市萝岗区青年路224号902房","identNo":"******************","faceImagePath":"20200918142836_cf1f104652dc4ac39","gender":1,"nation":"汉","mobile":"***********","identFrontPath":"20200918142836_2c8ec3c6c0e94d699","identStartDate":"","issueAuthority":"","verifiedSerialNo":"eb75da6abe854d59805c93927ed0435d","auditTime":"2020-09-18 11:41:37","identExpiresDate":"","name":"洪小林","verifiedWay":0,"verifiedStatus":2,"verifiedStatusDesc":"审核通过"}}}
            if("1".equals(restResult.getCode())){
                JSONObject jsonObject=(JSONObject)restResult.getData();
                return jsonObject;
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 企业实名证书申请
     * @param customerId FDD账户customerId
     * @param verifiedSerialNo 实名认证序列号
     * @return CompanyCertifiedApplyVO：开始时间和结束时间
     */
    public CompanyCertifiedApplyVO companyCertifiedApply(String customerId,String verifiedSerialNo){
        VerifiedCertApplyDTO verifiedCertApplyDTO=new VerifiedCertApplyDTO();
        verifiedCertApplyDTO.setCustomerId(customerId);// 客户编号
        verifiedCertApplyDTO.setVerifiedSerialNo(verifiedSerialNo);// 实名认证序列号
        verifiedCertApplyDTO.setCertType(2);// 证书类型1-编号证书，2-实名证书. (目前只能申请实名证书，与传的参数无关)
        try {
            RestResult restResult=clientFactory.certificateClient().certifiedApply(verifiedCertApplyDTO);// RestResult{code='1', msg='申请认证证书成功', data={"startTime":"20200918145257","endTime":"20200919145257"}}
            if("1".equals(restResult.getCode())){
                JSONObject jsonObject=(JSONObject)restResult.getData();
                String startTime=jsonObject.getString("startTime");
                String endTime=jsonObject.getString("endTime");

                return new CompanyCertifiedApplyVO(startTime,endTime);
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 企业用户信息查询
     * @param companyName 企业名称
     * @param creditNo 统一社会信用代码
     * @return 返回根据companyName模糊查询的信息列表
     */
    public JSONArray getCompanyUserInfos(String companyName,@Nullable String creditNo){
        CompanyUserInfoDTO companyUserInfoDTO=new CompanyUserInfoDTO();
        companyUserInfoDTO.setCompanyName(companyName);
        companyUserInfoDTO.setCreditNo(creditNo);

        try {
            RestResult restResult=clientFactory.companyClient().getCompanyUserInfos(companyUserInfoDTO);// RestResult{code='1', msg='操作成功', data=[{"companyVerifiedStatusVO":{"verifiedSerialNo":"3f79ed14d55b465d8259de52dd80f13f","verifiedStatus":4,"verifiedStatusDesc":"审核通过","companyVerifiedInfoVO":{"subbranchName":"中国建设银行股份有限公司广州黄埔支行","companyPrincipalType":1,"subbranchCity":"广州市","auditTime":"2020-12-30 10:35:55","creditImagePath":"20201230104620_83cd62a5b1a04b75a","companyName":"广州港物流有限公司","verifiedWay":0,"bankNo":"44001470901053000321","bankName":"建设银行","auditFailReason":"","creditNo":"914401011904317405","subbranchProvince":"广东省"},"personVerifiedInfoVO":{"birthday":"1978-04-08 00:00:00.0","address":"广州市天河区程界东村东南巷6号","identNo":"******************","faceImagePath":"20201230104621_ef0d53d3ebe74f72a","gender":2,"nation":"汉","mobile":"***********","identFrontPath":"20201230104620_b9c856b878ce45a09","identStartDate":"","issueAuthority":"","verifiedSerialNo":"2d26c991f3b7497e87f3cbfa3336e7c3","auditTime":"2020-12-28 09:43:44","identExpiresDate":"","name":"苏美兰","verifiedWay":0,"verifiedStatus":2,"verifiedStatusDesc":"审核通过"}},"customerId":"66d1507358234975916dfac9fc4e544c","companyAdminInfoVO":{"customerId":"f57c5ab5cf4c4d27bf903cfcfadf2464","account":"<EMAIL>"}}]}
            if("1".equals(restResult.getCode())){
                return (JSONArray)restResult.getData();
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }


    @Getter
    @Setter
    @AllArgsConstructor
    @ToString
    public static class CompanyCertificationUrlVO{
        private String transactionNo;// 事务编号
        private String url;// 认证地址
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @ToString
    public static class CompanyCertifiedApplyVO{
        private String startTime;// 开始时间
        private String endTime;// 结束时间
    }

}

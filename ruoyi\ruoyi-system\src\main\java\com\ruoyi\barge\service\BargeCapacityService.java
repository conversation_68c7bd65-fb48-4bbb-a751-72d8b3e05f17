package com.ruoyi.barge.service;

import com.ruoyi.barge.domain.bo.BargeCapacityPublishBO;
import com.ruoyi.barge.domain.vo.BargeCapacityPublishVO;
import com.ruoyi.barge.domain.vo.CargoPublishVO;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/11 10:56
 */
public interface BargeCapacityService {

    /**
     * 获取货源信息列表
     * @param bargeCapacityPublishBO
     * @return
     */
    List<CargoPublishVO> getCargoSourceList(BargeCapacityPublishBO bargeCapacityPublishBO);

    /**
     * 获取运力信息列表
     * @param bargeCapacityPublishBO
     * @return
     */
    List<BargeCapacityPublishVO> getCapacityList(BargeCapacityPublishBO bargeCapacityPublishBO);

    /**
     * 添加运力
     * @param bargeCapacityPublishBO
     * @return
     */
    AjaxResult addCapacity(BargeCapacityPublishBO bargeCapacityPublishBO);

    /**
     * 修改运力
     * @param bargeCapacityPublishBO
     * @return
     */
    AjaxResult updateCapacity(BargeCapacityPublishBO bargeCapacityPublishBO);

    /**
     * 删除运力信息
     * @param id
     * @return
     */
    AjaxResult deleteCapacity(Long id);
}

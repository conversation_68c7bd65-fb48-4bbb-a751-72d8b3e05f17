// pages/agentdelivery/update/update.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {

    form: {
      id: "",
      coutFormId: "",
      deliveryNo: "",
      bargeName: "",
      planWeight: ""
    },
    images: [],
    coutforms: [],

    showHalfDialog: false,

    coutFormIdSearch: ""
  },

  //确认提交
  handleConfirm(){
    if(this.data.form.id == null || this.data.form.id == ""){
      app.$post(app.$url.agentDelivery.add, this.data.form).then(res=>{
        wx.navigateBack({
          delta: 1
        })
        if(this.data.images.length > 0){
          this.uploadImages(res.data, this.data.images.length, 0, 1)
        }
      })
    } else {
      app.$post(app.$url.agentDelivery.update, this.data.form).then(res=>{
        wx.navigateBack({
          delta: 1
        })
        if(this.data.images.length > 0){
          this.uploadImages(this.data.form.id, this.data.images.length, 0, 1)
        }
      })
    }
  },

  //上传文件
  uploadImages(agentDeliveryId, originNum, currentNum, isAdd){
    wx.uploadFile({
      url: app.$url.agentDelivery.updateAgentDeliveryImage,
      filePath: this.data.images[currentNum],
      header: {
        Authorization: 'Bearer ' + wx.getStorageSync('token')
      },
      name: 'file',
      formData: {
        'agentDeliveryId': agentDeliveryId,
        'isAdd': isAdd
      },
      success: res => {
        app.$message("上传图片 " + (currentNum + 1) + " 成功")
        if(originNum == 2 && currentNum == 0){
          this.uploadImages(agentDeliveryId, originNum, 1, "0")
        }
      }
    })
  },

  //选择图片
  takePhoto(){
    let _this = this
    wx.showActionSheet({
      itemList: ['拍照','从相册中选择'],
      success(res) {
        if(res.tapIndex==0){ //0是拍照
          wx.chooseImage({
            count: 1,
            sizeType: ['original','compressed'],
            sourceType: ['camera'],
            success: function (res) {
                //res.tempFilePaths[0] 这个是图片
                let images = []
                let src
                if(_this.data.images.length == 1){
                  src = _this.data.images[0]
                  images.push(src)
                } else if(_this.data.images.length == 2){
                  src = _this.data.images[1]
                  images.push(src)
                  console.log(11)
                }
                images.push(res.tempFilePaths[0])
                _this.setData({
                  images: images
                })
             },
          })
        } else if(res.tapIndex==1){
          wx.chooseImage({
            count: 2,
            sizeType: ['original','compressed'],
            sourceType: ['album'],
            success: function(res) {
            //res.tempFilePaths[0] 这个是图片
              _this.setData({
                images: res.tempFilePaths
              })
            },
          })
        }
      }
    })
  },

  coutFormIdInput(){
    this.setData({
      'showHalfDialog': true
    });
  },

  // 出库单搜索输入框
  coutFormIdSearchInputChange(e){
    this.setData({
      coutFormIdSearch: e.detail.value
    })
  },

  //出库单搜索
  handleSearchCoutformIds(){
    app.$post(app.$url.agentDelivery.listCOutFormId, this.data.coutFormIdSearch).then(res=>{
      if(res.code == 200) {
        this.setData({
          coutforms: res.data
        })
        if(this.data.coutforms.length == 0) {
          app.$message('当前没有可匹配的出库单号');
        } 
      }else {
        app.$message(`获取出库单列表失败，原因:${res.msg}`);
      }
    })
  },

  handleCoutformIdChange(e){
    this.setData({
      "form.coutFormId": this.data.coutforms[e.detail.value]
    })
  },

  handleSelectCoutformId(){
    if(!this.data.form.coutFormId) {
      app.$message('请选择出库单号');
      return
    }
    this.setData({
      'showHalfDialog': false
    });
  },

  deletePhoto(e){
    wx.showModal({
      title: '警告',
      content: '确定删除第' + (e.currentTarget.dataset.item + 1) + '张图吗？',
      success: res => {
        if (res.confirm) {
          let images = this.data.images
          images.splice(e.currentTarget.dataset.item, 1)

          this.setData({
            images: images
          })
        } else if (res.cancel) {
          app.$message("取消删除")
        }
      }
    })
  },

  // 发货编号输入框
  deliveryNoInputChange(e){
    this.setData({
      "form.deliveryNo": e.detail.value
    })
  },

  // 计划吨数输入框
  planWeightInputChange(e){
    this.setData({
      "form.planWeight": e.detail.value
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const _this=this

    let update = false
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.on('acceptDataFromOpenerPage', function (data) {
      update = true
      _this.setData({
        "form.id": data.id,
        "form.coutFormId": data.coutFormId,
        "form.deliveryNo": data.deliveryNo,
        "form.bargeName": data.bargeName,
        "form.planWeight": data.planWeight
      })
      app.$post(app.$url.agentDelivery.selectAgentDeliveryImages, data.id).then(res=>{
        let images = []
        for(let i in res.data){
          let image = "https://bulkbarge.gzport.com" + res.data[i].url.substring(13);
          images.push(image)
        }
        _this.setData({
          images: images
        })
      })
    })

    if(!update){
      app.$post(app.$url.agentDelivery.selectUserBargeName).then(res=>{
        console.log(res)
        this.setData({
          "form.bargeName": res.msg
        })
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
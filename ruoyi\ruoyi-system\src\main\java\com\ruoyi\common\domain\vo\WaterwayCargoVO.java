package com.ruoyi.common.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.UploadAddress;
import lombok.*;

import java.util.HashMap;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/5 9:49
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class WaterwayCargoVO {

    /**
     * 托运单明细id
     */
    private Long consignDetailId;

    /**
     * 驳船名称
     */
    private String bargeName;

    /**
     * 托号
     */
    private String consignFlag;

    /**
     * 托运单主键id
     */
    private Long consignId;


    /**
     * 生成水路运单时间
     */
    private String recordDate;


    /**
     * 办理托运单时间
     */
    private String applyDate;

    /**
     * 出库单类型1.本司2.它司
     */
    private Integer wxOutorInformType;


    /**
     * 出库单号
     */
    private String outOrInformId;

    /**
     * 货物名称
     */
    private String cargeName;

    /**
     * 托运人公司
     */
    private String consigner;

    /**
     * 收货人
     */
    private String consignee;


    /**
     *  起运港
     */
    private String beginPort;


    /**
     *  目的港
     */
    private String endPort;

    /**
     *  其他 沿海/内河 港口（2021-02-07添加）
     */
    private String otherCoastalInlandPort;


    /**
     *  中转港
     */
    private String midPort;

    /**
     * 装货地点
     */
    private String shipmentPlace;

    /**
     * 卸货地点
     */
    private String shipmentunPlace;

    /**
     * 地磅号
     */
    private String loadometerId;

    /**
     * 提单号
     */
    private String consignmentFlag;

    /**
     * 包装方式
     */
    private String packageType;

    /**
     * 承运船舶公司
     */
    private String shippingCoName;

    /**
     * 托运单重量
     */
    private String rationWeight;

    /**
     * 托运单件数
     */
    private String rationPiece;

    /**
     * 托运单联系人电话
     */
    private String wxRationContactNumber;

    /**
     * 驳船主联系电话
     */
    private String bargeTel;

    /**
     * 费用结算方式
     */
    private String chargeBalanceType;

    /**
     * 月结算审核单位(船公司id)
     */
    private Long wxMonthChargeById;

    /**
     * 月结算审核单位(船公司名称)
     */
    private String wxMonthChargeByName;

    /**
     * 托运单实际装货量
     */
    private String workWeight;



    /**
     * 特约事项
     */
    private String specialProceeding;

    /**
     * 驳船预约时间
     */
    private String wxAppointmentTime;


    /**
     * 运费
     */
    private String transportCharge;

    /**
     * 停泊费
     */
    private String berthCharge;



    /**
     * 货港费
     */
    private String cargoPortCharge;


    /**
     * 围油栏费
     */
    private String serviceAgentCharge;


    /**
     * 代理费
     */
    private String businessAgentCharge;


    /**
     * 费用总合计
     */
    private String totalCharge;


    /**
     * 文件信息
     */
    private List<UploadAddress> uploadAddress;


    /**
     * 驳船状态，0：未审核 1：已审核 2：审核不通过 3：已配载 4：已报到 5：已离港 6:已退单
     * 用于给前端判断该订单是什么状态
     */
    private String flagBargeState;

    //------------  改单需要数据  -------------------

    /**
     * 可配置量
     */
    private String weightValue;

    /**
     * 可配载件数
     */
    private String pieceValue;

    /**
     * 派船公司id
     */
    private Long wxSelectShipById;

    /**
     * 派船公司名称
     */
    private String wxSelectShipByName;

    /**
     * 派船状态(0：待派船，1：以派船)
     */
    private Integer wxSelectShipState;

    /**
     * 派船类型(货主办理托运单时)(0:货主派船 1：船公司派船)
     */
    private Integer wxSelectShipType;


    /**
     *   出库单带出来的comId
     */
    private Long comId;

    /**
     * 小程序操作状态(0:待驳船主确认 1:待支付  2:待船公司审批(月结)，3船公司审批不通过(月结) 4:待驳船主预约
     * 5:驳船主已预约 6:取消预约 )
     */
    private Integer wxOperateState;

    /**
     * 驳船主键id
     */
    private Long bargeId;

    /**
     * 船舶载货量（A级）
     */
    private String bargeLoadA;

    /**
     * 船舶载货量（B级）
     */
    private String bargeLoadB;



    /**
     * 航区： A级、B级、null
     */
    private String navigatingZone;


    /**
     * 办单类型1-货主；   2-船公司
     */
    private Integer wxApplyUserType;


    /**
     * 水路运单号
     */
    private String waterwayCargoId;


    /**
     * 水路运单文件
     */
    private HashMap<String, String> base64Map;

    /**
     * 月结码
     */
    private String monthlyCode;

    /**
     * 挂靠公司名称
     */
    private String companyName;

    private String tscargoweightValue; //实装吨数

    /**
     * 月结申请审批时间
     */
    private String mthApplyCheckTime;
}

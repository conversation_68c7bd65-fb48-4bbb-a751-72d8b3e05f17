package com.ruoyi.databarge.domain.dto;

import com.ruoyi.common.core.domain.BaseParamEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * Description: 驳船预报管理搜索条件
 *
 * @Author: ChenJin on 2020/9/24.
 * @Date: 2020/9/24 11:12
 */
@Setter
@Getter
public class BargeCheckInSearchDTO extends BaseParamEntity {
    private Integer port;
    private String bargename;
    private String waterwaycargoid;
    private String outorinformid;
    private String startDate;
    private String endDate;
    private Integer flagbargestate;
    private Long pageNum;
    private Long pageSize;
}

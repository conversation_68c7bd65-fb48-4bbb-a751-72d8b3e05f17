package com.ruoyi.common.utils.ftp;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2021年06月30日 15:47
 */
@Configuration
@EnableConfigurationProperties(SftpProperties.class)
public class SftpConfig {

    @Bean
    public SftpFactory sftpFactory(SftpProperties properties) {
        return new SftpFactory(properties);
    }

    @Bean
    public SftpPool sftpPool(SftpFactory sftpFactory) {
        return new SftpPool(sftpFactory);
    }

    @Bean
    public BargeSftpUtil BargeSftpUtil(SftpPool pool) {
        return new BargeSftpUtil(pool);
    }
}

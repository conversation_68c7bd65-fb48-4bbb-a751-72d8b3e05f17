<!--pages/mainpage/transportdetail/transportdetail.wxml-->
<view class="detail-page" hover-class="none" hover-stop-propagation="false">
  <view class="detail-message" hover-class="none" hover-stop-propagation="false">
    <view class="message-item" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">驳船名称：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{transportObj.bargeName}}</view>
    </view>
    <view class="message-item" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">船舶识别号：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{transportObj.bargeIdentifier}}</view>
    </view>
    <view class="message-item" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">联系电话：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{transportObj.phone}}</view>
    </view>
    <view class="message-item" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">承载吨数：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{transportObj.capacity}}</view>
    </view>
    <view class="message-item" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">开始时间：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{transportObj.startTime}}</view>
    </view>
    <view class="message-item" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">结束时间：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{transportObj.endTime}}</view>
    </view>
    <view class="message-item" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">备注：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{transportObj.remark}}</view>
    </view>
  </view>
  <view class="detail-oper" hover-class="none" hover-stop-propagation="false">
    <button class="oper-btn" bindtap="handleConfirm">确定</button>
  </view>
</view>

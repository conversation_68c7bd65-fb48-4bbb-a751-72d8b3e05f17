package com.ruoyi.databarge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.databarge.domain.Pb6BargeCheckNode;

import java.util.List;

/**
 * <p>
 * 驳船单证退单改单审核流程表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-23
 */
public interface Pb6BargeCheckNodeService extends IService<Pb6BargeCheckNode> {

    List<Pb6BargeCheckNode> searchByPb6CargoconsignmentDetailId(Long pb6CargoconsignmentDetailId);

    Pb6BargeCheckNode searchRecentByLinkId(Long linkId);
}

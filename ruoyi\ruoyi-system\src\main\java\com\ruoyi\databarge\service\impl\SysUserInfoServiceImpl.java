package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.databarge.domain.SysUserInfo;
import com.ruoyi.databarge.mapper.SysUserInfoMapper;
import com.ruoyi.databarge.service.SysUserInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2021/3/31.
 * @Date: 2021/3/31 16:33
 */
@Service
public class SysUserInfoServiceImpl extends ServiceImpl<SysUserInfoMapper, SysUserInfo> implements SysUserInfoService {
    @Override
    public SysUserInfo searchOneByBargeId(Long bargeId) {
        QueryWrapper<SysUserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysUserInfo::getBargeid, bargeId);
        List<SysUserInfo> sysUserInfoList = super.list(queryWrapper);
        if(sysUserInfoList.size() > 0){
            return sysUserInfoList.get(0);
        }
        return null;
    }

    @Override
    public SysUser searchUserByBargeId(Long bargeId) {
        List<SysUser> sysUserList = super.baseMapper.searchUserByBargeId(bargeId);
        if(sysUserList == null || sysUserList.size() == 0){
            return null;
        } else if(sysUserList.size() == 1){
            return sysUserList.get(0);
        } else {
            throw new CustomException("当前驳船发现有多个驳船主管理员绑定");
        }
    }

    @Override
    public List<Long> searchSalesman(Long bargeId) {
        return baseMapper.searchSalesman(bargeId);
    }
}

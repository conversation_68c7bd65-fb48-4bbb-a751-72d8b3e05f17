package com.ruoyi.databarge.domain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.annotation.*;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * <p>
 * 发票开票记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "SHIP_INVOICE_HISTORY", autoResultMap = true)
@KeySequence(value = "SEQUENCE_SHIP_INVOICE_HISTORY")
public class ShipInvoiceHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 发票抬头类型,0企业,1个人,必填
     */
    @TableField("TYPE")
    private String type;

    /**
     * 驳船用户id,必填
     */
    @TableField("SHIP_USER_ID")
    private Long shipUserId;

    /**
     * 驳船用户名,必填
     */
    @TableField("SHIP_USER_NAME")
    private String shipUserName;

    /**
     * 购方名称,type为0为企业名称,type为1为个人名称,必填
     */
    @TableField("BUYERNAME")
    private String buyername;

    /**
     * 购方税号,企业要填,个人可为空,非必填
     */
    @TableField("TAXNUM")
    private String taxnum;

    /**
     * 购方手机(开票成功会短信提醒购方),必填
     */
    @TableField("PHONE")
    private String phone;

    /**
     * 购方地址,企业要填，个人可为空,非必填
     */
    @TableField("ADDRESS")
    private String address;

    /**
     * 购方银行账号,企业要填，个人可为空,非必填
     */
    @TableField("ACCOUNT")
    private String account;

    /**
     * 购方电话,非必填
     */
    @TableField("TELEPHONE")
    private String telephone;

    /**
     * 订单号,每个企业唯一,必填
     */
    @TableField("ORDERNO")
    private String orderno;

    /**
     * 开票时间,必填
     */
    @TableField("INVOICEDATE")
    private String invoicedate;

    /**
     * 开票员,必填
     */
    @TableField("CLERK")
    private String clerk;

    /**
     * 销方银行账号,非必填
     */
    @TableField("SALEACCOUNT")
    private String saleaccount;

    /**
     * 销方电话,非必填
     */
    @TableField("SALEPHONE")
    private String salephone;

    /**
     * 销方地址,非必填
     */
    @TableField("SALEADDRESS")
    private String saleaddress;

    /**
     * 销方税号,必填
     */
    @TableField("SALETAXNUM")
    private String saletaxnum;

    /**
     * 开票类型,1正票,2红票,必填
     */
    @TableField("KPTYPE")
    private String kptype;

    /**
     * 开票员,备注(不同开票服务器类型支持的备注长度不同，提交后会有相应校验),冲红时,必须在备注中注明“对应正数发票代码:XXXXXXXXX号码:YYYYYYYY”文案,其中“X”为发票代码,“Y”为发票号码,否则接口会自动添加该文案,非必填
     */
    @TableField("MESSAGE")
    private String message;

    /**
     * 收款人,非必填
     */
    @TableField("PAYEE")
    private String payee;

    /**
     * 复核人,非必填
     */
    @TableField("CHECKER")
    private String checker;

    /**
     * 对应蓝票发票代码,红票必填，不满12位请左补0
     */
    @TableField("FPDM")
    private String fpdm;

    /**
     * 对应蓝票发票号码,红票必填，不满8位请左补0
     */
    @TableField("FPHM")
    private String fphm;

    /**
     * 推送方式:-1不推送,0邮箱,1手机(默认),2邮箱和手机,非必填
     */
    @TableField("TSFS")
    private String tsfs;

    /**
     * 推送邮箱,tsfs为0或2时必填
     */
    @TableField("EMAIL")
    private String email;

    /**
     * 清单标志,0根据项目名称数自动产生清单,1将项目信息打印至清单,默认为0,卷票r不支持清单,非必填
     */
    @TableField("QDBZ")
    private String qdbz;

    /**
     * 清单项目名称,打印清单时对应发票票面项目名称,如详见销货清单,注意:税总要求清单项目名称为(详见销货清单),qdbz为1时,此项为必填
     */
    @TableField("QDXMMC")
    private String qdxmmc;

    /**
     * 代开标志,0非代开(默认),1代开,代开蓝票备注文案要求包含:"代开企业税号:***代开企业名称:***.";代开红票备注文案要求:"对应正数发票代码:***号码:***代开企业税号:***代开企业名称:***.",(代开企业税号与代开企业名称之间仅支持一个空格或无符号)，非必填
     */
    @TableField("DKBZ")
    private String dkbz;

    /**
     * 部门门店id(诺诺系统中的id),非必填
     */
    @TableField("DEPTID")
    private String deptid;

    /**
     * 开票员id(诺诺系统中的id),非必填
     */
    @TableField("CLERKID")
    private String clerkid;

    /**
     * 发票种类,p电子增值税普通发票(默认),c增值税普通发票(纸票),s增值税专用发票,e收购发票(电子),f收购发票(纸质),r增值税普通发票(卷式,卷票r只支持13行明细),非必填
     */
    @TableField("INVOICELINE")
    private String invoiceline;

    /**
     * 成品油标志,0非成品油(默认),1成品油,非必填
     */
    @TableField("CPYBZ")
    private String cpybz;

    /**
     * 红字信息表编号,专票冲红时,此项必填,且必须在备注中注明"开具红字增值税专用发票信息表编号ZZZZZZZZZZZZZZZZ"字样，其中"Z"为开具红字增值税专用发票所需要的长度为16位信息表编号
     */
    @TableField("BILLINFONO")
    private String billinfono;

    /**
     * 电子发票明细,List类型,必填
     */
    @TableField(value = "DETAIL", typeHandler = MyJacksonTypeHandler.class)
    private List<Detail> detail;

    /**
     * 水路运单单号,必填
     */
    @TableField("PB6_WATERWAYCARGO_ID")
    private Long pb6WaterwaycargoId;

    /**
     * 发票请求流水号,开票成功后返回,非必填
     */
    @TableField("FPQQLSH")
    private String fpqqlsh;

    /**
     * 发票状态：0开票中 、1已开票、2已冲红、3.待开票  普票必填
     */
    @TableField("STATE")
    private String state;

    /**
     * 发票类型：0普票、1专票 必填
     */
    @TableField("INVOICETYPE")
    private String invoicetype;

    /**
     * 用于保存红票对应蓝票发票代码
     */
    @TableField("BLUE_FPDM")
    private String blue_fpdm;

    /**
     * 用于保存红票对应蓝票发票号码
     */
    @TableField("BLUE_FPHM")
    private String blue_fphm;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private String create_time;

    @Getter
    @Setter
    public static class Detail{
        private String goodsname;// 商品名称。如FPHXZ=1，则此商品行为折扣行，此版本折扣行不允许多行折扣，折扣行必须紧邻被折扣行，项目名称必须与被折扣行一致
        private String num;// 数量。数量、单价必须都不填，或者都必填，不可只填一个；当数量、单价都不填时，不含税金额、税额、含税金额都必填。建议保留小数点后8位。冲红时项目数量为负数
        private String price;// 单价。数量、单价必须都不填，或者都必填，不可只填一个；当数量、单价都不填时，不含税金额、税额、含税金额都必填。建议保留小数点后8位。冲红时项目单价为正数
        private String hsbz;// 单价含税标志，0不含税，1含税
        private String taxrate;// 税率
        private String spec;// 规格型号
        private String unit;// 单位
        private String spbm;// 税收分类编码。签订免责协议客户可不传入，由接口进行匹配，如对接口速度敏感的企业，建议传入该字段
        private String zsbm;// 自行编码
        private String fphxz;// 发票行性质，0正常行，1折扣行，2被折扣行
        private String yhzcbs;// 优惠政策标识，0不使用，1使用
        private String zzstsgl;// 增值税特殊管理，如：即征即退、免税、简易征收等。当yhzcbs为1时，此项必填
        private String lslbs;// 零税率标识:''非零税率，1免税，2不征税，3普通零税率
        private String kce;// 扣除额，小数点后两位，差额征收的发票目前只支持一行明细，不含税差额=不含税金额-扣除额，税额=不含税差额*税率
        private String taxfreeamt;// 不含税金额。精确到小数点后面两位，红票为负，不含税金额、税额、含税金额任何一个不传时，会根据传入的单价，数量进行计算，可能和实际数值存在误差，建议都传入
        private String tax;// 税额。精确到小数点后面两位，红票为负，不含税金额、税额、含税金额任何一个不传时，会根据传入的单价，数量进行计算，可能和实际数值存在误差，建议都传入
        private String taxamt;// 含税金额。精确到小数点后面两位，红票为负，不含税金额、税额、含税金额任何一个不传时，会根据传入的单价，数量进行计算，可能和实际数值存在误差，建议都传入
    }

    @MappedTypes({List.class})
    @MappedJdbcTypes({JdbcType.VARCHAR})
    public static class MyJacksonTypeHandler extends AbstractJsonTypeHandler<List> {
        private Class<List> type;

        public MyJacksonTypeHandler(Class<List> type) {
            this.type = type;
        }

        protected List<ShipInvoiceHistory.Detail> parse(String json) {
            return JSON.parseObject(json,new TypeReference<List<Detail>>(){});
        }

        @Override
        protected String toJson(List obj) {
            return JSON.toJSONString(obj);
        }
    }
}

package com.ruoyi.base.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.ruoyi.base.domain.SSerial;
import com.ruoyi.base.service.ISSerialService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/2/2 12:02
 */
@RestController
@RequestMapping("/base/serial")
public class SSerialController extends BaseController {

    @Autowired
    private ISSerialService sSerialService;

    /**
     * 查询流水号列表
     */
    @PreAuthorize("@ss.hasPermi('base:serial:list')")
    @GetMapping("/list")
    public TableDataInfo list(SSerial sSerial)
    {
        startPage();
        List<SSerial> list = sSerialService.selectSSerialList(sSerial);
        return getDataTable(list);
    }



    /**
     * 获取流水号详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:serial:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(sSerialService.getById(id));
    }

    /**
     * 新增流水号
     */
    @PreAuthorize("@ss.hasPermi('base:serial:add')")
    @Log(title = "流水号", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody SSerial sSerial) throws Exception {
        return sSerialService.insertSSerial(sSerial);
    }

    /**
     * 修改流水号
     */
    @PreAuthorize("@ss.hasPermi('base:serial:edit')")
    @Log(title = "流水号", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody SSerial sSerial)
    {
        return sSerialService.updateSSerial(sSerial);
    }

    @PreAuthorize("@ss.hasPermi('base:serial:editCurrent')")
    @Log(title = "流水号", businessType = BusinessType.UPDATE)
    @PutMapping("/editCurrent")
    public AjaxResult editCurrent(@RequestBody SSerial sSerial)
    {

        SSerial oldSerial = sSerialService.getById(sSerial.getId());

        oldSerial.setCurrentNum(sSerial.getCurrentNum());

        sSerialService.updateById(oldSerial);

        return toAjax(1);
    }

    /**
     * 删除流水号
     */
    @PreAuthorize("@ss.hasPermi('base:serial:remove')")
    @Log(title = "流水号", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        sSerialService.removeByIds(Arrays.asList(ids));
        return toAjax(ids.length);
    }


}

package com.ruoyi.base.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/2/2 11:13
 */
@Data
@TableName("s_serial")
@KeySequence("SEQ_SSERIAL")
public class SSerial{
    private static final long serialVersionUID = 1L;

    /** 流水号id */
    @TableId(value = "id",type = IdType.INPUT)
    private Long id;

    /** 流水号名称 */
    @Excel(name = "流水号名称")
    private String moduleName;

    /** 当前值 */
    @Excel(name = "当前值")
    private Integer currentNum;

    /** 流水号代码 */
    @Excel(name = "流水号代码")
    private String moduleCode;

    /** 流水号前缀 */
    @Excel(name = "流水号前缀")
    private String preWord;

    /** 时间格式 */
    @Excel(name = "时间格式")
    private String timeFormat;

    /** 后缀位数 */
    @Excel(name = "后缀位数")
    private Integer sufNum;

    /** 后缀代码 */
    @Excel(name = "后缀代码")
    private String sufWord;

    /** 是否每日清空（Y是，N否） 默认N */
    @Excel(name = "是否每日清空", readConverterExp = "Y=是，N否")
    private String isDaily;

    /** 是否月度清空(Y是，N否)默认Y */
    @Excel(name = "是否月度清空(Y是，N否)默认Y")
    private String isMonth;

    /** 是否年度清空（Y是，N否）默认N */
    @Excel(name = "是否年度清空", readConverterExp = "Y=是，N否")
    private String isYear;



    @TableField(exist = false)
    private String deptName;

    /** 合同年份 */
    @Excel(name = "合同年份")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long contractYear;

    /** 合同类型 1 正常 0 其他 */
    @Excel(name = "合同类型 1 正常 0 其他")
    private String contractSpType;

    /** 乐观锁 */
    @Excel(name = "乐观锁")
    @Version
    private Long version;

    public SSerial(){

    }

    public SSerial(SSerial serial) {

        this.moduleName = serial.getModuleName();

        this.moduleCode = serial.getModuleCode();
        this.preWord = serial.getPreWord();
        this.timeFormat = serial.getTimeFormat();
        this.sufNum = serial.getSufNum();
        this.sufWord = serial.getSufWord();
        this.isMonth = serial.getIsMonth();
        this.isYear = serial.getIsYear();
        //this.deptId = serial.getDeptId();
        this.contractSpType = serial.getContractSpType();
//        this.contractYear = serial.getContractYear() + 1;

    }

}

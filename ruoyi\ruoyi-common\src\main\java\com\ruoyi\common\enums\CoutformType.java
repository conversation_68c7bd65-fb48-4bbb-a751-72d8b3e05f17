package com.ruoyi.common.enums;

/**
 * @Description 出库单办理类型
 * <AUTHOR>
 * @Date 2020/8/13  17:33
 */
public enum CoutformType {

    /**
     * 1.本司 2.它司(出库单办理)
     */
    COMPANY_DISPOSE(1,"本司办理"),
    OTHER_COMPANY_DISPOSE (2, "他司办理");

    private final Integer code;

    private final String codeName;

    CoutformType(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

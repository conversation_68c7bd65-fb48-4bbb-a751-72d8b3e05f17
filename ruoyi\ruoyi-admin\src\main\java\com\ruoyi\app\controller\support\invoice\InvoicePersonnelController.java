package com.ruoyi.app.controller.support.invoice;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.databarge.domain.InvoicePersonnel;
import com.ruoyi.databarge.mapper.InvoicePersonnelMapper;
import com.ruoyi.databarge.service.InvoicePersonnelService;
import net.bytebuddy.asm.Advice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ship/invoicePersonnel")
public class InvoicePersonnelController {
    @Autowired
    private  InvoicePersonnelService invoicePersonnelService;
    @Autowired
    private InvoicePersonnelMapper invoicePersonnelMapper;

    @GetMapping("/search")
    public AjaxResult search(){
        return  AjaxResult.success(invoicePersonnelMapper.search());
    }
    @PostMapping("/invoicePersonnelAdd")
    public AjaxResult invoicePersonnelAdd(@RequestBody InvoicePersonnel invoicePersonnel){
        invoicePersonnel.setCreateBy(SecurityUtils.getUsername());
        invoicePersonnel.setCreateTime(DateUtil.now());
        invoicePersonnelService.save(invoicePersonnel);
        return AjaxResult.success();
    }
    @PostMapping("/invoicePersonnelUpdate")
    public AjaxResult invoicePersonnelUppdate(@RequestBody InvoicePersonnel invoicePersonnel){
        invoicePersonnel.setUpdateBy(SecurityUtils.getUsername());
        invoicePersonnel.setUpdateTime(DateUtil.now());
        invoicePersonnelService.updateById(invoicePersonnel);
        return AjaxResult.success();
    }

    @GetMapping("/searchBywaterwaycargoid")
    public AjaxResult searchBywaterwaycargoid(@RequestParam(value="waterwaycargoid",required=false) String waterwaycargoid){
        return AjaxResult.success(invoicePersonnelMapper.searchBywaterwaycargoid(waterwaycargoid));
    }

}

<!--pages/consignment/consignmanager/consignmanager.wxml-->
<view class="consign-page" hover-class="none" hover-stop-propagation="false">
  <view class="consign-search" hover-class="none" hover-stop-propagation="false">
    <view class="search-area datesearch" hover-class="none" hover-stop-propagation="false">
      <view class="barge-search-area">
        <input type="text" class="search-ipt" placeholder="提单号/大船名称"  value="{{form.searchValue}}" bindinput="searchValueInput"/>
        <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-icon" wx:if="{{form.searchValue}}" bindtap="deleteSearchValue"></mp-icon>
        <mp-icon class="search-icon" type="field" icon="search" color="#aaaaaa" size="{{30}}" wx:if="{{!form.searchValue}}"></mp-icon>
      </view>
      <!-- <button class="search-area search-btn" bindtap="handleAgentdeliveryPage">代理发货</button> -->
    </view>
     <view class="search-area datesearch" hover-class="none" hover-stop-propagation="false">
      <picker class="search-data-box" mode="date" value="{{form.beginTime}}" bindchange="handleBeginDateChange">
        <view class="{{form.beginTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{form.beginTime ? form.beginTime :'开始时间'}}</view>
        <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-iconp" wx:if="{{form.beginTime}}" catchtap="deleteStartTime"></mp-icon>
      </picker>
      <text class="padding-text">-</text>
      <picker class="search-data-box" mode="date" value="{{form.endTime}}" bindchange="handleEndDateChange">
        <view class="{{form.endTime? 'search-date-selected' : 'search-date-text'}}" hover-class="none" hover-stop-propagation="false">{{form.endTime ? form.endTime :'截止时间'}}</view>
        <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-iconp" wx:if="{{form.endTime}}" catchtap="deleteEndTime"></mp-icon>
      </picker>
      <button class="publish-btn search-btn" bindtap="handleSearch">搜索</button>
    </view>
    <view class="consign-tablist" hover-class="none" hover-stop-propagation="false">
      <view class="{{activeTab==1?'consign-tab active-tab':'consign-tab'}}" data-tabid="1" data-wxOperateState="0" data-flagBargeState="" hover-class="none" hover-stop-propagation="false" bindtap="handleChangeTab">待报到</view>
      <!-- <view class="{{activeTab==2?'consign-tab active-tab':'consign-tab'}}" data-tabid="2" data-wxOperateState="1" data-flagBargeState="" hover-class="none" hover-stop-propagation="false" bindtap="handleChangeTab">待支付/待预约</view> -->
      <view class="{{activeTab==3?'consign-tab active-tab':'consign-tab'}}" data-tabid="3" data-wxOperateState="5" data-flagBargeState="6" hover-class="none" hover-stop-propagation="false" bindtap="handleChangeTab">待作业</view>
      <view class="{{activeTab==6?'consign-tab active-tab':'consign-tab'}}" data-tabid="6" data-wxOperateState="" data-flagBargeState="4" hover-class="none" hover-stop-propagation="false" bindtap="handleChangeTab">完工确认</view>
      <view class="{{activeTab==4?'consign-tab active-tab':'consign-tab'}}" data-tabid="4" data-wxOperateState="" data-flagBargeState="5" hover-class="none" hover-stop-propagation="false" bindtap="handleChangeTab">已完结</view>
      <!-- <view class="{{activeTab==5?'consign-tab active-tab':'consign-tab'}}" data-tabid="5" data-wxOperateState="" data-flagBargeState="" hover-class="none" hover-stop-propagation="false" bindtap="handleChangeTab">全部</view> -->
    </view>
  </view>
  <view class="consign-content" hover-class="none" hover-stop-propagation="false">
    <view class="data-item" wx:key="index" wx:for="{{listData}}" hover-class="none" hover-stop-propagation="false" data-item="{{item}}" >
      <view class="data-item-title" hover-class="none" hover-stop-propagation="false">
        <view class="item-title-left" hover-class="none" hover-stop-propagation="false">
          <text class="" selectable="false" space="false" decode="false">提单号：</text>
          <text class="" selectable="false" space="false" decode="false">{{item.billNo}}</text>
        </view>
        <view class="data-item-right" hover-class="none" hover-stop-propagation="false">{{item.wxOperateStateText}}</view>
      </view>
      <view class="data-content" hover-class="none" hover-stop-propagation="false">
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">驳船名称：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.bargeName}}</text>
        </view>
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">驳船航次：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.bargeNumber}}</text>
        </view>
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">大船名：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.shipName}}</text>
        </view>
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">货物名称：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.cargeName}}</text>
        </view>
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">起运港：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.beginPort}}</text>
        </view>
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">目的港：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.endPort}}</text>
        </view>
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">托运单重量：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.rationWeight}}吨</text>
        </view>
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">收货人：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.consignee}}</text>
        </view>
        <!-- <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">出库单号：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.outOrInformId}}</text>
        </view> -->
        <view class="data-content-message" hover-class="none" hover-stop-propagation="false">
          <text class="message-title" selectable="false" space="false" decode="false">申请时间：</text>
          <text class="message-value" selectable="false" space="false" decode="false">{{item.applyDate}}</text>
        </view>
      </view>
      <!-- isSubscription ： false - 未订阅    true - 订阅 -->
      <view class="data-operate" hover-class="none" hover-stop-propagation="false" wx:if="{{item.wxOperateStateText}}">
        <view>
          <button class="oper-btn" catchtap="confirmOrder" data-item="{{item}}" wx:if="{{item.wxOperateStateText == '待确认'}}">确认</button>
        </view>
        <view>
          <button class="oper-btn" catchtap="cancleConfirmOrder" data-item="{{item}}" wx:if="{{item.wxOperateStateText == '待报到' || item.wxOperateStateText == '报到完毕'}}">取消确认</button>
        </view>
        <view>
          <button class="oper-btn" catchtap="reGenerateWaterWayCargoApp" data-item="{{item}}" wx:if="{{item.wxOperateStateText == '已完成'}}">生成</button>
        </view>
        <view>
          <button class="oper-btn" catchtap="tapConfirmLoading" data-item="{{item}}" wx:if="{{item.wxOperateStateText == '已报到'}}">确认实装</button>
        </view>
        <view>
          <button class="oper-btn" catchtap="tapWaterWayFile" data-item="{{item}}">文件列表</button>
        </view>
        <view wx:if="{{(userInfos.userType == 14 || rolePermission.defray == ('weChat:barge:defray'))}}">
          <button class="oper-btn" catchtap="payOrder"  data-item="{{item}}" wx:if="{{item.wxOperateStateText == '待支付' || item.wxOperateStateText == '船公司审批不通过(月结)'}}">支付</button>
        </view>
        <!-- <view wx:if="{{userInfos.userType == 14 || rolePermission.reservation == ('weChat:barge:reservation')}}">
          <button class="oper-btn" catchtap="reservationOrder"  data-item="{{item}}" wx:if="{{item.wxOperateStateText == '待预约'}}">预约</button>
        </view>
        <view wx:if="{{userInfos.userType == 14 || rolePermission.reservation == ('weChat:barge:reservation')}}">
          <button class="oper-btn" catchtap="cancelReservationOrder"  data-item="{{item}}" wx:if="{{item.wxOperateStateText == '待报到'}}">取消预约</button>
        </view> -->
        <!-- <view wx:if="{{userInfos.userType == 14 || rolePermission.chargeback == ('weChat:barge:chargeback') }}">
          <button class="oper-btn" catchtap="chargeBackOrder"  data-item="{{item}}" wx:if="{{item.wxOperateStateText == '待确认' || item.wxOperateStateText == '待支付' || item.wxOperateStateText == '待预约' || item.wxOperateStateText == '待报道' || item.wxOperateStateText == '已完成'}}">退单</button>
        </view>
        <view wx:if="{{userInfos.userType == 14 || rolePermission.chargeback == ('weChat:barge:chargeback')}}">
          <button class="oper-btn" catchtap="modifyOrder"  data-item="{{item}}" wx:if="{{item.wxOperateStateText == '待确认'  || item.wxOperateStateText == '待支付'  || item.wxOperateStateText == '待预约' || item.wxOperateStateText == '待报道' || item.wxOperateStateText == '已完成'}}">改单</button>
        </view> -->

        <!-- <view wx:if="{{userInfos.userType == 14 && userInfos.userId == item.payUserId}}">
          <button class="oper-btn" catchtap="balanceRefundOrder"  data-item="{{item}}" wx:if="{{item.wxOperateStateText == '已完成'}}">差额退款</button>
        </view> -->
        <!-- <view>
          <button class="oper-btn" catchtap="subscribeOrder"  data-item="{{item}}" wx:if="{{!item.isSubscription && (item.wxOperateStateText == '待确认'  || item.wxOperateStateText == '待支付'  || item.wxOperateStateText == '待预约' || item.wxOperateStateText == '待报到' || item.wxOperateStateText == '已完成' || item.wxOperateStateText == '船公司审批不通过(月结)' || item.wxOperateStateText == '已报到')}}">订阅</button>
        </view> -->
        <!-- <view>
          <button class="oper-btn" catchtap="cancelSubscribeOrder"  data-item="{{item}}" wx:if="{{item.isSubscription && (item.wxOperateStateText == '待确认'  || item.wxOperateStateText == '待支付'  || item.wxOperateStateText == '待预约' || item.wxOperateStateText == '待报到' || item.wxOperateStateText == '已完成' || item.wxOperateStateText == '船公司审批不通过(月结)' || item.wxOperateStateText == '已报到')}}">取消订阅</button>
        </view> -->
        <!-- <view>
          <button wx:if="{{item.flagBargeState == 5 || item.wxOperateState == 4 || item.wxOperateState == 5}}" 
          class="oper-btn" catchtap="checkFapiao"  data-item="{{item}}" >查看发票</button>
        </view> -->
      </view>
      <!-- wxConfirmCheck 审核状态（0：待审核；1：审核通过；2：审核不通过 (目前不需要这个属性) -->
      <!-- <view class="audit-status-wrap" hover-class="none" hover-stop-propagation="false" wx:if="{{item.wxConfirmCheck}}">
        待确认
        <view class="audit-statis-text" hover-class="none" hover-stop-propagation="false" wx:if="{{item.wxOperateStateText == '待确认' && item.wxConfirmCheck ===0}}">
          待物流公司审核
        </view>
        待支付状态
        <view class="audit-statis-text" hover-class="none" hover-stop-propagation="false" wx:if="{{item.wxOperateStateText == '待支付' && item.wxConfirmCheck == 2}}">
          船公司审核不通过
        </view>
        <view class="audit-statis-text" hover-class="none" hover-stop-propagation="false" wx:if="{{item.wxOperateStateText == '待支付' && item.wxConfirmCheck === 0}}">
          待船公司审核
        </view>
      </view> -->
    </view>
  </view>
  <!-- 确认弹窗 -->
  <mp-dialog title="" show="{{showConfirmDialog}}" bindbuttontap="tapConfirmdDialog" buttons="{{confirmButtons}}" class="confirm-dialog-wrap">
    <view class="confirm-dialog-content" hover-class="none" hover-stop-propagation="false">
      <view class="confirm-dialog-each" hover-class="none" hover-stop-propagation="false">
        <view class="confirm-dialog-left" hover-class="none" hover-stop-propagation="false">
          目的港：
        </view>
        <input type="text" disabled class="confirm-dialog-input" value="{{confirmForm.endPort}}" catchinput="endPortInput"/>
      </view>
      <view class="confirm-dialog-each" hover-class="none" hover-stop-propagation="false">
        <view class="confirm-dialog-left" hover-class="none" hover-stop-propagation="false">
          配载吨数：
        </view>
        <input type="text" placeholder="" class="confirm-dialog-input" value="{{confirmForm.rationWeight}}" catchinput="rationWeightInput" disabled/>
      </view>
      <!-- <view class="confirm-dialog-each" hover-class="none" hover-stop-propagation="false">
        <view class="confirm-dialog-left" hover-class="none" hover-stop-propagation="false">
          支付方式：
        </view>
        <view class="confirm-dialog-right" hover-class="none" hover-stop-propagation="false">
          <radio-group bindchange="identityChange" class="iden-radio">
            <label class="iden-radio-item">
              <radio value="1" color="#00426B" checked="{{confirmForm.chargeBalanceType==1}}"></radio>
              <view class="iden-radio-text">现结</view>
            </label>
            <label class="iden-radio-item">
              <radio value="0" color="#00426B" checked="{{confirmForm.chargeBalanceType==0}}"></radio>
              <view class="iden-radio-text">月结</view>
            </label>
          </radio-group>
        </view>
      </view> -->

      <view class="confirm-dialog-each1" hover-class="none" hover-stop-propagation="false">
        提示：确认后，港盛开始配载。如有问题请联系港盛。
      </view>
    </view>
  </mp-dialog>

    <!-- 取消确认弹窗 -->
    <mp-dialog title="" show="{{showCancelConfirmDialog}}" bindbuttontap="tapCancelConfirmDialog" buttons="{{confirmButtons}}" class="cancel-reser-dialog-wrap">
    <view class="cancel-reser-dialog-content" hover-class="none" hover-stop-propagation="false">
      是否确认取消确认？
    </view>
  </mp-dialog>

  <!-- 退单弹窗 -->
  <mp-dialog title="" show="{{chargeBackDialog}}" bindbuttontap="tapChargeDialog" buttons="{{chargeBackButtons}}" class="charge-dialog-wrap">
    <textarea  class="charge-textarea" placeholder="请填写说明" value="{{chargeBackForm.modifyReason}}"  fixed="true" bindinput="modifyReasonInput">
    </textarea>
  </mp-dialog>
  
  <!-- 取消预约弹窗 -->
  <mp-dialog title="" show="{{showCancelReservationDialog}}" bindbuttontap="tapCancelReservationDialog" buttons="{{cancelReservationButtons}}" class="cancel-reser-dialog-wrap">
    <view class="cancel-reser-dialog-content" hover-class="none" hover-stop-propagation="false">
      是否确认取消预约？
    </view>
  </mp-dialog>

  <!-- 差额退款弹窗 -->
  <mp-dialog title="" show="{{showbalanceRefundDialog}}" bindbuttontap="tapBalanceRefundDialog" buttons="{{balanceRefundButtons}}" class="cancel-reser-dialog-wrap">
    <view class="cancel-reser-dialog-content" hover-class="none" hover-stop-propagation="false">
      是否申请差额退款？
    </view>
  </mp-dialog>

  

  <!-- 告知书弹窗 -->
  <mp-dialog
    use-slot
    title="来港小型船舶(驳船)告知书"
    show="{{ showNoticeDialog }}"
    buttons="{{noticeButtons}}"
    bind:buttontap="confirmNotice"
  >
    <view class="notice-dialog-content">
      <view class="notice-content">
        <text>{{noticeContent[currentPage-1].content}}</text>
        <!-- 在倒数第二页显示日期 -->
        <view wx:if="{{currentPage === totalPages - 1}}" >
          <view class="inline-container centered-date">
            <text>日期：{{currentConsignObj.createTime}}</text>
          </view>
        </view>
        <!-- 在最后一页显示驳船船长字输入框 -->
        <view wx:if="{{currentPage === totalPages}}" >
          <view class="inline-container" style="text-align: center;">
            <text>驳船船长签字/船章：</text>
            <view class="view-contain-ti small-input">
              <input class="input1" value="{{currentConsignObj.bargeCaptainSign}}" placeholder="请输入驳船船长签字/船章" bindinput="handleBargeCaptainSignInput" />
            </view>
          </view>
          <view class="inline-container centered-date">
            <text>日期：{{currentConsignObj.createTime}}</text>
          </view>
        </view>
      </view>
      
      <view class="page-control">
        <button 
          type="default" 
          size="small" 
          bindtap="prevPage"
          disabled="{{currentPage === 1}}"
        >上一页</button>
        
        <text class="page-number">{{currentPage}}/{{totalPages}}</text>
        
        <button 
          type="default" 
          size="small" 
          bindtap="nextPage"
          disabled="{{currentPage === totalPages}}"
        >下一页</button>
      </view>

      
    </view>
  </mp-dialog>

  <!-- 安全提醒告示书弹窗 -->
  <mp-dialog
    use-slot
    title="安全提醒告示书"
    show="{{ showSafetyNoticeDialog }}"
    buttons="{{safetyNoticeButtons}}"
    bind:buttontap="confirmSafetyNotice"
  >
    <view class="safety-notice-dialog-content">
      <view class="safety-notice-content">
        <text>

          {{bargeName}}：

          因你船所载货物具有易流态化特性，根据《IMSBC 规则》及其他相关规定要求，特对你船进行以下安全提醒：
          1. 保证货物含水量在货物适运水分极限以内；
          2. 按要求合理配载和装载货物，合理排放压舱水，合理平舱、隔舱等，
          3. 对高密度货物，应注意保证货物在舱内均匀铺开使重量平均分布；
          4. 采取有效措施使货物保持干燥；勿在雨天装货；
          5. 保持船舶污水井等管系通畅；
          6. 确保舱口风雨密；
          7. 应定期检查货物，注意货物流态化情况；
          8. 告知所有船员货物特性，制定应急部署方案等。
          9. 做好船舶稳性计算；
          10. 与船公司保持有效联系，随时可得到船舶公司技术支持。
          请按上述提醒，做好开航前准备，并在航行中注意安全！
          注：此记录一式二份，一份交船长，一份由实施现场检查的海事处留存。

        </text>
        <view class="inline-container right-aligned">
          <input class="input1 short-underline" value="{{currentConsignObj.maritimeBureau || '广州'}}" placeholder="请输入海事局" bindinput="handleMaritimeBureauInput" />
          <text>海事局</text>
          <input class="input1 short-underline" value="{{currentConsignObj.maritimeOffice || '沙角'}}" placeholder="请输入海事处" bindinput="handleMaritimeOfficeInput" />
          <text>海事处</text>
        </view>
        <text class="right-aligned date-text">{{currentConsignObj.createTime }}</text>
        <view class="inline-container right-aligned">
          <text>船舶签收栏：</text>
          <input class="input1 longer-underline" value="{{currentConsignObj.shipSign}}" placeholder="" bindinput="handleShipSignInput" />
        </view>
      </view>
    </view>
  </mp-dialog>

  <!-- 船长声明弹窗 -->
<mp-dialog
  use-slot
  title="船长声明"
  show="{{ showCaptainStatementDialog }}"
  buttons="{{ captainStatementButtons }}"
  bind:buttontap="confirmCaptainStatement"
>
  <view class="captain-statement-dialog-content">
    <view class="inline-container">
      
        <input class="view-contain-ti small-input" value="{{currentConsignObj.maritimeOffice}}" placeholder="请输入海事处" bindinput="handleMaritimeOfficeInput" />
      
      <text>海事处：</text>
    </view>
    
    <view class="inline-container">
      <text> 我船 {{currentConsignObj.bargeName}}, 于 </text>
      <input class="view-contain-ti small-input" value="{{currentConsignObj.berth}}" placeholder="请输入泊位" bindinput="handleBerthInput" /><text> 停泊</text>
    </view>
    <text>
      本航次装载货物名称为 {{currentConsignObj.cargeName}}, 装量为 {{currentConsignObj.totalTonnage}} 吨，计划于 {{currentConsignObj.plannedDepartureDate}} 开往 {{currentConsignObj.endPort}}。
      我船已经知悉此类货物运输的特殊性和危险性，并已按照有关规定，做好含水率确认、货物配载、平舱、保证货舱污水井和管系通畅、掌握天气、海况情况等安全措施。
      我船对自身航行安全负责，特此声明。
    </text>

    <view class="inline-container right-aligned">
      <text>船长(签名)：</text>
      <view class="view-contain-ti small-input">
        <input class="input1" value="{{currentConsignObj.captainSign}}" placeholder="请输入船长签名" bindinput="handleCaptainSignInput" />
      </view>
    </view>
         
    <text class="date-text">（加盖船章）
    {{currentConsignObj.createTime }}</text>
    
  </view>
</mp-dialog>

<!-- 在船长声明弹窗后添加新的安全检查表弹窗 -->
<mp-dialog
  use-slot
  title="散货船装卸船/锚地安全检查项目表"
  show="{{ showSafetyCheckDialog }}"
  buttons="{{safetyCheckButtons}}"
  bind:buttontap="confirmSafetyCheck"
>
  <view class="safety-check-dialog-content">
    <view class="safety-check-header">
      <view class="header-row">
        <text>船名：</text>
        <input class="input-field" value="{{currentConsignObj.bargeName}}" disabled/>
        <text>日期：</text>
        <input class="input-field" value="{{currentConsignObj.createTime}}" disabled/>
      </view>
      <view class="header-row">
        <text>锚地：</text>
        <input class="input-field"
               value="{{safetyCheckForm.anchorage}}"
               bindinput="handleAnchorageInput"/>
        <text>锚位：</text>
        <input class="input-field"
               value="{{safetyCheckForm.anchorPosition}}"
               bindinput="handleAnchorPositionInput"/>
      </view>
      <view class="header-row">
        <text>锚位水深：</text>
        <input class="input-field"
               type="digit"
               value="{{safetyCheckForm.anchorageDepth}}"
               bindinput="handleWaterDepthInput"/>
      </view>
      <view class="header-row">
        <text>到港吃水(读数/计算):</text>
        <input class="input-field"
               type="digit"
               value="{{safetyCheckForm.arrivalDraft}}"
               bindinput="handleArrivalDraftInput"/>
        <text>水上高度:</text>
        <input class="input-field"
               type="digit"
               value="{{safetyCheckForm.arrivalHeight}}"
               bindinput="handleArrivalHeightInput"/>
      </view>
      <view class="header-row">
        <text>计算出港吃水:</text>
        <input class="input-field"
               type="digit"
               value="{{safetyCheckForm.departureDraft}}"
               bindinput="handleDepartureDraftInput"/>
        <text>水上高度:</text>
        <input class="input-field"
               type="digit"
               value="{{safetyCheckForm.departureHeight}}"
               bindinput="handleDepartureHeightInput"/>
      </view>
    </view>

    <view class="safety-check-table">
      <view class="table-header">
        <view class="col-serial">序号</view>
        <view class="col-item">项目</view>
        <view class="col-ship">船舶</view>
        <!-- <view class="col-port">港口</view> -->
        <view class="col-remarks">备注</view>
      </view>
      
      <view class="table-row" wx:for="{{safetyCheckItems}}" wx:key="serial">
        <view class="col-serial">{{item.serial}}</view>
        <view class="col-item">
          {{item.item}}
          <!-- 额外字段输入框 -->
          <block wx:if="{{item.extraFields}}">
            <view class="extra-fields">
              <!-- 通信系统字段 -->
              <block wx:if="{{item.serial === '4'}}">
                <view class="sub-item">
                  <view class="sub-item-label">通信方式：</view>
                  <input class="input-field" 
                         value="{{item.extraFields.communicationMethod}}" 
                         data-index="{{index}}" 
                         data-field="communicationMethod"
                         bindinput="handleExtraFieldInput"/>
                </view>
                <view class="sub-item">
                  <view class="sub-item-label">语言：</view>
                  <input class="input-field" 
                         value="{{item.extraFields.language}}" 
                         data-index="{{index}}" 
                         data-field="language"
                         bindinput="handleExtraFieldInput"/>
                </view>
                <view class="sub-item">
                  <view class="sub-item-label">无线电话频道/电话号码：</view>
                  <input class="input-field" 
                         value="{{item.extraFields.radioChannel}}" 
                         data-index="{{index}}" 
                         data-field="radioChannel"
                         bindinput="handleExtraFieldInput"/>
                </view>
              </block>
              
              <!-- 通信联络人员字段 -->
              <block wx:if="{{item.serial === '5'}}">
                <view class="sub-item">
                  <view class="sub-item-label">船舶联络人员：</view>
                  <input class="input-field" 
                         value="{{item.extraFields.shipContact}}" 
                         data-index="{{index}}" 
                         data-field="shipContact"
                         bindinput="handleExtraFieldInput"/>
                </view>
                <view class="sub-item">
                  <view class="sub-item-label">浮吊联络人员：</view>
                  <input class="input-field" 
                         value="{{item.extraFields.floatingContact}}" 
                         data-index="{{index}}" 
                         data-field="floatingContact"
                         bindinput="handleExtraFieldInput"/>
                </view>
                <view class="sub-item">
                  <view class="sub-item-label">位置：</view>
                  <input class="input-field" 
                         value="{{item.extraFields.position}}" 
                         data-index="{{index}}" 
                         data-field="position"
                         bindinput="handleExtraFieldInput"/>
                </view>
              </block>

              <!-- 装卸能力字段 -->
              <block wx:if="{{item.serial === '13'}}">
                <view class="sub-item">
                  <view class="sub-item-label">浮吊1：</view>
                  <input class="input-field" 
                         value="{{item.extraFields.floatingCrane1}}" 
                         data-index="{{index}}" 
                         data-field="floatingCrane1"
                         bindinput="handleExtraFieldInput"/>
                </view>
                <view class="sub-item">
                  <view class="sub-item-label">浮吊2：</view>
                  <input class="input-field" 
                         value="{{item.extraFields.floatingCrane2}}" 
                         data-index="{{index}}" 
                         data-field="floatingCrane2"
                         bindinput="handleExtraFieldInput"/>
                </view>
              </block>

              <!-- 计划副本持有人字段 -->
              <block wx:if="{{item.serial === '14'}}">
                <view class="sub-item">
                  <view class="sub-item-label">计划副本持有人：</view>
                  <input class="input-field" 
                         value="{{item.extraFields.planHolder}}" 
                         data-index="{{index}}" 
                         data-field="planHolder"
                         bindinput="handleExtraFieldInput"/>
                </view>
              </block>
            </view>
          </block>
        </view>
        <view class="col-ship">
          <view class="custom-checkbox {{item.shipResponse}}" bindtap="toggleShipCheck" data-index="{{index}}"></view>
        </view>
        <!-- <view class="col-port">
          <checkbox checked="{{item.portCheck}}" 
                    bindtap="handlePortCheck" 
                    data-index="{{index}}"/>
        </view> -->
        <view class="col-remarks">
          <input value="{{item.remarks}}" 
                 bindinput="handleRemarksInput" 
                 data-index="{{index}}"
                 placeholder=""/>
        </view>
      </view>
    </view>

    <view class="safety-check-footer">
      <view class="signature-section">
        <text>同意以上各项内容：</text>
        <view class="signature-row">
          <text>时间：</text>
          <input class="input-field" value="{{safetyCheckForm.checkDate}}" disabled/>
        </view>
        <view class="signature-row">
          <text>姓名及职务：</text>
          <input class="input-field" value="{{safetyCheckForm.operatorNamePosition}}" bindinput="handleOperatorNamePositionInput"/>
        </view>
        <view class="signature-row">
          <text>船名：</text>
          <input class="input-field" value="{{currentConsignObj.bargeName}}" disabled/>
        </view>
      </view>
    </view>
  </view>
</mp-dialog>

<!-- 水路运单文件列表弹窗 -->
<mp-dialog
  use-slot
  title="水路运单文件列表"
  show="{{ showWaterWayFileDialog }}"
  buttons="{{waterWayFileButtons}}"
  bind:buttontap="closeWaterWayFileDialog"
>
  <view class="waterway-file-dialog-content">
    <view wx:if="{{waterWayFiles.length > 0}}" class="waterway-file-list">
      <view 
        class="waterway-file-item"
        wx:for="{{waterWayFiles}}" 
        wx:key="index"
        bindtap="openWaterWayFile"
        data-url="{{item.url}}"
      >
        <mp-icon icon="file" color="#00426B" size="{{20}}"></mp-icon>
        <text class="file-name">{{item.dataName || '水路运单文件'}}</text>
      </view>
    </view>
    <view wx:else class="no-files">
      暂无水路运单文件
    </view>
  </view>
</mp-dialog>

<!-- 安全装货确认书弹窗 -->
<mp-dialog title="安全装货确认书" show="{{showLoadingConfirmDialog}}" bindbuttontap="handleLoadingConfirm" buttons="{{loadingConfirmButtons}}">
  <view class="loading-confirm-content">
    <view class="confirm-text">
      本轮（船名）{{currentConsignObj.bargeName}}在
      <input class="inline-input" placeholder="请输入码头" bindinput="handlePortInput" value="{{loadingConfirmForm.port}}"/>
      码头（锚地、浮筒）装{{currentConsignObj.rationWeight}}吨，并于
      <picker 
        mode="time" 
        value="{{loadingConfirmForm.leaveTime}}" 
        bindchange="handleLeaveTimeChange"
        class="time-picker"
      >
        <view class="picker-text">
          {{loadingConfirmForm.leaveTime || '请选择时间'}}
        </view>
      </picker>
      时安全离港。本轮承诺严格按照负载能力配装、不超载, 港方已按船方适航要求装货。外贸直提驳船在海关放行前严禁离开锚地监管区。
    </view>
    <view class="signature-row">
      <text>船方确认：</text>
      <input class="signature-input" placeholder="请输入确认人" bindinput="handleShipConfirmInput" value="{{loadingConfirmForm.shipConfirm}}"/>
    </view>
    <view class="date-row">
      <text class="date-label">日期：</text>
      <picker mode="date" bindchange="handleConfirmDateChange">
        <view class="picker">
          {{loadingConfirmForm.confirmDate || '请选择日期'}}
        </view>
      </picker>
    </view>
  </view>
</mp-dialog>

</view>

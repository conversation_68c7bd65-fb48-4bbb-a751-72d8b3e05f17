package com.ruoyi.common.utils.message.tmpl.impl;



import com.ruoyi.common.utils.message.request.mpTem.DataValue;
import com.ruoyi.common.utils.message.request.mpTem.Miniprogram;
import com.ruoyi.common.utils.message.tmpl.TmplBase;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试模版
 * <AUTHOR>
 *
 */

public class TmplBaseImpl extends TmplBase {
	
	public TmplBaseImpl(DataValue first, DataValue keyword1, DataValue keyword2, DataValue keyword3, DataValue remark, Miniprogram miniprogram, String templateId){
		Map<String, DataValue> map = new HashMap<String, DataValue>();
		map.put("first", first);
		map.put("keyword1", keyword1);
		map.put("keyword2", keyword2);
		map.put("keyword3", keyword3);
		map.put("remark", remark);
		this.data = map;
		this.miniprogram = miniprogram;
		//模版id
		this.tmplID = templateId;
	}
	public TmplBaseImpl(DataValue first, DataValue keyword1, DataValue keyword2, DataValue keyword3, DataValue keyword4, DataValue remark, Miniprogram miniprogram, String templateId){
		Map<String, DataValue> map = new HashMap<String, DataValue>();
		map.put("first", first);
		map.put("keyword1", keyword1);
		map.put("keyword2", keyword2);
		map.put("keyword3", keyword3);
		map.put("keyword4", keyword4);
		map.put("remark", remark);
		this.data = map;
		this.miniprogram = miniprogram;
		//模版id
		this.tmplID = templateId;
	}
	public TmplBaseImpl(DataValue first, DataValue keyword1, DataValue keyword2, DataValue keyword3, DataValue keyword4, Miniprogram miniprogram, String templateId, Integer i){
		Map<String, DataValue> map = new HashMap<String, DataValue>();
		map.put("channel", first);
		map.put("orderNumber", keyword1);
		map.put("state", keyword2);
		map.put("doSomething", keyword3);
		map.put("remark", keyword4);
		this.data = map;
		this.miniprogram = miniprogram;
		//模版id
		this.tmplID = templateId;
	}
}

package zqit.app;


import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fdd.api.client.dto.AccountIdDTO;
import com.fdd.api.client.dto.ContractDownloadDTO;
import com.fdd.api.client.release.base.ClientFactory;
import com.fdd.api.client.res.RestResult;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.databarge.domain.vo.Pb6BargeCompanyVO;
import com.ruoyi.databarge.service.Pb6BargeCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/8 9:40
 */
@Slf4j
public class Test {

    public static final String FDD_URL = "https://esign-api.gzport.com/";

    public static final String FDD_APP_ID = "100000";

    public static final String FDD_APP_SECRET = "Dj8jeJ02HF5jFd8F0GG9fE8B";

    private static final ClientFactory clientFactory = ClientFactory.instance(FDD_URL,FDD_APP_ID,FDD_APP_SECRET);

    public static void main(String[] args) {
        List<FddCommonService.CompanySealVO> list = sealList("1abf5102d180470bb83d8e6acf4c273e");
        list.forEach(item -> log.info(String.valueOf(item.getName())));
    }

    /**
     * 查询所有印章
     * @param customerId FDD账户customerId
     */
    public static List<FddCommonService.CompanySealVO> sealList(String customerId){
        AccountIdDTO accountIdDTO=new AccountIdDTO();
        accountIdDTO.setAccountId(customerId);// 客户编号

        try {
            RestResult restResult=clientFactory.sealClient().sealList(accountIdDTO);// RestResult{code='1', msg='操作成功', data=[{"accountId":"524945f093fd40919864db339fb3c99e","path":"20200921161243_6df598baaae347bd9","isDefault":false,"createdDate":"2020-09-21 16:12:44","name":"gzport公章100","id":"0338c070b1284d6da10350d19f3615a1"},{"accountId":"524945f093fd40919864db339fb3c99e","path":"20200918145329_bb8c58b60add497b9","isDefault":true,"createdDate":"2020-09-18 14:53:30","name":"广州港测试","id":"3e40c5b34de7486e9372966a827dbd60"}]}
            if("1".equals(restResult.getCode())){
                JSONArray jsonArray=(JSONArray)restResult.getData();
                Iterator iterator=jsonArray.iterator();
                List<FddCommonService.CompanySealVO> companySealVOList=new ArrayList<>(jsonArray.size());
                while (iterator.hasNext()){
                    JSONObject jsonObject=(JSONObject)iterator.next();
                    companySealVOList.add(new FddCommonService.CompanySealVO(
                            jsonObject.getString("id"),
                            jsonObject.getString("name"),
                            jsonObject.getString("createdDate"),
                            jsonObject.getBoolean("isDefault"),
                            jsonObject.getString("path"),
                            jsonObject.getString("accountId")
                    ));
                }
                return companySealVOList;
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 下载合同
     * @param docNo 合同编号
     * @return ByteArrayOutputStream
     */
    public static ByteArrayOutputStream downloadContract(String docNo) {
        ContractDownloadDTO contractDownloadDTO = new ContractDownloadDTO();
        contractDownloadDTO.setDocNo(docNo);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            RestResult restResult = clientFactory.contractClient().downloadContract(contractDownloadDTO);
            if("1".equals(restResult.getCode())){
                ByteArrayInputStream bis = (ByteArrayInputStream) restResult.getData();
                byte[] b = new byte[1024];
                int n;
                while ((n = bis.read(b)) != -1)
                {
                    bos.write(b, 0, n);
                }
                IoUtil.close(bis);
                IoUtil.close(bos);
            }else{
                throw new RuntimeException(restResult.getMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bos;
    }
}

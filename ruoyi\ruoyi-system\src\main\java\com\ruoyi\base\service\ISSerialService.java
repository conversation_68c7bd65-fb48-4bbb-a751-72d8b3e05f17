package com.ruoyi.base.service;

import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.base.domain.SSerial;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/2/2 11:16
 */
public interface ISSerialService extends IService<SSerial> {

    List<SSerial> selectSSerialList(SSerial sSerial);

    R insertSSerial(SSerial sSerial) throws Exception;

    AjaxResult updateSSerial(SSerial sSerial);

    int getSerialByCode(String code);



    String getSerialNumberByCode(String code);

    String getSerialNumberByCodeDeptYearType(String code, Long year, String spType) throws Exception;

    String getSerialByCodeYearDate(String code, Date date) throws Exception;

    void updateSerialCron(String type);

}

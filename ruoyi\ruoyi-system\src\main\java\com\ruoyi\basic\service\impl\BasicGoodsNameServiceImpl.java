package com.ruoyi.basic.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.basic.domain.BasicGoodsName;
import com.ruoyi.basic.mapper.BasicGoodsNameMapper;
import com.ruoyi.basic.service.BasicGoodsNameService;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【BASIC_GOODS_NAME】的数据库操作Service实现
* @createDate 2023-11-16 11:43:52
*/
@Service
public class BasicGoodsNameServiceImpl extends ServiceImpl<BasicGoodsNameMapper, BasicGoodsName>
    implements BasicGoodsNameService {

    @Override
    public List<BasicGoodsName> selectBasicGoodsNameList(BasicGoodsName basicGoodsName) {
        return baseMapper.selectBasicGoodsNameList(basicGoodsName);
    }

    @Override
    public AjaxResult addBasicGoodsName(BasicGoodsName basicGoodsName) {
        BasicGoodsName goodsName = baseMapper.selectBasicGoodsNameByCategoryIdAndName(basicGoodsName);
        if(goodsName != null){
            return AjaxResult.error("货名已存在");
        }
        if(baseMapper.insert(basicGoodsName) > 0){
            return AjaxResult.success("新增货名成功");
        }
        return AjaxResult.error("新增货名失败");
    }

    @Override
    public AjaxResult updateBasicGoodsName(BasicGoodsName basicGoodsName) {
        if(baseMapper.updateById(basicGoodsName) > 0){
            return AjaxResult.success("修改货名成功");
        }
        return AjaxResult.error("修改货名失败");
    }

    @Override
    public AjaxResult deleteBasicGoodsName(String goodsNameId) {
        if(baseMapper.deleteById(goodsNameId) > 0){
            return AjaxResult.success("删除货名成功");
        }
        return AjaxResult.error("删除货名失败");
    }

    @Override
    public BasicGoodsName selectBasicGoodsNameByName(String goodsName) {
        return baseMapper.selectBasicGoodsNameByName(goodsName);
    }

    @Override
    public List<BasicGoodsName> selectBasicGoodsNameByGoodsCategory(String goodsCategory) {
        return baseMapper.selectBasicGoodsNameByGoodsCategory(goodsCategory);
    }

    @Override
    public AjaxResult deleteBasicGoodsNameByCategoryId(String goodsCategoryId) {
        return baseMapper.deleteBasicGoodsNameByCategoryId(goodsCategoryId);
    }
}





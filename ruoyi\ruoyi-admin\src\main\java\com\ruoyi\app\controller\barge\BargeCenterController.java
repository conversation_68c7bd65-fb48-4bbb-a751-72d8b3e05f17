package com.ruoyi.app.controller.barge;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.barge.domain.bo.BargeUserBO;
import com.ruoyi.barge.mapper.SysUserBargeMapper;
import com.ruoyi.barge.service.BargeCenterService;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.BargeInfoAudit;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.enums.UserType;
import com.ruoyi.common.service.BargeInfoAuditService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.controller.common.CommonController;
import com.ruoyi.web.utils.BargeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/29 17:20
 */
@Api("驳船主-我的-api")
@Slf4j
@RestController
@RequestMapping("/barge/center")
public class BargeCenterController {

    @Autowired
    private BargeCenterService bargeCenterService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private SysUserBargeMapper sysUserBargeMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private CommonController commonController;

    @Autowired
    private BargeInfoAuditService bargeInfoAuditService;

    /**
     * 绑定用户类型
     * @param user
     * @return
     */
    @ApiOperation("用户类型绑定")
    @PostMapping("/updateUserType")
    public AjaxResult updateUserType(@RequestBody SysUser user){
        user.setUpdateBy(SecurityUtils.getUsername());
        // 删除原有用户与驳船关系
        SysUserBarge sub = new SysUserBarge();
        sub.setUserId(user.getUserId());
        QueryWrapper<SysUserBarge> wrapper = new QueryWrapper<>(sub);
        sysUserBargeMapper.delete(wrapper);
        // 删除角色信息
        sysUserRoleMapper.deleteUserRoleByUserId(user.getUserId());

        // 添加管理员角色
        if (UserType.BARGEADMIN.getCode().equals(user.getUserType())) {
            /*List<SysUserRole> list = new ArrayList<>();
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(user.getUserId());
            sysUserRoleMapper.deleteUserRoleByUserId(user.getUserId());
            userRole.setRoleId(3L);
            list.add(userRole);
            sysUserRoleMapper.batchUserRole(list);*/

            List<SysRole> sysRoles = sysRoleMapper.selectRoleAll();
            List<SysUserRole> list = new ArrayList<>();
            sysRoles.forEach(role->{
                if ("bargeAdmin".equals(role.getRoleKey())) {
                    SysUserRole userRole = new SysUserRole();
                    userRole.setUserId(user.getUserId());
                    userRole.setRoleId(role.getRoleId());
                    list.add(userRole);
                }
            });
            if (list.size() <= 0) {
                log.error("角色为空");
                return AjaxResult.error("请联系管理员添加相应角色");
            }
            sysUserRoleMapper.batchUserRole(list);
        }
        return userService.updateUserProfile(user)>0?AjaxResult.success():AjaxResult.error();
    }


    /**
     * 个人备案
     * @param
     * @return
     */
    @ApiOperation("个人备案")
    @PostMapping("/personRecord")
    public AjaxResult personRecord(@RequestBody @Valid BargeUserBO bargeUserBO, BindingResult result) {

        if (bargeUserBO.getUserId() == null) {
            log.error("用户id不能为空");
            return AjaxResult.error("用户id不能为空");
        }

        if (StringUtils.isBlank(bargeUserBO.getIdentityId()) || !IdcardUtil.isValidCard(bargeUserBO.getIdentityId())) {
            log.error("身份证号码不合法");
            return AjaxResult.error("身份证号码不合法");
        }

        // 判断BindingResult是否保存错误的验证信息，如果有，则直接return
        if (result.hasErrors()) {
            return AjaxResult.getErrors(result);
        }

        return bargeCenterService.personRecord(bargeUserBO);
    }

    /**
     * 备案详情（个人、驳船）
     * @return
     */
    @PostMapping("/recordDetail")
    public AjaxResult recordDetail(@RequestBody BargeInfoBO bargeInfoBO) {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        bargeInfoBO.setUserId(userId);
        return bargeCenterService.recordDetail(bargeInfoBO);
    }

    /**
     * 检查备案是否成功
     * @return
     */
    @PostMapping("/checkRecordIsSuccess")
    public AjaxResult checkRecordIsSuccess() {
        return AjaxResult.success(bargeCenterService.checkRecordIsSuccess());
    }

    /**
     * 切换身份--驳船业务人员
     * @return
     */
    @PostMapping("/switchIdentity")
    public AjaxResult switchIdentity() {
        return bargeCenterService.switchIdentity();
    }

    /**
     * 获取驳船列表
     * @return
     */
    @ApiOperation("获取驳船列表")
    @PostMapping("/getBargeList")
    public AjaxResult getBargeList(@RequestBody BargeInfoBO bargeInfoBO) {
        return bargeCenterService.getBargeList(bargeInfoBO);
    }

    /**
     * 新增驳船
     */
    @ApiOperation("新增驳船")
    @PostMapping("/addBarge")
    public AjaxResult addBarge(@RequestBody @Valid BargeInfoBO bargeInfoBO, BindingResult result) {

        // 判断BindingResult是否保存错误的验证信息，如果有，则直接return
        if (result.hasErrors()) {
            return AjaxResult.getErrors(result);
        }
        char[] arr = bargeInfoBO.getBargeName().toCharArray();
        String string = new String();
        for (int i = 0 ; i < arr.length ; i++){
            if(arr[i] == '粵'){
                arr[i] = '粤';
            }
            string += arr[i];
        }
        bargeInfoBO.setBargeName(string);
        return bargeCenterService.addBarge(bargeInfoBO);
    }

    /**
     * 驳船备案
     * @return
     */
    @ApiOperation("驳船备案")
    @PostMapping("/bargeRecord")
    public AjaxResult bargeRecord(@RequestBody BargeInfoBO bargeInfoBO) {

        String seal = bargeInfoBO.getSealImg();
        if (StringUtils.isNotBlank(seal)) {
            String path = commonController.saveSeal(seal,SecurityUtils.getLoginUser().getUser().getUserId());
            bargeInfoBO.setSealPath(path);
        }

        return bargeCenterService.bargeRecord(bargeInfoBO);
    }

    @ApiOperation("驳船修改时新增")
    @PostMapping("/bargeRecordAdd")
    public AjaxResult bargeRecordAdd(@RequestBody BargeInfoBO bargeInfoBO) {

        bargeCenterService.checkBargeInfoBo(bargeInfoBO);

        String seal = bargeInfoBO.getSealImg();
        if (StringUtils.isNotBlank(seal)) {
            String path = commonController.saveSeal(seal,SecurityUtils.getLoginUser().getUser().getUserId());
            bargeInfoBO.setSealPath(path);
        }
        BargeInfoAudit bargeInfoAudit = new BargeInfoAudit();
        BeanUtils.copyProperties(bargeInfoBO,bargeInfoAudit);
        return bargeInfoAuditService.addBarge(bargeInfoAudit);
    }

    /**
     * 获取船员列表
     * @return
     */
    @ApiOperation("获取船员列表")
    @PostMapping("/getCrewList")
    public AjaxResult getCrewList(@RequestBody BargeUserBO bargeUserBO) {
        return bargeCenterService.getCrewList(bargeUserBO);
    }

    /**
     * 添加船员
     * @return
     */
    @ApiOperation("添加船员")
    @PostMapping("/addCrew")
    public AjaxResult addCrew(@RequestBody @Valid BargeUserBO bargeUserBO, BindingResult result) {

        // 判断BindingResult是否保存错误的验证信息，如果有，则直接return
        if (result.hasErrors()) {
            return AjaxResult.getErrors(result);
        }

        return bargeCenterService.addCrew(bargeUserBO);
    }

    /**
     * 删除船员
     * @param bargeUserBO
     * @return
     */
    @ApiOperation("删除船员")
    @PostMapping("/delCrew")
    public AjaxResult delCrew(@RequestBody BargeUserBO bargeUserBO) {

        List<SysUserBarge> list = bargeUserBO.getUserBargeList();
        if (list == null || list.size() <= 0) {
            log.error("选择船员为空");
            return AjaxResult.error("请选择船员");
        }

        return bargeCenterService.delCrew(bargeUserBO);
    }

    /**
     * 获取水路运单
     * @param waterwayCargo waterwayCargoId-水路运单编号
     * @return
     */
    @PostMapping("/getLoading")
    public AjaxResult getLoading(@RequestBody WaterwayCargo waterwayCargo) {
        if (StringUtils.isBlank(waterwayCargo.getWaterwayCargoId())) {
            log.info("水路货物运单编号为空");
            return AjaxResult.error("水路货物运单编号不能为空");
        }

        return bargeCenterService.getLoading(waterwayCargo.getWaterwayCargoId());
    }

    /**
     * 确认实装数
     * @param waterwayCargo 确认是否实装： Y 确认实装， N是没有确认实装
     * @return
     */
    @PostMapping("/confirmLoadingOver")
    public AjaxResult confirmLoadingOver(@RequestBody WaterwayCargo waterwayCargo) {
        /*if (StringUtils.isNotBlank(waterwayCargo.getWaterwayCargoId())) {
            log.info("水路货物运单编号为空");
            return AjaxResult.error("水路货物运单编号不能为空");
        }*/
        if (null == waterwayCargo.getWaterwayCargoId()) {
            log.info("水路货物运单号为空");
            return AjaxResult.error("水路货物运单号不能为空");
        }
        if (StringUtils.isBlank(waterwayCargo.getConfirmloadingover())) {
            log.info("确认状态为空");
            return AjaxResult.error("确认状态不能为空");
        }

        AjaxResult result = bargeCenterService.confirmLoadingOver(waterwayCargo);
        int code = Convert.toInt(result.get(AjaxResult.CODE_TAG));
        log.info("确认实装数[code：{}]", code);

        if (HttpStatus.SUCCESS == code) {
            // 异步开票
            BargeUtils.invoicing(waterwayCargo.getWaterwayCargoId());
            // 异步盖章
            BargeUtils.defrayStamp(waterwayCargo.getWaterwayCargoId(), BargeUtils.ALL_FILE);
        }

        return result;
    }
}

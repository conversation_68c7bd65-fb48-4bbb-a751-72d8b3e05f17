<!--pages/checkfapiao/checkfapiao.wxml-->
<wxs module="fapiaoState">
  module.exports = {
    changeStatus: function(state) {
      if(state == '0') {
        return '开票中';
      }
      if(state == '1') {
        return '已开票'
      }
      if(state == '2') {
        return '已冲正'
      }
      if(state == '3') {
        return '待开票'
      }
    }
  }
</wxs>
<view class="check-page"  hover-class="none" hover-stop-propagation="false">
  <view  hover-class="none" wx:if="{{chargeBalanceType == 1}}"  hover-stop-propagation="false">
  <view hover-class="none" wx:if="{{fapiaoMsg.invoicetype == 0 }}" hover-stop-propagation="false">
      <view class="consign-title"  hover-class="none" hover-stop-propagation="false">
        <text>附件列表</text>
        <button class="operate-btn" bindtap="handleOpenEmailDialog">发送到邮箱</button>
      </view>
      <view class="" wx:if="{{fileList && fileList.length >= 1}}" hover-class="none" hover-stop-propagation="false">
        <view class="consign-message" bindtap="handleOpenPdf" data-item="{{item}}" wx:for="{{fileList}}" wx:key="index" hover-class="none" hover-stop-propagation="false">
          <view class="message-title fapiao-title" hover-class="none" hover-stop-propagation="false">{{waterWayCargoId}}</view>
          <view class="message-value fapiao-operate" hover-class="none" hover-stop-propagation="false">
            <button class="operate-btn">预览发票</button>
          </view>
        </view>
      </view>
      <view wx:else class="consign-message" hover-class="none" hover-stop-propagation="false">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">该发票还未开票，暂无附件</view>
      </view>
    </view>
    <view class="consign-title" hover-class="none" hover-stop-propagation="false">发票信息</view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">发票类型：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{fapiaoMsg.invoicetype == 0 ?'普票':'专票'}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">结算方式：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{chargeBalanceType == 0 ?'月结':'现结'}}</view>
    </view>
    <view class="consign-message" wx:if="{{fapiaoMsg.invoicetype == 0 }}" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">发票状态：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{fapiaoState.changeStatus(fapiaoMsg.state)}}</view>
    </view>
    <view class="" wx:if="{{fapiaoMsg.invoicetype == 0}}" hover-class="none" hover-stop-propagation="false">
      <view class="consign-message" hover-class="none" hover-stop-propagation="false">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">抬头类型：</view>
        <view class="message-value" hover-class="none" hover-stop-propagation="false">{{fapiaoMsg.type == 0 ? '企业':'个人'}}</view>
      </view>
      <view class="consign-message" hover-class="none" hover-stop-propagation="false">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">抬头：</view>
        <view class="message-value" hover-class="none" hover-stop-propagation="false">{{fapiaoMsg.buyername}}</view>
      </view>
      <view class="consign-message" hover-class="none" hover-stop-propagation="false" wx:if="{{fapiaoMsg.type == 0}}">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">纳税人识别号：</view>
        <view class="message-value" hover-class="none" hover-stop-propagation="false">{{fapiaoMsg.taxnum}}</view>
      </view>
      <view class="consign-message" hover-class="none" hover-stop-propagation="false">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">手机：</view>
        <view class="message-value" hover-class="none" hover-stop-propagation="false">{{fapiaoMsg.phone}}</view>
      </view>
      <view class="consign-message" hover-class="none" hover-stop-propagation="false" wx:if="{{fapiaoMsg.type == 0}}">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">地址：</view>
        <view class="message-value" hover-class="none" hover-stop-propagation="false">{{fapiaoMsg.address}}</view>
      </view>
      <view class="consign-message" hover-class="none" hover-stop-propagation="false" wx:if="{{fapiaoMsg.type == 0}}">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">银行卡号：</view>
        <view class="message-value" hover-class="none" hover-stop-propagation="false">{{fapiaoMsg.account}}</view>
      </view>
      <view class="consign-message" hover-class="none" hover-stop-propagation="false">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">邮箱：</view>
        <view class="message-value" hover-class="none" hover-stop-propagation="false">{{fapiaoMsg.email || ''}}</view>
      </view>
    </view>
    <view class="consign-other-msg" wx:if="{{fapiaoMsg.invoicetype == 1}}" hover-class="none" hover-stop-propagation="false">
      提示：专票请在线下开发票。
    </view>
  </view>
  <view wx:else hover-class="none" hover-stop-propagation="false">
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">结算方式：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{chargeBalanceType == 0 ?'月结':'现结'}}</view>
    </view>
    <view class="consign-other-msg" hover-class="none" hover-stop-propagation="false">
      提示：月结请在线下开发票。
    </view>
  </view>
</view>
<mp-dialog title="{{userEmail ? '您的邮箱如下':'账号尚未绑定邮箱，请填写邮箱'}}" show="{{batchDialog}}" mask="true" mask-closable="false" bindbuttontap="tapBatchDialogButton" buttons="{{batchButtons}}">
  <input type="text" class="add-message-emailipt" placeholder="请输入邮箱" value="{{userEmail}}"  bindinput="emailInput"/>
</mp-dialog>

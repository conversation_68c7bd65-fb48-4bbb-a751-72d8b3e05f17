package com.ruoyi.common.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.bo.UploadBO;
import com.ruoyi.common.enums.DataType;
import com.ruoyi.common.enums.UploadDataType;
import com.ruoyi.common.enums.UserType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.ftp.BargeCargoFtpUtils;
import com.ruoyi.common.utils.ftp.BargeFtpUtils;
import com.ruoyi.common.utils.ftp.BargeSftpUtil;
import com.ruoyi.common.utils.ftp.FtpUtils;
import com.ruoyi.common.domain.UploadAddress;
import com.ruoyi.common.mapper.UploadAddressMapper;
import com.ruoyi.common.service.FtpService;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.databarge.domain.Pb6Waterwaycargo;
import com.ruoyi.databarge.mapper.Pb6WaterwaycargoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/29 16:47
 */
@Slf4j
@Service
public class FtpServiceImpl implements FtpService {


    @Autowired
    private UploadAddressMapper uploadAddressMapper;
    @Autowired
    private FtpUtils ftpUtils;
    @Autowired
    private BargeFtpUtils bargeFtpUtils;
    @Autowired
    private BargeCargoFtpUtils cargoFtpUtils;
    @Autowired
    private Pb6WaterwaycargoMapper pb6WaterwaycargoMapper;
    @Value("${mkdirPath}")
    private String mkdirPath;

    @Autowired
    private BargeSftpUtil bargeSftpUtil;

    /**
     * 上传
     * @param file
     * @param uploadBO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult ftpUpload(MultipartFile file,@Valid UploadBO uploadBO) {

        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        String loginUserType = loginUser.getUserType();
        Long userId = loginUser.getUserId();

        log.info(loginUserType);

        String url;

        // 判断用户类型
        if (UserType.BARGEADMIN.getCode().equals(loginUserType)
                || UserType.BARGEUSER.getCode().equals(loginUserType)) {
            // 驳船主端保存在本地服务器

            /*//目标路径
            File mkdirFile = new File(mkdirPath + userId + "/");
            //如果文件目录不存在，就执行创建
            if(!mkdirFile.isDirectory()){
                mkdirFile.mkdirs();
            }
            //目标文件名称
            String targetName = file.getOriginalFilename();
            url = mkdirPath + userId + "/" + targetName;
            log.error(url);
            this.bargeUpload(url, file);*/
            try {
                url = bargeFtpUtils.uploadFile(file, userId);
            } catch (Exception e) {
                log.error("上传失败");
                throw new CustomException("上传失败", e, 500);
            }
        } else if (UploadDataType.BARGE_RECORD_DATA.getType().equals(uploadBO.getLinkType()) && (UserType.CARRIERADMIN.getCode().equals(loginUserType) || UserType.CARRIEUSER.getCode().equals(loginUserType))) {
            try {
                url = bargeFtpUtils.uploadFile(file, userId);
            } catch (Exception e) {
                log.error("上传失败");
                throw new CustomException("上传失败", e, 500);
            }
        } else {
            // 货主端、托运联系人、船公司端上传ftp服务器
            try {
                url = ftpUtils.uploadFile(file, userId);
            } catch (Exception e) {
                log.error("上传失败");
                throw new CustomException("上传失败", e, 500);
            }
        }

        UploadAddress upload = new UploadAddress();
        // 链接表id
        upload.setLinkId(uploadBO.getLinkId());
        upload.setLinkType(uploadBO.getLinkType());
        upload.setLinkTable(UploadDataType.typeToTable(uploadBO.getLinkType()));
        upload.setUrl(url);
        upload.setUploadTime(new Date());
        upload.setUploadUserId(userId);
        upload.setLinkCompanyId(uploadBO.getLinkCompanyId());
        upload.setDataName(DataType.codeToCodeName(uploadBO.getDataType()));
        upload.setStatus(1);
        upload.setDataType(uploadBO.getDataType());

        int count = uploadAddressMapper.insert(upload);

        if (count <= 0) {
            log.info("上传失败");
            return AjaxResult.error("上传失败");
        }

        log.info("上传成功");

        return AjaxResult.success("上传成功",upload);
    }

    /**
     * 下载
     * @param url
     * @param response
     * @param request
     */
    @Override
    public void download(String url, HttpServletResponse response, HttpServletRequest request) {

        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        String loginUserType = loginUser.getUserType();

        log.info(loginUserType);

        try {
            String downloadName = StringUtils.substringAfterLast(url, "/");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, downloadName));

            if (UserType.BARGEADMIN.getCode().equals(loginUserType)
                    || UserType.BARGEUSER.getCode().equals(loginUserType)) {
                // 驳船主
                // 参数1-下载目标文件的路径，参数2-输出流
                FileUtils.writeBytes(url, response.getOutputStream());
            } else {
                // 货主端、托运联系人、船公司端上传ftp服务器
                // 参数1-下载目标文件的路径，参数2-输出流
                ftpUtils.downloadFile(url, response.getOutputStream());
            }
        } catch (Exception e) {
            log.error("下载失败");
            throw new CustomException("下载失败", e, 500);
        }
    }

    /**
     * web端上传
     * @param file
     * @param userName
     * @return
     */
    @Override
    public AjaxResult webUpload(MultipartFile file, String userName) {

        //目标路径
        File mkdirFile = new File(mkdirPath);
        //如果文件目录不存在，就执行创建
        if(!mkdirFile.isDirectory()){
            mkdirFile.mkdirs();
        }
        //目标文件名称
        String targetName = file.getOriginalFilename();
        String url = mkdirPath + targetName;
        this.bargeUpload(url, file);
        return null;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public String selaUpload(String sealImg) {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        String base64Data =  sealImg.split(",")[1];
        byte[] bytes = Base64.decode(base64Data);
        //目标路径
        File mkdirFile = new File(mkdirPath + userId + "/seal/");
        //如果文件目录不存在，就执行创建
        if(!mkdirFile.isDirectory()){
            mkdirFile.mkdirs();
        }
        //目标文件名称
        String targetName = "seal_"+ UUID.randomUUID().toString() +".png";
        String url = mkdirPath + userId + "/seal/" + targetName;
        log.info(url);
        File targetFile = new File(url);
        try (FileOutputStream fos = new FileOutputStream(targetFile)) {
            //写入目标文件
            fos.write(bytes, 0, bytes.length);
            fos.flush();
        } catch (IOException e) {
            log.error("上传失败，IO异常");
            throw new CustomException("上传失败，IO异常", e, 500);
        }

        return url;
    }

    /**
     * 保存告知书等文件
     * @param fileList
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult saveNoticeFile(List<HashMap<String, Object>> fileList, String waterwayCargoId, Long userId, Long companyId, String contractNo) {
        TimeInterval timer = DateUtil.timer();
        log.info("开始保存告知书文件 - waterwayCargoId:{} - 当前时间:{}", waterwayCargoId, DateUtil.now());
        fileList.forEach(item -> {
            String fileName = (String) item.get("fileName");
            ByteArrayOutputStream byteArrayOutputStream = (ByteArrayOutputStream) item.get("outStream");
            InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            // 保存文件到10.200.17.54里的/root/gzgtest/cargoFile
            String path = null;
            try {
                //path = cargoFtpUtils.uploadFile(inputStream,fileName, Md5Utils.MD5EncodeUtf8(waterwayCargoId));
                path = bargeSftpUtil.uploadFile(inputStream, fileName, Md5Utils.MD5EncodeUtf8(waterwayCargoId));
            } catch (Exception e) {
                log.error("- waterwayCargoId:{} - 花费时间:{}ms", waterwayCargoId, timer.interval());
                log.error("上传文件失败", e);
            } finally {
                IoUtil.close(byteArrayOutputStream);
                IoUtil.close(inputStream);
            }
            Object pb6Bargework = item.get("pb6BargeworkId");
            Long pb6BargeworkId = null;
            if (pb6Bargework != null) {
                pb6BargeworkId = (Long) pb6Bargework;
            } else {
                log.error("pb6BargeworkId为空");
            }

            QueryWrapper<Pb6Waterwaycargo> wrapper = new QueryWrapper<>();
            wrapper.eq("id", waterwayCargoId);
            Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoMapper.selectOne(wrapper);

            UploadAddress upload = new UploadAddress();
            // 链接表id
            //upload.setLinkId(pb6BargeworkId);
            // 关联水路运单表id
            upload.setLinkId(pb6Waterwaycargo.getId());
            upload.setLinkType(UploadDataType.NOTICE_DATA.getType());
            upload.setUrl(path);
            upload.setUploadTime(new Date());
            // 支付人id
            upload.setUploadUserId(userId);
            // 月结公司id
            upload.setLinkCompanyId(companyId);
            upload.setDataName(fileName.substring(0, fileName.lastIndexOf(".")));
            upload.setStatus(1);
            upload.setDataType(Objects.requireNonNull(DataType.codeNameToCode(fileName.substring(0, fileName.lastIndexOf(".")))));
            upload.setContractNo(contractNo);

            UploadAddress ua = uploadAddressMapper.selectOne(new QueryWrapper<UploadAddress>()
                    .eq("link_id", upload.getLinkId())
                    .eq("link_type", upload.getLinkType())
                    .eq("data_type", upload.getDataType())
                    .eq("upload_user_id", upload.getUploadUserId()));

            if (StringUtils.isNull(ua)) {
                uploadAddressMapper.insert(upload);
            } else {
                upload.setId(ua.getId());
                uploadAddressMapper.updateById(upload);
            }


        });
        log.info("保存文件成功 - waterwayCargoId:{} - 花费时间:{}ms", waterwayCargoId, timer.interval());
        return AjaxResult.success("保存文件成功");
    }


    /**
     * 保存水路运单等文件
     * @param fileList
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult saveCargoFile(List<HashMap<String, Object>> fileList, String waterwayCargoId, Long userId, Long companyId, String contractNo) {
        TimeInterval timer = DateUtil.timer();
        log.info("开始保存文件 - waterwayCargoId:{} - 当前时间:{}", waterwayCargoId, DateUtil.now());
        fileList.forEach(item -> {
            String fileName = (String) item.get("fileName");
            ByteArrayOutputStream byteArrayOutputStream = (ByteArrayOutputStream) item.get("outStream");
            InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            // 保存文件到10.200.17.54里的/root/gzgtest/cargoFile
            String path = null;
            try {
                //path = cargoFtpUtils.uploadFile(inputStream,fileName, Md5Utils.MD5EncodeUtf8(waterwayCargoId));
                path = bargeSftpUtil.uploadFile(inputStream, fileName, Md5Utils.MD5EncodeUtf8(waterwayCargoId));
            } catch (Exception e) {
                log.error("- waterwayCargoId:{} - 花费时间:{}ms", waterwayCargoId, timer.interval());
                log.error("上传文件失败", e);
            } finally {
                IoUtil.close(byteArrayOutputStream);
                IoUtil.close(inputStream);
            }
            Object pb6Bargework = item.get("pb6BargeworkId");
            Long pb6BargeworkId = null;
            if (pb6Bargework != null) {
                pb6BargeworkId = (Long) pb6Bargework;
            } else {
                log.error("pb6BargeworkId为空");
            }

            QueryWrapper<Pb6Waterwaycargo> wrapper = new QueryWrapper<>();
            wrapper.eq("id", waterwayCargoId);
            Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoMapper.selectOne(wrapper);

            UploadAddress upload = new UploadAddress();
            // 链接表id
            //upload.setLinkId(pb6BargeworkId);
            // 关联水路运单表id
            upload.setLinkId(pb6Waterwaycargo.getId());
            upload.setLinkType(UploadDataType.CONSIGN_DATA.getType());
            upload.setUrl(path);
            upload.setUploadTime(new Date());
            // 支付人id
            upload.setUploadUserId(userId);
            // 月结公司id
            upload.setLinkCompanyId(companyId);
            upload.setDataName(fileName.substring(0, fileName.lastIndexOf(".")));
            upload.setStatus(1);
            upload.setDataType(Objects.requireNonNull(DataType.codeNameToCode(fileName.substring(0, fileName.lastIndexOf(".")))));
            upload.setContractNo(contractNo);

            UploadAddress ua = uploadAddressMapper.selectOne(new QueryWrapper<UploadAddress>()
                    .eq("link_id", upload.getLinkId())
                    .eq("link_type", upload.getLinkType())
                    .eq("data_type", upload.getDataType())
                    .eq("upload_user_id", upload.getUploadUserId()));

            if (StringUtils.isNull(ua)) {
                uploadAddressMapper.insert(upload);
            } else {
                upload.setId(ua.getId());
                uploadAddressMapper.updateById(upload);
            }


        });
        log.info("保存文件成功 - waterwayCargoId:{} - 花费时间:{}ms", waterwayCargoId, timer.interval());
        return AjaxResult.success("保存文件成功");
    }

    @Override
    public void downloadCargoFile(String url, HttpServletResponse response, HttpServletRequest request) {
        log.info(url);
        try {
            String downloadName = StringUtils.substringAfterLast(url, "/");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, downloadName));
            cargoFtpUtils.downloadFile(url, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载失败");
            throw new CustomException("下载失败", e, 500);
        }
    }

    @Override
    public ByteArrayOutputStream downloadCargoFile(String url) {
        log.info(url);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            cargoFtpUtils.downloadFile(url, outputStream);
        } catch (Exception e) {
            log.error("发送邮件-下载文件失败");
            throw new CustomException("发送邮件-下载文件失败", e, 500);
        }
        IoUtil.close(outputStream);
        return outputStream;
    }

    private void bargeUpload(String url, MultipartFile file) {
        //创建目标文件
        File targetFile = new File(url);
        try (FileOutputStream fos = new FileOutputStream(targetFile);
            InputStream stream = file.getInputStream()) {
            //写入目标文件
            byte[] buffer = new byte[1024*1024];
            int byteRead = 0;
            while((byteRead=stream.read(buffer))!=-1){
                fos.write(buffer, 0, byteRead);
                fos.flush();
            }
        } catch (IOException e) {
            log.error("上传失败，IO异常");
            throw new CustomException("上传失败，IO异常", e, 500);
        }
    }
}

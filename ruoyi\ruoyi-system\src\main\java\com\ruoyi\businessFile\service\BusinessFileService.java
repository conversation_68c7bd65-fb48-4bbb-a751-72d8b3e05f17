package com.ruoyi.businessFile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.businessFile.domain.BusinessFile;
import com.ruoyi.businessFile.domain.VFileSummary;
import com.ruoyi.businessFile.domain.vo.BusinessFileVo;
import com.ruoyi.businessFile.domain.vo.FileVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 15:02
 */
public interface BusinessFileService extends IService<BusinessFile> {

    List<FileVo> listConFileByfileBusinessID(FileVo fileVo);

    VFileSummary listVFileSummary(String fileBusinessType, String fileBusinessId);

    // 获取当前file，包含url
    BusinessFileVo selectBusinessFileByFileId(String fileId);

    // 根据大船ID查询是否所需文件都已上传
    boolean isAllFileUpload(Long fileBusinessId);

}

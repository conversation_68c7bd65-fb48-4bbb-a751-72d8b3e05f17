package com.ruoyi.common.utils.ftp;

import com.jcraft.jsch.ChannelSftp;
import com.ruoyi.common.exception.CustomException;
import lombok.Data;
import org.apache.commons.pool2.impl.GenericObjectPool;

/**
 * <AUTHOR>
 * @date 2021年06月30日 15:43
 */
@Data
public class SftpPool {

    private GenericObjectPool<ChannelSftp> pool;

    public SftpPool(SftpFactory factory) {
        this.pool = new GenericObjectPool<>(factory, factory.getProperties().getPool());
    }

    /**
     * 获取一个sftp连接对象
     *
     * @return sftp连接对象
     */
    public ChannelSftp borrowObject() {
        try {
            return pool.borrowObject();
        } catch (Exception e) {
            throw new CustomException("获取ftp连接失败", e);
        }
    }

    /**
     * 归还一个sftp连接对象
     *
     * @param channelSftp sftp连接对象
     */
    public void returnObject(ChannelSftp channelSftp) {
        if (channelSftp != null) {
            pool.returnObject(channelSftp);
        }
    }
}

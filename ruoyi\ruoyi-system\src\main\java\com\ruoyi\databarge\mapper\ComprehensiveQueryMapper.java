package com.ruoyi.databarge.mapper;

import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.dto.ComprehensiveQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ComprehensiveQueryMapper {
    //线上办理托运单：
    List<Pb6Cargoconsignment> searchPb6Cargoconsignment(@Param("comprehensiveQueryDTO")ComprehensiveQueryDTO comprehensiveQueryDTO);
    //生成电子水路运单：
    List<UploadAddressDomain> searchUploadAddress(@Param("comprehensiveQueryDTO")ComprehensiveQueryDTO comprehensiveQueryDTO);
    //船方自动报到的数量：
    List<Pb6Bargework> searchPb6Bargework(@Param("comprehensiveQueryDTO")ComprehensiveQueryDTO comprehensiveQueryDTO);
    //确认实装数的驳船数量：
    List<Pb6Waterwaycargo> searchPb6Waterwaycargo(@Param("comprehensiveQueryDTO")ComprehensiveQueryDTO comprehensiveQueryDTO);

}

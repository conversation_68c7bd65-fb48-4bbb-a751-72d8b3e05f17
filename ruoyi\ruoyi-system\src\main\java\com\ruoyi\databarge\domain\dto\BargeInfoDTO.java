package com.ruoyi.databarge.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/9/2.
 * @Date: 2020/9/2 16:41
 */
@Getter
@Setter
public class BargeInfoDTO {

    private Long id;
    private String bargeid;
    private String bargename;
    private String bargeoperator;
    private String mmsi;
    private String bargeweight;
    private String bargeloada;
    private String bargeloadb;
    private String validsaildate;
    private String belongarea;
    private String bargeowner;
    private String bargelinkman;
    private String contactphone;
    private Long comid;
    private String cfullname;
    private Integer bindingtype;
    private Integer status;
    private String isuse;

    //驳船主信息
    private String identityId;
    private String phonenumber;
    private String nickName;

    // 最小散货密度
    private String loadingWeightMin;
    // 最大散货密度
    private String loadingWeightMax;
    // 禁装货物
    private String cargoNotAllowed;
}

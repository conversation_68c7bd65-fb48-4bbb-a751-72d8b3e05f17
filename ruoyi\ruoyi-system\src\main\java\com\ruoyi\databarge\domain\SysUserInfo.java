package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Description:
 *
 * @Author: ChenJin on 2021/3/31.
 * @Date: 2021/3/31 16:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "sys_user_info")
@KeySequence("seq_sys_user_info")
public class SysUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    private String username;

    private String identity;

    private String phone;

    //一个船ID只对应一条数据
    private Long bargeid;
}

package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 驳船基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName( value = "PB6_BARGEINFO")
@KeySequence("SEQ_PB6_BARGEINFO")
public class Pb6Bargeinfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 驳船标识
     */
    @TableField("BARGEID")
    @Excel(name = "驳船标识")
    private String bargeid;

    /**
     * 驳船名称
     */
    @TableField("BARGENAME")
    @Excel(name = "驳船名称")
    private String bargename;

    /**
     * 驳船载货量（A级）
     */
    @TableField("BARGELOADA")
    @Excel(name = "驳船载货量（A级）")
    private String bargeloada;

    /**
     * 驳船载货量（B级）
     */
    @TableField("BARGELOADB")
    @Excel(name = "驳船载货量（B级）")
    private String bargeloadb;

    /**
     * 证书有效期
     */
    @TableField("VALIDSAILDATE")
    @Excel(name = "证书有效期")
    private String validsaildate;

    /**
     * 所属地区
     */
    @TableField("BELONGAREA")
    private String belongarea;

    /**
     * 驳船所有人
     */
    @TableField("BARGEOWNER")
    @Excel(name = "驳船联系人")
    private String bargeowner;

    /**
     * 联系电话
     */
    @TableField("CONTACTPHONE")
    @Excel(name = "联系电话")
    private String contactphone;

    /**
     * 驳船类型
     */
    @TableField("BARGETYPE")
    private String bargetype;

    /**
     * 是否在黑名单
     */
    @TableField("FLAGBLACKLIST")
    private String flagblacklist;

    /**
     * 录入人
     */
    @TableField("RECORDER")
    private String recorder;

    /**
     * 录入时间
     */
    @TableField("RECORDDATE")
    private String recorddate;

    /**
     * 驳船联系人
     */
    @TableField("BARGELINKMAN")
    private String bargelinkman;

    /**
     * 改建日期
     */
    @TableField("REMAKEDATE")
    private String remakedate;

    /**
     * 驳船呼号
     */
    @TableField("BARGECALL")
    private String bargecall;

    /**
     * 驳船制造厂
     */
    @TableField("MAKEFACTORY")
    private String makefactory;

    /**
     * 驳船改建厂
     */
    @TableField("REMAKEFACTORY")
    private String remakefactory;

    /**
     * 驳船经营人
     */
    @TableField("BARGEOPERATOR")
    private String bargeoperator;

    /**
     * 总长
     */
    @TableField("TOTALLENGTH")
    private String totallength;

    /**
     * 船长
     */
    @TableField("BARGELENGTH")
    private String bargelength;

    /**
     * 船宽
     */
    @TableField("BARGEWIDTH")
    private String bargewidth;

    /**
     * 型深
     */
    @TableField("MODELDEEP")
    private String modeldeep;

    /**
     * 最大船宽
     */
    @TableField("MAXBARGEWIDTH")
    private String maxbargewidth;

    /**
     * 最大船高
     */
    @TableField("MAXBARGEHIGH")
    private String maxbargehigh;

    /**
     * 满载水线长
     */
    @TableField("LOADWATERLINELENGTH")
    private String loadwaterlinelength;

    /**
     * 空载吃水
     */
    @TableField("UNLOADDRAUGHT")
    private String unloaddraught;

    /**
     * 满载吃水
     */
    @TableField("LOADDRAUGHT")
    private String loaddraught;

    /**
     * 空载排水量
     */
    @TableField("UNLOADDISPLACEMENT")
    private String unloaddisplacement;

    /**
     * 满载排水量
     */
    @TableField("LOADDISPLACEMENT")
    private String loaddisplacement;

    /**
     * 结构型式
     */
    @TableField("STRUCTURE")
    private String structure;

    /**
     * 船索引
     */
    @TableField("BARGEINDEX")
    private String bargeindex;

    /**
     * 船检登记号
     */
    @TableField("BARGECHECKID")
    private String bargecheckid;

    /**
     * 安放龙骨日期
     */
    @TableField("EMPLACEKEELDATE")
    private String emplacekeeldate;

    /**
     * 录入人代理主键
     */
    @TableField("RECORDERID")
    private Long recorderid;

    /**
     * 修改人
     */
    @TableField("MODIFYMAN")
    private String modifyman;

    /**
     * 修改人代理主键
     */
    @TableField("MODIFYMANID")
    private Long modifymanid;

    /**
     * 修改人时间
     */
    @TableField("MODIFYDATE")
    private String modifydate;

    /**
     * 驳船名称首字母
     */
    @TableField("PYCAP")
    private String pycap;

    /**
     * 是否为导入数据
     */
    @TableField("ISIMPORT")
    private String isimport;

    /**
     * 特约事项
     */
    @TableField("MEMO")
    private String memo;

    /**
     * 运输证编号
     */
    @TableField("TRANSPORTNUM")
    private String transportnum;

    /**
     * 经营范围
     */
    @TableField("BUSINESSSCOPE")
    private String businessscope;

    /**
     * 年审有效期
     */
    @TableField("ANNUALVALIDSAILDATE")
    private String annualvalidsaildate;

    /**
     * 驳船净吨
     */
    @TableField("BARGEWEIGHT")
    private String bargeweight;

    /**
     * 是否为客户
     */
    @TableField("SPECIALCUSTOMER")
    private String specialcustomer;

    /**
     * 特殊客户日期
     */
    @TableField("SPECIALCUSDATE")
    private String specialcusdate;

    /**
     * 特殊用户运单号
     */
    @TableField("SPECIALID")
    private String specialid;

    /**
     * 驳船总吨（小虎）
     */
    @TableField("BARGETOTALWEIGHT")
    private String bargetotalweight;

    /**
     * MMSI标识
     */
    @TableField("MMSI")
    @Excel(name = "MMSI标识")
    private String mmsi;

    /**
     * 是否是网上营业厅办理
     */
    @TableField("ISOLORDER")
    private String isolorder;

    /**
     * 最新联系电话
     */
    @TableField("NEWCONTACTNUM")
    private String newcontactnum;

    /**
     * 微信号
     */
    @TableField("OPENID")
    private String openid;

    /**
     * 是否使用
     */
    @TableField("ISUSE")
    @Excel(name = "是否使用")
    private String isuse;

    /**
     * 审核时间
     */
    @TableField("AUDITTIME")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Timestamp audittime;

    /**
     * 是否印章有船公司名称（物流公司） Y
     */
    @TableField("AUDITSTATE")
    private String auditstate;

    // 装载重量最小
    @TableField("LOADING_WEIGHT_MIN")
    @Excel(name = "最小散货密度")
    private String loadingWeightMin;

    // 装载重量最大
    @TableField("LOADING_WEIGHT_MAX")
    @Excel(name = "最大散货密度")
    private String loadingWeightMax;

    // 不允许装载货物
    @TableField("CARGO_NOT_ALLOWED")
    @Excel(name = "不允许装载货物")
    private String cargoNotAllowed;

}

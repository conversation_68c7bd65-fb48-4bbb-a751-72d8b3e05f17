package com.ruoyi.databarge.domain.dto;

import com.ruoyi.common.core.domain.BaseParamEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2020/12/8 19:51
 * @Description:
 */
@Getter
@Setter
public class ShipInvoiceHistorySearchDTO extends BaseParamEntity {

    private List<Long> idList;
    private List<Long> shipUserIdList;
//    private Integer invoiceYear;
//    private Integer invoiceMonth;
    private String begindate;
    private String enddate;
    private Long pb6WaterwaycargoId;
    private String shipname;
    private String buyername;//购方名
    private String bargename;//驳船名
    private String shipmentplace;//起运港
    private String waterwaycargoid;
    private String invoicetype;//发票类型：0普票、1专票 必填
    private String state;//发票状态：0开票中 、1已开票、2已冲红 3.待开票 普票必填
    private String fphm;//发票号码
    private Long pageNum;
    private Long pageSize;
    private String port;
}

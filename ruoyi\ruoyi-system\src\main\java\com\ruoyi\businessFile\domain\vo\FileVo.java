package com.ruoyi.businessFile.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/3/9 15:00
 */
@Data
public class FileVo {

    private String fileId;
    private String fileTypeId;
    private String fileBusinessType;
    private String fileBusinessID;
    private String fileName;
    private String fileTypeName;
    private Integer fileSort;
    private String url;
    private Long fileSysid;
    private String path;
    private String fileIsMust;

    private String createBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}

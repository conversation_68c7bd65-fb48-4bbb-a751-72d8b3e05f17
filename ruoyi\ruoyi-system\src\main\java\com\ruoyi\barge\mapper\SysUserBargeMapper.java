package com.ruoyi.barge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.barge.domain.SysUserBarge;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.vo.BargeInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/30 10:27
 */
public interface SysUserBargeMapper extends BaseMapper<SysUserBarge> {

    /**
     * 根据驳船主id查询驳船备份相关信息
     * @param userId
     * @return
     */
    BargeInfoVO queryBargeByUserId(Long userId);

    /**
     * 根据驳船主id查询驳船相关信息
     * @param userId
     * @return
     */
    BargeInfoVO queryBargeInfoByUserId(Long userId);

    /**
     * 根据驳船id查询驳船主
     * @param userId
     * @param bargeId
     * @return
     */
    SysUser queryBargeUserByBargeId(@Param("userId") Long userId, @Param("bargeId") Long bargeId);

    // 根据用户id查询是否绑定驳船
    SysUserBarge queryUserBargeByUserId(Long userId);

    /**
     * 根据驳船id查询所有用户信息
     * @param bargeId 驳船id
     * @return 用户详情
     */
    List<SysUser> queryAllUserByBargeId(@Param("bargeId") Long bargeId);

    /**
     * 根据手机号获取驳船用户
     * @param phone
     * @return
     */
    SysUser queryUserByPhone(String phone);
}

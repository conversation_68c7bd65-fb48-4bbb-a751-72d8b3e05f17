package com.ruoyi.app.controller.support.fdd;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @Date 2020/10/16 11:30
 * @Description:
 */
public class Sha256Util {
    public static String getSha256(String oriString) {
        MessageDigest messageDigest;
        String encString = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(oriString.getBytes(StandardCharsets.UTF_8));
            encString = Md5Util.byteArrayToHexString(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return encString;
    }
}
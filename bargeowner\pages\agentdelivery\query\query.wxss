/* pages/consignment/consignmanager/consignmanager.wxss */
.consign-page{
  width: 100%;
  padding-top: 100rpx;
}
.consign-search{
  position: fixed;
  top: 0rpx;
  left: 0rpx;
  right: 0rpx;
  height: 110rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #ddd;
  background: #ffffff;
  z-index: 100;
}
.search-area{
  height: 70rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  display: flex;
  flex-direction: row;  
  align-items: center;
  justify-content: center;
  width: 700rpx;
  margin: 10rpx 0;
}
.search-ipt{
  height: 70rpx;
  line-height: 70rpx;
  flex: 1;
  width: 100%;
  padding: 0 20rpx;
}
.search-icon{
  flex-shrink: 0;
}
.consign-tablist{
  height: 70rpx;
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: space-around;
  align-items: center;
}
.consign-tab{
  padding: 0 15rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 30rpx;
  color: #999999;
  position: relative;
}
.consign-tab.active-tab {
  color: #00426B;
  position: relative;
}
.consign-tab.active-tab::after{
  transform: translateX(-50%) scale(1);
}
.consign-tab::after{
  position: absolute;
  bottom: 0rpx;
  content: '';
  height: 4rpx;
  width: 100%;
  background: #00426B;
  left: 50%;
  transform: translateX(-50%) scale(0);
  transition: all 1s;
}
.consign-content{
  padding: 0rpx 20rpx 0;
}
.data-item{
  padding: 10rpx 0rpx;
  margin: 0;
  background: #f8f8f8;
  border-radius: 10rpx;
  box-shadow: 0rpx -3rpx 3rpx 0rpx rgba(51,51,51,0.04);
}
.data-item-title{
  display: flex;
  height: 70rpx;
  padding: 0 20rpx;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;
}
.item-title-left{
  flex: 1;
  color: #333333;
  font-size: 28rpx;
}
.data-item-right{
  padding-left: 20rpx;
  color: #333333;
  font-size: 28rpx;
}
.data-content{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 0 20rpx;
}
.data-content-message{
  min-width: 50%;
  display: flex;
  flex-direction: row;
  height: 70rpx;
  font-size: 28rpx;
  align-items: center;
}
.message-title{
  color: #999999;
}
.message-value{
  color: #333333;
}
.data-operate{
  max-height: 100rpx;
  display: flex;
  flex-direction: row;
  border-top: 1px solid #ddd;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
}
.data-operate .oper-btn{
  height: 50rpx;
  width: 140rpx;
  border-radius: 30rpx;
  border: 1px solid #00426B;
  color: #00426B;
  font-size: 28rpx;
  padding: 0rpx;
  margin: 10rpx;
  line-height: 46rpx;
  font-weight: normal;
}
.confirm-dialog-wrap .confirm-dialog-content{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.confirm-dialog-wrap  .confirm-dialog-each{
  flex-shrink: 0;
  width: 100%;
  height: 90rpx;
  display: flex;
  align-items: center;
  font-size:30rpx;
  color:#333;
}
.confirm-dialog-wrap  .confirm-dialog-each1{
  flex-shrink: 0;
  width: 100%;
  height: 90rpx;
  font-size:28rpx;
  color:#333;
}

.confirm-dialog-input{
  border:1rpx solid #ddd;
  padding:0 10rpx;
  text-align:left;
}
.confirm-dialog-wrap  .confirm-dialog-left{
  flex-shrink: 0;
  width: 190rpx;
  height: 90rpx;
  line-height: 90rpx;
  padding-right: 8rpx;
}
.confirm-dialog-wrap  .confirm-dialog-right{
  flex:1;
  display: flex;
  align-items: center;
}
.iden-radio{
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: center;
  
}
.iden-radio-item{
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right:20rpx;
}
.iden-radio-text{
  color: #333;
  font-size: 30rpx;
  padding-left: 10rpx;
}
.audit-status-wrap{
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  border-top: 1px solid #ddd;
  padding: 0 20rpx;
}
.audit-statis-text{
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 30rpx;
  color:#333;
}
.charge-dialog-wrap{
  width: 100%;
  height: 100%;
}
.charge-dialog-wrap .weui-dialog__bd{
  display: flex;
  justify-content: center;
}
.charge-dialog-wrap .charge-textarea{
  width: 100%;
  height: 200rpx;
  border:1rpx solid #ddd;
  padding: 20rpx;
  color: #333;
  font-size: 30rpx;
  text-align: left;
}
.cancel-reser-dialog-wrap .cancel-reser-dialog-content{
  width: 100%;
  height: auto;
  padding: 0 10rpx;
  color: #333;
  font-size: 30rpx;
}
.holder-class{
  color:#ccc;
  font-size: 26rpx;
  font-weight: 500;
  line-height: 50rpx;
}
.close-icon{
  flex-shrink: 0;
  width: 40rpx;
  text-align: center;
  height: 70rpx;
  line-height: 70rpx;
  color: #ddd;
  margin-right: 10rpx;
}
.datesearch {
  border: none;
}
.data-item {
  border: 1px solid #ddd;
  margin-top: 30rpx;
  background: #ffffff;
  border-radius: 10rpx;
  padding: 10rpx 0rpx;
}
.data-list{
  padding: 10rpx 20rpx;
  margin-top: 170rpx;
  background: #f5f5f5;
}
.data-item-title{
  height: 80rpx;
  border-bottom: 1px solid #ddd;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx;
}
.detail-icon{
  flex-shrink: 0;
  padding: 0 5rpx;
}
.data-item-text{
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.data-item-message{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding-right:10rpx;
  width:100%;
}
.message-wrap{
  min-width: 50%;
  display: flex;
  flex-direction: row;
  height: 60rpx;
  padding:0 10rpx;
}
.message-wrap-title{
  color: #999;
  font-size: 28rpx;
}
.message-wrap-value{
  color: #00426B;
  font-size: 28rpx;
}
 .datesearch .publish-btn{
  width: 150rpx;
  border: none;
  border-radius: 10rpx;
  height: 60rpx;
  line-height: 60rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
  padding: 0rpx;
  background: #00426B;
  color: #fff;
  font-weight: normal;

}
.search-right{
  margin-left: 10rpx;
}
 .publish-btn.search-btn {
  width: 180rpx;
}
.search-data-box{
  position: relative;
  height: 70rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  flex-shrink: 0;
  flex: 1;
  display: flex;
  align-items: center;
}
.padding-text{
  padding: 0 5rpx;
  flex-shrink: 0;
}
.search-date-text{
  height: 60rpx;
  width: 85%;
  position: absolute;
  left: 0;
  top: 15rpx;
  padding: 0 20rpx;
  color: #808080;
}
.search-date-selected{
  height: 60rpx;
  width: 85%;
  position: absolute;
  left: 0;
  top: 15rpx;
  padding: 0 20rpx;
  color: #333;
}
.search-ipt-wrap{
  flex-shrink: 0;
  width: 100%;
  height: 70rpx;
  padding: 0 15rpx;
  border: 1px solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
}
.close-icon{
  flex-shrink: 0;
  width: 40rpx;
  text-align: center;
  height: 70rpx;
  line-height: 70rpx;
  color: #ddd;
}
.close-iconp{
  flex-shrink: 0;
  position: absolute;
  right: 6rpx;
  top: 15rpx;
  width: 40rpx;
  height:auto;
  color: #ddd;
}
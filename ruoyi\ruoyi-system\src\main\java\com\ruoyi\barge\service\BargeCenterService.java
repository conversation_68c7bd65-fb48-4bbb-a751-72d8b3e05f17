package com.ruoyi.barge.service;

import com.ruoyi.barge.domain.bo.BargeUserBO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.vo.BargeInfoVO;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/29 22:06
 */
public interface BargeCenterService {

    /**
     * 个人备案
     * @param bargeUserBO
     * @return
     */
    AjaxResult personRecord(BargeUserBO bargeUserBO);

    /**
     * 备案详情
     * @param bargeInfoBO
     * @return
     */
    AjaxResult recordDetail(BargeInfoBO bargeInfoBO);

    /**
     * 切换身份
     * @return
     */
    AjaxResult switchIdentity();

    /**
     * 获取驳船列表
     * @param bargeInfoBO
     * @return
     */
    AjaxResult getBargeList(BargeInfoBO bargeInfoBO);

    /**
     * 新增驳船
     * @param bargeInfoBO
     * @return
     */
    AjaxResult addBarge(BargeInfoBO bargeInfoBO);

    /**
     * 驳船备案
     * @param bargeInfoBO 驳船备案基本信息
     * @return
     */
    AjaxResult bargeRecord(BargeInfoBO bargeInfoBO);

    /**
     * 检测备案是否成功
     * @return
     */
    AjaxResult checkRecordIsSuccess();

    /**
     * 检查是否备案
     * @param userId
     * @return
     */
    BargeInfoVO checkRecord(Long userId);

    /**
     * 获取船员列表
     * @param bargeUserBO
     * @return
     */
    AjaxResult getCrewList(BargeUserBO bargeUserBO);

    /**
     * 添加船员
     * @param bargeUserBO
     * @return
     */
    AjaxResult addCrew(BargeUserBO bargeUserBO);

    /**
     * 删除船员
     * @param bargeUserBO
     * @return
     */
    AjaxResult delCrew(BargeUserBO bargeUserBO);

    /**
     * 获取水路运单
     * @param waterwayCargoId
     * @return
     */
    AjaxResult getLoading(String waterwayCargoId);

    /**
     * 确认实装数
     * @param waterwayCargo
     * @return
     */
    AjaxResult confirmLoadingOver(WaterwayCargo waterwayCargo);

    /**
     * 根据主键获取驳船信息
     * @param id
     * @return
     */
    BargeInfo getBargeById(Long id);

    AjaxResult checkBargeInfoBo(BargeInfoBO bargeInfoBO);
}

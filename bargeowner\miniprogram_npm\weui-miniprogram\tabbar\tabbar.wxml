<view class="weui-tabbar {{extClass}}">
  <!-- 选中的时候往 weui-tabbar__item 加 class:weui-bar__item_on -->
  <view data-index='{{index}}' bindtap="tabChange" wx:for="{{list}}" wx:key="index" class="weui-tabbar__item {{index === current ? 'weui-bar__item_on' : ''}}">
    <view style="position: relative;display:inline-block;">
      <image src="{{current === index ? item.selectedIconPath : item.iconPath}}" class="weui-tabbar__icon"></image>
      <mp-badge wx:if="{{item.badge || item.dot}}" content="{{item.badge}}" style="position: absolute;top:-2px;left:calc(100% - 3px)"></mp-badge>
    </view>
    <view class="weui-tabbar__label">{{item.text}}</view>
  </view>
</view>
package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.databarge.domain.PubPort;
import com.ruoyi.databarge.domain.dto.CountryDTO;
import com.ruoyi.databarge.domain.dto.PubPortSearchDTO;
import com.ruoyi.databarge.mapper.PubPortMapper;
import com.ruoyi.databarge.service.PubPortService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * ??? 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
@Service
public class PubPortServiceImpl extends ServiceImpl<PubPortMapper, PubPort> implements PubPortService {

    @Override
    public IPage<PubPort> searchPageByNameAndCountry(PubPortSearchDTO pubPortSearchDTO) {
        Long pageNum= Optional.ofNullable(pubPortSearchDTO.getPageNum()).orElse(0L);
        Long pageSize= Optional.ofNullable(pubPortSearchDTO.getPageSize()).orElse(-1L);
        return super.baseMapper.searchPageByNameAndCountry(new Page<>(pageNum, pageSize), pubPortSearchDTO);
    }

    @Override
    public boolean updateWLInfo(PubPort pubPort) {
        PubPort port = super.getById(pubPort.getId());
        port.setNavigatingzone(pubPort.getNavigatingzone());
        port.setMaritimecode(pubPort.getMaritimecode());
        port.setFirstmaritimecode(pubPort.getFirstmaritimecode());
        if(StringUtils.isNotBlank(pubPort.getFirstmaritimecode())){
            port.setBelongwlcompany(1);
        } else {
            port.setBelongwlcompany(0);
        }
        return super.updateById(port);
    }

    @Override
    public List<CountryDTO> searchCountryByCName(String countrycname) {
        return super.baseMapper.searchCountryByCName(countrycname);
    }

    @Override
    public List<PubPort> searchPortByName(String searchName) {
        return super.baseMapper.searchPortByName(searchName);
    }

    @Override
    public String getPortcnameById(Long id) {
        if(id==null) {
            return null;
        } else {
            return super.baseMapper.getPortcnameById(id);
        }
    }

    @Override
    public List<PubPort> searchWlPortByName(String bargeName) {
        return list(new LambdaQueryWrapper<PubPort>().eq(PubPort::getBelongwlcompany, "1")
            .eq(PubPort::getPortcname, bargeName));
    }
}

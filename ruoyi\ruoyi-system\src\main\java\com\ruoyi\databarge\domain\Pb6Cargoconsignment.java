package com.ruoyi.databarge.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 托运单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName( value = "PB6_CARGOCONSIGNMENT")
@KeySequence("SEQ_PB6_CARGOCONSIGNMENT")
public class Pb6Cargoconsignment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 是否核销
     */
    @TableField("FLAGVERIFICATION")
    private String flagverification;

    /**
     * 申请日期
     */
    @TableField("APPLYDATE")
    private String applydate;

    /**
     * 驳船名
     */
    @TableField("BARGENAME")
    private String bargename;

    /**
     * 起运港
     */
    @TableField("BEGINPORT")
    private String beginport;

    /**
     * 货物名称
     */
    @TableField("CARGENAME")
    private String cargename;

    /**
     * 公司Id
     */
    @TableField("COMID")
    private Long comid;

    /**
     * 托号
     */
    @TableField("CONSIGNFLAG")
    private String consignflag;

    /**
     * 收货人
     */
    @TableField("CONSIGNEE")
    private String consignee;

    /**
     * 发货人/托运人
     */
    @TableField("CONSIGNER")
    private String consigner;

    /**
     * 提单号
     */
    @TableField("CONSIGNMENTFLAG")
    private String consignmentflag;

    /**
     * 目的港
     */
    @TableField("ENDPORT")
    private String endport;

    /**
     * 办单日期
     */
    @TableField("FORMTIME")
    private String formtime;

    /**
     * 地磅号
     */
    @TableField("LOADOMETERID")
    private String loadometerid;

    /**
     * 中转港
     */
    @TableField("MIDPORT")
    private String midport;

    /**
     * 修改时间
     */
    @TableField("MODIFYDATE")
    private String modifydate;

    /**
     * 修改人
     */
    @TableField("MODIFYMAN")
    private String modifyman;

    /**
     * 修改人代理主键
     */
    @TableField("MODIFYMANID")
    private BigDecimal modifymanid;

    /**
     * 出/入库单号
     */
    @TableField("OUTORINFORMID")
    private String outorinformid;

    /**
     * 包装方式
     */
    @TableField("PACKAGETYPE")
    private String packagetype;

    /**
     * 配载件数
     */
    @TableField("RATIONPIECE")
    private String rationpiece;

    /**
     * 配载吨数
     */
    @TableField("RATIONWEIGHT")
    private String rationweight;

    /**
     * 装货地点（大概）
     */
    @TableField("SHIPMENTPLACE")
    private String shipmentplace;

    /**
     * 卸货地点（大概）
     */
    @TableField("SHIPMENTUNPLACE")
    private String shipmentunplace;

    /**
     * 承运船舶公司
     */
    @TableField("SHIPPINGCONAME")
    private String shippingconame;

    /**
     * 特约事项
     */
    @TableField("SPECIALPROCEEDING")
    private String specialproceeding;

    /**
     * 结算方式
     */
    @TableField("SSTYLETYPE")
    private String sstyletype;

    /**
     * 托运人公司
     */
    @TableField("SHIPPERDEPT")
    private String shipperdept;

    /**
     * 是否核销出库单
     */
    @TableField("FLAGCOUTFORM")
    private String flagcoutform;

    /**
     * 审批出库单人
     */
    @TableField("CHECKMAN")
    private String checkman;

    /**
     * 审批出库单时间
     */
    @TableField("CHECKTIME")
    private String checktime;

    /**
     * 作业类型
     */
    @TableField("WORKTYPE")
    private String worktype;

    /**
     * 办理方式（1为网页端，2为app 端）
     */
    @TableField("ONINERESOURSE")
    private String onineresourse;

    /**
     * 水路货物运单编号
     */
    @TableField("WATERWAYCARGOID")
    private String waterwaycargoid;

    /**
     * 费用结算方式
     */
    @TableField("CHARGEBALANCETYPE")
    private String chargebalancetype;

    /**
     * 出库单类型: 1.本司2.它司(出库单办理)
     */
    @TableField("WXOUTORINFORMTYPE")
    private Integer wxoutorinformtype;

    /**
     * 办单类型: 1-货主；   2-船公司
     */
    @TableField("WXAPPLYUSERTYPE")
    private Integer wxapplyusertype;

    /**
     * 派船类型(货主办理托运单时): 0:自己派船 1：船公司派船
     */
    @TableField("WXSELECTSHIPTYPE")
    private Integer wxselectshiptype;

    /**
     * 派船状态: 0：待派船，1：以派船
     */
    @TableField("WXSELECTSHIPSTATE")
    private Integer wxselectshipstate;

    /**
     * 派船公司id
     */
    @TableField("WXSELECTSHIPBYID")
    private BigDecimal wxselectshipbyid;

    /**
     * 派船公司名称
     */
    @TableField("WXSELECTSHIPBYNAME")
    private String wxselectshipbyname;

    /**
     * 创建人id
     */
    @TableField("WXCREATEUSERBYID")
    private BigDecimal wxcreateuserbyid;

    /**
     * 创建时间
     */
    @TableField("WXCREATETIME")
    private String wxcreatetime;

    /**
     * 创建人所属公司
     */
    @TableField("WXCREATECOMPANYID")
    private String wxcreatecompanyid;

    /**
     *  其他 沿海/内河 港口（2021-02-07添加）
     */
    @TableField("OTHERCOASTALINLANDPORT")
    private String otherCoastalInlandPort;

    // 大船ID
    @TableField("SHIP_INFO_ID")
    private Long shipInfoId;

    @TableLogic
    private String delFlag;

    private String settlementStatus;

    private Long settlementId;

    // 积载因数
    private String stowageFactor;

    // 结算单位
    private String settlementCompany;

    // 提单号
    private String billNo;

    // 总吨数
    private BigDecimal totalTonnage;

    // 金额
    private BigDecimal amount;

    // 费率
    private BigDecimal rate;

    // 货物类别
    private String cargoType;

    // 生产系统提单id
    private Long gzpId;


}

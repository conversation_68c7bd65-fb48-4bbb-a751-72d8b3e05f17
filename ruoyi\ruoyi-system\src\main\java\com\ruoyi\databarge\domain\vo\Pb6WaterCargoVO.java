package com.ruoyi.databarge.domain.vo;

import com.ruoyi.databarge.domain.UploadAddressDomain;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/12/15.
 * @Date: 2020/12/15 11:16
 */
@Getter
@Setter
public class Pb6WaterCargoVO {

    private String waterwaycargoid; //水路运单号
    private String bargename; //驳船名称
    private String outorinformid; // 出/入库单号
    private String shipnumber; //航次号
    private String uniqecode; //到验号
    private String shipname; //大船名
    private BigDecimal rationweight; //配载吨数
    private String cargename; //货名
    private String consignee; //收货人
    private String beginport; //起运港
    private String midport; //中转港
    private String endport; //目的港
    private String cargosize; //付款单位
    private String consigner; //托运人
    private String chargebalancetype; //费用结算方式
    private String isoffline; //线上还是线下， Y为线上，其余为线下
    private BigDecimal businessagentchargerate;  //代理费率
    private BigDecimal businessagentcharge; //代理费
    private BigDecimal cargoportchargerate; //货物港务费率
    private BigDecimal cargoportcharge; //货物港务费
    private BigDecimal serviceagentchargerate; //围油栏费率
    private BigDecimal serviceagentcharge; //围油栏费
    private BigDecimal berthchargerate; //停泊费率
    private BigDecimal berthcharge; //停泊费
    private BigDecimal transportchargerate; //运费费率
    private BigDecimal transportcharge; //运费
    private BigDecimal totalcharge; //费用总合计
    private String rationprincipal; //配载人
    private Integer flagbargestate; //运单状态
    private String bargelinkman; //驳船联系人
    private String contactphone; //联系电话
    private String consignflag; //托号
    private String confirmloadingover; //确认是否实装： Y 确认实装， N是没有确认实装
    private String state; // 发票状态 0开票中  1已开票  2已冲红  3待开票
    private String fphm; //发票号码

    private int uploadaddresscount;

    private String tscargoweightvalue; // 实装数

    private String sendpractime; //发送实装数时间

    private Integer wxoperatestate;

    private Integer paynum; //现结支付成功数

    private String isreporting;//绿色驳船申报报道

    private String fiveDayReporting; //五日内是否申报绿色驳船
}

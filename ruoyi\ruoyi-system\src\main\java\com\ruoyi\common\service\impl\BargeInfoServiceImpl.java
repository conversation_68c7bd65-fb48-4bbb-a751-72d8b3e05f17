package com.ruoyi.common.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.domain.BargeInfo;
import com.ruoyi.common.domain.bo.BargeInfoBO;
import com.ruoyi.common.domain.vo.BargeInfoVO;
import com.ruoyi.common.mapper.BargeInfoMapper;
import com.ruoyi.common.service.BargeInfoService;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 运单实现
 * <AUTHOR>
 * @Date 2020/8/5 9:53
 */
@Service
public class BargeInfoServiceImpl extends ServiceImpl<BargeInfoMapper, BargeInfo> implements BargeInfoService {
//    @Autowired
//    private BargeInfoAuditService bargeInfoAuditService;
//    @Autowired
//    private BargeCompanyLinkService bargeCompanyLinkService;

//    @Override
//    @Transactional
//    public void addCarrierBarge(BargeInfoAudit bargeInfoAudit) {
//        SysUser user = SecurityUtils.getLoginUser().getUser();
//        String time = DateUtils.getTime();
//        if(bargeInfoAudit.getId()!=null){//已存在的驳船(修改、挂靠操作)
//
//            if(bargeInfoAudit.getCheckFlag()!=null){
//                //更新驳船的审核状态
//
////                if(bargeInfoAudit.getCheckFlag().intValue()==CheckFlagEnum.BARGE_AUDIT.getCode()){
////                }else if(bargeInfoAudit.getCheckFlag().intValue()==CheckFlagEnum.UPDATE_BARGE_AUDIT.getCode()){
////                    bargeInfoAudit.setUpdateCheck(0);
////                }
//                bargeInfoAudit.setUpdateCheck(0);
//                bargeInfoAudit.setModifyManId(user.getUserId());
//                bargeInfoAudit.setModifyMan(user.getNickName());
//                bargeInfoAudit.setModifyDate(time);
//                bargeInfoAuditService.addCarrierBarge(bargeInfoAudit);
//            }
//
//            //如果涉及到挂靠审核，则需要保存挂靠关系
//            if(bargeInfoAudit.getBargeCompanyLink()!=null){
//                bargeCompanyLinkService.bingdingBarge(bargeInfoAudit.getBargeCompanyLink());
//            }
//
//        }else{//未存在的驳船备案（备案、挂靠）
//            //如果涉及到挂靠审核，则需要保存挂靠关系
////            BargeInfo bargeInfo = new BargeInfo();
////            BeanUtils.copyProperties(bargeInfoAudit,bargeInfo);
////            bargeInfo.setRecorderId(user.getUserId());
////            bargeInfo.setRecorder(user.getNickName());
////            bargeInfo.setRecordDate(time);
////            baseMapper.insert(bargeInfo);
//            bargeInfoAuditService.save(bargeInfoAudit);
//            //如果涉及到挂靠审核，则需要保存挂靠关系
//            BargeCompanyLink bargeCompanyLink = bargeInfoAudit.getBargeCompanyLink();
//            if(bargeCompanyLink !=null){
//                bargeCompanyLink.setBargeId(bargeInfoAudit.getId());
//                bargeCompanyLinkService.bingdingBarge(bargeCompanyLink);
//            }
//
//        }
//    }
    @Override
    public List<BargeInfoVO> getBargeList(BargeInfoBO bargeInfoBO) {
        return baseMapper.getBargeList(bargeInfoBO);
    }

    @Override
    public List<BargeInfoVO> getBargeListByCompanyId(BargeInfoBO bargeInfoBO) {
        return baseMapper.getBargeListByCompanyId(bargeInfoBO);
    }

    @Override
    public List<BargeInfoVO> getBargeListByCompanyUser(BargeInfoBO bargeInfoBO) {
        return baseMapper.getBargeListByCompanyUser(bargeInfoBO);
    }

    @Override
    public List<BargeInfoVO> getBargeListByNoCompany(BargeInfoBO bargeInfoBO) {
        bargeInfoBO.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
        return baseMapper.getBargeListByNoCompany(bargeInfoBO);
    }
    //    @Override
//    @Transactional
//    public Boolean bargeAudit(BargeInfoAudit bargeInfoAudit) {
//        //当审核通过时：1.更新审核状态及信息，2.更新备案、挂靠信息 3.更新备案资料连接
//        if(bargeInfoAudit.getCheckFlag().intValue()==1){//备案审核
//            if(bargeInfoAudit.getRecordCheck()==1){//审核通过
//                //1.更新审核状态
//                baseMapper.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfo>().eq("id",bargeInfoAudit.getId())
//                                                    .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//                //删除已审核的备份数据
//                bargeInfoAuditService.removeById(bargeInfoAudit.getId());
//            }else{//审核不通过
//                baseMapper.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfo>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//                bargeInfoAuditService.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfoAudit>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//            }
//        }else if(bargeInfoAudit.getCheckFlag().intValue()==2){//备案+挂靠
//            if(bargeInfoAudit.getRecordCheck()==1 && bargeInfoAudit.getBindingCheck().intValue()==1){
//                //1.更新审核状态
//                baseMapper.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfo>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//                //删除已审核的备份数据
//                bargeInfoAuditService.removeById(bargeInfoAudit.getId());
//            }else{
//                baseMapper.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfo>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//                bargeInfoAuditService.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfoAudit>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//            }
//        }else if(bargeInfoAudit.getCheckFlag().intValue()==3){//修改审核
//            if(bargeInfoAudit.getUpdateCheck().intValue()==1){
//                //1.更新审核状态
//                baseMapper.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfo>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//                //删除已审核的备份数据
//                bargeInfoAuditService.removeById(bargeInfoAudit.getId());
//            }else{
//                baseMapper.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfo>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//                bargeInfoAuditService.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfoAudit>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//            }
//        }else if(bargeInfoAudit.getCheckFlag().intValue()==4){//船只变更审核
//            if(bargeInfoAudit.getBindingCheck().intValue()==1){
//                //1.更新审核状态
//                baseMapper.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfo>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//                //删除已审核的备份数据
//                bargeInfoAuditService.removeById(bargeInfoAudit.getId());
//            }else{
//                baseMapper.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfo>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//                bargeInfoAuditService.update(bargeInfoAudit,
//                        new QueryWrapper<BargeInfoAudit>().eq("id",bargeInfoAudit.getId())
//                                .eq("checkFlag",bargeInfoAudit.getCheckFlag())
//                );
//            }
//        }
//
//        return true;
//    }


}

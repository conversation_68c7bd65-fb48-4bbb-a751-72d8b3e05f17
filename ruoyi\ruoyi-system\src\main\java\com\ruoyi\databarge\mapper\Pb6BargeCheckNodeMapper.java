package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.databarge.domain.Pb6BargeCheckNode;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 驳船单证退单改单审核流程表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-23
 */
public interface Pb6BargeCheckNodeMapper extends BaseMapper<Pb6BargeCheckNode> {

    Pb6BargeCheckNode searchRecentByLinkId(@Param("linkId") Long linkId);
}

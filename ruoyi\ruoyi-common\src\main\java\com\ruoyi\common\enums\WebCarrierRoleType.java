package com.ruoyi.common.enums;

/**
 * pc端船公司角色类型
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/18 15:30
 */
public enum WebCarrierRoleType {

    /**
     * 0-未知 3-管理员 4-业务人员
     */
    UNKNOWN(0, "未知"),
    ADMIN(3, "管理员"),
    NORMAL(4, "业务人员");

    private final Integer code;

    private final String codeName;

    WebCarrierRoleType(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

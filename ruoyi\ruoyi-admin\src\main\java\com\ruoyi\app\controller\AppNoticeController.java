package com.ruoyi.app.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.bo.UserMessageBO;
import com.ruoyi.common.service.AppNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订阅消息通知 控制层
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/5 9:20
 */
@Api("订阅消息通知 - api")
@Slf4j
@RestController
@RequestMapping("/notice")
public class AppNoticeController {

    @Autowired
    private AppNoticeService appNoticeService;

    /**
     * 获取消息通知列表
     * @param userMessageBO
     * @return
     */
    @ApiOperation("获取消息通知列表")
    @PostMapping("/getNoticeList")
    public AjaxResult getNoticeList(@RequestBody UserMessageBO userMessageBO) {
        return appNoticeService.getNoticeList(userMessageBO);
    }

    /**
     * 更新消息状态
     * @param userMessageBO
     * @return
     */
    @ApiOperation("更新消息状态")
    @PostMapping("/updateIsRead")
    public AjaxResult updateIsRead(@RequestBody UserMessageBO userMessageBO) {

        Long userMessageId = userMessageBO.getUserMessageId();

        if (userMessageId == null) {
            log.error("AppNoticeController - updateIsRead - 用户消息主键id不能为空");
            return AjaxResult.error("用户消息主键id不能为空");
        }

        return appNoticeService.updateIsRead(userMessageBO);
    }

    /**
     * 订阅
     * @param userMessageBO
     * @return
     */
    @ApiOperation("订阅")
    @PostMapping("/subscription")
    public AjaxResult subscription(@RequestBody UserMessageBO userMessageBO) {
        if (userMessageBO.getConsignId() == null) {
            return AjaxResult.error("运单id不能为空");
        }
        if (userMessageBO.getConsignDetailId() == null) {
            return AjaxResult.error("运单明细id不能为空");
        }
        return appNoticeService.subscription(userMessageBO);
    }

    /**
     * 取消订阅
     * @param userMessageBO
     * @return
     */
    @ApiOperation("取消订阅")
    @PostMapping("/cancelSubscription")
    public AjaxResult cancelSubscription(@RequestBody UserMessageBO userMessageBO) {
        if (userMessageBO.getConsignId() == null) {
            return AjaxResult.error("运单id不能为空");
        }
        if (userMessageBO.getConsignDetailId() == null) {
            return AjaxResult.error("运单明细id不能为空");
        }
        return appNoticeService.cancelSubscription(userMessageBO);
    }

    /**
     * 发送消息
     * @param userMessageBO sendMessageType - @SendMessageEnum
     *                      发送非订阅消息 userIds - 发送消息对象用户id集合
     *                      发送订阅消息 订阅consignId - 托运单主表id，consignDetailId - 托运单明细表id
     * @return
     */
    @ApiOperation("发送消息")
    @PostMapping("/sendMessage")
    public AjaxResult sendMessage(@RequestBody UserMessageBO userMessageBO) {
        return appNoticeService.sendMessage(userMessageBO);
    }
}

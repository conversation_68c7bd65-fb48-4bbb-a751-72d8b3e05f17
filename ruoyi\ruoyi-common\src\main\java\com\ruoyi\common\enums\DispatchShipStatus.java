package com.ruoyi.common.enums;

/**
 * @Description 派船状态： 0：待派船，1：已派船
 * <AUTHOR>
 * @Date 2020/8/12  21:19
 */
public enum DispatchShipStatus {

    /**
     * 派船状态： 0：待派船，1：已派船
     *
     */
    AWAIT_DISPATCH(0, "待派船"),
    BEEN_DISPATCH(1, "已派船");

    private final Integer code;

    private final String codeName;

    DispatchShipStatus(Integer code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public Integer getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }

}

<!--pages/consignment/consigndetail/consigndetail.wxml-->
<view class="consign-detail" hover-class="none" hover-stop-propagation="false">
  <view class="consign-bottom-title" hover-class="none" hover-stop-propagation="false" wx:if="{{form.dataList && form.dataList.length>0}}">
    <text class="annex-text" selectable="false" space="false" decode="false">附件列表</text>
    <button class="download-btn" bindtap="batchDown">发送邮箱</button>
  </view>
  <view class="order-list" hover-class="none" hover-stop-propagation="false"  wx:if="{{form.dataList && form.dataList.length>0}}">
    <view class="order-item" hover-class="none" hover-stop-propagation="false" wx:for="{{form.dataList}}" wx:key="index" data-item="{{item}}" data-index="{{index}}">
      <view class="order-item-title" hover-class="none" hover-stop-propagation="false">{{item.dataName}}</view>
      <view class="checktxt" data-item="{{item}}" data-index="{{index}}" bindtap = "getSeaWayBill">查看</view>
    </view>
  </view>
  <view class="consign-top" hover-class="none" hover-stop-propagation="false">
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">驳船名称：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.bargeName}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运单号：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.consignFlag}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">水路运单：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.waterWayCargoId}}</view>
    </view>
    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">创建时间：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.applyDate}}</view>
    </view> -->
  </view>

  <view class="consign-top" hover-class="none" hover-stop-propagation="false">
    <view class="consign-title" hover-class="none" hover-stop-propagation="false">
      运单信息
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">申请日期：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.applyDate}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">出库单号：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.outOrInformId}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">货物名称：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.cargeName}}</view>
    </view>

    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运人公司：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.consigner}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">起运港：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.beginPort}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">目的港：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.endPort}}</view>
    </view>
    <view wx:if="{{form.endPort=='其他内河港'}}" class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">其他内河港：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.otherCoastalInlandPort}}</view>
    </view>
    <view wx:if="{{form.endPort=='其他沿海港'}}" class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">其他沿海港：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.otherCoastalInlandPort}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">中转港：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.midPort}}</view>
    </view>
    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">发货人：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.consigner}}</view>
    </view> -->
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">收货人：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.consignee}}</view>
    </view>
    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">装货地点：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.shipmentPlace}}</view>
    </view> -->

    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">卸货地点：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.shipmentunPlace}}</view>
    </view> -->
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">地磅单号：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.loadometerId}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">发货符号：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.consignmentFlag}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">包装方式：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.packageType}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">承运船舶公司：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.shippingCoName}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运单配载重量（吨）：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.rationWeight}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运单配载件数(件)：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.rationPiece == null ? '' : form.rationPiece}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运单联系人：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.wxRationContactNumber}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">驳船主联系电话：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.bargeTel}}</view>
    </view>

    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">费用结算方式：</view>
      <radio-group bindchange="radioChange" class="radio-list">
        <label class="radio-item">
          <radio class="" value="0"  color="#00426B" checked="{{form.chargeBalanceType==='0'}}" disabled="{{form.chargeBalanceType==1}}"></radio>
          <view class="" hover-class="none" hover-stop-propagation="false">公司月结</view>
        </label>
        <label class="radio-item">
          <radio class="" value="1"   color="#00426B" checked="{{form.chargeBalanceType==1}}" disabled="{{form.chargeBalanceType==0}}"></radio>
          <view class="" hover-class="none" hover-stop-propagation="false">现结</view>
        </label>
      </radio-group>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">月结单位：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.wxMonthChargeByName}}</view>
    </view>
    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">托运单实际装货量：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.workWeight}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">是否加急：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{!form.isHarry ? "" :(form.isHarry == 'Y' ? '是' : '否') }}</view>
    </view> -->
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">特约事项：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.specialProceeding}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">预约时间：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.wxAppointmentTime}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">实装数：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.workWeight}}</view>
    </view>
  </view>

  <view class="consign-top" hover-class="none" hover-stop-propagation="false">
    <view class="consign-title" hover-class="none" hover-stop-propagation="false">
      费用信息
    </view>
    <!-- <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">运费：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.transportCharge}}</view>
    </view> -->
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">货物港物费：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.cargoPortCharge}}</view>
    </view>

    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">围油栏费：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.serviceAgentCharge}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">代理费：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.businessAgentCharge}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">停泊费：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.berthCharge}}</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="message-title" hover-class="none" hover-stop-propagation="false">总费用：</view>
      <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.totalCharge}}</view>
    </view>
  </view>
  <view class="consign-top" hover-class="none" hover-stop-propagation="false" wx:if="{{payRecordList.length >= 1}}">
    <view class="" hover-class="none" hover-stop-propagation="false" wx:if="{{payBackItem && payBackItem.id}}">
      <view class="consign-title" hover-class="none" hover-stop-propagation="false">退款说明</view>
      <view class="consign-message" hover-class="none" hover-stop-propagation="false">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">实装吨数：</view>
        <view class="message-value" hover-class="none" hover-stop-propagation="false">{{form.transportCharge}}</view>
      </view>
      <view class="consign-message" hover-class="none" hover-stop-propagation="false">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">退款计算：</view>
        <view class="message-value" hover-class="none" hover-stop-propagation="false">({{form.rationWeight}}-{{form.transportCharge}})/{{form.rationWeight}}*{{form.totalCharge}}</view>
      </view>
      <view class="consign-message" hover-class="none" hover-stop-propagation="false">
        <view class="message-title" hover-class="none" hover-stop-propagation="false">应退费用：</view>
        <view class="message-value" hover-class="none" hover-stop-propagation="false">{{((form.rationWeight-form.transportCharge)/form.rationWeight*form.totalCharge)}}</view>
      </view>
    </view>
    
    <view class="consign-title" hover-class="none" hover-stop-propagation="false">
      支付记录
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="consign-half-item" hover-class="none" hover-stop-propagation="false">支付方式：<text class="value-fontcolor">{{payType}}</text></view>
       <view class="consign-half-item" hover-class="none" hover-stop-propagation="false">支付状态：<text class="value-fontcolor">{{payStatusObj['status'+payStatus]}}</text></view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false">
      <view class="consign-col3-item" hover-class="none" hover-stop-propagation="false">付款时间</view>
      <view class="consign-col2-item" hover-class="none" hover-stop-propagation="false">实付金额</view>
      <view class="consign-col2-item" hover-class="none" hover-stop-propagation="false">订单状态</view>
    </view>
    <view class="consign-message" hover-class="none" hover-stop-propagation="false" wx:for="{{payRecordList}}" wx:key="index">
      <view class="consign-col3-item" style="color: #333;" hover-class="none" hover-stop-propagation="false">{{item.paytime || ''}}</view>
      <view class="consign-col2-item" wx:if="{{item.paysign ==0}}" style="color: #333;" hover-class="none" hover-stop-propagation="false">{{item.actualmount}}元</view>
      <view class="consign-col2-item" wx:else style="color: #333;" hover-class="none" hover-stop-propagation="false">-{{item.count}}元</view>
      <view class="consign-col2-item" style="color: #333;" hover-class="none" hover-stop-propagation="false">{{payStatusObj['status'+item.status]}}</view>
    </view>
  </view>



  <!-- <view class="deliver-item" hover-class="none" hover-stop-propagation="false" bindtap="outBoundJump">
    驳船出库记录查询
  </view>
  <view class="deliver-item" hover-class="none" hover-stop-propagation="false">
    查询轨迹
  </view> -->
  <view class="detail-oper" hover-class="none" hover-stop-propagation="false">
    <button class="oper-btn" bindtap="handleConfirm">确定</button>
  </view>
  <!-- 批量下载弹窗 -->
  <mp-dialog title="{{userEmail ? '您的邮箱如下':'账号尚未绑定邮箱，请填写邮箱'}}" show="{{batchDialog}}" mask="true" mask-closable="false" bindbuttontap="tapBatchDialogButton" buttons="{{batchButtons}}">
    <input type="text" class="add-message-emailipt" placeholder="请输入邮箱" value="{{userEmail}}"  bindinput="emailInput"/>
  </mp-dialog>
</view>
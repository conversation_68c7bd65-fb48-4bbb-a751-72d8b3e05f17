package com.ruoyi.consignor.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Description  货源实体类
 * <AUTHOR>
 * @Date 2020/7/24 15:33
 */
@Data
@TableName("cargo_source_publish")
@KeySequence("seq_cargo_source_publish")
public class CargoSourceInfo {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 货主id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 货主名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 联系电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * remark
     */
    @TableField("remark")
    private String remark;

    /**
     * 货源号
     */
    @TableField("cargo_source_id")
    private String cargoSourceId;



// -------  新增字段 -------------
    /**
     * 货物名称
     */
    @TableField("cargoname")
    private String cargoName;

    /**
     * 出发地
     */
    @TableField("placeofdeparture")
    private String placeOfDeparture;

    /**
     * 目的地
     */
    @TableField("destination")
    private String destination;

    /**
     * 货物数量
     */
    @TableField("cargonumber")
    private Integer cargoNumber;

    /**
     * 货物重量
     */
    @TableField("cargoweight")
    private String cargoWeight;

    /**
     * 货物体积
     */
    @TableField("cargobulk")
    private String CargoBulk;




}

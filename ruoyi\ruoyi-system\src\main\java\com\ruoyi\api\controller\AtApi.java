package com.ruoyi.api.controller;

import com.ruoyi.api.domain.AtMessageParam;
import com.ruoyi.api.service.AtService;
import com.ruoyi.api.service.impl.AtServiceImpl;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/11/1 9:02
 */
@RestController
@RequestMapping("/at/atApi")
public class AtApi extends BaseController {

    @Autowired
    AtServiceImpl atService;

    // 接收安通消息
    @PostMapping("/atMessage/receive")
    public AjaxResult atMessage(@RequestBody AtMessageParam atMessageParam) {
        return atService.atMessage(atMessageParam);
    }

}

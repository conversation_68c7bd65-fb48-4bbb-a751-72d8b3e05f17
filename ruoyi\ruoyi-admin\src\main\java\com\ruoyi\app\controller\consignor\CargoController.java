package com.ruoyi.app.controller.consignor;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.dto.CargocmentDTO;
import com.ruoyi.consignor.utils.CargoUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-16 17:35
 */
@Api(value = "退改单-api", tags = "退改单-api")
@Slf4j
@RestController
@RequestMapping("/order/cargo")
public class CargoController {

    @Autowired
    private CargoUtil cargoUtil;

    /**
     * 退改单
     *
     * @param cargocmentDTO cargoconsignmentdetail - id 托运单明细id必填
     *                      cargoconsignment - id 托运单主表id必填
     *                      state 退改单标识必填 1-退单，2-改单
     *                      uploadAddress 文件信息
     * @return
     */
    @PostMapping("/changeCargo")
    public AjaxResult changeCargo(@RequestBody CargocmentDTO cargocmentDTO) {
        return cargoUtil.modify(cargocmentDTO);
    }

    /**
     * 取消退改单
     *
     * @param cargocmentdetailBO stateOperation - 1.取消改单, 2.取消退单 - 必填
     *                           WXnode - 0提交审核，1物流公司直接审核，2码头审核， 3物流公司间接审核 - 审核节点状态必填
     *                           applyModify - 必填 - 营业厅申请改单/退单（0为申请退单中，1为申请退单通过，2为申请退单失败,3为申请改单中，
     *                           4为申请改单成功，5为申请改单失败
     *                           modifyReason - 退改单原因
     * @return
     */
    @PostMapping("/changeCargoCancel")
    public AjaxResult changeCargoCancel(@RequestBody CargocmentdetailBO cargocmentdetailBO) {
        return cargoUtil.cancel(cargocmentdetailBO);
    }
}

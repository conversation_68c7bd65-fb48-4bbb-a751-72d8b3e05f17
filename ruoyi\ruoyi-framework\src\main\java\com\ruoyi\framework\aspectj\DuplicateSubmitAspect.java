package com.ruoyi.framework.aspectj;

import com.ruoyi.common.annotation.DuplicateSubmitToken;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.DuplicateSubmitException;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Description: 防止表单重复提交拦截器
 *
 * @Author: ChenJin on 2021/9/2.
 * @Date: 2021/9/2 16:38
 */
@Aspect
@Component
@Slf4j
public class DuplicateSubmitAspect {
    public static final String DUPLICATE_TOKEN_KEY = "duplicate_token_key";
    @Autowired
    private RedisCache redisCache;
    @Pointcut("@annotation(com.ruoyi.common.annotation.DuplicateSubmitToken)")
    public void webLog() {
    }

    @Before("webLog() && @annotation(token)")
    public void before(final JoinPoint joinPoint, DuplicateSubmitToken token){
        if (token != null) {
            String methodName = joinPoint.getSignature().getName();
            StringBuilder key = new StringBuilder(DUPLICATE_TOKEN_KEY);
            if(SecurityUtils.getAuthentication().getPrincipal() instanceof SysUser) {
                SysUser sysUser = (SysUser) SecurityUtils.getAuthentication().getPrincipal();
                key.append("-").append(sysUser.getUserId()).append("-").append(methodName);
            }
            if(redisCache.getCacheObject(key.toString()) != null){
                throw new DuplicateSubmitException("5秒中不能重复提交");
            } else {
                redisCache.setCacheObject(key.toString(), DUPLICATE_TOKEN_KEY, 5
                        , TimeUnit.SECONDS);
            }
        }
    }
}

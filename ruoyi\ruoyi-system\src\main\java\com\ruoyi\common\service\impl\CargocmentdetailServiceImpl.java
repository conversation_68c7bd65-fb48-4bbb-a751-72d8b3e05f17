package com.ruoyi.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.Cargoconsignmentdetail;
import com.ruoyi.common.domain.CargoconsignmentdetailBak;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.vo.CargocmentVO;
import com.ruoyi.common.domain.vo.CargocmentdetailVO;
import com.ruoyi.common.enums.BookingNoteStatusEnum;
import com.ruoyi.common.enums.PayWayEnum;
import com.ruoyi.common.enums.WxOperateStatus;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.CargocmentdetailMapper;
import com.ruoyi.common.mapper.CargoconsignmentdetailBakMapper;
import com.ruoyi.common.mapper.WaterwayCargoMapper;
import com.ruoyi.common.service.CargocmentdetailService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description 运单详情实现
 * <AUTHOR>
 * @Date 2020/8/5 9:52
 */
@Service
public class CargocmentdetailServiceImpl extends ServiceImpl<CargocmentdetailMapper, Cargoconsignmentdetail> implements CargocmentdetailService {
    @Autowired
    CargoconsignmentdetailBakMapper bakMapper;
    @Autowired
    private WaterwayCargoMapper waterwayCargoMapper;

    @Override
    @Transactional
    public void startAudit(CargocmentdetailBO cargocmentdetailBO) {
        List<Long> detailIds = cargocmentdetailBO.getDetailIds();
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        String datetime = DateUtils.getTime();
        if (detailIds.size() > 0) {//月结批量审批、退单、改单审批
            Integer auditResult = cargocmentdetailBO.getAuditResult();
            List<Cargoconsignmentdetail> list = new ArrayList<>();//更新列表
            if(cargocmentdetailBO.getAuditFlag().intValue()==1){//退单更新
                for(Long id:detailIds){
                    Cargoconsignmentdetail detail = new Cargoconsignmentdetail();
                    detail.setId(id);
                    if(auditResult.intValue()==0){
//                        detail.setWxApplyModifyAudit(WxOperateStatus.CHARGEBACK_FAIL_CHECK.getCode());
//                        detail.setWxApplyAuditman(loginUser.getNickName());
//                        detail.setWxApplyAudittime(datetime);
                    }else if(auditResult.intValue()==1){
//                        detail.setWxApplyModifyAudit(WxOperateStatus.CHARGEBACK_PASS_CHECK.getCode());
                        detail.setApplyModify(BookingNoteStatusEnum.CHARGEBACK_ING_CHECK.getCode().toString());
//                        detail.setWxApplyAuditman(loginUser.getNickName());
//                        detail.setWxApplyAudittime(datetime);
                    }else{
                        throw new CustomException("参数有误");
                    }
                    detail.setWxUpdateById(loginUser.getUserId());
                    detail.setWxUpdateTime(datetime);
                    list.add(detail);
                }
                this.updateBatchById(list);

            }else if(cargocmentdetailBO.getAuditFlag().intValue()==2){//改单审核
                for(Long id:detailIds){
                    Cargoconsignmentdetail detail = new Cargoconsignmentdetail();
                    detail.setId(id);
                    if(auditResult.intValue()==0){//审核不通过
                        //不同意则数据还原
                        List<CargoconsignmentdetailBak> cargoconsignmentdetailBaks = bakMapper.selectList(new QueryWrapper<CargoconsignmentdetailBak>()
                                .eq("id", id)
                                .orderByDesc("BAKTIME"));
                        if(cargoconsignmentdetailBaks.size()>0){
                            CargoconsignmentdetailBak cargoconsignmentdetailBak = cargoconsignmentdetailBaks.get(0);
//                            cargoconsignmentdetailBak.setWxApplyman(loginUser.getNickName());
//                            cargoconsignmentdetailBak.setWxApplytime(datetime);
                            cargoconsignmentdetailBak.setFailureReasons(cargocmentdetailBO.getAuditReasons()+",审核不通过，数据已还原。");
                            bakMapper.updateById(cargoconsignmentdetailBak);
                        }
                        //审核不通过，则进行数据还原操作，
//                        detail.setWxApplyModifyAudit(WxOperateStatus.CHARGEBACK_FAIL_CHECK.getCode());
//                        detail.setWxApplyAuditman(loginUser.getNickName());
//                        detail.setWxApplyAudittime(datetime);
//                        CargoconsignmentdetailBak detailBak = bakMapper.selectById(id);
//                        detail.setChargeBalanceType(detailBak.getChargeBalanceType());
//                        detail.setRationWeight(detailBak.getRationWeight());
//                        detail.setWxRationContactNumber(detailBak.getWxRationContactNumber());
//                        detail.setWxAppointmentTime(detailBak.getWxAppointmentTime());
//                        detail.setWxMonthChargeById(detailBak.getWxMonthChargeById());
//                        detail.setWxMonthChargeByName(detailBak.getWxMonthChargeByName());
//                        detail.setWxUpdateById(loginUser.getUserId());
//                        detail.setWxUpdateTime(datetime);
                        //this.updateById(detail);//还原数据
                        //bakMapper.deleteById(id);//删除备份数据

                    }else if(auditResult.intValue()==1){
//                        detail.setWxApplyModifyAudit(WxOperateStatus.CHARGEBACK_PASS_CHECK.getCode());
                        detail.setApplyModify(BookingNoteStatusEnum.MODIFICATION_ING_CHECK.toString());
//                        detail.setWxApplyAuditman(loginUser.getNickName());
//                        detail.setWxApplyAudittime(datetime);
                        detail.setWxUpdateById(loginUser.getUserId());
                        detail.setWxUpdateTime(datetime);
                        this.updateById(detail);//通过数据
                        //bakMapper.deleteById(id);//删除备份数据
                    }else{
                        throw new CustomException("参数有误");
                    }

                    //list.add(detail);
                }
                //this.updateBatchById(list);

            }else if(cargocmentdetailBO.getAuditFlag().intValue()==3) {//月结待审核列表查询
                int audit;
                if(auditResult.intValue()==0){
                    audit= WxOperateStatus.COMPANY_FAIL_CHECK.getCode();

                }else if(auditResult.intValue()==1){
                    audit=WxOperateStatus.WAIT_BARGE_RESERVE.getCode();
                }else{
                    throw new CustomException("参数有误");
                }
                for(Long id:detailIds){
                    Cargoconsignmentdetail detail = new Cargoconsignmentdetail();
                    detail.setId(id);
                    detail.setChargeBalanceType(PayWayEnum.MONTHLY_PAY.getCodeName());
                    detail.setWxOperateState(audit);
                    detail.setWxUpdateById(loginUser.getUserId());
                    detail.setWxUpdateTime(datetime);
                    list.add(detail);
                }
                this.updateBatchById(list);

                if (audit == WxOperateStatus.WAIT_BARGE_RESERVE.getCode()) {
                    // pb6_waterwaycargo.Cargosize写入月结公司名称
                    list.forEach(detail -> {
                        Cargoconsignmentdetail csd = this.getById(detail.getId());
                        WaterwayCargo waterwayCargo = waterwayCargoMapper.selectOne(new QueryWrapper<WaterwayCargo>()
                                .eq("WATERWAYCARGOID", csd.getWaterwayCargoId()));
                        waterwayCargo.setCARGOSIZE(csd.getWxMonthChargeByName());
                        waterwayCargo.setCHARGEBALANCETYPE(PayWayEnum.MONTHLY_PAY.getCodeName());
                        waterwayCargoMapper.updateById(waterwayCargo);
                    });
                }
            }
        }
    }
    /**
     * 查询待审核的运单列表（1-退单，2-改单 3-月结）
     */
    @Override
    public IPage<CargocmentdetailVO> auditList(CargocmentdetailBO cargocmentdetailBO) {

        Long pageNum= Optional.ofNullable(cargocmentdetailBO.getPageNum()).orElse(0L);
        Long pageSize= Optional.ofNullable(cargocmentdetailBO.getPageSize()).orElse(-1L);

        if(cargocmentdetailBO.getAuditFlag().intValue()==1){//退单列表查询
            cargocmentdetailBO.setAuditState(WxOperateStatus.CHARGEBACK_WAIT_CHECK.getCode());
            return baseMapper.auditList(new Page<>(pageNum,pageSize), cargocmentdetailBO);
        }else if(cargocmentdetailBO.getAuditFlag().intValue()==2){//改单待审核列表查询
            cargocmentdetailBO.setAuditState(WxOperateStatus.CHARGEBACK_WAIT_CHECK.getCode());
            return baseMapper.auditList(new Page<>(pageNum,pageSize), cargocmentdetailBO);
        }else if(cargocmentdetailBO.getAuditFlag().intValue()==3){//月结待审核列表查询
            //cargocmentdetailBO.setWxOperateState(WxOperateStatus.WAIT_COMPANY_CHECK.getCode());
            return baseMapper.getMonthAuditList(new Page<>(pageNum,pageSize), cargocmentdetailBO);
        }else if(cargocmentdetailBO.getAuditFlag()==4){//差额退款


        }

        return null;
    }

    /**
     *  待确认-物流公司审核处理结果
     * @param cargoconsignmentdetail
     */
    @Override
    public void undeterminedDispose(Cargoconsignmentdetail cargoconsignmentdetail) {
        Cargoconsignmentdetail newCargoconsignmentdetail = new Cargoconsignmentdetail();
        if (Integer.valueOf(cargoconsignmentdetail.getApplyModify()).equals(BookingNoteStatusEnum.MODIFICATION_YES_CHECK.getCode())) {
            newCargoconsignmentdetail.setId(cargoconsignmentdetail.getId());
            newCargoconsignmentdetail.setWxOperateState(WxOperateStatus.WAIT_PAY.getCode());
            baseMapper.updateById(newCargoconsignmentdetail);
        }

        // TODO 保存失败的原因到托运到操作日志表中
    }


    @Override
    public List<CargocmentVO> bargebillsList(CargocmentdetailBO cargocmentdetailBO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        cargocmentdetailBO.setUserId(user.getUserId());
        return baseMapper.bargebillsList(cargocmentdetailBO);
    }
}

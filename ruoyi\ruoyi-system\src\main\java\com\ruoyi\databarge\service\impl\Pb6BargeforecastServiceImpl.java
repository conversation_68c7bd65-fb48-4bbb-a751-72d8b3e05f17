package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.databarge.domain.Pb6Bargeforecast;
import com.ruoyi.databarge.mapper.Pb6BargeforecastMapper;
import com.ruoyi.databarge.service.Pb6BargeforecastService;
import org.springframework.stereotype.Service;

@Service
public class Pb6BargeforecastServiceImpl extends ServiceImpl<Pb6BargeforecastMapper, Pb6Bargeforecast> implements Pb6BargeforecastService {

    @Override
    public Pb6Bargeforecast searchPb6BargeforecastByWaterwaycargoid(String waterwaycargoid){
        QueryWrapper<Pb6Bargeforecast> queryWrapper =new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb6Bargeforecast::getWaterwaycargoid,waterwaycargoid);
        return super.getOne(queryWrapper);
    }
}

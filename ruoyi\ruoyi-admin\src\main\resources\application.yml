# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.0.0
  # 版权年份
  copyrightYear: 2019
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
#  profile: D:/ruoyi/uploadPath
  profile: /home/<USER>/uploadPath
  # 获取ip地址开关
  addressEnabled: false

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080 定时任务8081
  port: 1220
  servlet:
    # 应用的访问路径
    context-path: /gsapp
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid,redis,ftp,wechart
    #active: druid-dev,redis-dev,ftp-dev,wechart
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 50MB
      # 设置总上传的文件大小
      max-request-size: 100MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: *********** #************* #**************
    #host: **************
    # 端口，默认为6379
    port: 6379 #26379 #6379
    # 密码
    password: Cw123456
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  # email
  mail:
    host: smtp.qiye.163.com
    username: <EMAIL>
    password: KrWf6ySXY4HMdELz
    protocol: smtp
    properties.mail.smtp.port: 465
    default-encoding: UTF-8
    properties.mail.smtp.auth: true
    properties.mail.smtp.ssl.enable: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 120

## MyBatis配置
mybatis:
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

#mybatis-plus
mybatis-plus:
  type-handlers-package: com.ruoyi.**.domain
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.ruoyi.**.domain
  configuration:
    # 开启驼峰命名
    map-underscore-to-camel-case: true
    jdbc-type-for-null: 'null'

# PageHelper分页插件
pagehelper:
  helperDialect: oracle
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

sso:
  login: https://online.gzport.com
  authentication: https://online.gzport.com/sso
  bargecompany: https://bulkcustomer.gzport.com/shipmanager/middleware

dataLocal:
  barge: https://bulkbarge.gzport.com
  cargo: https://bulkcustomer.gzport.com


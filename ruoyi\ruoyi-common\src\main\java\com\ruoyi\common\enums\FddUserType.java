package com.ruoyi.common.enums;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/28 0:26
 */
public enum FddUserType {

    GUANGZHOU_PORT("0", "广州港"),
    BARGE_USER("1", "驳船主"),
    SHIP_COMPANY_USER("2", "船公司"),
    SHIP_COMPANY_BARGE("3", "船公司驳船");

    private final String code;

    private final String codeName;

    FddUserType(String code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public String getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }
}

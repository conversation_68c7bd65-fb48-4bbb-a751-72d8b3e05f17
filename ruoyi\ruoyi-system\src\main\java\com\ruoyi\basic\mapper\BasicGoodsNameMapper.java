package com.ruoyi.basic.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.basic.domain.BasicGoodsName;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【BASIC_GOODS_NAME】的数据库操作Mapper
* @createDate 2023-11-16 11:43:52
* @Entity com.ruoyi.project.basic.domain.BasicGoodsName
*/
public interface BasicGoodsNameMapper extends BaseMapper<BasicGoodsName> {

    // 货名List
    List<BasicGoodsName> selectBasicGoodsNameList(BasicGoodsName basicGoodsName);

    // 根据货类ID和货名查询货名信息
    BasicGoodsName selectBasicGoodsNameByCategoryIdAndName(BasicGoodsName basicGoodsName);

    // 根据货名查询货名信息
    BasicGoodsName selectBasicGoodsNameByName(String goodsName);

    // 根据货类查询货名信息
    List<BasicGoodsName> selectBasicGoodsNameByGoodsCategory(String goodsCategory);

    // 根据货类id删除所有货名
    AjaxResult deleteBasicGoodsNameByCategoryId(String goodsCategoryId);

}





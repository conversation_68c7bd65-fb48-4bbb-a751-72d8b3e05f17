package com.ruoyi.databarge.pay;

import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class PayUtil {
    public static final String BASE_URL = "https://epay.gzport.com/Epay/SendMsgServlet";
    //    public static final String BASE_URL="http://222.249.226.92:4007/Epay/SendMsgServlet";
    public static final String GATEWAY_PAY_URL = "https://epay.gzport.com/gzgpaym/pay.html";
    public static final String SECURITY_KEY = "be0531c44b5b47faa6b51bbd47b3acb5";  //驳船主小程序
    public static final String CHAIN_ID = "BC0101"; //驳船主小程序

    public static final String SECURITY_KEY_BARGE_COMPANY = "d21ae6a272a84dcc92614b49c86e8175"; //船公司（货主）小程序
    public static final String CHAIN_ID_BARGE_COMPANY = "BC0102"; //船公司（货主）小程序

    public static final String COMPANY_ID_XS = "2005";
    public static final String COMPANY_ID_LS = "2503";
    public static final String COMPANY_ID_XG = "2500";
    public static final String COMPANY_ID_HP = "2502";
    public static final String APPLY_TRAN_CODE = "GZG001"; //创建订单
    public static final String GET_ORDER_TRAN_CODE = "GZG002"; //查询订单
    public static final String REFUND_TRAN_CODE = "GZG004"; //退款
    public static final String REFUND_GET_TRAN_CODE = "GZG025";
    //    public static final String REDIRECT_URL = "https://bulkbarge.gzport.com/prod-api/barge/pay/redirect";// 统一回调URL
    public static final String REDIRECT_URL = "/pages/consignment/reservation/reservation";// 驳船主小程序统一回调URL

    public static final String REDIRECT_URL_BARGE_COMPANY = "/pages/consigncontact/reservation/reservation";// 船公司（货主）小程序统一回调URL

    private AtomicInteger atomicInteger = new AtomicInteger(21);

    @Autowired
    private RestTemplate restTemplate;

    /**
     * {
     *     "reqmsg": {
     *         "reqhead": {
     *             "trancode": "GZG001",
     *             "sendtime": "2020-07-20 11:00:00",
     *             "chanid": "BS0101",
     *             "chanflow": "BS0101202007211"
     *         },
     *         "cdtrid": "2005",
     *         "amt": "0.01",
     *         "billid": "1",
     *         "billdesc": "洪小林的订单",
     *         "operuser": "hongjj11",
     *         "remark": "备注",
     *         "reqtype":"h5",
     *         "costcode":null,
     *         "resulturl": "https://vcbooking.gzport.com/prod-api/pay/redirect",
     *         "paymentnm":"王伦辉",
     *         "paymentacct":"wlh",
     *         // chanid chanflow cdtrid amt security_key
     *         "signature":"733e94ae97956475858d9a2e79a4c7ab0b63e2c5ff14b204b4c91a9f99d19d77"
     *     }
     * }
     *
     * {
     *     "resmsg": {
     *         "reshead": {
     *             "procd": "0000",
     *             "proinfo": "处理成功"
     *         },
     *         "gatewayurl": "https://epay.gzport.com/gzgpaym/pay.html",
     *         "signature": "1cff26ee137c778191d02c4c53228942f9b8cb9874b2408936b820ce61e700c4",
     *         "orderid": "O20200721150001797678",
     *         "reserved1": "6129AFAFFA58C23866140A6672FA04E17FBDC9A0F21617D3D4E97CA539C11A36"
     *     }
     * }
     */
//    public PayApplyVO apply(String amount, String description){
//        LocalDateTime localDateTime=LocalDateTime.now();
//
//        String chanflow=CHAIN_ID+localDateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"))+atomicInteger.getAndIncrement();
//
//        PayApplyDTO payApplyDTO = PayApplyDTO
//                .builder()
//                .reqmsg(PayApplyDTO
//                        .Reqmsg
//                        .builder()
//                        .reqhead(Reqhead
//                                .builder()
//                                .trancode(APPLY_TRAN_CODE)
//                                .sendtime(localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
//                                .chanid(CHAIN_ID)
//                                .chanflow(chanflow)
//                                .build())
//                        .cdtrid(COMPANY_ID)
//                        .amt(amount)
//                        .billid(null)
//                        .billdesc(description)
//                        .operuser(null)
//                        .remark(null)
//                        .reqtype("h5")
//                        .costcode(null)
//                        .resulturl("https://vcbooking.gzport.com/prod-api/pay/redirect")
//                        .paymentnm(null)
//                        .paymentacct(null)
//                        .signature(Sha256Util.getSha256(CHAIN_ID+chanflow+COMPANY_ID+amount+SECURITY_KEY))
//                        .build())
//                .build();
//
//        System.out.println(JSON.toJSONString(payApplyDTO));
//
//        PayApplyVO payApplyVO=restTemplate.postForObject(BASE_URL,payApplyDTO,PayApplyVO.class);
//        return payApplyVO;
//    }
//
//    public String getGatewayPayUrl(String orderid){
//        return GATEWAY_PAY_URL+"?orderid="+orderid+"&chanid="+CHAIN_ID+"&reqtype=h5";
//    }

    /**
     * {
     *     "reqmsg": {
     *         "reqhead": {
     *             "trancode": "GZG002",
     *             "sendtime": "2020-07-21 15:20:20",
     *             "chanid": "BS0101",
     *             // chanid orgorderid
     *             "chanflow": "BS0101O20200721150001797678"
     *         },
     *         "orgorderid": "O20200721150001797678",
     *         "orgchanflow": "BS0101202007211",
     *         // "signature": "chanid chanflow orgorderid orgchanflow security_key"
     *         "signature": "b2f272645c9ab9ee1fd51b8901b59f0bfbc2d5542a36e4d02ba310611a84c3c2"
     *     }
     * }
     *
     * {
     *     "resmsg": {
     *         "reshead": {
     *             "procd": "0000",
     *             "proinfo": "处理成功"
     *         },
     *         "orderid": "O20200721150001797678",
     *         "sts": "01",
     *         "date": "2020-07-21",
     *         "amt": "0.01",
     *         "cdtrid": "2005",
     *         "cdtrnm": "船舶代理部-新沙点",
     *         "payorgnm": "微信/支付宝",
     *         "billid": "1",
     *         "billdesc": "洪小林的订单",
     *         "payid": "9951595315805078171212922",
     *         "paytime": "2020-07-21 15:17:50",
     *         "remark": "备注",
     *         "busprocd": "0000",
     *         "busprodesc": "处理成功",
     *         "payway": "020202|020102|020101",
     *         "resulturl": "https://vcbooking.gzport.com/prod-api/pay/redirect",
     *         "signature": "497fe31381d20a30d7ab7750793b8ef3726577752545a230d22b34a8276d54e3"
     *     }
     * }
     */
//    public PayApplyGetVO getApply(String oriOrderid, String oriChanflow){
//        LocalDateTime localDateTime=LocalDateTime.now();
//
//        String chanflow=CHAIN_ID+oriOrderid;
//
//        PayApplyGetDTO payApplyGetDTO = PayApplyGetDTO
//                .builder()
//                .reqmsg(PayApplyGetDTO
//                        .Reqmsg
//                        .builder()
//                        .reqhead(Reqhead
//                                .builder()
//                                .trancode(GET_ORDER_TRAN_CODE)
//                                .sendtime(localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
//                                .chanid(CHAIN_ID)
//                                .chanflow(chanflow)
//                                .build())
//                        .orgorderid(oriOrderid)
//                        .orgchanflow(oriChanflow)
//                        .signature(Sha256Util.getSha256(CHAIN_ID+chanflow+oriOrderid+oriChanflow+SECURITY_KEY))
//                        .build())
//                .build();
//
//        System.out.println(JSON.toJSONString(payApplyGetDTO));
//
//        PayApplyGetVO payApplyGetVO=restTemplate.postForObject(BASE_URL,payApplyGetDTO,PayApplyGetVO.class);
//        return payApplyGetVO;
//    }

    /**
     * {
     *     "reqmsg": {
     *         "reqhead": {
     *             "trancode": "GZG004",
     *             "sendtime": "2020-07-21 15:20:20",
     *             "chanid": "BS0101",
     *             // 新的流水号，不是之前的
     *             "chanflow": "BS0101202007212"
     *         },
     *         "cdtrid":"2005",
     *         "refundamt":"0.01",
     *         // 之前的流水号，不是新的
     *         "chanflow":"BS0101202007211",
     *         "operuser":"root",
     *         "remark":"无",
     *         // chanid reqmsg.chanflow refundamt security_key
     *         "signature": "3eb17d73e7a59e77ff896c29fdb59f84e96bacb7261db61ff4becc103b978ba6"
     *     }
     * }
     *
     * {
     *     "resmsg": {
     *         "reshead": {
     *             "procd": "0000",
     *             "proinfo": "处理成功"
     *         },
     *         "refundid": "O20200721150001798668",
     *         "refundamt": "0.01",
     *         "refundsts": "04",
     *         "signature": "13a1b2d690eecec06868b30fd39f57b6f54205779fd075558d2b1b8cab86bf47"
     *     }
     * }
     */
//    public PayRefundVO refund(String amount,String oriChanflow){
//        LocalDateTime localDateTime=LocalDateTime.now();
//
//        String newChanflow=CHAIN_ID+localDateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"))+atomicInteger.getAndIncrement();
//
//        PayRefundDTO payRefundDTO=PayRefundDTO
//                .builder()
//                .reqmsg(PayRefundDTO.Reqmsg
//                        .builder()
//                        .reqhead(Reqhead
//                                .builder()
//                                .trancode(REFUND_TRAN_CODE)
//                                .sendtime(localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
//                                .chanid(CHAIN_ID)
//                                .chanflow(newChanflow)// 新的流水号，不是之前的
//                                .build())
//                        .cdtrid(COMPANY_ID)
//                        .refundamt(amount)
//                        .chanflow(oriChanflow)// 之前的流水号，不是新的
//                        .operuser(null)
//                        .remark(null)
//                        .signature(Sha256Util.getSha256(CHAIN_ID+oriChanflow+amount+SECURITY_KEY))
//                        .build())
//                .build();
//
//        System.out.println(JSON.toJSONString(payRefundDTO));
//
//        PayRefundVO payRefundVO=restTemplate.postForObject(BASE_URL,payRefundDTO,PayRefundVO.class);
//
//        return payRefundVO;
//    }

    /**
     * {
     *     "reqmsg": {
     *         "reqhead": {
     *             "trancode": "GZG025",
     *             "sendtime": "2020-07-20 14:20:20",
     *             "chanid": "BS0101",
     *             "chanflow": "BS0101O20200721150001797678"
     *         },
     *         "orderno":"O20200721150001797678"
     *     }
     * }
     *
     * {
     *     "resmsg": {
     *         "reshead": {
     *             "procd": "0000",
     *             "proinfo": "处理成功"
     *         },
     *         "list": {
     *             "orderno": "O20200721150001797678",
     *             "refundamt": "0.01",
     *             "refundsts": "04",
     *             "refundtime": "2020-07-21",
     *             "reorderno": "O20200721150001798668"
     *         }
     *     }
     * }
     */
//    public PayRefundGetVO getRefund(String orderid){
//        LocalDateTime localDateTime=LocalDateTime.now();
//
//        PayRefundGetDTO payRefundGetDTO=PayRefundGetDTO
//                .builder()
//                .reqmsg(PayRefundGetDTO.Reqmsg
//                        .builder()
//                        .reqhead(Reqhead
//                                .builder()
//                                .trancode(REFUND_GET_TRAN_CODE)
//                                .sendtime(localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
//                                .chanid(CHAIN_ID)
//                                .chanflow(CHAIN_ID+orderid)
//                                .build())
//                        .orderno(orderid)
//                        .build())
//                .build();
//
//        System.out.println(JSON.toJSONString(payRefundGetDTO));
//
//        PayRefundGetVO payRefundGetVO=restTemplate.postForObject(BASE_URL,payRefundGetDTO,PayRefundGetVO.class);
//        return payRefundGetVO;
//    }
}

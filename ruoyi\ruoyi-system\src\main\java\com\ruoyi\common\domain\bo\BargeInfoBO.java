package com.ruoyi.common.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.domain.UploadAddress;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/7/29 22:22
 */
@Data
public class BargeInfoBO extends BaseEntity {

    /**
     * 驳船id主键
     */
    private Long id;

    private Long userId;//当前用户id
    private String userName;//当前用户名称

    /**
     * 船舶识别号
     */
    @NotBlank(message = "船舶识别号不能为空")
    private String bargeId;

    /**
     * 驳船名称
     */
    @NotBlank(message = "驳船名称不能为空")
    private String bargeName;

    /**
     * 船舶载货量（A级）
     */
    private String bargeLoadA;

    /**
     * 船舶载货量（B级）
     */
    private String bargeLoadB;

    /**
     * 证书有效期
     */
    private String validSailDate;

    /**
     * 船籍港
     */
    private String belongArea;

    /**
     * 驳船所有人
     */
    private String bargeOwner;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 录入人
     */
    private String recorder;

    /**
     * 驳船经营人
     */
    private String bargeOperator;

    /**
     * MMSI标识
     */
    private String mmsi;

    /**
     * 驳船净吨
     */
    private String bargeWeight;

    /**
     * 驳船所属公司id
     */
    private Long bargeCompanyId;
    /**
     * 审核标识(1.备案，2，修改备案)
     */
    private Integer checkFlag;
    /**
     * 备案审核(0：待审核，1：审核通过 2.审核不通过(待审核备份数据保留))
     */
    private Integer recordCheck;
    /**
     * 修改审核(0：待审核，1：审核通过 2.审核不通过(待审核备份数据保留))
     */
    private Integer updateCheck;

    /**
     * 挂靠审核（0-修改挂靠，1-挂靠）
     */
    private Integer isAudit;
    /**
     * 绑定关系是否已生效状态（0-无效，1-生效）
     */
    private Integer status;
    /**
     * 修改审核(0：待审核，1：审核通过 2.审核不通过(待审核备份数据保留))
     */
    private Integer auditStatus;

    /**
     * 0-待审核，1-审核通过 2-审核不通过 null-全部
     */
    private Integer checkState;//前端查询的条件筛选

    /**
     * 选择派船时填写的配载量
     */
    private String rationWeight;//配载重量


    private String rationPiece; //配载件数
    /**
     * 驳船主联系电话
     */
    private String bargeTel;
    /**
     * 托运单联系人
     */
    private String wxRationContactNumber;//托运单联系人

    /**
     * 驳船顺序号，添加托运单详情驳船信息时 用到
     */
    private String serialNumber;

    /**
     * 驳船托运单的结算方式
     */
    private String chargeBalanceType;//结算方式

    private Integer wxMonthChargeById;//结算船公司id

    private String wxMonthChargeByName;//结算船公司

    /**
     * 托运单明细id
     */
    private Long cargoConsignmentDetailId;

    private List<UploadAddress> dataList;

    private List<UploadAddress> delDataList;

    /**
     * 绑定类型（1-自有、2-临时挂靠、3-长期挂靠，4-其他）
     */
    private Integer bindingType;

    /**
     * 驳船类型 1-自由船 2-挂靠船
     */
    private String bargeType;

    /**
     * 驳船挂靠公司id
     */
    private Long companyId;

    /**
     * 1.查询船公司已有的驳船(只是已审核过的驳船)  2.查询当前船公司用户有关的船（包含船公司的船及与当前用户有管的船、包含待审核中的驳船）
     */
    private String searchBargeType;//船公司使用

    /**
     * 印章图片 base64
     */
    private String sealImg;

    /**
     * 印章存放路径
     */
    private String sealPath;

    // 装载重量最小
    @TableField("LOADING_WEIGHT_MIN")
    private String loadingWeightMin;

    // 装载重量最大
    @TableField("LOADING_WEIGHT_MAX")
    private String loadingWeightMax;

    // 不允许装载货物
    @TableField("CARGO_NOT_ALLOWED")
    private String cargoNotAllowed;
}

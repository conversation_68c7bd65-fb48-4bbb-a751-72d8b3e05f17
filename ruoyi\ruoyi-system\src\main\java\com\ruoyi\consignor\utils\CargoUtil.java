package com.ruoyi.consignor.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.cglib.CglibUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.*;
import com.ruoyi.common.domain.bo.CargocmentdetailBO;
import com.ruoyi.common.domain.dto.CargocmentDTO;
import com.ruoyi.common.enums.*;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.mapper.*;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 托运单操作 部分工具类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-09 14:32
 */
@Slf4j
@Component
public class CargoUtil {

    @Autowired
    private CargocmentdetailMapper cargocmentdetailMapper;

    @Autowired
    private BargeCheckNoteMapper bargeCheckNoteMapper;

    @Autowired
    private BargeCheckMessageMapper bargeCheckMessageMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private UploadAddressMapper uploadAddressMapper;

    @Autowired
    private CargocmentMapper cargocmentMapper;

    @Autowired
    private CargoconsignmentdetailBakMapper cargoconsignmentdetailBakMapper;

    @Autowired
    private CargoconsignmentBakMapper cargoconsignmentBakMapper;

    @Autowired
    private BargeInfoMapper bargeInfoMapper;

    /**
     * 退单改单方法
     *
     * @param cargocmentDTO cargoconsignmentdetail - id 托运单明细id必填
     *                      cargoconsignment - id 托运单主表id必填
     *                      state 退改单标识必填 1-退单，2-改单
     *                      uploadAddress 文件信息
     * @return 响应状态
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult modify(CargocmentDTO cargocmentDTO) {
        // 数据校验
        checkCargo(cargocmentDTO);

        // 待修改托运单明细数据
        Cargoconsignmentdetail detailModified = cargocmentDTO.getCargoconsignmentdetail();

        // 托运单明细原有的数据
        Cargoconsignmentdetail detailOld = cargocmentdetailMapper.selectById(detailModified.getId());
        if (StringUtils.isNull(detailOld)) {
            String msg = StringUtils.format("该托运单明细(id:{})不存在", detailModified.getId());
            log.error(msg);
            throw new CustomException(msg);
        }

        // 待修改托运单主表数据
        Cargoconsignment consignmentModified = cargocmentDTO.getCargoconsignment();

        // 托运单主表原有的数据
        Cargoconsignment consignmentOld = cargocmentMapper.selectById(consignmentModified.getId());
        if (StringUtils.isNull(consignmentOld)) {
            String msg = StringUtils.format("该托运单主表(id:{})不存在", detailModified.getId());
            log.error(msg);
            throw new CustomException(msg);
        }

        // 托运单创建人名称
        SysUser createCargoUser = sysUserMapper.selectUserById(Convert.toLong(consignmentOld.getWxCreateUserById()));
        String userName = null;
        if (StringUtils.isNotNull(createCargoUser)) {
            userName = createCargoUser.getUserName();
        }

        BargeCheckNode bargeCheckNode = new BargeCheckNode();
        BargeCheckMessage bargeCheckMessage = new BargeCheckMessage();

        // 1-退单，2-改单
        Integer state = cargocmentDTO.getState();

        switch (state) {
            case 1:
                // 退单
                cargoChargeback(detailOld);

                // 保存改单/退单 BargeCheckNote表操作记录
                bargeCheckNode.setWxNodeFlag(0);
                bargeCheckNode.setWxNode(0);
                bargeCheckNodeOfCargo(bargeCheckNode, userName, detailModified.getId());

                // 保存改单/退单 BargeCheckMessage表操作记录
                if (FlagBargeState.REPORT.getCode().equals(detailOld.getFlagBargeState())) {
                    bargeCheckMessage.setMType(BargeCheckMessageType.CHARGEBACK_DOCK_AUDIT.getCode().shortValue());
                } else {
                    bargeCheckMessage.setMType(BargeCheckMessageType.RETURNED_ORDER_LOGISTICS_COMPANY_CHECK.getCode().shortValue());
                }
                bargeCheckMessageOfCargo(bargeCheckMessage, createCargoUser, detailOld);

                log.info("退单成功");
                return AjaxResult.success("退单成功");
            case 2:
                // 改单
                cargoModify(cargocmentDTO.getUploadAddress(), createCargoUser, detailOld, detailModified, consignmentOld, consignmentModified);

                // 判断是已报到还是已审核或已配载进度
                if (FlagBargeState.REPORT.getCode().equals(detailOld.getFlagBargeState())) {
                    bargeCheckNode.setWxNode(WxNodeEnum.DIRECT_AUDIT_BY_LOGISTICS_COMPANY.getCode());
                    bargeCheckMessage.setMType(BargeCheckMessageType.CHANGE_DOCK_AUDIT.getCode().shortValue());
                } else {
                    bargeCheckNode.setWxNode(WxNodeEnum.SUBMIT_AUDIT.getCode());
                    bargeCheckMessage.setMType(BargeCheckMessageType.CHANGE_LOGISTICS_COMPANY_AUDIT.getCode().shortValue());
                }
                bargeCheckNode.setWxNodeFlag(1);
                bargeCheckNodeOfCargo(bargeCheckNode, userName, detailModified.getId());
                bargeCheckMessageOfCargo(bargeCheckMessage, createCargoUser, detailOld);

                log.info("改单成功");
                return AjaxResult.success("改单成功");
            default:
                log.error("未知退改单标识");
                throw new CustomException("未知退改单标识");
        }
    }

    /**
     * 取消退改单
     *
     * @param cargocmentdetailBO stateOperation - 1.取消改单, 2.取消退单 - 必填
     *                           WXnode - 0提交审核，1物流公司直接审核，2码头审核， 3物流公司间接审核 - 审核节点状态必填
     *                           applyModify - 必填 - 营业厅申请改单/退单（0为申请退单中，1为申请退单通过，2为申请退单失败,3为申请改单中，
     *                           4为申请改单成功，5为申请改单失败
     *                           modifyReason - 退改单原因
     * @return 响应状态
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AjaxResult cancel(CargocmentdetailBO cargocmentdetailBO) {

        if (StringUtils.isNull(cargocmentdetailBO.getStateOperation())
                || StringUtils.isNull(cargocmentdetailBO.getId())) {
            log.error("明细单ID 或 (退/改单)取消状态不能为空");
            throw new CustomException("明细单ID 或 (退/改单)取消状态不能为空");
        }


        Cargoconsignmentdetail cargoconsignmentdetail = cargocmentdetailMapper.selectById(cargocmentdetailBO.getId());
        if (StringUtils.isNotNull(cargoconsignmentdetail)) {
            if (FlagBargeState.ALTER.getCode().equals(cargoconsignmentdetail.getFlagBargeState())
                    || FlagBargeState.CHARGEBACK.getCode().equals(cargoconsignmentdetail.getFlagBargeState())) {
                log.error("托运单已经改单成功或退款成功");
                throw new CustomException("托运单已经改单成功或退款成功");
            }
        }

        // 查询是否已经码头审核过了
        List<BargeCheckMessage> bargeCheckMessages = bargeCheckMessageMapper.selectList(new LambdaQueryWrapper<BargeCheckMessage>()
                .eq(BargeCheckMessage::getPb6CargoConsignmentDetailId, cargocmentdetailBO.getId()));

        for (BargeCheckMessage bargeCheckMessage : bargeCheckMessages) {
            if ("1".equals(Convert.toStr(bargeCheckMessage.getAuditFlag()))) {
                log.error("码头已经审核成功由物流公司审核，不能取消改单/退单");
                throw new CustomException("码头已经审核成功由物流公司审核，不能取消改单/退单");
            }
        }

        // 删除Messages记录
        List<Long> ids = bargeCheckMessages.stream().map(BargeCheckMessage::getId).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(ids)) {
            bargeCheckMessageMapper.deleteBatchIds(ids);
        }

        // 取消退改单
        cargocmentdetailMapper.cancelOperation(cargocmentdetailBO);

        // 备份文件改成失效
        List<UploadAddress> uploadAddresses = uploadAddressMapper.selectList(new LambdaQueryWrapper<UploadAddress>()
                .eq(UploadAddress::getLinkId, cargoconsignmentdetail.getConsignId())
                .eq(UploadAddress::getBakFlag, "2"));
        if (StringUtils.isNotEmpty(uploadAddresses)) {
            List<Long> addressIds = uploadAddresses.stream().map(UploadAddress::getId).collect(Collectors.toList());
            UploadAddress uploadAddress = new UploadAddress();
            uploadAddress.setStatus(0);
            int i = uploadAddressMapper.update(uploadAddress, new LambdaQueryWrapper<UploadAddress>()
                    .in(UploadAddress::getId, addressIds));
            if (i <= 0) {
                log.error("改单修改文件失效失败");
                throw new CustomException("改单修改文件失效失败");
            }
        }

        // 记录取消申请的记录
        SysUser user = sysUserMapper.selectUserById(cargoconsignmentdetail.getWxCreateById());
        BargeCheckNode bargeCheckNode = new BargeCheckNode();
        bargeCheckNode.setWxNodeFlag(2);
        bargeCheckNode.setWxApplyAuditMan(user.getUserName());
        bargeCheckNode.setWxApplyAuditTime(DateUtils.getTime());
        bargeCheckNode.setAuditStates(CheckEnum.WAIT_CHECK.getCode());
        bargeCheckNode.setLinkId(cargocmentdetailBO.getId());

        int insert = bargeCheckNoteMapper.insert(bargeCheckNode);


        if (insert <= 0) {
            log.error("(退/改单)取消申请记录保存失败");
            throw new CustomException("(退/改单)取消申请记录保存失败");
        }


        log.info("(退/改单)取消成功");
        return AjaxResult.success("(退/改单)取消成功");
    }

    /**
     * 退单操作
     *
     * @param detailOld 托运单原有数据
     */
    private void cargoChargeback(Cargoconsignmentdetail detailOld) {
        Cargoconsignmentdetail cargoconsignmentdetail = new Cargoconsignmentdetail();
        cargoconsignmentdetail.setId(detailOld.getId());
        cargoconsignmentdetail.setApplyModify(BookingNoteStatusEnum.CHARGEBACK_ING_CHECK.getCode().toString());
        cargoconsignmentdetail.setWxNode(WxNodeEnum.SUBMIT_AUDIT.getCode());
        cargoconsignmentdetail.setModifyreason(detailOld.getModifyreason());
        cargoconsignmentdetail.setNewWaybill("0");
        int result = cargocmentdetailMapper.updateById(cargoconsignmentdetail);
        if (result <= 0) {
            String msg = StringUtils.format("托运单明细id:{}，退单失败", detailOld.getId());
            log.error(msg);
            throw new CustomException(msg);
        }
    }

    /**
     * 改单操作
     *
     * @param uploadAddress       文件实体，从
     * @param createCargoUser     创建托运单用户
     * @param detailOld           托运单明细原有数据实体
     * @param detailModified      托运单明细待修改实体
     * @param consignmentOld      托运单主表原有数据实体
     * @param consignmentModified 托运单主表待修改实体
     */
    private void cargoModify(List<UploadAddress> uploadAddress, SysUser createCargoUser,
                             Cargoconsignmentdetail detailOld, Cargoconsignmentdetail detailModified,
                             Cargoconsignment consignmentOld, Cargoconsignment consignmentModified) {


        // 文件信息 处理
        backupCargoData(uploadAddress, consignmentOld.getId(), createCargoUser.getUserId());

        // 备份托运单主表实体
        CargoconsignmentBak cargoconsignmentBak = new CargoconsignmentBak();
        // 备份托运单明细表实体
        CargoconsignmentdetailBak cargoconsignmentdetailBak = new CargoconsignmentdetailBak();

        // 保存备份托运单明细表
        BeanUtils.copyPropertiesIgnoreNull(detailModified, detailOld);
        BeanUtils.copyPropertiesIgnoreNull(detailOld, cargoconsignmentdetailBak);

        // 添加修改信息
        cargoconsignmentdetailBak.setWxUpdateById(createCargoUser.getUserId());
        cargoconsignmentdetailBak.setWxUpdateTime(DateUtils.getTime());


        Cargoconsignmentdetail cargoconsignmentdetail = new Cargoconsignmentdetail();

        // 判断是已报到还是已审核或已配载进度
        if (FlagBargeState.REPORT.getCode().equals(detailOld.getFlagBargeState())) {
            cargoconsignmentdetailBak.setWxNode(WxNodeEnum.DIRECT_AUDIT_BY_LOGISTICS_COMPANY.getCode());
            cargoconsignmentdetailBak.setAuditStates(CheckEnum.WAIT_CHECK.getCode());
            cargoconsignmentdetail.setWxNode(WxNodeEnum.DIRECT_AUDIT_BY_LOGISTICS_COMPANY.getCode());
        } else {
            cargoconsignmentdetailBak.setWxNode(WxNodeEnum.SUBMIT_AUDIT.getCode());
            cargoconsignmentdetailBak.setAuditStates(CheckEnum.WAIT_CHECK.getCode());
            cargoconsignmentdetail.setWxNode(WxNodeEnum.SUBMIT_AUDIT.getCode());
        }
        cargoconsignmentdetail.setAuditStates(CheckEnum.WAIT_CHECK.getCode());

        // 如果是已审核的状态 修改驳船主电话（覆盖PB6_BARGEINFO表的驳船电话）
        if (FlagBargeState.YES_CHECK.getCode().equals(detailOld.getFlagBargeState())) {
            if (detailOld.getBargeId() != null && StringUtils.isNotBlank(detailOld.getBargeTel())) {
                BargeInfo bargeInfoTemp = new BargeInfo();
                bargeInfoTemp.setId(detailOld.getBargeId());
                bargeInfoTemp.setContactPhone(detailOld.getBargeTel());
                bargeInfoMapper.updateById(bargeInfoTemp);
            }
        }

        CargoconsignmentdetailBak bak = cargoconsignmentdetailBakMapper.selectById(cargoconsignmentdetailBak.getId());
        if (StringUtils.isNotNull(bak)) {
            cargoconsignmentdetailBakMapper.updateById(cargoconsignmentdetailBak);
        } else {
            cargoconsignmentdetailBakMapper.insert(cargoconsignmentdetailBak);
        }

        // 保存备份托运单主表
        BeanUtils.copyPropertiesIgnoreNull(consignmentModified, consignmentOld);
        BeanUtils.copyPropertiesIgnoreNull(consignmentOld, cargoconsignmentBak);
        CargoconsignmentBak consignmentBak = cargoconsignmentBakMapper.selectById(cargoconsignmentBak.getId());
        if (StringUtils.isNotNull(consignmentBak)) {
            cargoconsignmentBakMapper.updateById(cargoconsignmentBak);
        } else {
            cargoconsignmentBakMapper.insert(cargoconsignmentBak);
        }

        // 设置改单申请
        cargoconsignmentdetail.setId(detailModified.getId());
        cargoconsignmentdetail.setApplyModify(BookingNoteStatusEnum.MODIFICATION_ING_CHECK.getCode().toString());
        cargoconsignmentdetail.setModifyreason(detailModified.getModifyreason());
        cargoconsignmentdetail.setNewWaybill("0");
        int result = cargocmentdetailMapper.updateById(cargoconsignmentdetail);
        if (result <= 0) {
            String msg = StringUtils.format("托运单明细id:{}，改单失败", detailOld.getId());
            log.error(msg);
            throw new CustomException(msg);
        }
    }

    /**
     * 保存改单/退单 BargeCheckNote表操作记录
     *
     * @param userName                 该托运单的创建人的名称
     * @param cargoconsignmentdetailId 托运单明细id
     */
    private void bargeCheckNodeOfCargo(BargeCheckNode bargeCheckNode, String userName, Long cargoconsignmentdetailId) {
        bargeCheckNode.setWxApplyMan(userName);
        bargeCheckNode.setWxApplyTime(DateUtils.getTime());
        bargeCheckNode.setAuditStates(Integer.valueOf(FlagBargeState.NO_CHECK.getCode()));
        bargeCheckNode.setLinkId(cargoconsignmentdetailId);
        int result = bargeCheckNoteMapper.insert(bargeCheckNode);
        if (result <= 0) {
            String msg = StringUtils.format("托运单明细id:{}，保存改单/退单 BargeCheckNote表操作记录 失败", cargoconsignmentdetailId);
            log.error(msg);
            throw new CustomException(msg);
        }
    }

    /**
     * 保存改单/退单 BargeCheckMessage表操作记录
     *
     * @param bargeCheckMessage 审核信息实体
     * @param createCargoUser   托运单明细创建人实体
     * @param detailOld         托运单原有数据实体
     */
    private void bargeCheckMessageOfCargo(BargeCheckMessage bargeCheckMessage, SysUser createCargoUser, Cargoconsignmentdetail detailOld) {
        bargeCheckMessage.setApplyManId(createCargoUser.getUserId());
        bargeCheckMessage.setApplyMan(createCargoUser.getUserName());
        bargeCheckMessage.setApplyTime(new Date());
        bargeCheckMessage.setAuditFlag(Short.valueOf(MessageIsRead.UNREAD.getCode()));
        bargeCheckMessage.setBargeName(detailOld.getBargeName());
        bargeCheckMessage.setWaterWayCargoId(detailOld.getWaterwayCargoId());
        bargeCheckMessage.setPb6CargoConsignmentDetailId(detailOld.getId());
        bargeCheckMessage.setConsignFlag(detailOld.getConsignFlag());
        int result = bargeCheckMessageMapper.insert(bargeCheckMessage);
        if (result <= 0) {
            String msg = StringUtils.format("托运单明细id:{}，保存改单/退单 BargeCheckMessage表操作记录 失败", detailOld.getId());
            log.error(msg);
            throw new CustomException(msg);
        }
    }

    /**
     * 设置为备份文件 审核成功才恢复正式文件
     *
     * @param uploadAddress      文件实体
     * @param cargoconsignmentId 托运单主表id
     * @param createCargoUserId  创建托运单的用户id
     */
    private void backupCargoData(List<UploadAddress> uploadAddress, Long cargoconsignmentId, Long createCargoUserId) {
        if (StringUtils.isEmpty(uploadAddress)) {
            return;
        }
        // 关联id 设置为备份文件 审核成功才恢复正式文件
        List<Long> ids = uploadAddress.stream().map(UploadAddress::getId).collect(Collectors.toList());

        // 判断是否有原来的文件
        List<UploadAddress> uploadAddresses = uploadAddressMapper.selectBatchIds(ids);
        for (UploadAddress address : uploadAddresses) {

            UploadAddress uploadAddres = new UploadAddress();
            // bean拷贝
            CglibUtil.copy(address, uploadAddres);

            // 关联id
            uploadAddres.setLinkId(cargoconsignmentId);
            // 改成是备份文件
            uploadAddres.setBakFlag(2);

            if (StringUtils.isNotNull(uploadAddres.getLinkId())) {
                uploadAddres.setId(null);
                uploadAddres.setUploadUserId(createCargoUserId);
                uploadAddres.setUploadTime(new Date());
                int result = uploadAddressMapper.insert(address);
                if (result <= 0) {
                    String msg = StringUtils.format("托运单主表id:{}，关联文件(id:{})失败", cargoconsignmentId, address.getId());
                    log.error(msg);
                    throw new CustomException(msg);
                }
            } else {
                int result = uploadAddressMapper.updateById(uploadAddres);
                if (result <= 0) {
                    String msg = StringUtils.format("托运单主表id:{}，关联文件(id:{})失败", cargoconsignmentId, address.getId());
                    log.error(msg);
                    throw new CustomException(msg);
                }
            }
        }
    }

    /**
     * 数据校验
     *
     * @param cargocmentDTO state 1-退单，2-改单
     *                      cargoconsignmentdetail - id不能为空 - 托运单明细
     */
    private void checkCargo(CargocmentDTO cargocmentDTO) {
        String msg;
        if (StringUtils.isNull(cargocmentDTO)) {
            msg = StringUtils.format("传入实体不能为空，cargocmentDTO：{}", cargocmentDTO);
            log.error(msg);
            throw new CustomException(msg);

        } else if (StringUtils.isNull(cargocmentDTO.getState())) {
            msg = StringUtils.format("退改单标识不能为空，cargocmentDTO.state：{}", cargocmentDTO.getState());
            log.error(msg);
            throw new CustomException(msg);

        } else if (StringUtils.isNull(cargocmentDTO.getCargoconsignmentdetail())
                || StringUtils.isNull(cargocmentDTO.getCargoconsignmentdetail().getId())) {
            msg = StringUtils.format("修改托运单的明细id不能为空，cargocmentDTO.cargoconsignmentdetail：{}", cargocmentDTO.getCargoconsignmentdetail());
            log.error(msg);
            throw new CustomException(msg);

        } else if (StringUtils.isNull(cargocmentDTO.getCargoconsignment())
                || StringUtils.isNull(cargocmentDTO.getCargoconsignment().getId())) {
            msg = StringUtils.format("修改托运单的主表id不能为空，cargocmentDTO.cargoconsignment：{}", cargocmentDTO.getCargoconsignment());
            log.error(msg);
            throw new CustomException(msg);
        }
    }
}

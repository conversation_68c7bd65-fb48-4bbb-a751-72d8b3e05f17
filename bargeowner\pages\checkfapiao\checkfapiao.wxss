/* pages/checkfapiao/checkfapiao.wxss */
.consign-message {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 70rpx;
  border-bottom: 1px solid #ddd;
  padding: 0 30rpx;
  width: 100%;
}
.message-title {
  color: #999;
  font-size: 28rpx;
  flex-shrink: 0;
}
.message-value {
  color: #333;
  font-size: 28rpx;
  flex:1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.consign-title {
  width:100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  padding: 0 30rpx;
  border-bottom: 1px solid #ddd;
  flex-direction: row;
  justify-content: space-between;
}
.consign-other-msg{
  box-sizing: border-box;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #666;
}
.message-title.fapiao-title {
  color: rgb(88, 88, 255);
  font-size: 28rpx;
  text-decoration: underline;
}
.message-value.fapiao-operate {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}
.check-page .operate-btn {
  width: 180rpx;
  height: 50rpx;
  margin: 0;
  padding: 0;
  font-size: 28rpx;
  border: 1px solid #00426B;
  color: #00426B;
  line-height: 46rpx;
  border-radius: 25rpx;
}
.add-message-emailipt{
  width: 100%;
  height:55rpx;
  color: #333;
  padding: 0 20rpx;
  font-size: 30rpx;
  border: 1rpx solid #ddd;
  text-align: left ;
}
package com.ruoyi.app.controller.databarge;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.app.controller.barge.BargeConsignController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.domain.WaterwayCargo;
import com.ruoyi.common.domain.bo.UserMessageBO;
import com.ruoyi.common.enums.BargeCheckMessageType;
import com.ruoyi.common.enums.CheckEnum;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.AppNoticeService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.message.WechatMessageUtil;
import com.ruoyi.databarge.DataScope;
import com.ruoyi.databarge.domain.*;
import com.ruoyi.databarge.domain.dto.Pb6BargeWorkDTO;
import com.ruoyi.databarge.domain.dto.Pb6CargoconsignmentdetailDTO;
import com.ruoyi.databarge.domain.dto.ShipxyDTO;
import com.ruoyi.databarge.domain.vo.Pb11TallysheetVO;
import com.ruoyi.databarge.domain.vo.Pb6BargeWorkVo;
import com.ruoyi.databarge.domain.vo.Pb6WaterCargoVO;
import com.ruoyi.databarge.domain.vo.PortLoadingMsgVO;
import com.ruoyi.databarge.mapper.Pb6BargeworkMapper;
import com.ruoyi.databarge.mapper.Pb6CargoconsignmentdetailMapper;
import com.ruoyi.databarge.service.*;
import com.ruoyi.databarge.shipxy.PortMapPoint;
import com.ruoyi.databarge.wechat.MpMessageDTO;
import com.ruoyi.databarge.wechat.MpMessageResult;
import com.ruoyi.databarge.wechat.MpUtil;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.utils.BargeUtils;
import com.ruoyi.wechat.service.WechatMpAccessTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import oshi.jna.platform.mac.DiskArbitration;

import javax.xml.bind.SchemaOutputResolver;
import java.awt.geom.Point2D;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/barge/bargework")
@Slf4j
public class Pb6BargeworkController {
    @Autowired
    private Pb6CargoconsignmentdetailService pb6CargoconsignmentdetailService;

    @Autowired
    private Pb6BargeinfoService pb6BargeinfoService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private Pb6WaterwaycargoService pb6WaterwaycargoService;

    @Autowired
    private Pb6BargeworkService pb6BargeworkService;

    @Autowired
    private Pb6BargeforecastService pb6BargeforecastService;

    @Autowired
    private Pb6BargeCheckMessageService pb6BargeCheckMessageService;

    @Autowired
    private UploadAddressDomainService uploadAddressDomainService;

    @Autowired
    private Pb6BargeworkMapper pb6BargeworkMapper;

    @Autowired
    private Pb6CargoconsignmentdetailMapper pb6CargoconsignmentdetailMapper;
    @Autowired
    private Pb3CoutformService pb3CoutformService;
    @Autowired
    private PubCoutformreducerecordService pubCoutformreducerecordService;
    @Autowired
    private PubReducerecordService pubReducerecordService;
    @Autowired
    private ISysUserService iSysUserService;
    @Autowired
    private BargeConsignController bargeConsignController;

    @Autowired
    private WechatMpUserService wechatMpUserService;

    @Autowired
    private WechatMpAccessTokenService wechatMpAccessTokenService;

    @Autowired
    private AppNoticeService appNoticeService;

    @Autowired
    private WechatMessageUtil wechatMessageUtil;

    @Autowired
    private MpUtil mpUtil;


    //自动报到
//    @Scheduled(cron = "0 0/10 * * * ?")
    @GetMapping("/bargeworkAdd")
    public void pb6BargeworkAdd() {
        List<Pb6Cargoconsignmentdetail> bargeList = pb6CargoconsignmentdetailService.searchBargeByWxappointmenttime();
        if (bargeList.size() > 0) {
            for (Pb6Cargoconsignmentdetail Pb6Cargoconsignmentdetail : bargeList) {
                //2022.03.01  PB6_CARGOCONSIGNMENTDETAIL.applymodify为0，1，3都不能自动报到
                if (Pb6Cargoconsignmentdetail.getApplymodify() == null) {
                    pb6BargeworkAddMethod(Pb6Cargoconsignmentdetail);
                } else {
                    if (Pb6Cargoconsignmentdetail.getApplymodify().equals("0") || Pb6Cargoconsignmentdetail.getApplymodify().equals("1") || Pb6Cargoconsignmentdetail.getApplymodify().equals("3")) {
                        log.warn(" PB6_CARGOCONSIGNMENTDETAIL.applymodify为0，1，3都不能自动报到");
                    } else {
                        pb6BargeworkAddMethod(Pb6Cargoconsignmentdetail);
                    }
                }
            }
        } else {
            log.warn("没预约驳船");
        }
    }

    //驳船适航证书超过有效期发送消息让重新注册提交
    //@Scheduled(cron = "0 0 10 * * ?")
    public void ValidSailOverTime() throws URISyntaxException {
        List<SysUser> list = iSysUserService.validSailOverTimeUser();
        for (SysUser sysUser : list) {
            Date date = DateUtils.getNowDate();
            if (sysUser != null && com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(sysUser.getUnionId()) && date.compareTo(DateUtils.parseDate(sysUser.getValidsaildate())) > 0) {
                List<WechatMpUser> wechatMpUserList = wechatMpUserService.list(Wrappers.<WechatMpUser>query().eq("unionid", sysUser.getUnionId()));
                if (wechatMpUserList.size() == 0) {
                    throw new CustomException("该用户未关注广州港船务有限公司!");
                } else if (wechatMpUserList.size() == 1) {

                    Map<String, Object> params = new HashMap<>();
                    params.put("first", "您有一个通知请查收");
                    params.put("keyword1", "您的驳船'" + sysUser.getBargename() + "'适航证书已过期，请重新上传");
                    params.put("keyword2", DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss"));
                    params.put("remark", "马上登录小程序吧");

                    //发送公众号消息
                    String accessToken = wechatMpAccessTokenService.getAccessToken();
                    MpMessageDTO mpMessageDTO = MpMessageDTO.builder().touser(wechatMpUserList.get(0).getOpenid())
                            .template_id("mxsKHT1Lvb3dGZmBHPLj0GmsvB9Yb9F6UOMLZPBy7Wo")
                            .data("first", MpMessageDTO.MpMessageDataField.builder().value(params.get("first").toString()).color("#000000").build())
                            .data("keyword1", MpMessageDTO.MpMessageDataField.builder().value(params.get("keyword1").toString()).color("#000000").build())
                            .data("keyword2", MpMessageDTO.MpMessageDataField.builder().value(params.get("keyword2").toString()).color("#000000").build())
                            .data("remark", MpMessageDTO.MpMessageDataField.builder().value(params.get("remark") != null ? params.get("remark").toString() : "").color("#000000").build())
                            .build();

                    MpMessageResult mpMessageResult = mpUtil.sendMessage(accessToken, mpMessageDTO);

                    log.info("发送历史消息：{}", mpMessageResult);
                } else {
                    throw new CustomException("数据库数据错误!");
                }
            } else {
                log.warn("用户不存在或没有unionid，发送消息失败！");
            }
        }
    }

    //南粮在发送实装数后24小时自动确认
    //@Scheduled(cron = "0 0 0/6 * * ?")
    public void NLConfirmAuto() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date date = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date date1 = calendar.getTime();
        String start = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", date);
        String end = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", date1);

        List<Pb6WaterCargoVO> list = pb6WaterwaycargoService.selectNLConfirmAuto(start, end);
        if (list != null) {
            list.forEach(item -> {
                WaterwayCargo waterwayCargo = new WaterwayCargo();
                waterwayCargo.setConfirmtime(DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss"));
                waterwayCargo.setConfirmloadingover("Y");
                waterwayCargo.setCONFIRMBARGEEID(15392L);
                waterwayCargo.setWaterwayCargoId(item.getWaterwaycargoid());
                waterwayCargo.setFLAGBARGESTATE("2");
                if (pb6WaterwaycargoService.updateAuto(waterwayCargo)) {
                    log.info("南粮水路运单号：" + item.getWaterwaycargoid() + "自动确认成功");
                }
                ;
            });
        }
    }

    public void pb6BargeworkAddMethod(Pb6Cargoconsignmentdetail Pb6Cargoconsignmentdetail) {
        if (StringUtils.isNotBlank(Pb6Cargoconsignmentdetail.getWaterwaycargoid())) {
            Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.searchWaterwaycargoByWaterwaycargoid(Pb6Cargoconsignmentdetail.getWaterwaycargoid());
            try {
                pb6WaterwaycargoService.canOrderOrForcast(pb6Waterwaycargo, 2);//判断是否停单
                String mmsi = pb6BargeinfoService.searchMmsiByBargeName(Pb6Cargoconsignmentdetail.getBargename());//通过船名找mmsi
                if (isGreenBarge(pb6Waterwaycargo.getComid(),Pb6Cargoconsignmentdetail.getBargename())||"N".equals(pb6Waterwaycargo.getIsreporting())){
                    if (mmsi != null) {
                        String shipxy = restTemplate.getForObject(String.format("http://api.shipxy.com/apicall/GetSingleShip?k=%s&enc=%s&id=%s", "ffb8c7463962415cb6427417e0d6ec29", "1", mmsi), String.class);
                        JSONObject json = JSONObject.parseObject(shipxy);
                        List<Object> o = (List<Object>) json.get("data");
                        Object jsonObject = JSONObject.toJSON(o.get(0));
                        ShipxyDTO shipxyDTO1 = JSONObject.parseObject(jsonObject.toString(), ShipxyDTO.class);
                        String a = shipxyDTO1.getLon();
                        String b = shipxyDTO1.getLat();
                        Point2D.Double bargePoint = new Point2D.Double(Double.parseDouble(b), Double.parseDouble(a));
                        boolean c = false;
                        if (pb6Waterwaycargo.getComid() == 1) {
                            c = pb6BargeinfoService.IsPtInPoly(bargePoint, PortMapPoint.xsMap());
                        } else if (pb6Waterwaycargo.getComid() == 16) {
                            c = pb6BargeinfoService.IsPtInPoly(bargePoint, PortMapPoint.lsMap());
                        } else if (pb6Waterwaycargo.getComid() == 3 || pb6Waterwaycargo.getComid() == 4) {
                            c = pb6BargeinfoService.IsPtInPoly(bargePoint, PortMapPoint.xgMap());
                        } else if (pb6Waterwaycargo.getComid() == 2) {
                            c = pb6BargeinfoService.IsPtInPoly(bargePoint, PortMapPoint.hpMap());
                        }
                        //判断完驳船范围后进行装驳报到,测试时==false
                        if (c) {
                            Pb6Bargework pb6Bargework = new Pb6Bargework();
                            pb6Bargework.setWaterwaycargoidid(pb6Waterwaycargo.getId());
                            pb6Bargework.setWaterwaycargoid(pb6Waterwaycargo.getWaterwaycargoid());
                            pb6Bargework.setRegisterprincipal("自动报到");//报到负责人
                            Date now = new Date();
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String registerTime = dateFormat.format(now);
                            pb6Bargework.setRegistertime(registerTime);
                            if (pb6Waterwaycargo.getComid() != 1 && pb6Waterwaycargo.getComid() != 4) {
                                pb6Bargework.setFlagstraight("N");//是否直通过水
                                pb6Bargework.setFlagloadometer("N");//是否过磅
                            } else if (pb6Waterwaycargo.getComid() == 4) {
                                pb6Bargework.setIscoal("N");//是否配煤
                            }
                            if (pb6Waterwaycargo.getComid() == 4) {
                                pb6Bargework.setWorktype("流程装驳");
                            } else if (pb6Waterwaycargo.getComid() == 2) {
                                pb6Bargework.setWorktype("过水作业");
                            }
                            pb6Bargework.setComid(pb6Waterwaycargo.getComid());
                            if (pb6Waterwaycargo.getComid() == 1) {
                                pb6Bargework.setShipmentrate("0.98");//发货比例
                            } else if (pb6Waterwaycargo.getComid() == 4) {
                                List<String> stringList=pb6BargeworkMapper.searchShipmentrate(pb6Waterwaycargo.getOutorinformid());
                                if (stringList.size()>0){
                                    pb6Bargework.setShipmentrate(stringList.get(0));
                                }
                            } else {
                                pb6Bargework.setShipmentrate("1");
                            }
                            pb6Bargework.setShipname(pb6Waterwaycargo.getBargeName());
                            pb6Bargework.setUniquecode(pb6BargeworkService.searchUniqecode(pb6Waterwaycargo.getOutorinformid()));
                            pb6Bargework.setIsbindworkclass("N");
                            pb6Bargework.setIsworking("Y");
                            pb6Bargework.setCustomer(pb6Waterwaycargo.getConsigner());
                            Pb6Bargework pb6BargeworkOld = pb6BargeworkService.getOne(Wrappers.<Pb6Bargework>lambdaQuery().eq(Pb6Bargework::getWaterwaycargoid, Pb6Cargoconsignmentdetail.getWaterwaycargoid()));
                            pb6Bargework.setFlagframeorder("N");
                            if (pb6Waterwaycargo.getComid() == 1) {
                                pb6Bargework.setWorkid((long) 11);
                            } else if (pb6Waterwaycargo.getComid() == 16) {
                                pb6Bargework.setWorkid((long) 202);
                            } else if (pb6Waterwaycargo.getComid() == 4) {
                                pb6Bargework.setWorkid((long) 41);
                            } else if (pb6Waterwaycargo.getComid() == 3) {
                                pb6Bargework.setWorkid((long) 32);
                                //心港作业区
                                List<Map<String, Object>> cargotypeList = pb6BargeworkService.findCargoNameByCoutformid(pb6Waterwaycargo.getOutorinformid());
                                if (cargotypeList.size() > 0) {
                                    pb6Bargework.setWorkid((long) 32);
                                } else {
                                    pb6Bargework.setWorkid((long) 31);
                                }
                            }
                            if (pb6Waterwaycargo.getComid() == 4) {
                                reduceForXJ(pb6Waterwaycargo, "预扣");
                                pb6Bargework.setFlagyukou((long) 1);
                                pb6Bargework.setYukouweight(pb6Waterwaycargo.getRationweight());
                            }

                            if (pb6Waterwaycargo.getComid() == 3) {
                                reduceXG(pb6Waterwaycargo, "预扣");
                                pb6Bargework.setFlagyukou((long) 1);
                                pb6Bargework.setYukouweight(pb6Waterwaycargo.getRationweight());
                            }

                            SysUser sysUser = iSysUserService.selectUserById(Pb6Cargoconsignmentdetail.getWxointmentmid());
                            pb6Bargework.setContactphone(sysUser.getPhonenumber());
                            pb6Bargework.setBargelinkman(sysUser.getNickName());
                            //判断改水路运单之前是否有报到，有则删除之前的
                            if (pb6BargeworkOld == null) {
                                pb6BargeworkService.save(pb6Bargework);
                            } else {
                                pb6BargeworkService.removeById(pb6BargeworkOld.getId());
                                pb6BargeworkService.save(pb6Bargework);
                            }
                            pb6Waterwaycargo.setFlagbargestate("1");
                            pb6WaterwaycargoService.updateById(pb6Waterwaycargo);
                            Pb6Cargoconsignmentdetail.setFlagbargestate("4");
                            pb6CargoconsignmentdetailService.updateById(Pb6Cargoconsignmentdetail);
                            //录入自动报到消息
                            Pb6BargeCheckMessage pb6BargeCheckMessage = new Pb6BargeCheckMessage();
                            pb6BargeCheckMessage.setMtype(13);
                            pb6BargeCheckMessage.setApplytime(DateUtils.strToSqlDate(DateUtils.getTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
                            pb6BargeCheckMessage.setBargename(Pb6Cargoconsignmentdetail.getBargename());
                            pb6BargeCheckMessage.setRemark("驳船自动报到");
                            pb6BargeCheckMessage.setAuditflag(0);
                            pb6BargeCheckMessage.setApplyman("admin");
                            pb6BargeCheckMessage.setApplymanid(Long.parseLong("1"));
                            pb6BargeCheckMessage.setWaterwaycargoid(Pb6Cargoconsignmentdetail.getWaterwaycargoid());
                            pb6BargeCheckMessageService.save(pb6BargeCheckMessage);
                            log.info(Pb6Cargoconsignmentdetail.getBargename() + "报到成功..");
                            BargeUtils.defrayStamp(Pb6Cargoconsignmentdetail.getWaterwaycargoid(), "水路货物运单.pdf");
                            log.info("生成水路运单成功");
                        } else {
                            log.error(Pb6Cargoconsignmentdetail.getBargename() + ":船没进入范围..");
                        }
                    } else {
                        log.error(Pb6Cargoconsignmentdetail.getBargename() + ":没有MMSI..");
                    }
                }else{
                    pb6BargeCheckMessageService.sendMessageForGreenBarge(Pb6Cargoconsignmentdetail);
                    log.error(Pb6Cargoconsignmentdetail.getBargename() + ":没有绿色申报");
                }

            } catch (Exception e) {
                System.out.println("异常跳出" + e);
            }
        } else {
            log.error(Pb6Cargoconsignmentdetail.getBargename() + ":托运单明细表中水路运单字段为空..");
        }
    }

    public Boolean isGreenBarge(Long comId, String bargeName) {
        System.out.println(bargeName+comId);
        Boolean isGreenBarge=false;
        int wharfId =139;
        String account=new String();
        String code=new String();

        if (comId==16){
            wharfId=139;
            account="ApiUser139";
            code="6a15a02ad19b0558af0eb03b7fffc6eb";
        }else if (comId==1){
            wharfId=153;
            account="ApiUser140";
            code="154b16804f2aad8341c14fab816cc8e3";
        }else if (comId==3 ){
            wharfId=154;
            account="ApiUser154";
            code="adb130e2ab77d94f633631e315ff8a7d";
        }else if (comId==2){
            wharfId=152;
            account="ApiUser152";
            code="3e210d60bddf6b354b7373a7a1be1f32";
        }else if (comId==4){
            wharfId=155;
            account="ApiUser155";
            code="8908eb328a42ef905a2b07f412e46a76";
        }
        try {
            CloseableHttpClient client = HttpClients.createDefault();
            JSONObject postJson = new JSONObject();
            postJson.put("un", account);
            postJson.put("pwd", code);
            HttpUriRequest post = RequestBuilder.post("https://gps-outsideservice.csiitl.com/OutsideService/Token").
                    setEntity(new StringEntity(postJson.toString(), ContentType.APPLICATION_JSON)).build();
            System.out.println(post);
            HttpResponse postRes = client.execute(post);
            System.out.println(postRes);
            String a = EntityUtils.toString(postRes.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(a);
            String header = "Bearer " + jsonObject.getJSONObject("content").getString("token");
            JSONObject getJson = new JSONObject();
            getJson.put("WharfId", wharfId);
            getJson.put("ShipName", bargeName);
            getJson.put("DayNum",5);//5天内
//            HttpUriRequest get = RequestBuilder.get("https://gps-outsideservice.csiitl.com/OutsideService/ShipDeclareCompleteExistsByWharf").
//                    setHeader("Authorization", header).setEntity(new StringEntity(getJson.toString(), ContentType.APPLICATION_JSON)).build();
            HttpUriRequest get = RequestBuilder.get("https://gps-outsideservice.csiitl.com/OutsideService/ShipDeclareCompleteExistsByWharfAndDayNum").
                    setHeader("Authorization", header).addParameter("WharfId", String.valueOf(wharfId)).
                    addParameter("ShipName",bargeName)
                    .addParameter("DayNum","5").build();
            HttpResponse getRes  = client.execute(get);
            String b = EntityUtils.toString(getRes.getEntity());
            JSONObject getJsonObject = JSONObject.parseObject(b);
            System.out.println(getJson);
            System.out.println(getJsonObject);
            isGreenBarge=getJsonObject.getJSONObject("content").getBoolean("hasArrived");
            log.info(bargeName+"调用申报接口");
        } catch (IOException e) {
            e.printStackTrace();
            return isGreenBarge;
        }
        return isGreenBarge;
    }
    //取消报到 （2022.06.15 暂时取消该功能）
//    @GetMapping("/bargeworkCancle")
//    public void bargeworkCancle() {
//        List<Pb6Bargework> pb6BargeworkList = pb6BargeworkMapper.pb6BargeworkCancle();
//        if (pb6BargeworkList.size() > 0) {
//            for (Pb6Bargework pb6Bargework : pb6BargeworkList) {
//                Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getOne(Wrappers.<Pb6Waterwaycargo>lambdaQuery().
//                        eq(Pb6Waterwaycargo::getWaterwaycargoid, pb6Bargework.getWaterwaycargoid()));
//                Pb6Cargoconsignmentdetail pb6Cargoconsignmentdetail = pb6CargoconsignmentdetailService.getOne(Wrappers.<Pb6Cargoconsignmentdetail>lambdaQuery().
//                        eq(Pb6Cargoconsignmentdetail::getWaterwaycargoid, pb6Bargework.getWaterwaycargoid()));
//                String mmsi = pb6BargeinfoService.searchMmsiByBargeName(pb6Cargoconsignmentdetail.getBargename());//通过船名找mmsi
//                try {
//                    if (mmsi != null) {
//                        String shipxy = restTemplate.getForObject(String.format("http://api.shipxy.com/apicall/GetSingleShip?k=%s&enc=%s&id=%s", "ffb8c7463962415cb6427417e0d6ec29", "1", mmsi), String.class);
//                        JSONObject json = JSONObject.parseObject(shipxy);
//                        List<Object> o = (List<Object>) json.get("data");
//                        Object jsonObject = JSONObject.toJSON(o.get(0));
//                        ShipxyDTO shipxyDTO1 = JSONObject.parseObject(jsonObject.toString(), ShipxyDTO.class);
//                        String a = shipxyDTO1.getLon();
//                        String b = shipxyDTO1.getLat();
//                        Point2D.Double bargePoint = new Point2D.Double(Double.parseDouble(b), Double.parseDouble(a));
//                        boolean c = true;
//                        if (pb6Waterwaycargo.getComid() == 1) {
//                            c = pb6BargeinfoService.IsPtInPoly(bargePoint, PortMapPoint.xsMap());
//                        } else if (pb6Waterwaycargo.getComid() == 16) {
//                            c = pb6BargeinfoService.IsPtInPoly(bargePoint, PortMapPoint.lsMap());
//                        } else if (pb6Waterwaycargo.getComid() == 3 || pb6Waterwaycargo.getComid() == 4) {
//                            c = pb6BargeinfoService.IsPtInPoly(bargePoint, PortMapPoint.xgMap());
//                        }else if (pb6Waterwaycargo.getComid() == 2) {
//                            c = pb6BargeinfoService.IsPtInPoly(bargePoint, PortMapPoint.hpMap());
//                        }
//                        //当船不在范围内，取消报到
//                        if (!c) {
//                            List<Pb6BargeCheckMessage> pb6BargeCheckMessageList = pb6BargeCheckMessageService.list(Wrappers.<Pb6BargeCheckMessage>lambdaQuery().
//                                    eq(Pb6BargeCheckMessage::getWaterwaycargoid, pb6Bargework.getWaterwaycargoid()).eq(Pb6BargeCheckMessage::getMtype, 13).
//                                    eq(Pb6BargeCheckMessage::getDelflag, 0));
//                            if (pb6BargeCheckMessageList.size() > 0) {
//                                for (Pb6BargeCheckMessage pb6BargeCheckMessage : pb6BargeCheckMessageList) {
//                                    pb6BargeCheckMessage.setDelflag(1);
//                                    pb6BargeCheckMessageService.getById(pb6BargeCheckMessage);
//                                }
//                            }
//                            if (pb6Waterwaycargo.getComid() == 4) {
//                                reduceForXJ(pb6Waterwaycargo, "解扣");
//                                pb6Bargework.setFlagjiekou((long) 1);
//                                pb6Bargework.setJiekouweight(pb6Waterwaycargo.getRationweight());
//                            }
//
//                            if (pb6Waterwaycargo.getComid() == 3) {
//                                reduceXG(pb6Waterwaycargo, "解扣");
//                                pb6Bargework.setFlagjiekou((long) 1);
//                                pb6Bargework.setJiekouweight(pb6Waterwaycargo.getRationweight());
//                            }
//
//                            pb6Waterwaycargo.setFlagbargestate("0");//改回配载状态
//                            pb6WaterwaycargoService.updateById(pb6Waterwaycargo);
//                            pb6Cargoconsignmentdetail.setFlagbargestate("3");//改回配载状态
//                            pb6CargoconsignmentdetailService.updateById(pb6Cargoconsignmentdetail);
//                            pb6BargeworkService.updateById(pb6Bargework);
//                            log.info(pb6Bargework.getId() + "取消自动报到成功");
//                        }
//                    } else {
//                        log.error(pb6Cargoconsignmentdetail.getBargename() + ":没有MMSI..");
//                    }
//                } catch (Exception e) {
//                    System.out.println("异常跳出" + e);
//                }
//
//            }
//        }
//    }

    @PostMapping("/searchPageBargeWork")
    public AjaxResult searchPageBargeWork(@RequestBody Pb6BargeWorkDTO pb6BargeWorkDTO) {
        DataScope.getDataScope("t", pb6BargeWorkDTO, Boolean.FALSE);
        IPage<Pb6BargeWorkVo> pb6BargeWorkVoIPage = pb6BargeworkService.searchPagePb6WaterCargo(pb6BargeWorkDTO);
        pb6BargeWorkVoIPage.getRecords().forEach(i -> {
            String phoneNumber = pb6BargeworkMapper.searchPhonenumber(i.getWaterwaycargoid());
            if (StringUtils.isNotEmpty(phoneNumber)) {
                i.setPhoneNumber(phoneNumber);
            }
            PortLoadingMsgVO portLoadingMsgVO = pb6BargeCheckMessageService.searchPortLoadingInfo(i.getWaterwaycargoid());
            if (StringUtils.isNotEmpty(portLoadingMsgVO.getTscargoweightValue())) {
                i.setTscargoweightValue(portLoadingMsgVO.getTscargoweightValue());
            }
            List<UploadAddressDomain> FileList = uploadAddressDomainService.searchPb6WatercargoFile(i.getWaterwaycargoid());
            if (FileList.size() > 0) {
                i.setIsFile("Y");
            }
            if (i.getFlagbargestate().equals("0")) {
                i.setFlagbargestate("已配载");
            } else if (i.getFlagbargestate().equals("1")) {
                if (i.getEndworktime() != null && i.getEndworktime().length() > 10) {
                    i.setFlagbargestate("完工");
                } else {
                    i.setFlagbargestate("未完工");
                }
            } else if (i.getFlagbargestate().equals("2")) {
                i.setFlagbargestate("已离港");
            } else if (i.getFlagbargestate().equals("3")) {
                i.setFlagbargestate("已取消报到");
            } else if (i.getFlagbargestate().equals("4")) {
                i.setFlagbargestate("已退单");
            }
        });
        return AjaxResult.success(pb6BargeWorkVoIPage);
    }

    //@Scheduled(cron = "0 10 0 * * ?")
    public void cancelPb6Cargoconsignmentdetail() {
        Calendar ca = Calendar.getInstance();//得到一个Calendar的实例
        ca.setTime(new Date()); //设置时间为当前时间
        ca.add(Calendar.DATE, -1);
        Date lastDate = ca.getTime(); //结果
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        List<Pb6Cargoconsignmentdetail> bargeList = pb6CargoconsignmentdetailMapper.searchBargeByWxappointmenttime(formatter.format(lastDate), formatter1.format(lastDate));
        if (bargeList.size() > 0) {
            for (Pb6Cargoconsignmentdetail Pb6Cargoconsignmentdetail : bargeList) {
                Pb6Cargoconsignmentdetail.setWxoperatestate(4);
                Pb6Cargoconsignmentdetail.setWxappointmenttime(null);
                pb6CargoconsignmentdetailService.updateById(Pb6Cargoconsignmentdetail);
            }
        }
    }

    //西基公司预扣/解扣出库单
    public void reduceForXJ(Pb6Waterwaycargo pb6Waterwaycargo, String type) {
        if (pb6Waterwaycargo.getFlagbargestate().equals("0")) {
            if (StringUtils.isNotEmpty(pb6Waterwaycargo.getRationweight()) || StringUtils.isNotEmpty(pb6Waterwaycargo.getRationpiece())) {
                BigDecimal reduceBigDecimal = new BigDecimal(pb6Waterwaycargo.getRationweight());

                String dynamicWeight = "0";//吨
                String dynamicVolume = "0";//体积吨
                String dynamicAmt = "0";//件数
                Pb3Coutform pb3Coutform = pb3CoutformService.getById(pb6Waterwaycargo.getOutorinformidid());
                dynamicWeight = pb3Coutform.getDynamicweight();
                String recode = pb3Coutform.getDynamicweight();
                dynamicAmt = pb3Coutform.getDynamicamt();
                dynamicVolume = pb3Coutform.getDynamicvolume();
                if (StringUtils.isNotEmpty(pb6Waterwaycargo.getRationweight())) {
                    if (type.equals("预扣")) {
                        dynamicWeight = String.valueOf(new BigDecimal(dynamicWeight).doubleValue() - new BigDecimal(pb6Waterwaycargo.getRationweight()).doubleValue());
                    } else {
                        dynamicWeight = String.valueOf(new BigDecimal(dynamicWeight).doubleValue() + new BigDecimal(pb6Waterwaycargo.getRationweight()).doubleValue());
                    }
                    pb3Coutform.setDynamicweight(dynamicWeight);
                }
                if (StringUtils.isNotEmpty(pb6Waterwaycargo.getRationpiece())) {
                    if (type.equals("预扣")) {
                        dynamicAmt = String.valueOf(new BigDecimal(dynamicAmt).doubleValue() - new BigDecimal(pb6Waterwaycargo.getRationpiece()).doubleValue());
                    } else {
                        dynamicAmt = String.valueOf(new BigDecimal(dynamicAmt).doubleValue() + new BigDecimal(pb6Waterwaycargo.getRationpiece()).doubleValue());
                    }
                    pb3Coutform.setDynamicamt(dynamicAmt);
                }
                if (reduceBigDecimal != null) {
                    PubCoutformreducerecord pubCoutformreducerecord = new PubCoutformreducerecord();
                    if (type.equals("预扣")) {
                        pb3Coutform.setReason("驳船自动报到预扣");
                        pubCoutformreducerecord.setYukouweight(pb6Waterwaycargo.getRationweight());
                        pubCoutformreducerecord.setRemark("驳船自动报到扣减动态结存:" + recode + "-" + pb6Waterwaycargo.getRationweight() + "=" + dynamicWeight);
                    } else {
                        pb3Coutform.setReason("驳船取消自动报到解扣");
                        pubCoutformreducerecord.setJiekouweight(pb6Waterwaycargo.getRationweight());
                        pubCoutformreducerecord.setRemark("驳船自动报到加回动态结存:" + recode + "+" + pb6Waterwaycargo.getRationweight() + "=" + dynamicWeight);
                    }
                    pb3Coutform.setChangepart("驳船自动报到");
                    pb3CoutformService.updateById(pb3Coutform);
                    pubCoutformreducerecord.setCoutformnum(pb3Coutform.getCoutformid());
                    pubCoutformreducerecord.setOperatepart("驳船自动报到");
                    pubCoutformreducerecord.setOperatetime(DateUtils.getTime());
                    pubCoutformreducerecord.setOperatetype(type);
                    pubCoutformreducerecord.setOrdernum(pb6Waterwaycargo.getWaterwaycargoid());
                    pubCoutformreducerecord.setUniqecode(pb3Coutform.getUniqecode());
                    pubCoutformreducerecord.setShipname(pb3Coutform.getShipname());
                    pubCoutformreducerecord.setRemainweight(pb3Coutform.getRemainweight());
                    pubCoutformreducerecord.setDynamicweight(pb3Coutform.getDynamicweight());
                    pubCoutformreducerecord.setCoutformreason(pb3Coutform.getReason());
                    pubCoutformreducerecord.setImpawnnumber(pb3Coutform.getImpawnnumber());
                    pubCoutformreducerecord.setImpawn(pb3Coutform.getImpawn());
                    pubCoutformreducerecord.setCorporationid(pb3Coutform.getCorporationid().toString());
                    pubCoutformreducerecord.setReduceweight(pb6Waterwaycargo.getRationweight());
                    pubCoutformreducerecordService.save(pubCoutformreducerecord);
                }
            }
        }
    }

    //新港公司预扣/解扣出库单
    public void reduceXG(Pb6Waterwaycargo pb6Waterwaycargo, String type) {
        if (pb6Waterwaycargo.getOutorinformid().substring(0, 3).equals("BCK")) {
            BigDecimal reduceBigDecimal = new BigDecimal(pb6Waterwaycargo.getRationweight());
            PubReducerecord pubReducerecord = new PubReducerecord();
            pubReducerecord.setCoutformnum(pb6Waterwaycargo.getOutorinformid());
            pubReducerecord.setOperatetype("新增/预扣");
            pubReducerecord.setOperatepart("驳船自动报到");
            if (type.equals("预扣")) {
                pubReducerecord.setYukou((long) 1);
                pubReducerecord.setYukouweight(pb6Waterwaycargo.getRationweight());//生成系统换了单位 千克转化为吨
            } else {
                pubReducerecord.setJiekou((long) 0);
                pubReducerecord.setJiekouweight(pb6Waterwaycargo.getRationweight());
            }
            pubReducerecord.setOrdernum(String.valueOf(pb6Waterwaycargo.getWaterwaycargoid()));
            pubReducerecord.setOperatetime(DateUtils.getTime());
            pubReducerecord.setReduceweight(pb6Waterwaycargo.getRationweight());
            pubReducerecordService.save(pubReducerecord);

            String dynamicWeight = "0";//吨
            String dynamicVolume = "0";//体积吨
            String dynamicAmt = "0";//件数
            Pb3Coutform pb3Coutform = pb3CoutformService.getById(pb6Waterwaycargo.getOutorinformidid());
            dynamicWeight = pb3Coutform.getDynamicweight();
            String recode = pb3Coutform.getDynamicweight();
            dynamicAmt = pb3Coutform.getDynamicamt();
            dynamicVolume = pb3Coutform.getDynamicvolume();
            if (StringUtils.isNotEmpty(pb6Waterwaycargo.getRationweight())) {
                if (type.equals("预扣")) {
                    dynamicWeight = String.valueOf(new BigDecimal(dynamicWeight).doubleValue() - new BigDecimal(pb6Waterwaycargo.getRationweight()).doubleValue());
                } else {
                    dynamicWeight = String.valueOf(new BigDecimal(dynamicWeight).doubleValue() + new BigDecimal(pb6Waterwaycargo.getRationweight()).doubleValue());
                }
                pb3Coutform.setDynamicweight(dynamicWeight);
            }
            if (StringUtils.isNotEmpty(pb6Waterwaycargo.getRationpiece())) {
                if (type.equals("预扣")) {
                    dynamicAmt = String.valueOf(new BigDecimal(dynamicAmt).doubleValue() - new BigDecimal(pb6Waterwaycargo.getRationpiece()).doubleValue());
                } else {
                    dynamicAmt = String.valueOf(new BigDecimal(dynamicAmt).doubleValue() + new BigDecimal(pb6Waterwaycargo.getRationpiece()).doubleValue());
                }
                pb3Coutform.setDynamicamt(dynamicAmt);
            }
            if (reduceBigDecimal != null) {
                PubCoutformreducerecord pubCoutformreducerecord = new PubCoutformreducerecord();
                if (type.equals("预扣")) {
                    pb3Coutform.setReason("驳船自动报到预扣");
                    pubCoutformreducerecord.setYukouweight(pb6Waterwaycargo.getRationweight());
                    pubCoutformreducerecord.setRemark("驳船自动报到扣减动态结存:" + recode + "-" + pb6Waterwaycargo.getRationweight() + "=" + dynamicWeight);
                } else {
                    pb3Coutform.setReason("驳船取消自动报到解扣");
                    pubCoutformreducerecord.setJiekouweight(pb6Waterwaycargo.getRationweight());
                    pubCoutformreducerecord.setRemark("驳船自动报到加回动态结存:" + recode + "+" + pb6Waterwaycargo.getRationweight() + "=" + dynamicWeight);
                }
                pb3Coutform.setChangepart("驳船自动报到");
                pb3CoutformService.updateById(pb3Coutform);
                pubCoutformreducerecord.setCoutformnum(pb3Coutform.getCoutformid());
                pubCoutformreducerecord.setOperatepart("驳船自动报到");
                pubCoutformreducerecord.setOperatetime(DateUtils.getTime());
                pubCoutformreducerecord.setOperatetype(type);
                pubCoutformreducerecord.setOrdernum(pb6Waterwaycargo.getWaterwaycargoid());
                pubCoutformreducerecord.setUniqecode(pb3Coutform.getUniqecode());
                pubCoutformreducerecord.setShipname(pb3Coutform.getShipname());
                pubCoutformreducerecord.setRemainweight(pb3Coutform.getRemainweight());
                pubCoutformreducerecord.setDynamicweight(pb3Coutform.getDynamicweight());
                pubCoutformreducerecord.setCoutformreason(pb3Coutform.getReason());
                pubCoutformreducerecord.setImpawnnumber(pb3Coutform.getImpawnnumber());
                pubCoutformreducerecord.setImpawn(pb3Coutform.getImpawn());
                pubCoutformreducerecord.setCorporationid(pb3Coutform.getCorporationid().toString());
                pubCoutformreducerecord.setReduceweight(pb6Waterwaycargo.getRationweight());
                pubCoutformreducerecordService.save(pubCoutformreducerecord);
            }
        }
    }

    @PostMapping("/searchPb11Tallysheet")
    public AjaxResult searchPb11Tallysheet(@RequestBody String waterwaycargoid) {
        List<Pb11TallysheetVO> pb11TallysheetVOList = pb6BargeworkMapper.searchPb11Tallysheet(waterwaycargoid);
        return AjaxResult.success(pb11TallysheetVOList);
    }

    @PostMapping("/searchBargeWork")
    public AjaxResult searchBargeWork(@RequestBody String waterwaycargoid) {
//        List<Pb6Bargework> pb6BargeworkList=pb6BargeworkService.list(Wrappers.<Pb6Bargework>lambdaQuery().
//                eq(Pb6Bargework::getWaterwaycargoid,waterwaycargoid));
        Pb6Waterwaycargo pb6Waterwaycargo = pb6WaterwaycargoService.getOne(Wrappers.<Pb6Waterwaycargo>lambdaQuery().
                eq(Pb6Waterwaycargo::getWaterwaycargoid, waterwaycargoid));
        if (pb6Waterwaycargo != null) {
            if (pb6Waterwaycargo.getFlagbargestate().equals("1")) {
                return AjaxResult.success();
            }
        }
        return AjaxResult.success("没有报到");
    }
}

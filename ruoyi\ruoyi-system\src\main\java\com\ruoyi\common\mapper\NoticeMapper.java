package com.ruoyi.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.domain.SysMessage;
import com.ruoyi.common.domain.SysMessageModel;
import com.ruoyi.common.domain.SysUserMessage;
import com.ruoyi.common.domain.SysUserSubscription;
import com.ruoyi.common.domain.bo.UserMessageBO;
import com.ruoyi.common.domain.vo.UserMessageVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/5 10:39
 */
public interface NoticeMapper extends BaseMapper<SysUserMessage> {

    /**
     * 用户订阅
     * @param subscription
     * @return
     */
    int addSubscription(SysUserSubscription subscription);

    /**
     * 用户取消订阅
     * @param subscription
     * @return
     */
    int deleteSubscription(SysUserSubscription subscription);

    /**
     * 查询用户订阅
     * @param subscription
     * @return
     */
    List<SysUserSubscription> selectSubscription(SysUserSubscription subscription);

    /**
     * 新增消息
     * @param sysMessage
     * @return
     */
    int addMessage(SysMessage sysMessage);

    /**
     * 新增用户消息
     * @param sysUserMessage
     * @return
     */
    int addUserMessage(SysUserMessage sysUserMessage);

    /**
     * 获取消息通知列表
     * @param userMessageBO
     * @return
     */
    List<UserMessageVO> getUserMessageList(UserMessageBO userMessageBO);

    /**
     * 更新用户消息读取状态
     * @param userMessageBO
     * @return
     */
    int updateIsReadById(UserMessageBO userMessageBO);

    /**
     * 根据code查询消息模板
     * @param code
     * @return
     */
    SysMessageModel selectMegModelByCode(String code);

    /**
     * 获取订阅状态
     * @param subscription
     * @return
     */
    Boolean getSubscription(SysUserSubscription subscription);
}

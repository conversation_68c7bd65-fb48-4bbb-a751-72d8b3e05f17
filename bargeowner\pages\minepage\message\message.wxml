<!--pages/minepage/message/message.wxml-->
<view class="message-page" hover-class="none" hover-stop-propagation="false">
  <view class="message-search" hover-class="none" hover-stop-propagation="false">
    <view class="search-ipt-wrap" hover-class="none" hover-stop-propagation="false">
      <input class="search-ipt" value="" type="text"  placeholder="驳船名称/标题" maxlength="30" value="{{form.searchValue}}" bindinput="searchValueInput"></input>
      <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-icon" wx:if="{{form.searchValue}}" bindtap="deleteSearchValue"></mp-icon>
    </view>
    <view class="date-select" hover-class="none" hover-stop-propagation="false">
      <view class="date-view" hover-class="none" hover-stop-propagation="false">
        <picker mode="date" value="{{form.beginTime}}"  bindchange="handleBeginDateChange" class="search-data-box">
          <input class="date-ipt" value="{{form.beginTime}}" type="digit"  placeholder="开始时间" maxlength="30" bindinput="handleBeginDateChange" disabled="{{true}}"></input>
          <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-iconp" wx:if="{{form.beginTime}}" catchtap="deleteBeginTime"></mp-icon>
        </picker>
      </view>
      <view class="date-range" hover-class="none" hover-stop-propagation="false">—</view>
      <view class="date-view" hover-class="none" hover-stop-propagation="false">
        <picker mode="date" value="{{form.endTime}}"  bindchange="handleEndDateChange"  class="search-data-box">
          <input class="date-ipt" value="{{form.endTime}}" type="digit"  placeholder="截止时间" maxlength="30" bindinput="handleEndDateChange" disabled="{{true}}"></input>
          <mp-icon  icon="close2" color="#aaa" size="{{16}}" class="close-iconp" wx:if="{{form.endTime}}" catchtap="deleteEndTime"></mp-icon>
        </picker>
      </view>
      <button class="search-btn" bindtap="searchHandle">查</button>
    </view>
  </view>
  <view class="message-list" hover-class="none" hover-stop-propagation="false">
    <view class="message-item" wx:for="{{messageList}}" wx:key="index" data-item="{{item}}" data-index="{{index}}" hover-class="none" hover-stop-propagation="false" bindtap="jumpDetail">
      <view class="iconfont icontongzhi notice-icon" hover-class="none" hover-stop-propagation="false"></view>
      <view class="message-content" hover-class="none" hover-stop-propagation="false">
        <view class="message-content-title" hover-class="none" hover-stop-propagation="false">{{item.title}}</view>
        <text class="message-content-text" selectable="false" space="false" decode="false">
          {{item.content}}
        </text>
        <!-- <rich-text nodes="{{item.content}}" class="message-content-text" hover-class="none" hover-stop-propagation="false"></rich-text> -->
        <view class="message-content-date" hover-class="none" hover-stop-propagation="false">{{item.createTime}}</view>
      </view>
      <!-- isRead消息状态，0-未读(有红点)，1-已读 -->
      <view class="read-status" hover-class="none" hover-stop-propagation="false"  wx:if="{{item.isRead==='0'}}"></view>
      <mp-icon class="detail-icon" type="outline" icon="arrow" color="#aaaaaa" size="{{16}}"></mp-icon>
    </view>
  </view>
</view>
package com.ruoyi.web.controller.carrier;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jcraft.jsch.ChannelSftp;
import com.ruoyi.app.controller.support.fdd.FddCommonService;
import com.ruoyi.app.controller.support.fdd.FddCompanyService;
import com.ruoyi.app.controller.support.ftp.FtpTemplate;
import com.ruoyi.carrier.domain.bo.CarrierBO;
import com.ruoyi.carrier.service.CarrierInfoService;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.GMUser;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.domain.Customer;
import com.ruoyi.common.domain.OLPicture;
import com.ruoyi.common.domain.OlUser;
import com.ruoyi.common.enums.UserType;
import com.ruoyi.common.enums.WebCarrierRoleType;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.service.CustomerService;
import com.ruoyi.common.service.OlPictureService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ftp.FtpUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.databarge.domain.Pb30OlUser;
import com.ruoyi.databarge.domain.ShipFddUserRel;
import com.ruoyi.databarge.service.Pb30OlUserService;
import com.ruoyi.databarge.service.ShipFddUserRelService;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.ssouser.domain.SSOUser;
import com.ruoyi.ssouser.service.SSOUserSerivce;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.controller.common.CommonController;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.websocket.server.PathParam;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 船公司 PC端
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/17 11:53
 */
@Slf4j
@RestController
@RequestMapping("/noauth")
public class CarrierRegisterController {

    @Autowired
    private SysLoginService loginService;
    @Autowired
    private ISysUserService iSysUserService;
    @Autowired
    private CarrierInfoService carrierInfoService;

    @Autowired
    private CustomerService customerService;
    @Autowired
    private OlPictureService olPictureService;
    @Autowired
    private CommonController commonController;
    @Autowired
    private ShipFddUserRelService shipFddUserRelService;

    @Autowired
    private FtpUtils ftpUtils;
    @Autowired
    SSOUserSerivce ssoUserSerivce;

    @Autowired
    private FddCompanyService fddCompanyService; //印章认证

    @Autowired
    private Pb30OlUserService pb30OlUserService;

    @Value("${sso.bargecompany}")
    private String ssoBargecompany;
    @Value("${sso.login}")
    private String ssoLogin;

    @Value("${sso.authentication}")
    private String ssoAuthentication;


//    @PostMapping("/sso")
//    public AjaxResult getssouser(@RequestBody SSOUser ssoUser){
//        SSOUser ssoUser1 = ssoUserSerivce.getSSOUser(ssoUser);
//        return AjaxResult.success(ssoUser1);
//    }

    @GetMapping("/getUUID")
    public AjaxResult getUUID(){
        // 唯一标识
        String uuid = IdUtils.simpleUUID();
        AjaxResult ajax = AjaxResult.success();
        ajax.put("uuid", uuid);
        return ajax;
    }
    //网上营业厅统一登陆-----------------------------------开始
    @GetMapping("/index.xhtml")
    public void bulkcustomer(@PathParam("token") String token, HttpServletResponse response) throws IOException {
        System.out.println("token:"+token);
        if(StringUtils.isNotEmpty(token)){
            response.sendRedirect(ssoBargecompany+"?token="+token);
            //注册界面
            //直接登陆界面
        }else{
            response.sendRedirect(ssoLogin);
        }

    }
    //认证跳转
    @PostMapping("/registerUser.xhtml")
    public AjaxResult getSSOuser(@RequestBody LoginBody loginBody) {
        String token = loginBody.getSsoToken();
        String uuid = loginBody.getUuid();
        if(StringUtils.isNotEmpty(token)){
            try {
                return checkSSOuser(token, uuid);
            } catch (Exception e) {
                e.printStackTrace();
                throw new CustomException("认证失败",e.getCause());
            }
        }else{
            return AjaxResult.error();
        }
    }
    //获取网上营业厅当前用户登陆信息
    private AjaxResult checkSSOuser(String ssoToken,String uuid) throws URISyntaxException, IOException {
        Map<String,Object> resultMap=new HashMap();
        CloseableHttpClient aDefault = HttpClients.createDefault();
        HttpGet httpGet=new HttpGet();
        httpGet.setURI(new URI(ssoAuthentication+"?cmdId=1002&params="+ssoToken));
        CloseableHttpResponse response = aDefault.execute(httpGet);
        if (response.getStatusLine().getStatusCode() == HttpStatus.SUCCESS) {
            String strResult = EntityUtils.toString(response.getEntity());
            JSONObject jsonResult = JSONObject.parseObject(strResult);
            int status = jsonResult.getIntValue("status");
            if(status==1){//获取当前用户成功
                JSONObject data = jsonResult.getJSONObject("data");
                String account = data.getString("account");

                SSOUser temp = new SSOUser();
                temp.setAccount(account);
                SSOUser ssoUser = ssoUserSerivce.getSSOUser(temp);
                Map<String, Object> map = new HashMap<>();
                map.put("account", ssoUser.getAccount());
                map.put("name", data.getString("name"));
                map.put("phone", data.getString("phone"));

                GMUser gmUser = iSysUserService.selectAppUserByGmis(account);//判断用户是否在船公司业务系统中注册
                if (gmUser != null) {//已注册
                    SysUser sysUser = iSysUserService.selectUserByGmUserId(gmUser.getId());//判断用户是否本系统存在
                    if (sysUser != null) {//用户存在则生成token
                        String token = loginService.login("pc", "bargeCompany", null, sysUser.getPhonenumber(), sysUser.getUserName(), null, null,
                                uuid,9999);

                        resultMap.put("token", token);
                        resultMap.put("register", false);
                    }else{//不存在则更新到
                        //更新到业务数据库
                        /*sysUser=new SysUser();
                        sysUser.setUserType(gmUser.getRoleId()==0? UserType.CARRIEUSER.getCode():UserType.CARRIERADMIN.getCode());
                        sysUser.setUserName(gmUser.getUserId());
                        sysUser.setPhonenumber(gmUser.getContactCellPhone());
                        sysUser.setGmUserId(gmUser.getId());
                        sysUser.setNickName(gmUser.getContactPerson());
                        //用户添加，授权
                        int i = iSysUserService.insertUser(sysUser);*/
                        resultMap.put("registeruser", map);
                        resultMap.put("register", true);
                    }
                    /*String token = loginService.login("pc","bargeCompany",null,sysUser.getPhonenumber(),sysUser.getUserName(), null, null,
                            uuid);

                    resultMap.put("token",token);
                    resultMap.put("register",false);*/

                }else{
                    resultMap.put("registeruser", map);
                    resultMap.put("register", true);
                }
                return AjaxResult.success(resultMap);
            }

        }
        return AjaxResult.error();
    }
    //网上营业厅统一登陆-----------------------------------结束

    @PostMapping("/checkAdmin")
    public AjaxResult checkAdmin(@RequestBody CarrierBO carrierBO) {
        if (StringUtils.isNull(carrierBO.getCompanyId()) || StringUtils.isNull(carrierBO.getRoleType())) {
            return AjaxResult.error("公司id和角色类型不能为空");
        }
        // 判断该船公司是否已有管理员注册
        if (StringUtils.isNotNull(carrierBO.getCompanyId())) {
            SysUser sysUser = new SysUser();
            sysUser.setCompanyId(carrierBO.getCompanyId());
            sysUser.setUserType(UserType.CARRIERADMIN.getCode());
            List<SysUser> list = iSysUserService.getUser(sysUser);

            if (list.size() <= 0 && carrierBO.getRoleType() != 3) {
                return AjaxResult.success("请先让管理员注册后，其他人员才可以注册登录。", false);
            } else if (list.size() > 0 && carrierBO.getRoleType() == 3) {
                return AjaxResult.success("该公司已有管理员账号，请勿重复注册。", false);
            } else if (list.size() <= 0 && carrierBO.getRoleType() == 3) {
                // 查询PB30_OL_USER.roleid=3 and  PB30_OL_USER.company=‘他选的公司’是否存在记录，
                // 是的话如果 comid 不含2，则是货主，如果comid含2又含其他，就是货主+船公司，如果只有2，就是船公司
                // 不存在则可以注册
                Pb30OlUser pb30Users = pb30OlUserService.getOne(new LambdaQueryWrapper<Pb30OlUser>()
                        .eq(Pb30OlUser::getCompanyId, carrierBO.getCompanyId())
                        .eq(Pb30OlUser::getRoleId, 3)
                        .eq(Pb30OlUser::getUserId, carrierBO.getAccount())
                );

                if (StringUtils.isNotNull(pb30Users)) {
                    String[] comIds = StrUtil.split(pb30Users.getComId(), ",");
                    for (String comId : comIds) {
                        if ("2".equals(comId)) {
                            return AjaxResult.success("可以登录", true);
                        }
                    }
                    log.error("您不是该船公司管理员，请检查信息。comId：{}", Arrays.toString(comIds));
                    return AjaxResult.success("您不是该船公司管理员，请检查信息。", false);
                }

                /*List<String> comIds = pb30Users.stream().filter(item -> item.getUserId().equals(carrierBO.getAccount()))
                        .map(Pb30OlUser::getComId)
                        .collect(Collectors.toList());*/

                /*if (comIds.size() <= 0) {
                    log.error("您不是该船公司管理员，请检查信息。comId：{}", comIds);
                    return AjaxResult.success("您不是该船公司管理员，请检查信息。", false);
                }*/

            }
        }
        return AjaxResult.success("可以登录", true);
    }

    /**
     * 注册
     * @param carrierBO
     * @return
     */
    @PostMapping("/webRegister")
    public AjaxResult webRegister(@RequestBody @Valid CarrierBO carrierBO, BindingResult result) {
        // 判断BindingResult是否保存错误的验证信息，如果有，则直接return
        if (result.hasErrors()) {
            return AjaxResult.getErrors(result);
        }

        // 判断该船公司是否已有管理员注册
        /*if (WebCarrierRoleType.ADMIN.getCode().equals(carrierBO.getRoleType())) {
            SysUser sysUser = new SysUser();
            sysUser.setCompanyId(carrierBO.getCompanyId());
            sysUser.setUserType(UserType.CARRIERADMIN.getCode());
            List<SysUser> list = iSysUserService.getUser(sysUser);
            if (list.size() > 0) {
                throw new CustomException("该船公司已有管理员，请重新选择");
            }
        }*/

        SSOUser temp = new SSOUser();
        temp.setAccount(carrierBO.getAccount());
        SSOUser ssoUser = ssoUserSerivce.getSSOUser(temp);
        if (ssoUser == null) {
            throw new CustomException("网上营业厅统一登录没有此用户");
        }
        carrierBO.setEmail(ssoUser.getEmail());
        carrierBO.setPassword(ssoUser.getPassword());
        log.info("SSO、sysuser注册开始");
        AjaxResult res = carrierInfoService.webRegister(carrierBO);
        log.info("SSO、sysuser注册成功");
        long userId = Convert.convert(Long.class,res.get("data"));
        if (WebCarrierRoleType.ADMIN.getCode().equals(carrierBO.getRoleType())) {
            String seal = carrierBO.getSealfileBase64();
            if (StringUtils.isBlank(seal)) {
                throw new CustomException("印章为空");
            }
            log.info("开始进行FDD注册");
            // 调用fdd企业注册方法
            String customerId = fddCompanyService.companyRegister(IdUtils.fastSimpleUUID(), carrierBO.getCompanyName());
            log.info("FDD的customerId");
            // 调用fdd企业实名认证方法 https://bulkcustomer.gzport.com/gzgapp/callback/updateSealState
            // https://vcbooking.gzport.com/test-api/gzgapp/callback/updateSealState
            FddCompanyService.CompanyCertificationUrlVO results = fddCompanyService.companyCertificationUrl(customerId, "https://bulkcustomer.gzport.com/gzgapp/callback/updateSealState");
            log.info("FDD的results");
            String path = commonController.saveSeal(seal, userId);
            log.info("保存FDD");
            //保存信息
            shipFddUserRelService.save(new ShipFddUserRel()
                    .setType("2")
                    .setReviewStatus("0")
                    .setSealUrl(path)
                    .setShipUserId(userId)
                    .setFddAccountId(customerId)
                    .setVerifiedSerialNo(results.getTransactionNo())
                    .setUrl(results.getUrl())
            );
        }
        return res;
    }

    @PostMapping("/customerList")
    public AjaxResult customerList(@RequestBody Customer customer){

        List<Customer> customerList = customerService.getCustomerList(customer);

        return AjaxResult.success(customerList);
    }

    //资料上传
    @RequestMapping("/upload")
    public AjaxResult uploadFiles(@RequestParam("files") MultipartFile[] files,@RequestParam("userId") String userId){

        List<OLPicture> list=new ArrayList<>();

        try {
            ChannelSftp channelSftp = ftpUtils.getConnect();

            //上传到ftp并保存路径到数据库
            if(files.length>0){
                for(int i = 0;i < files.length;i++){
                    MultipartFile file = files[i];
                    //保存文件
                    if (!file.isEmpty()){

                        String url = ftpUtils.uploadFiles(file, userId, channelSftp);
                        OLPicture olPicture=new OLPicture();
                        System.out.println(file.getOriginalFilename());
                        String[] split = file.getOriginalFilename().split("\\.");
                        file.getContentType();
                        olPicture.setUserId(userId);
                        olPicture.setImageInfo(split[0]);
                        olPicture.setImageUrl(url+"/");
                        olPicture.setImageName(parseImageName(split[0]));
                        olPicture.setImageType(split[1]);

                        list.add(olPicture);
                    }
                }
            }
            olPictureService.savepictrues(list,userId);
            return AjaxResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error();
        }

    }

    /**
     * 转换图片名称
     * @param simpleName
     * @return
     */
    private String parseImageName(String simpleName){
        switch (simpleName){
            case "yyzz":
                return "三证合一营业执照";
            case "sqs":
                return "船公司授权书";
            case "frsfzzmxx":
                return "法人身份证反面信息";
            case "frsfzfmxx":
                return "法人身份证正面信息";
            case "sfzzmxx":
                return "身份证反面信息";
            case "sfzfmxx":
                return "身份证正面信息";
            default:
                return null;
        }
    }
    /**
     * 获取船公司普通用户列表
     * @return
     */
    @PostMapping("/getCarrierUserList")
    public AjaxResult getCarrierUserList() {
        return AjaxResult.success("获取成功", carrierInfoService.getCarrierUserList());
    }

    /**
     * 获取印章认证跳转地址
     * @return
     */
    @RequestMapping("sealAuthUrl")
    public AjaxResult getSealAuthUrl(@RequestBody OlUser olUser){
        if(StringUtils.isEmpty(olUser.getUserId()) || StringUtils.isEmpty(olUser.getCompany())){
            throw new CustomException("认证参数异常");
        }
        //获取企业编号
        String companyNo = fddCompanyService.companyRegister(olUser.getUserId(), olUser.getCompany());
        if(StringUtils.isNotEmpty(companyNo)){
            /**
             * 1. 此客户编号 -- fdd账号--FDD_ACCOUNT_ID
             * 2. 回调地址https://船公司地址/gzgapp/callback/updateCompanySealState
             */
            String notifyUrl="http://127.0.0.1:8080/gzgapp/callback/updateCompanySealState";
            FddCompanyService.CompanyCertificationUrlVO companyCertificationUrlVO = fddCompanyService.companyCertificationUrl(companyNo, notifyUrl);
            if(companyCertificationUrlVO !=null){
                return AjaxResult.success(companyCertificationUrlVO);
            }
        }
        return null;

    }
}

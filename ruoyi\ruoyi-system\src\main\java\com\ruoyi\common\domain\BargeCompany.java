package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 驳船-公司 关系实体
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/10/15 11:26
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "PB6_BARGE_COMPANY")
@KeySequence("SEQ_PB6_BARGE_COMPANY")
public class BargeCompany {

    @TableId(value = "ID",type = IdType.INPUT)
    private Long id;

    /**
    * 驳船id
    */
    @TableField("bargeId")
    private Long bargeId;

    /**
    * 挂靠公司id
    */
    @TableField("companyId")
    private Long companyId;

    /**
    * 挂靠类型（（1-自有、2-临时挂靠、3-长期挂靠，4-其他））
    */
    @TableField("bindingType")
    private Short bindingType;

    /**
    * 是否生效(挂靠审核使用)  1-生效  0-无效
    */
    @TableField("status")
    private Short status;

    /**
    * 修改挂靠类型（（1-自有、2-临时挂靠、3-长期挂靠，4-其他））
    */
    @TableField("updateBindingType")
    private Short updateBindingType;

    /**
    * 审核人
    */
    @TableField("auditById")
    private Long auditById;

    /**
    * 审核时间
    */
    @TableField("auditTime")
    private String auditTime;

    /**
    * 挂靠审核（0-修改挂靠审核，1-挂靠审核， 2-取消挂靠审核）
    */
    @TableField("isAudit")
    private Short isAudit;

    /**
    * 创建人
    */
    @TableField("createById")
    private Long createById;

    /**
    * 创建时间
    */
    @TableField("createTime")
    private String createTime;

    /**
    * 修改人
    */
    @TableField("updateById")
    private Long updateById;

    /**
    * 修改时间
    */
    @TableField("updateTime")
    private String updateTime;

    /**
    * "挂靠审核（0：待审核，1：审核通过 2.审核不通过）"
    */
    @TableField("auditStatus")
    private Short auditStatus;

    /**
    * 审核人名称
    */
    @TableField("auditByName")
    private String auditByName;

    /**
    * 创建人名称
    */
    @TableField("createByName")
    private String createByName;

    /**
    * 修改人名称
    */
    @TableField("updateByName")
    private String updateByName;

    /**
    * 是否已删除标识（0-已删除，1-生效）
    */
    @TableField("isDelete")
    private Short isDelete;

    @TableField("COMPANYNAME")
    private String companyName;
}
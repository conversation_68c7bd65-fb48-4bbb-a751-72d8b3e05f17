package com.ruoyi.databarge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.databarge.domain.Pb6Bargeinfo;
import com.ruoyi.databarge.domain.dto.Pb6BargeinfoAuditSearchDTO;
import com.ruoyi.databarge.domain.vo.BargeCompanyResultVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Description:
 *
 * @Author: ChenJin on 2020/8/27.
 * @Date: 2020/8/27 16:19
 */
public interface Pb6BargeinfoMapper  extends BaseMapper<Pb6Bargeinfo> {

    @Select("select BARGENAME from PB6_BARGEINFO where id=#{id}")
    public String getBargeNameById(Long id);

    IPage<BargeCompanyResultVO> searchPagePb6Bargeinfo(Page<BargeCompanyResultVO> page, @Param("pb6BargeinfoAuditSearchDTO") Pb6BargeinfoAuditSearchDTO pb6BargeinfoAuditSearchDTO);

    BargeCompanyResultVO searchPb6BargeinfoById(@Param("pb6BargeInfoId") Long pb6BargeInfoId);

    Integer insertById(@Param("pb6Bargeinfo") Pb6Bargeinfo pb6Bargeinfo);

    //通过驳船挂靠表中存的驳船基本信息表ID 查询驳船基本信息
    Pb6Bargeinfo searchPb6BargeinfoByPb6BargeCompanyId(@Param("pb6BargeInfoAuditId") Long pb6BargeInfoAuditId);

    String searchMmsiByBargeName(@Param("bargeName") String bargeName);
}

package com.ruoyi.basic.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/1
 * @Description
 */
@Data
@TableName("MENU_FINEREPORT")
@KeySequence("MENU_FINEREPORT_SEQ")
public class BasicFineReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 单证管理id */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /** 菜单名称 */
    private String menuName;

    /** 报表名称 */
    private String moduleName;

    /** 报表url */
    private String moduleUrl;
}

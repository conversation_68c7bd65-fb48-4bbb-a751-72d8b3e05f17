package com.ruoyi.common.enums;

import java.util.Objects;

/**
 * 支付方式 枚举
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/8/10 15:24
 */
public enum PayWayEnum {

    /**
     * 0-月结，1-现结-微信支付
     */
    MONTHLY_PAY("0", "月结"),
    WX_PAY("1", "现结");

    private final String code;

    private final String codeName;

    PayWayEnum(String code, String codeName) {
        this.code = code;
        this.codeName = codeName;
    }

    public String getCode() {
        return code;
    }

    public String getCodeName() {
        return codeName;
    }

    /**
     * 枚举值获取描述值
     * @param code
     * @return
     */
    public static String codeToCodeName(String code){
        if(Objects.nonNull(code)){
            for (PayWayEnum status : PayWayEnum.values()) {
                if (status.code.equals(code)){
                    return status.codeName;
                }
            }
        }
        return null;
    }

    /**
     * 描述值获取枚举值
     * @param codeName
     * @return
     */
    public static String codeNameToCode(String codeName){
        if(Objects.nonNull(codeName)){
            for (PayWayEnum status : PayWayEnum.values()) {
                if (status.codeName.equals(codeName)){
                    return status.code;
                }
            }
        }
        return null;
    }
}

package com.ruoyi.databarge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.databarge.domain.Pb6BargeCheckNode;
import com.ruoyi.databarge.mapper.Pb6BargeCheckNodeMapper;
import com.ruoyi.databarge.service.Pb6BargeCheckNodeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 驳船单证退单改单审核流程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-23
 */
@Service
public class Pb6BargeCheckNodeServiceImpl extends ServiceImpl<Pb6BargeCheckNodeMapper, Pb6BargeCheckNode> implements Pb6BargeCheckNodeService {

    @Override
    public List<Pb6BargeCheckNode> searchByPb6CargoconsignmentDetailId(Long pb6CargoconsignmentDetailId) {
        QueryWrapper<Pb6BargeCheckNode> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Pb6BargeCheckNode::getLinkid, pb6CargoconsignmentDetailId).orderByDesc(Pb6BargeCheckNode::getId);
        return super.list(queryWrapper);
    }

    @Override
    public Pb6BargeCheckNode searchRecentByLinkId(Long linkId) {
        return super.baseMapper.searchRecentByLinkId(linkId);
    }
}

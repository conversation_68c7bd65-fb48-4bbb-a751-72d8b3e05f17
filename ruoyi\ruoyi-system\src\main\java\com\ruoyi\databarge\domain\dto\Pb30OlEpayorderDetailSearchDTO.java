package com.ruoyi.databarge.domain.dto;

import com.ruoyi.common.core.domain.BaseParamEntity;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2020/11/17 10:01
 * @Description:
 */
@Getter
@Setter
public class Pb30OlEpayorderDetailSearchDTO extends BaseParamEntity {

    private String waterwaycargoid;
    //private String paytime;
    private String payStartTime;
    private String payEndTime;
    private String comid;
    private String status;
    private String paytype;
    private String resourse;
    private String state; //发票状态
    private String flagbargestate; //驳船状态

    @NotNull(message = "不能为空!")
    private Integer pageNum;
    @NotNull(message = "不能为空!")
    private Integer pageSize;

}
